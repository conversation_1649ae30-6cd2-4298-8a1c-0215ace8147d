/**
 * @file 收据模板 - 实现收据文档的模板渲染功能
 * <AUTHOR> Team
 * @description 
 * 这个文件定义了收据文档的模板类，包括：
 * - ReceiptTemplate 类，继承自 BaseTemplate
 * - 收据专用的渲染逻辑和样式配置
 * - 收据字段验证和格式化处理
 * - 支持多种收据样式主题
 */

// #region 导入依赖模块
import { BaseTemplate } from './base-template.js';
import { ReceiptDocument } from '../models/receipt-document.js';
import { StringUtils } from '../core/utils/string-utils.js';
import { DateUtils } from '../core/utils/date-utils.js';
// #endregion

// #region 收据模板类定义
/**
 * @class ReceiptTemplate - 收据模板类
 * @description 专门用于渲染收据文档的模板类，继承自BaseTemplate
 */
export class ReceiptTemplate extends BaseTemplate {
    /**
     * 构造函数 - 初始化收据模板实例
     * @param {Object} config - 模板配置对象
     */
    constructor(config = {}) {
        // 设置收据模板的默认配置
        const defaultConfig = {
            name: config.name || 'receipt-template',
            type: 'receipt',
            version: config.version || '1.0.0',
            metadata: {
                title: '收据模板',
                description: '用于生成收据文档的标准模板',
                author: 'SmartOffice Team',
                created: new Date(),
                ...config.metadata
            },
            
            // 收据专用的渲染配置
            renderConfig: {
                theme: 'classic', // 默认主题
                language: 'zh-CN', // 默认语言
                currency: 'RM', // 默认货币
                dateFormat: 'YYYY-MM-DD', // 日期格式
                showTax: true, // 是否显示税务信息
                showQR: false, // 是否显示二维码
                compactMode: false, // 是否启用紧凑模式
                ...config.renderConfig
            },
            
            // 收据专用样式配置
            styles: {
                // 容器样式
                container: {
                    width: '80mm',
                    maxWidth: '300px',
                    margin: '0 auto',
                    padding: '10px',
                    fontFamily: 'Arial, "Microsoft YaHei", sans-serif',
                    fontSize: '12px',
                    lineHeight: '1.4',
                    color: '#333',
                    backgroundColor: '#fff'
                },
                
                // 页眉样式
                header: {
                    textAlign: 'center',
                    marginBottom: '15px',
                    borderBottom: '1px solid #ddd',
                    paddingBottom: '10px'
                },
                
                // 标题样式
                title: {
                    fontSize: '16px',
                    fontWeight: 'bold',
                    marginBottom: '5px',
                    color: '#000'
                },
                
                // 公司信息样式
                company: {
                    fontSize: '11px',
                    color: '#666',
                    marginBottom: '5px'
                },
                
                // 收据信息样式
                receiptInfo: {
                    marginBottom: '15px',
                    fontSize: '11px'
                },
                
                // 项目表格样式
                itemsTable: {
                    width: '100%',
                    borderCollapse: 'collapse',
                    marginBottom: '10px'
                },
                
                // 表格行样式
                tableRow: {
                    borderBottom: '1px dotted #ccc'
                },
                
                // 表格单元格样式
                tableCell: {
                    padding: '3px 0',
                    verticalAlign: 'top'
                },
                
                // 合计区域样式
                totals: {
                    borderTop: '1px solid #333',
                    paddingTop: '10px',
                    marginTop: '10px'
                },
                
                // 页脚样式
                footer: {
                    marginTop: '15px',
                    paddingTop: '10px',
                    borderTop: '1px dotted #ccc',
                    textAlign: 'center',
                    fontSize: '10px',
                    color: '#999'
                },
                
                ...config.styles
            },
            
            // 收据字段验证规则
            validationRules: {
                receiptNumber: {
                    type: 'string',
                    minLength: 1,
                    maxLength: 50,
                    pattern: /^[A-Z0-9\-]+$/i
                },
                amount: {
                    type: 'number',
                    validator: (value) => value > 0
                },
                date: {
                    validator: (value) => value instanceof Date || !isNaN(Date.parse(value))
                },
                customerName: {
                    type: 'string',
                    minLength: 1,
                    maxLength: 100
                },
                ...config.validationRules
            },
            
            // 必填字段列表
            requiredFields: [
                'receiptNumber',
                'amount',
                'date',
                'customerName',
                ...config.requiredFields || []
            ]
        };
        
        // 调用父类构造函数
        super(defaultConfig);
        
        // 收据专用属性
        this.supportedThemes = ['classic', 'modern', 'elegant', 'compact'];
        this.supportedLanguages = ['zh-CN', 'en-US', 'ms-MY'];
        this.supportedCurrencies = ['RM', 'USD', 'CNY'];
    }

    /**
     * 执行实际渲染 - 实现基类的抽象方法
     * @param {Object} data - 预处理后的数据对象
     * @param {Object} options - 渲染选项配置
     * @returns {Promise<string>} 渲染结果字符串
     * @protected
     */
    async _doRender(data, options) {
        try {
            // 确保数据完整性
            const receiptData = await this._ensureDataCompleteness(data);
            
            // 根据主题选择渲染方法
            switch (options.theme) {
                case 'modern':
                    return await this._renderModernTheme(receiptData, options);
                case 'elegant':
                    return await this._renderElegantTheme(receiptData, options);
                case 'compact':
                    return await this._renderCompactTheme(receiptData, options);
                default:
                    return await this._renderClassicTheme(receiptData, options);
            }
        } catch (error) {
            throw new Error(`收据模板渲染失败: ${error.message}`);
        }
    }

    /**
     * 确保数据完整性 - 补充缺失的收据数据
     * @param {Object} data - 原始数据
     * @returns {Promise<Object>} 完整的收据数据
     * @private
     */
    async _ensureDataCompleteness(data) {
        const receiptData = { ...data };
        
        // 确保有收据号
        if (!receiptData.receiptNumber) {
            receiptData.receiptNumber = this._generateReceiptNumber();
        }
        
        // 确保有日期
        if (!receiptData.date) {
            receiptData.date = new Date();
        }
        
        // 格式化日期
        if (receiptData.date instanceof Date || typeof receiptData.date === 'string') {
            receiptData.formattedDate = DateUtils.format(
                new Date(receiptData.date),
                this.renderConfig.dateFormat,
                this.renderConfig.language
            );
        }
        
        // 计算税务信息
        if (receiptData.amount && typeof receiptData.amount === 'number') {
            receiptData.taxRate = receiptData.taxRate || 0;
            receiptData.taxAmount = receiptData.amount * receiptData.taxRate / 100;
            receiptData.totalAmount = receiptData.amount + receiptData.taxAmount;
            
            // 格式化金额
            receiptData.formattedAmount = StringUtils.formatCurrency(
                receiptData.amount,
                this.renderConfig.currency,
                2,
                this.renderConfig.language
            );
            
            receiptData.formattedTaxAmount = StringUtils.formatCurrency(
                receiptData.taxAmount,
                this.renderConfig.currency,
                2,
                this.renderConfig.language
            );
            
            receiptData.formattedTotalAmount = StringUtils.formatCurrency(
                receiptData.totalAmount,
                this.renderConfig.currency,
                2,
                this.renderConfig.language
            );
        }
        
        // 确保有客户信息
        if (!receiptData.customerName) {
            receiptData.customerName = '匿名客户';
        }
        
        // 处理支付方式
        if (!receiptData.paymentMethod) {
            receiptData.paymentMethod = 'cash';
        }
        
        receiptData.paymentMethodText = this._getPaymentMethodText(
            receiptData.paymentMethod,
            this.renderConfig.language
        );
        
        return receiptData;
    }

    /**
     * 渲染经典主题收据 - 标准的收据样式
     * @param {Object} data - 收据数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderClassicTheme(data, options) {
        const html = `
            <div class="receipt-container classic-theme">
                ${await this._renderHeader(data, options)}
                ${await this._renderReceiptInfo(data, options)}
                ${await this._renderItems(data, options)}
                ${await this._renderTotals(data, options)}
                ${await this._renderFooter(data, options)}
            </div>
        `;
        
        return html;
    }

    /**
     * 渲染现代主题收据 - 现代化的收据样式
     * @param {Object} data - 收据数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderModernTheme(data, options) {
        const html = `
            <div class="receipt-container modern-theme">
                <div class="modern-header">
                    ${await this._renderHeader(data, options)}
                </div>
                <div class="modern-content">
                    ${await this._renderReceiptInfo(data, options)}
                    ${await this._renderItems(data, options)}
                    ${await this._renderTotals(data, options)}
                </div>
                <div class="modern-footer">
                    ${await this._renderFooter(data, options)}
                </div>
            </div>
        `;
        
        return html;
    }

    /**
     * 渲染优雅主题收据 - 优雅精致的收据样式
     * @param {Object} data - 收据数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderElegantTheme(data, options) {
        const html = `
            <div class="receipt-container elegant-theme">
                <div class="elegant-border">
                    ${await this._renderHeader(data, options)}
                    <div class="elegant-divider"></div>
                    ${await this._renderReceiptInfo(data, options)}
                    ${await this._renderItems(data, options)}
                    <div class="elegant-divider"></div>
                    ${await this._renderTotals(data, options)}
                    ${await this._renderFooter(data, options)}
                </div>
            </div>
        `;
        
        return html;
    }

    /**
     * 渲染紧凑主题收据 - 节省空间的收据样式
     * @param {Object} data - 收据数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderCompactTheme(data, options) {
        const html = `
            <div class="receipt-container compact-theme">
                ${await this._renderCompactHeader(data, options)}
                ${await this._renderCompactContent(data, options)}
                ${await this._renderCompactFooter(data, options)}
            </div>
        `;
        
        return html;
    }

    /**
     * 渲染收据页眉 - 生成收据的页眉部分
     * @param {Object} data - 收据数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderHeader(data, options) {
        const company = data.companyInfo || {};
        const companyName = company.name || data.companyName || '智能办公系统';
        const companyAddress = company.address || data.companyAddress || '';
        const companyPhone = company.phone || data.companyPhone || '';
        
        return `
            <div class="receipt-header">
                ${options.showLogo && company.logo ? `
                <div class="company-logo">
                    <img src="${company.logo}" alt="${companyName}" class="logo-image">
                </div>
                ` : ''}
                <div class="receipt-title">收据 / RECEIPT</div>
                <div class="company-info">
                    <div class="company-name">${companyName}</div>
                    ${companyAddress ? `<div class="company-address">${companyAddress}</div>` : ''}
                    ${companyPhone ? `<div class="company-phone">Tel: ${companyPhone}</div>` : ''}
                </div>
                ${options.showStamp && company.stamp ? `
                <div class="company-stamp">
                    <img src="${company.stamp}" alt="公司印章" class="stamp-image">
                </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * 渲染收据信息 - 生成收据号、日期等基本信息
     * @param {Object} data - 收据数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderReceiptInfo(data, options) {
        return `
            <div class="receipt-info">
                <div class="info-row">
                    <span class="info-label">收据号码:</span>
                    <span class="info-value">${data.receiptNumber}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">日期:</span>
                    <span class="info-value">${data.formattedDate}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">客户:</span>
                    <span class="info-value">${data.customerName}</span>
                </div>
                ${data.channel ? `
                <div class="info-row">
                    <span class="info-label">渠道:</span>
                    <span class="info-value">${data.channel}</span>
                </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * 渲染服务项目 - 生成服务项目列表
     * @param {Object} data - 收据数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderItems(data, options) {
        if (!data.items || !Array.isArray(data.items) || data.items.length === 0) {
            // 如果没有项目列表，创建一个简单的项目
            return `
                <div class="receipt-items">
                    <div class="item-row">
                        <span class="item-description">${data.description || '服务费用'}</span>
                        <span class="item-amount">${data.formattedAmount}</span>
                    </div>
                </div>
            `;
        }
        
        const itemsHtml = data.items.map(item => {
            const formattedAmount = StringUtils.formatCurrency(
                item.amount || 0,
                this.renderConfig.currency,
                2,
                this.renderConfig.language
            );
            
            return `
                <div class="item-row">
                    <span class="item-description">${item.description || item.name || '服务项目'}</span>
                    ${item.quantity && item.quantity > 1 ? `
                    <span class="item-quantity">x${item.quantity}</span>
                    ` : ''}
                    <span class="item-amount">${formattedAmount}</span>
                </div>
            `;
        }).join('');
        
        return `
            <div class="receipt-items">
                ${itemsHtml}
            </div>
        `;
    }

    /**
     * 渲染合计信息 - 生成金额合计、税费等信息
     * @param {Object} data - 收据数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderTotals(data, options) {
        let totalsHtml = '';
        
        // 如果显示税务信息且有税费
        if (options.showTax && data.taxAmount > 0) {
            totalsHtml += `
                <div class="total-row">
                    <span class="total-label">小计:</span>
                    <span class="total-value">${data.formattedAmount}</span>
                </div>
                <div class="total-row">
                    <span class="total-label">税费 (${data.taxRate}%):</span>
                    <span class="total-value">${data.formattedTaxAmount}</span>
                </div>
            `;
        }
        
        totalsHtml += `
            <div class="total-row total-final">
                <span class="total-label">合计:</span>
                <span class="total-value">${data.formattedTotalAmount || data.formattedAmount}</span>
            </div>
        `;
        
        // 支付方式
        totalsHtml += `
            <div class="payment-row">
                <span class="payment-label">支付方式:</span>
                <span class="payment-value">${data.paymentMethodText}</span>
            </div>
        `;
        
        return `
            <div class="receipt-totals">
                ${totalsHtml}
            </div>
        `;
    }

    /**
     * 渲染收据页脚 - 生成感谢语、联系信息等
     * @param {Object} data - 收据数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderFooter(data, options) {
        const thankYouText = this._getThankYouText(this.renderConfig.language);
        const currentTime = DateUtils.format(new Date(), 'YYYY-MM-DD HH:mm:ss', this.renderConfig.language);
        const company = data.companyInfo || {};
        
        return `
            <div class="receipt-footer">
                ${options.showFooterImage && company.footer ? `
                <div class="company-footer-image">
                    <img src="${company.footer}" alt="公司页脚" class="footer-image">
                </div>
                ` : ''}
                <div class="thank-you">${thankYouText}</div>
                ${data.notes ? `<div class="notes">${data.notes}</div>` : ''}
                <div class="print-time">打印时间: ${currentTime}</div>
                ${options.showQR ? `<div class="qr-code">[二维码位置]</div>` : ''}
            </div>
        `;
    }

    /**
     * 渲染紧凑页眉 - 紧凑主题的页眉
     * @param {Object} data - 收据数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderCompactHeader(data, options) {
        return `
            <div class="compact-header">
                <span class="compact-title">收据</span>
                <span class="compact-number">${data.receiptNumber}</span>
                <span class="compact-date">${data.formattedDate}</span>
            </div>
        `;
    }

    /**
     * 渲染紧凑内容 - 紧凑主题的主要内容
     * @param {Object} data - 收据数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderCompactContent(data, options) {
        return `
            <div class="compact-content">
                <div class="compact-customer">客户: ${data.customerName}</div>
                <div class="compact-amount">金额: ${data.formattedTotalAmount || data.formattedAmount}</div>
                <div class="compact-payment">方式: ${data.paymentMethodText}</div>
            </div>
        `;
    }

    /**
     * 渲染紧凑页脚 - 紧凑主题的页脚
     * @param {Object} data - 收据数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderCompactFooter(data, options) {
        return `
            <div class="compact-footer">
                <div class="compact-thanks">谢谢惠顾</div>
            </div>
        `;
    }

    /**
     * 生成收据号 - 创建唯一的收据编号
     * @returns {string} 收据号
     * @private
     */
    _generateReceiptNumber() {
        const date = new Date();
        const dateStr = DateUtils.format(date, 'YYYYMMDD');
        const timeStr = DateUtils.format(date, 'HHmmss');
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `RCP${dateStr}${timeStr}${random}`;
    }

    /**
     * 获取支付方式文本 - 根据语言获取支付方式的显示文本
     * @param {string} paymentMethod - 支付方式代码
     * @param {string} language - 语言代码
     * @returns {string} 支付方式文本
     * @private
     */
    _getPaymentMethodText(paymentMethod, language) {
        const texts = {
            'zh-CN': {
                'cash': '现金',
                'card': '银行卡',
                'alipay': '支付宝',
                'wechat': '微信支付',
                'transfer': '银行转账',
                'other': '其他'
            },
            'en-US': {
                'cash': 'Cash',
                'card': 'Card',
                'alipay': 'Alipay',
                'wechat': 'WeChat Pay',
                'transfer': 'Bank Transfer',
                'other': 'Other'
            },
            'ms-MY': {
                'cash': 'Tunai',
                'card': 'Kad',
                'alipay': 'Alipay',
                'wechat': 'WeChat Pay',
                'transfer': 'Pindahan Bank',
                'other': 'Lain-lain'
            }
        };
        
        return texts[language]?.[paymentMethod] || texts['zh-CN'][paymentMethod] || paymentMethod;
    }

    /**
     * 获取感谢语文本 - 根据语言获取感谢语
     * @param {string} language - 语言代码
     * @returns {string} 感谢语文本
     * @private
     */
    _getThankYouText(language) {
        const texts = {
            'zh-CN': '谢谢惠顾！',
            'en-US': 'Thank you for your business!',
            'ms-MY': 'Terima kasih atas perniagaan anda!'
        };
        
        return texts[language] || texts['zh-CN'];
    }

    /**
     * 获取支持的主题列表 - 返回收据模板支持的所有主题
     * @returns {Array<Object>} 主题信息数组
     */
    getSupportedThemes() {
        return [
            {
                id: 'classic',
                name: '经典',
                description: '标准的收据样式，适合大多数场景'
            },
            {
                id: 'modern',
                name: '现代',
                description: '现代化设计，更加美观大方'
            },
            {
                id: 'elegant',
                name: '优雅',
                description: '优雅精致，适合高端场所'
            },
            {
                id: 'compact',
                name: '紧凑',
                description: '节省空间，适合快速打印'
            }
        ];
    }

    /**
     * 验证收据数据 - 检查收据数据的完整性和有效性
     * @param {Object} data - 要验证的收据数据
     * @returns {Object} 验证结果 {isValid: boolean, errors: Array}
     */
    validateReceiptData(data) {
        const errors = [];
        
        // 检查必填字段
        if (!data.receiptNumber) {
            errors.push('缺少收据号码');
        }
        
        if (!data.amount || data.amount <= 0) {
            errors.push('金额必须大于0');
        }
        
        if (!data.customerName) {
            errors.push('缺少客户名称');
        }
        
        if (!data.date) {
            errors.push('缺少日期');
        }
        
        // 检查数据格式
        if (data.amount && typeof data.amount !== 'number') {
            errors.push('金额格式不正确');
        }
        
        if (data.taxRate && (data.taxRate < 0 || data.taxRate > 100)) {
            errors.push('税率必须在0-100之间');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 创建默认收据数据 - 生成一个包含默认值的收据数据对象
     * @param {Object} overrides - 要覆盖的默认值
     * @returns {Object} 默认收据数据
     */
    createDefaultData(overrides = {}) {
        return {
            receiptNumber: this._generateReceiptNumber(),
            date: new Date(),
            customerName: '客户',
            amount: 0,
            taxRate: 0,
            paymentMethod: 'cash',
            description: '服务费用',
            companyName: '智能办公系统',
            notes: '',
            ...overrides
        };
    }
}
// #endregion

// #region 工厂函数
/**
 * 创建收据模板实例 - 工厂函数，方便创建收据模板
 * @param {Object} config - 模板配置
 * @returns {ReceiptTemplate} 收据模板实例
 */
export function createReceiptTemplate(config = {}) {
    return new ReceiptTemplate(config);
}

/**
 * 创建预定义的收据模板 - 创建具有预定义样式的收据模板
 * @param {string} theme - 主题名称
 * @param {Object} config - 额外配置
 * @returns {ReceiptTemplate} 收据模板实例
 */
export function createPresetReceiptTemplate(theme = 'classic', config = {}) {
    const presetConfig = {
        ...config,
        renderConfig: {
            theme,
            ...config.renderConfig
        }
    };
    
    return new ReceiptTemplate(presetConfig);
}
// #endregion 