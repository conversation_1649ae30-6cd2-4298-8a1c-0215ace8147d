/**
 * @file PDF渲染器 - 整合后的PDF格式渲染器
 * <AUTHOR> Team
 * @description 
 * 这是重构后的PDF渲染器，整合了原有的PDF生成功能
 * 支持多种PDF生成引擎和高质量输出
 * 
 * 重构说明：
 * - 继承自统一的BaseRenderer
 * - 整合了原有PDF渲染逻辑
 * - 支持多种PDF生成引擎
 * - 优化了PDF质量和性能
 */

// #region 导入依赖
import { BaseRenderer } from './base-renderer.js';
import { HTMLRenderer } from './html-renderer.js';
// #endregion

// #region PDF渲染器类定义
/**
 * @class PDFRenderer - PDF渲染器
 * @description 专门用于PDF格式的文档渲染
 */
export class PDFRenderer extends BaseRenderer {
    /**
     * 构造函数 - 初始化PDF渲染器
     * @param {Object} config - 渲染器配置
     */
    constructor(config = {}) {
        super({
            name: 'PDFRenderer',
            version: '2.0.0',
            type: 'pdf',
            format: 'pdf',
            ...config
        });
        
        // PDF特定配置
        this.pdfConfig = {
            engine: 'html2pdf', // 'html2pdf', 'jspdf', 'puppeteer'
            quality: 'high',
            format: 'A4',
            orientation: 'portrait',
            margin: {
                top: '20mm',
                bottom: '20mm',
                left: '15mm',
                right: '15mm'
            },
            enableImages: true,
            enableFonts: true,
            enableColors: true,
            compression: true,
            ...config.pdfConfig
        };
        
        // HTML渲染器实例（用于生成HTML内容）
        this.htmlRenderer = new HTMLRenderer(config);
        
        this.logger.info('PDFRenderer', 'constructor', 'PDF渲染器创建完成');
    }
    
    /**
     * 执行PDF渲染
     * @param {Object} preparedData - 准备好的数据
     * @returns {Promise<Object>} 渲染结果
     * @protected
     */
    async _executeRender(preparedData) {
        const { document, options } = preparedData;
        
        this.logger.info('PDFRenderer', '_executeRender', '开始PDF渲染');
        
        try {
            // 首先生成HTML内容
            const htmlResult = await this.htmlRenderer._executeRender(preparedData);
            const htmlContent = htmlResult.content;
            
            // 根据引擎选择PDF生成方法
            let pdfData;
            switch (this.pdfConfig.engine) {
                case 'html2pdf':
                    pdfData = await this._renderWithHtml2PDF(htmlContent, options);
                    break;
                case 'jspdf':
                    pdfData = await this._renderWithJsPDF(htmlContent, options);
                    break;
                case 'puppeteer':
                    pdfData = await this._renderWithPuppeteer(htmlContent, options);
                    break;
                default:
                    throw new Error(`不支持的PDF引擎: ${this.pdfConfig.engine}`);
            }
            
            return {
                content: pdfData,
                type: 'pdf',
                size: pdfData.byteLength || pdfData.length,
                format: this.pdfConfig.format,
                engine: this.pdfConfig.engine
            };
            
        } catch (error) {
            this.logger.error('PDFRenderer', '_executeRender', 'PDF渲染失败', error);
            throw error;
        }
    }
    
    /**
     * 使用html2pdf引擎渲染PDF
     * @param {string} htmlContent - HTML内容
     * @param {Object} options - 渲染选项
     * @returns {Promise<Blob>} PDF数据
     * @private
     */
    async _renderWithHtml2PDF(htmlContent, options) {
        this.logger.info('PDFRenderer', '_renderWithHtml2PDF', '使用html2pdf引擎渲染');
        
        // 检查html2pdf是否可用
        if (typeof window === 'undefined' || !window.html2pdf) {
            throw new Error('html2pdf库未加载');
        }
        
        // 创建临时容器
        const tempContainer = document.createElement('div');
        tempContainer.innerHTML = htmlContent;
        tempContainer.style.position = 'absolute';
        tempContainer.style.left = '-9999px';
        tempContainer.style.top = '-9999px';
        document.body.appendChild(tempContainer);
        
        try {
            // 配置html2pdf选项
            const html2pdfOptions = {
                margin: [
                    this._convertToMm(this.pdfConfig.margin.top),
                    this._convertToMm(this.pdfConfig.margin.right),
                    this._convertToMm(this.pdfConfig.margin.bottom),
                    this._convertToMm(this.pdfConfig.margin.left)
                ],
                filename: `document_${Date.now()}.pdf`,
                image: { 
                    type: 'jpeg', 
                    quality: this.pdfConfig.quality === 'high' ? 0.98 : 0.85 
                },
                html2canvas: { 
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff'
                },
                jsPDF: { 
                    unit: 'mm', 
                    format: this.pdfConfig.format.toLowerCase(), 
                    orientation: this.pdfConfig.orientation 
                }
            };
            
            // 生成PDF
            const pdfBlob = await window.html2pdf()
                .set(html2pdfOptions)
                .from(tempContainer)
                .outputPdf('blob');
            
            return pdfBlob;
            
        } finally {
            // 清理临时容器
            document.body.removeChild(tempContainer);
        }
    }
    
    /**
     * 使用jsPDF引擎渲染PDF
     * @param {string} htmlContent - HTML内容
     * @param {Object} options - 渲染选项
     * @returns {Promise<Blob>} PDF数据
     * @private
     */
    async _renderWithJsPDF(htmlContent, options) {
        this.logger.info('PDFRenderer', '_renderWithJsPDF', '使用jsPDF引擎渲染');
        
        // 检查jsPDF是否可用
        if (typeof window === 'undefined' || !window.jsPDF) {
            throw new Error('jsPDF库未加载');
        }
        
        // 创建jsPDF实例
        const { jsPDF } = window.jsPDF;
        const pdf = new jsPDF({
            orientation: this.pdfConfig.orientation,
            unit: 'mm',
            format: this.pdfConfig.format.toLowerCase()
        });
        
        // 解析HTML并添加到PDF
        // 注意：jsPDF的HTML支持有限，这里需要简化处理
        const textContent = this._extractTextFromHTML(htmlContent);
        const lines = pdf.splitTextToSize(textContent, 180); // A4宽度约180mm
        
        let y = 20; // 起始Y位置
        const lineHeight = 7; // 行高
        
        for (const line of lines) {
            if (y > 280) { // 接近页面底部时换页
                pdf.addPage();
                y = 20;
            }
            pdf.text(line, 15, y);
            y += lineHeight;
        }
        
        // 返回PDF Blob
        return pdf.output('blob');
    }
    
    /**
     * 使用Puppeteer引擎渲染PDF
     * @param {string} htmlContent - HTML内容
     * @param {Object} options - 渲染选项
     * @returns {Promise<Buffer>} PDF数据
     * @private
     */
    async _renderWithPuppeteer(htmlContent, options) {
        this.logger.info('PDFRenderer', '_renderWithPuppeteer', '使用Puppeteer引擎渲染');
        
        // 注意：Puppeteer通常在Node.js环境中使用，浏览器环境中不可用
        throw new Error('Puppeteer引擎在浏览器环境中不可用');
    }
    
    /**
     * 从HTML中提取纯文本
     * @param {string} htmlContent - HTML内容
     * @returns {string} 纯文本内容
     * @private
     */
    _extractTextFromHTML(htmlContent) {
        // 创建临时DOM元素来解析HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = htmlContent;
        
        // 移除script和style标签
        const scripts = tempDiv.querySelectorAll('script, style');
        scripts.forEach(el => el.remove());
        
        // 获取纯文本
        let text = tempDiv.textContent || tempDiv.innerText || '';
        
        // 清理多余的空白字符
        text = text.replace(/\s+/g, ' ').trim();
        
        return text;
    }
    
    /**
     * 转换尺寸单位为毫米
     * @param {string} size - 尺寸字符串（如 "20mm", "1in", "72px"）
     * @returns {number} 毫米值
     * @private
     */
    _convertToMm(size) {
        if (typeof size === 'number') {
            return size;
        }
        
        const value = parseFloat(size);
        const unit = size.replace(value.toString(), '').toLowerCase();
        
        switch (unit) {
            case 'mm':
                return value;
            case 'cm':
                return value * 10;
            case 'in':
                return value * 25.4;
            case 'px':
                return value * 0.264583; // 96 DPI
            case 'pt':
                return value * 0.352778;
            default:
                return value; // 假设是毫米
        }
    }
    
    /**
     * 验证PDF引擎可用性
     * @returns {Object} 引擎可用性状态
     */
    checkEngineAvailability() {
        const availability = {
            html2pdf: typeof window !== 'undefined' && !!window.html2pdf,
            jspdf: typeof window !== 'undefined' && !!window.jsPDF,
            puppeteer: false // 浏览器环境中不可用
        };
        
        this.logger.info('PDFRenderer', 'checkEngineAvailability', 'PDF引擎可用性', availability);
        
        return availability;
    }
    
    /**
     * 设置PDF配置
     * @param {Object} config - PDF配置
     */
    setPDFConfig(config) {
        this.pdfConfig = {
            ...this.pdfConfig,
            ...config
        };
        
        this.logger.info('PDFRenderer', 'setPDFConfig', 'PDF配置已更新', this.pdfConfig);
    }
    
    /**
     * 获取PDF配置
     * @returns {Object} PDF配置
     */
    getPDFConfig() {
        return { ...this.pdfConfig };
    }
    
    /**
     * 获取支持的PDF格式
     * @returns {Array<string>} 支持的格式列表
     */
    getSupportedFormats() {
        return ['A4', 'A3', 'A5', 'Letter', 'Legal'];
    }
    
    /**
     * 获取支持的方向
     * @returns {Array<string>} 支持的方向列表
     */
    getSupportedOrientations() {
        return ['portrait', 'landscape'];
    }
}
// #endregion

// #region 工厂函数
/**
 * 创建PDF渲染器实例
 * @param {Object} config - 配置
 * @returns {PDFRenderer} PDF渲染器实例
 */
export function createPDFRenderer(config = {}) {
    return new PDFRenderer(config);
}

/**
 * 创建预设的PDF渲染器
 * @param {string} preset - 预设名称 ('high-quality', 'fast', 'compact')
 * @returns {PDFRenderer} PDF渲染器实例
 */
export function createPresetPDFRenderer(preset = 'high-quality') {
    const presets = {
        'high-quality': {
            pdfConfig: {
                engine: 'html2pdf',
                quality: 'high',
                format: 'A4',
                enableImages: true,
                enableFonts: true,
                compression: false
            }
        },
        'fast': {
            pdfConfig: {
                engine: 'jspdf',
                quality: 'medium',
                format: 'A4',
                enableImages: false,
                enableFonts: false,
                compression: true
            }
        },
        'compact': {
            pdfConfig: {
                engine: 'html2pdf',
                quality: 'medium',
                format: 'A4',
                enableImages: true,
                enableFonts: true,
                compression: true
            }
        }
    };
    
    const config = presets[preset] || presets['high-quality'];
    return new PDFRenderer(config);
}
// #endregion
