/**
 * @file 缓存管理器 - SmartOffice 缓存管理系统
 * <AUTHOR> Team
 * @description 
 * 这个文件实现了缓存管理系统，提供：
 * - 多级缓存策略
 * - 缓存生命周期管理
 * - 缓存性能优化
 * - 缓存统计和监控
 * - 缓存清理和回收
 */

// #region 导入依赖模块
import { EventEmitter } from '../events/event-emitter.js';
import { CacheEvents, AppEvents } from '../events/event-types.js';
import { getLogger } from '../utils/logger.js';
// #endregion

// #region CacheManager 缓存管理器类
/**
 * @class CacheManager - 缓存管理器
 * @description 管理应用程序的多级缓存系统
 */
export class CacheManager extends EventEmitter {
    /**
     * 构造函数 - 初始化缓存管理器
     * @param {Object} config - 缓存配置
     */
    constructor(config = {}) {
        super();
        
        this.logger = getLogger();
        this.logger.debug('CacheManager', 'constructor', '初始化缓存管理器');
        
        // 配置
        this.config = {
            // 内存缓存配置
            memoryCache: {
                maxSize: 50 * 1024 * 1024, // 50MB
                maxItems: 1000,
                ttl: 30 * 60 * 1000, // 30分钟
                cleanupInterval: 5 * 60 * 1000 // 5分钟清理一次
            },
            // 本地存储缓存配置
            localStorage: {
                maxSize: 10 * 1024 * 1024, // 10MB
                prefix: 'smartoffice_cache_',
                ttl: 24 * 60 * 60 * 1000 // 24小时
            },
            // 会话存储缓存配置
            sessionStorage: {
                maxSize: 5 * 1024 * 1024, // 5MB
                prefix: 'smartoffice_session_',
                ttl: 60 * 60 * 1000 // 1小时
            },
            // 缓存策略
            strategy: 'lru', // lru, lfu, fifo
            enableCompression: true,
            enableEncryption: false,
            ...config
        };
        
        // 缓存存储
        this.caches = {
            memory: new Map(),
            localStorage: new Map(),
            sessionStorage: new Map()
        };
        
        // 缓存元数据
        this.metadata = {
            memory: new Map(),
            localStorage: new Map(),
            sessionStorage: new Map()
        };
        
        // 缓存统计
        this.stats = {
            hits: 0,
            misses: 0,
            sets: 0,
            deletes: 0,
            clears: 0,
            evictions: 0,
            totalSize: 0,
            hitRate: 0
        };
        
        // 清理定时器
        this.cleanupTimer = null;
        
        this._initializeStorage();
        this._startCleanupTimer();
        
        this.logger.info('CacheManager', 'constructor', '缓存管理器初始化完成', {
            config: this.config
        });
    }

    /**
     * 初始化存储
     * @private
     */
    _initializeStorage() {
        // 从本地存储恢复缓存
        this._loadFromLocalStorage();
        this._loadFromSessionStorage();
        
        // 监听存储事件
        if (typeof window !== 'undefined') {
            window.addEventListener('storage', (e) => {
                this._handleStorageChange(e);
            });
            
            window.addEventListener('beforeunload', () => {
                this._saveToStorage();
            });
        }
    }

    /**
     * 从本地存储加载缓存
     * @private
     */
    _loadFromLocalStorage() {
        if (typeof localStorage === 'undefined') return;
        
        try {
            const prefix = this.config.localStorage.prefix;
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(prefix)) {
                    const cacheKey = key.substring(prefix.length);
                    const data = localStorage.getItem(key);
                    
                    if (data) {
                        const parsed = JSON.parse(data);
                        
                        // 检查是否过期
                        if (parsed.expiry && Date.now() > parsed.expiry) {
                            localStorage.removeItem(key);
                            continue;
                        }
                        
                        this.caches.localStorage.set(cacheKey, parsed.value);
                        this.metadata.localStorage.set(cacheKey, {
                            size: this._calculateSize(parsed.value),
                            created: parsed.created,
                            accessed: Date.now(),
                            expiry: parsed.expiry,
                            hits: 0
                        });
                    }
                }
            }
            
            this.logger.debug('CacheManager', '_loadFromLocalStorage', 
                `从本地存储加载了 ${this.caches.localStorage.size} 个缓存项`);
                
        } catch (error) {
            this.logger.error('CacheManager', '_loadFromLocalStorage', '从本地存储加载缓存失败', {
                error: error.message
            });
        }
    }

    /**
     * 从会话存储加载缓存
     * @private
     */
    _loadFromSessionStorage() {
        if (typeof sessionStorage === 'undefined') return;
        
        try {
            const prefix = this.config.sessionStorage.prefix;
            
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                if (key && key.startsWith(prefix)) {
                    const cacheKey = key.substring(prefix.length);
                    const data = sessionStorage.getItem(key);
                    
                    if (data) {
                        const parsed = JSON.parse(data);
                        
                        // 检查是否过期
                        if (parsed.expiry && Date.now() > parsed.expiry) {
                            sessionStorage.removeItem(key);
                            continue;
                        }
                        
                        this.caches.sessionStorage.set(cacheKey, parsed.value);
                        this.metadata.sessionStorage.set(cacheKey, {
                            size: this._calculateSize(parsed.value),
                            created: parsed.created,
                            accessed: Date.now(),
                            expiry: parsed.expiry,
                            hits: 0
                        });
                    }
                }
            }
            
            this.logger.debug('CacheManager', '_loadFromSessionStorage', 
                `从会话存储加载了 ${this.caches.sessionStorage.size} 个缓存项`);
                
        } catch (error) {
            this.logger.error('CacheManager', '_loadFromSessionStorage', '从会话存储加载缓存失败', {
                error: error.message
            });
        }
    }

    /**
     * 获取缓存值
     * @param {string} key - 缓存键
     * @param {Object} options - 选项
     * @returns {*} 缓存值
     */
    get(key, options = {}) {
        const {
            level = 'auto', // auto, memory, localStorage, sessionStorage
            updateAccess = true
        } = options;
        
        let value = null;
        let foundLevel = null;
        
        // 根据级别查找缓存
        if (level === 'auto') {
            // 按优先级查找：内存 -> 会话存储 -> 本地存储
            const levels = ['memory', 'sessionStorage', 'localStorage'];
            
            for (const currentLevel of levels) {
                value = this._getFromLevel(key, currentLevel, updateAccess);
                if (value !== null) {
                    foundLevel = currentLevel;
                    break;
                }
            }
        } else {
            value = this._getFromLevel(key, level, updateAccess);
            foundLevel = level;
        }
        
        // 更新统计
        if (value !== null) {
            this.stats.hits++;
            
            // 触发缓存命中事件
            this.emit(CacheEvents.CACHE_HIT, {
                key,
                level: foundLevel,
                timestamp: Date.now()
            });
            
            this.logger.debug('CacheManager', 'get', '缓存命中', {
                key,
                level: foundLevel
            });
        } else {
            this.stats.misses++;
            
            // 触发缓存未命中事件
            this.emit(CacheEvents.CACHE_MISS, {
                key,
                timestamp: Date.now()
            });
            
            this.logger.debug('CacheManager', 'get', '缓存未命中', { key });
        }
        
        // 更新命中率
        this._updateHitRate();
        
        return value;
    }

    /**
     * 从指定级别获取缓存
     * @param {string} key - 缓存键
     * @param {string} level - 缓存级别
     * @param {boolean} updateAccess - 是否更新访问时间
     * @returns {*} 缓存值
     * @private
     */
    _getFromLevel(key, level, updateAccess) {
        const cache = this.caches[level];
        const metadata = this.metadata[level];
        
        if (!cache || !cache.has(key)) {
            return null;
        }
        
        const meta = metadata.get(key);
        
        // 检查是否过期
        if (meta && meta.expiry && Date.now() > meta.expiry) {
            this._deleteFromLevel(key, level);
            return null;
        }
        
        // 更新访问信息
        if (updateAccess && meta) {
            meta.accessed = Date.now();
            meta.hits++;
        }
        
        return cache.get(key);
    }

    /**
     * 设置缓存值
     * @param {string} key - 缓存键
     * @param {*} value - 缓存值
     * @param {Object} options - 选项
     */
    set(key, value, options = {}) {
        const {
            level = 'memory',
            ttl = null,
            priority = 'normal' // low, normal, high
        } = options;
        
        const now = Date.now();
        const size = this._calculateSize(value);
        
        // 计算过期时间
        let expiry = null;
        if (ttl !== null) {
            expiry = now + ttl;
        } else if (this.config[level] && this.config[level].ttl) {
            expiry = now + this.config[level].ttl;
        }
        
        // 检查容量限制
        this._ensureCapacity(level, size);
        
        // 设置缓存
        this.caches[level].set(key, value);
        this.metadata[level].set(key, {
            size,
            created: now,
            accessed: now,
            expiry,
            hits: 0,
            priority
        });
        
        // 保存到持久化存储
        if (level === 'localStorage' || level === 'sessionStorage') {
            this._saveToStorage(key, value, level, expiry);
        }
        
        // 更新统计
        this.stats.sets++;
        this.stats.totalSize += size;
        
        // 触发缓存设置事件
        this.emit(CacheEvents.CACHE_SET, {
            key,
            level,
            size,
            timestamp: now
        });
        
        this.logger.debug('CacheManager', 'set', '设置缓存', {
            key,
            level,
            size: `${size} bytes`
        });
    }

    /**
     * 删除缓存
     * @param {string} key - 缓存键
     * @param {Object} options - 选项
     */
    delete(key, options = {}) {
        const { level = 'all' } = options;
        
        let deleted = false;
        
        if (level === 'all') {
            // 从所有级别删除
            for (const currentLevel of Object.keys(this.caches)) {
                if (this._deleteFromLevel(key, currentLevel)) {
                    deleted = true;
                }
            }
        } else {
            deleted = this._deleteFromLevel(key, level);
        }
        
        if (deleted) {
            this.stats.deletes++;
            
            // 触发缓存删除事件
            this.emit(CacheEvents.CACHE_DELETE, {
                key,
                level,
                timestamp: Date.now()
            });
            
            this.logger.debug('CacheManager', 'delete', '删除缓存', { key, level });
        }
        
        return deleted;
    }

    /**
     * 从指定级别删除缓存
     * @param {string} key - 缓存键
     * @param {string} level - 缓存级别
     * @returns {boolean} 是否删除成功
     * @private
     */
    _deleteFromLevel(key, level) {
        const cache = this.caches[level];
        const metadata = this.metadata[level];
        
        if (!cache || !cache.has(key)) {
            return false;
        }
        
        // 更新统计
        const meta = metadata.get(key);
        if (meta) {
            this.stats.totalSize -= meta.size;
        }
        
        // 删除缓存
        cache.delete(key);
        metadata.delete(key);
        
        // 从持久化存储删除
        if (level === 'localStorage' && typeof localStorage !== 'undefined') {
            localStorage.removeItem(this.config.localStorage.prefix + key);
        } else if (level === 'sessionStorage' && typeof sessionStorage !== 'undefined') {
            sessionStorage.removeItem(this.config.sessionStorage.prefix + key);
        }
        
        return true;
    }

    /**
     * 清空缓存
     * @param {Object} options - 选项
     */
    clear(options = {}) {
        const { level = 'all' } = options;
        
        if (level === 'all') {
            // 清空所有级别
            for (const currentLevel of Object.keys(this.caches)) {
                this._clearLevel(currentLevel);
            }
        } else {
            this._clearLevel(level);
        }
        
        this.stats.clears++;
        this.stats.totalSize = 0;
        
        // 触发缓存清空事件
        this.emit(CacheEvents.CACHE_CLEAR, {
            level,
            timestamp: Date.now()
        });
        
        this.logger.info('CacheManager', 'clear', '清空缓存', { level });
    }

    /**
     * 清空指定级别的缓存
     * @param {string} level - 缓存级别
     * @private
     */
    _clearLevel(level) {
        const cache = this.caches[level];
        const metadata = this.metadata[level];
        
        if (cache) {
            cache.clear();
        }
        
        if (metadata) {
            metadata.clear();
        }
        
        // 清空持久化存储
        if (level === 'localStorage' && typeof localStorage !== 'undefined') {
            const prefix = this.config.localStorage.prefix;
            const keysToRemove = [];
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(prefix)) {
                    keysToRemove.push(key);
                }
            }
            
            keysToRemove.forEach(key => localStorage.removeItem(key));
        } else if (level === 'sessionStorage' && typeof sessionStorage !== 'undefined') {
            const prefix = this.config.sessionStorage.prefix;
            const keysToRemove = [];
            
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                if (key && key.startsWith(prefix)) {
                    keysToRemove.push(key);
                }
            }
            
            keysToRemove.forEach(key => sessionStorage.removeItem(key));
        }
    }

    /**
     * 检查缓存是否存在
     * @param {string} key - 缓存键
     * @param {Object} options - 选项
     * @returns {boolean} 是否存在
     */
    has(key, options = {}) {
        const { level = 'auto' } = options;
        
        if (level === 'auto') {
            // 检查所有级别
            for (const currentLevel of Object.keys(this.caches)) {
                if (this._hasInLevel(key, currentLevel)) {
                    return true;
                }
            }
            return false;
        } else {
            return this._hasInLevel(key, level);
        }
    }

    /**
     * 检查指定级别是否有缓存
     * @param {string} key - 缓存键
     * @param {string} level - 缓存级别
     * @returns {boolean} 是否存在
     * @private
     */
    _hasInLevel(key, level) {
        const cache = this.caches[level];
        const metadata = this.metadata[level];
        
        if (!cache || !cache.has(key)) {
            return false;
        }
        
        // 检查是否过期
        const meta = metadata.get(key);
        if (meta && meta.expiry && Date.now() > meta.expiry) {
            this._deleteFromLevel(key, level);
            return false;
        }
        
        return true;
    }

    /**
     * 确保缓存容量
     * @param {string} level - 缓存级别
     * @param {number} newItemSize - 新项目大小
     * @private
     */
    _ensureCapacity(level, newItemSize) {
        const config = this.config[level];
        if (!config) return;
        
        const cache = this.caches[level];
        const metadata = this.metadata[level];
        
        // 检查项目数量限制
        if (config.maxItems && cache.size >= config.maxItems) {
            this._evictItems(level, 1);
        }
        
        // 检查大小限制
        if (config.maxSize) {
            const currentSize = this._calculateLevelSize(level);
            
            while (currentSize + newItemSize > config.maxSize && cache.size > 0) {
                this._evictItems(level, 1);
            }
        }
    }

    /**
     * 驱逐缓存项
     * @param {string} level - 缓存级别
     * @param {number} count - 驱逐数量
     * @private
     */
    _evictItems(level, count) {
        const cache = this.caches[level];
        const metadata = this.metadata[level];
        
        if (!cache || cache.size === 0) return;
        
        const strategy = this.config.strategy;
        let keysToEvict = [];
        
        switch (strategy) {
            case 'lru': // Least Recently Used
                keysToEvict = this._getLRUKeys(level, count);
                break;
            case 'lfu': // Least Frequently Used
                keysToEvict = this._getLFUKeys(level, count);
                break;
            case 'fifo': // First In First Out
                keysToEvict = this._getFIFOKeys(level, count);
                break;
            default:
                keysToEvict = Array.from(cache.keys()).slice(0, count);
        }
        
        // 执行驱逐
        for (const key of keysToEvict) {
            this._deleteFromLevel(key, level);
            this.stats.evictions++;
            
            // 触发驱逐事件
            this.emit(CacheEvents.CACHE_EVICTED, {
                key,
                level,
                strategy,
                timestamp: Date.now()
            });
        }
        
        this.logger.debug('CacheManager', '_evictItems', `驱逐了 ${keysToEvict.length} 个缓存项`, {
            level,
            strategy,
            keys: keysToEvict
        });
    }

    /**
     * 获取LRU键列表
     * @param {string} level - 缓存级别
     * @param {number} count - 数量
     * @returns {Array} 键列表
     * @private
     */
    _getLRUKeys(level, count) {
        const metadata = this.metadata[level];
        
        return Array.from(metadata.entries())
            .sort((a, b) => a[1].accessed - b[1].accessed)
            .slice(0, count)
            .map(entry => entry[0]);
    }

    /**
     * 获取LFU键列表
     * @param {string} level - 缓存级别
     * @param {number} count - 数量
     * @returns {Array} 键列表
     * @private
     */
    _getLFUKeys(level, count) {
        const metadata = this.metadata[level];
        
        return Array.from(metadata.entries())
            .sort((a, b) => a[1].hits - b[1].hits)
            .slice(0, count)
            .map(entry => entry[0]);
    }

    /**
     * 获取FIFO键列表
     * @param {string} level - 缓存级别
     * @param {number} count - 数量
     * @returns {Array} 键列表
     * @private
     */
    _getFIFOKeys(level, count) {
        const metadata = this.metadata[level];
        
        return Array.from(metadata.entries())
            .sort((a, b) => a[1].created - b[1].created)
            .slice(0, count)
            .map(entry => entry[0]);
    }

    /**
     * 计算数据大小
     * @param {*} data - 数据
     * @returns {number} 大小（字节）
     * @private
     */
    _calculateSize(data) {
        try {
            return new Blob([JSON.stringify(data)]).size;
        } catch (error) {
            // 降级计算
            return JSON.stringify(data).length * 2; // 假设每个字符2字节
        }
    }

    /**
     * 计算级别总大小
     * @param {string} level - 缓存级别
     * @returns {number} 总大小
     * @private
     */
    _calculateLevelSize(level) {
        const metadata = this.metadata[level];
        let totalSize = 0;
        
        for (const meta of metadata.values()) {
            totalSize += meta.size;
        }
        
        return totalSize;
    }

    /**
     * 保存到存储
     * @param {string} key - 缓存键
     * @param {*} value - 缓存值
     * @param {string} level - 存储级别
     * @param {number} expiry - 过期时间
     * @private
     */
    _saveToStorage(key, value, level, expiry) {
        try {
            const data = {
                value,
                created: Date.now(),
                expiry
            };
            
            const serialized = JSON.stringify(data);
            
            if (level === 'localStorage' && typeof localStorage !== 'undefined') {
                localStorage.setItem(this.config.localStorage.prefix + key, serialized);
            } else if (level === 'sessionStorage' && typeof sessionStorage !== 'undefined') {
                sessionStorage.setItem(this.config.sessionStorage.prefix + key, serialized);
            }
        } catch (error) {
            this.logger.error('CacheManager', '_saveToStorage', '保存到存储失败', {
                key,
                level,
                error: error.message
            });
        }
    }

    /**
     * 保存所有缓存到存储
     * @private
     */
    _saveToStorage() {
        // 这个方法在页面卸载时调用，确保内存缓存被保存
        // 实际实现可能需要根据具体需求调整
    }

    /**
     * 处理存储变化
     * @param {StorageEvent} event - 存储事件
     * @private
     */
    _handleStorageChange(event) {
        const { key, newValue, oldValue } = event;
        
        if (!key) return;
        
        // 检查是否是我们的缓存键
        const localPrefix = this.config.localStorage.prefix;
        const sessionPrefix = this.config.sessionStorage.prefix;
        
        let cacheKey = null;
        let level = null;
        
        if (key.startsWith(localPrefix)) {
            cacheKey = key.substring(localPrefix.length);
            level = 'localStorage';
        } else if (key.startsWith(sessionPrefix)) {
            cacheKey = key.substring(sessionPrefix.length);
            level = 'sessionStorage';
        }
        
        if (cacheKey && level) {
            if (newValue === null) {
                // 键被删除
                this._deleteFromLevel(cacheKey, level);
            } else {
                // 键被更新
                try {
                    const data = JSON.parse(newValue);
                    this.caches[level].set(cacheKey, data.value);
                    this.metadata[level].set(cacheKey, {
                        size: this._calculateSize(data.value),
                        created: data.created,
                        accessed: Date.now(),
                        expiry: data.expiry,
                        hits: 0
                    });
                } catch (error) {
                    this.logger.error('CacheManager', '_handleStorageChange', '处理存储变化失败', {
                        key: cacheKey,
                        error: error.message
                    });
                }
            }
        }
    }

    /**
     * 开始清理定时器
     * @private
     */
    _startCleanupTimer() {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
        }
        
        this.cleanupTimer = setInterval(() => {
            this._cleanup();
        }, this.config.memoryCache.cleanupInterval);
    }

    /**
     * 清理过期缓存
     * @private
     */
    _cleanup() {
        const now = Date.now();
        let cleanedCount = 0;
        
        // 清理所有级别的过期缓存
        for (const level of Object.keys(this.caches)) {
            const metadata = this.metadata[level];
            const keysToDelete = [];
            
            for (const [key, meta] of metadata.entries()) {
                if (meta.expiry && now > meta.expiry) {
                    keysToDelete.push(key);
                }
            }
            
            for (const key of keysToDelete) {
                this._deleteFromLevel(key, level);
                cleanedCount++;
            }
        }
        
        if (cleanedCount > 0) {
            this.logger.debug('CacheManager', '_cleanup', `清理了 ${cleanedCount} 个过期缓存项`);
            
            // 触发清理事件
            this.emit(CacheEvents.CACHE_EXPIRED, {
                count: cleanedCount,
                timestamp: now
            });
        }
    }

    /**
     * 更新命中率
     * @private
     */
    _updateHitRate() {
        const total = this.stats.hits + this.stats.misses;
        this.stats.hitRate = total > 0 ? this.stats.hits / total : 0;
    }

    /**
     * 获取缓存统计
     * @returns {Object} 统计信息
     */
    getStats() {
        const stats = { ...this.stats };
        
        // 添加级别统计
        stats.levels = {};
        for (const level of Object.keys(this.caches)) {
            stats.levels[level] = {
                count: this.caches[level].size,
                size: this._calculateLevelSize(level)
            };
        }
        
        return stats;
    }

    /**
     * 获取缓存信息
     * @param {string} level - 缓存级别（可选）
     * @returns {Object} 缓存信息
     */
    getInfo(level = null) {
        if (level) {
            return {
                count: this.caches[level]?.size || 0,
                size: this._calculateLevelSize(level),
                keys: Array.from(this.caches[level]?.keys() || [])
            };
        }
        
        const info = {};
        for (const currentLevel of Object.keys(this.caches)) {
            info[currentLevel] = this.getInfo(currentLevel);
        }
        
        return info;
    }

    /**
     * 销毁缓存管理器
     */
    destroy() {
        // 停止清理定时器
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = null;
        }
        
        // 保存缓存到持久化存储
        this._saveToStorage();
        
        // 清空所有缓存
        this.clear();
        
        // 移除事件监听器
        this.removeAllListeners();
        
        this.logger.info('CacheManager', 'destroy', '缓存管理器已销毁');
    }
}
// #endregion

// #region 导出
export default CacheManager;
// #endregion