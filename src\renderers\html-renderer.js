/**
 * @file HTML渲染器 - 重构后的HTML格式渲染器
 * <AUTHOR> Team
 * @description
 * 这个文件定义了HTML渲染器类，继承自重构后的UnifiedRenderer
 * 专门负责将文档模型渲染为HTML格式，支持响应式设计和打印优化
 *
 * 重构说明：
 * - 继承自新的UnifiedRenderer统一基类
 * - 移除了与BaseRenderer的重复代码
 * - 优化了HTML生成和CSS处理逻辑
 * - 增强了响应式和打印支持
 */

// #region 导入依赖模块
import { UnifiedRenderer } from './unified-renderer.js';
// #endregion

/**
 * HTML渲染器类
 * 继承自UnifiedRenderer，专门用于HTML格式的渲染
 */
export class HTMLRenderer extends UnifiedRenderer {
    /**
     * 构造函数
     * @param {Object} config - 渲染器配置
     */
    constructor(config = {}) {
        super({
            name: 'HTMLRenderer',
            version: '2.0.0',
            type: 'html',
            format: 'html',
            ...config
        });
        
        // HTML特定配置
        this.htmlConfig = {
            // 文档类型
            doctype: '<!DOCTYPE html>',
            
            // HTML版本
            htmlVersion: 'html5',
            
            // 字符编码
            charset: 'utf-8',
            
            // 视口设置
            viewport: 'width=device-width, initial-scale=1.0',
            
            // CSS处理
            inlineCSS: false,
            minifyCSS: true,
            
            // JavaScript处理
            includeJS: false,
            minifyJS: true,
            
            // HTML优化
            minifyHTML: true,
            removeComments: true,
            collapseWhitespace: true,
            
            // 兼容性
            compatibility: {
                ie: false,
                mobile: true,
                print: true
            },
            
            ...config.htmlConfig
        };
        
        // HTML模板引擎
        this.templateEngine = null;
        
        // CSS处理器
        this.cssProcessor = null;
        
        // HTML优化器
        this.htmlOptimizer = null;
        
        console.log('[HTMLRenderer] HTML渲染器初始化完成');
    }

    /**
     * 获取支持的格式
     * @returns {Array} 支持的格式列表
     */
    getSupportedFormats() {
        return ['html', 'web', 'preview'];
    }
    
    /**
     * 获取渲染器特性
     * @returns {Array} 特性列表
     */
    getFeatures() {
        return [
            'html-rendering',
            'css-processing',
            'responsive-design',
            'print-support',
            'optimization',
            'template-engine'
        ];
    }

    /**
     * 执行渲染（重写父类方法）
     * @param {Object} preparedData - 准备好的数据
     * @returns {Promise<Object>} 渲染结果
     * @protected
     */
    async _executeRender(preparedData) {
        const { document, options } = preparedData;
        
        console.log('[HTMLRenderer] 开始HTML渲染');
        
        // 合并HTML配置
        const mergedOptions = { ...this.htmlConfig, ...options };
        
        // 渲染内容
        const content = await document.renderContent();
        
        // 使用父类的通用处理方法
        const styles = await super._processStyles(document, mergedOptions);
        const layout = await super._processLayout(document, mergedOptions);
        const positions = await super._processPositions(document, layout, mergedOptions);
        
        // 生成HTML结构
        let html = await this._generateHTMLStructure({
            content,
            styles,
            layout,
            positions
        }, mergedOptions);
        
        // 处理CSS
        html = await this._processCSS(html, mergedOptions);
        
        // 处理JavaScript
        html = await this._processJavaScript(html, mergedOptions);
        
        // 优化HTML
        html = await this._optimizeHTML(html, mergedOptions);
        
        console.log('[HTMLRenderer] HTML渲染完成');
        
        return {
            type: 'html',
            content: html,
            metadata: {
                size: html.length,
                charset: mergedOptions.charset,
                doctype: mergedOptions.doctype,
                optimized: mergedOptions.minifyHTML
            }
        };
    }

    // 注意：样式、布局、位置处理方法已移至父类UnifiedRenderer
    // 如需HTML特定处理，可以覆盖父类方法

    /**
     * 构建完整的HTML文档 - 包装模板内容为完整的HTML文档
     * @param {string} templateHTML - 模板生成的HTML内容
     * @param {Object} data - 渲染数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} 完整的HTML文档字符串
     * @private
     */
    async _buildFullHTML(templateHTML, data, options) {
        const doctype = options.includeDoctype ? '<!DOCTYPE html>' : '';
        const htmlLang = this._getLanguageCode(options.language || 'zh-CN');
        
        // 构建HTML文档头部
        const head = await this._buildHTMLHead(data, options);
        
        // 构建HTML文档主体
        const body = await this._buildHTMLBody(templateHTML, data, options);
        
        // 组装完整的HTML文档
        const fullHTML = `${doctype}
<html lang="${htmlLang}">
${head}
${body}
</html>`;
        
        return fullHTML;
    }

    /**
     * 生成HTML结构
     * @param {Object} data - 渲染数据
     * @param {Object} options - 选项
     * @returns {Promise<string>} HTML结构
     * @private
     */
    async _generateHTMLStructure(data, options) {
        const { doctype, charset, viewport } = options;
        const { content, styles, layout, positions } = data;
        
        // 构建HTML文档
        let html = doctype + '\n';
        html += '<html lang="zh-CN">\n';
        html += '<head>\n';
        html += `  <meta charset="${charset}">\n`;
        html += `  <meta name="viewport" content="${viewport}">\n`;
        html += '  <title>SmartOffice文档</title>\n';
        
        // 添加CSS
        if (styles && styles.css) {
            html += '  <style>\n';
            html += styles.css;
            html += '\n  </style>\n';
        }
        
        // 添加响应式CSS
        if (options.compatibility?.mobile) {
            html += '  <style>\n';
            html += this._getResponsiveCSS();
            html += '\n  </style>\n';
        }
        
        // 添加打印CSS
        if (options.compatibility?.print) {
            html += '  <style media="print">\n';
            html += this._getPrintCSS();
            html += '\n  </style>\n';
        }
        
        html += '</head>\n';
        html += '<body>\n';
        
        // 添加内容容器
        html += '  <div class="smartoffice-document">\n';
        
        // 添加内容
        if (content) {
            html += this._processContent(content, positions);
        }
        
        html += '  </div>\n';
        html += '</body>\n';
        html += '</html>';
        
        return html;
    }

    /**
     * 构建HTML头部 - 生成HTML文档的head部分
     * @param {Object} data - 渲染数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML头部字符串
     * @private
     */
    async _buildHTMLHead(data, options) {
        const title = this._getDocumentTitle(data, options);
        const metaTags = options.includeMetaTags ? this._getMetaTags(options) : '';
        const css = options.includeCSS ? await this._getFullCSS(data, options) : '';
        
        return `<head>
    <meta charset="UTF-8">
    ${metaTags}
    <title>${title}</title>
    ${css}
</head>`;
    }

    /**
     * 处理内容
     * @param {Object} content - 内容数据
     * @param {Object} positions - 位置信息
     * @returns {string} 处理后的HTML内容
     * @private
     */
    _processContent(content, positions) {
        let html = '';
        
        // 如果内容是字符串，直接使用
        if (typeof content === 'string') {
            html = content;
        } else if (content && content.html) {
            html = content.html;
        } else {
            html = '<p>暂无内容</p>';
        }
        
        // 应用位置信息
        if (positions && positions.elements) {
            html = this._applyPositions(html, positions.elements);
        }
        
        return html;
    }
    
    /**
     * 应用位置信息
     * @param {string} html - HTML内容
     * @param {Array} elements - 元素位置列表
     * @returns {string} 应用位置后的HTML
     * @private
     */
    _applyPositions(html, elements) {
        // 为每个元素应用位置样式
        for (const element of elements) {
            const { id, x, y, width, height } = element;
            const positionStyle = `position: absolute; left: ${x}px; top: ${y}px; width: ${width}px; height: ${height}px;`;
            
            // 查找并更新元素样式
            const regex = new RegExp(`(<[^>]*id=["']${id}["'][^>]*)(style=["']([^"']*)["\'])?([^>]*>)`, 'gi');
            html = html.replace(regex, (match, before, styleAttr, existingStyle, after) => {
                const newStyle = existingStyle ? `${existingStyle}; ${positionStyle}` : positionStyle;
                return `${before}style="${newStyle}"${after}`;
            });
        }
        
        return html;
    }
    
    /**
     * 获取默认样式
     * @returns {string} 默认CSS样式
     * @private
     */
    _getDefaultStyles() {
        return `
            .smartoffice-document {
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                font-size: 14px;
                line-height: 1.6;
                color: #333;
                background: #fff;
                padding: 20px;
                max-width: 1200px;
                margin: 0 auto;
            }
            
            .smartoffice-document h1,
            .smartoffice-document h2,
            .smartoffice-document h3,
            .smartoffice-document h4,
            .smartoffice-document h5,
            .smartoffice-document h6 {
                margin: 1em 0 0.5em 0;
                font-weight: bold;
            }
            
            .smartoffice-document p {
                margin: 0.5em 0;
            }
            
            .smartoffice-document table {
                border-collapse: collapse;
                width: 100%;
                margin: 1em 0;
            }
            
            .smartoffice-document th,
            .smartoffice-document td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }
            
            .smartoffice-document th {
                background-color: #f5f5f5;
                font-weight: bold;
            }
        `;
    }

    /**
     * 构建HTML主体 - 生成HTML文档的body部分
     * @param {string} templateHTML - 模板HTML内容
     * @param {Object} data - 渲染数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML主体字符串
     * @private
     */
    async _buildHTMLBody(templateHTML, data, options) {
        const bodyClass = this._getBodyClass(options);
        const bodyAttributes = this._getBodyAttributes(options);
        const wrapperHTML = this._wrapContentWithContainer(templateHTML, options);
        const scriptsHTML = options.enableInteraction ? this._getInteractionScripts(options) : '';
        
        return `<body class="${bodyClass}" ${bodyAttributes}>
    ${wrapperHTML}
    ${scriptsHTML}
</body>`;
    }

    /**
     * 获取文档标题 - 根据数据和配置生成文档标题
     * @param {Object} data - 渲染数据
     * @param {Object} options - 渲染选项
     * @returns {string} 文档标题
     * @private
     */
    _getDocumentTitle(data, options) {
        // 根据文档类型生成标题
        const documentTypeMap = {
            'receipt': '收据',
            'invoice': '发票',
            'driver-agreement': '司机协议',
            'quotation': '报价单'
        };
        
        const baseTitle = documentTypeMap[data.documentType] || '文档';
        const documentNumber = data.receiptNumber || data.invoiceNumber || 
                              data.agreementNumber || data.quotationNumber || '';
        
        return documentNumber ? `${baseTitle} - ${documentNumber}` : baseTitle;
    }

    /**
     * 获取meta标签 - 生成HTML文档的meta标签
     * @param {Object} options - 渲染选项
     * @returns {string} meta标签HTML
     * @private
     */
    _getMetaTags(options) {
        const viewport = options.includeViewport ? 
            '<meta name="viewport" content="width=device-width, initial-scale=1.0">' : '';
        
        return `    ${viewport}
    <meta name="generator" content="SmartOffice HTML Renderer v${this.version}">
    <meta name="description" content="SmartOffice系统生成的文档">
    <meta name="robots" content="noindex, nofollow">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">`;
    }

    /**
     * 获取完整CSS - 组合基础CSS、主题CSS和自定义CSS
     * @param {Object} data - 渲染数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} 完整的CSS样式
     * @private
     */
    async _getFullCSS(data, options) {
        let css = '';
        
        // 基础CSS
        css += this.baseCSS;
        
        // 主题CSS
        const theme = options.theme || 'classic';
        if (this.themeCSS[theme]) {
            css += this.themeCSS[theme];
        }
        
        // 响应式CSS
        if (options.enableResponsive) {
            css += this._getResponsiveCSS();
        }
        
        // 打印CSS
        if (options.printFriendly) {
            css += this._getPrintCSS();
        }
        
        // 自定义CSS
        if (options.customCSS) {
            css += options.customCSS;
        }
        
        // 根据配置决定是否内联CSS
        if (options.inlineCSS) {
            return `    <style type="text/css">\n${css}\n    </style>`;
        } else {
            // 这里可以实现外链CSS的逻辑
            return `    <style type="text/css">\n${css}\n    </style>`;
        }
    }

    /**
     * 获取基础CSS样式 - 返回所有文档类型的基础样式
     * @returns {string} 基础CSS样式
     * @private
     */
    _getBaseCSS() {
        return `
        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: ${this.renderConfig.fontFamily};
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        /* 容器样式 */
        .document-container {
            max-width: 210mm;
            margin: 20px auto;
            background: #fff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            padding: 20mm;
            min-height: 297mm;
        }
        
        /* 标题样式 */
        .document-title {
            text-align: center;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: bold;
            color: #000;
        }
        
        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        th, td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        
        /* 文本样式 */
        .text-center { text-align: center; }
        .text-left { text-align: left; }
        .text-right { text-align: right; }
        .font-bold { font-weight: bold; }
        .font-normal { font-weight: normal; }
        
        /* 间距样式 */
        .mb-10 { margin-bottom: 10px; }
        .mb-20 { margin-bottom: 20px; }
        .mb-30 { margin-bottom: 30px; }
        .mt-10 { margin-top: 10px; }
        .mt-20 { margin-top: 20px; }
        .mt-30 { margin-top: 30px; }
        
        /* 分隔线 */
        .divider {
            border-top: 1px solid #ddd;
            margin: 20px 0;
        }
        
        /* 签名区域 */
        .signature-section {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
        }
        
        .signature-box {
            width: 45%;
            text-align: center;
        }
        
        .signature-line {
            border-bottom: 1px solid #333;
            margin: 20px 0 10px 0;
            height: 40px;
        }
        `;
    }

    /**
     * 获取主题CSS样式 - 返回不同主题的样式定义
     * @returns {Object} 主题CSS样式对象
     * @private
     */
    _getThemeCSS() {
        return {
            classic: `
                /* 经典主题 */
                .classic-theme .document-container {
                    border: 2px solid #333;
                }
                .classic-theme .document-title {
                    border-bottom: 3px double #333;
                    padding-bottom: 15px;
                }
            `,
            
            modern: `
                /* 现代主题 */
                .modern-theme .document-container {
                    border-radius: 8px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                }
                .modern-theme .document-title {
                    color: #2c3e50;
                    font-weight: 300;
                }
                .modern-theme table th {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }
            `,
            
            elegant: `
                /* 优雅主题 */
                .elegant-theme .document-container {
                    border: 1px solid #d4c4a8;
                    background: linear-gradient(145deg, #fefefe 0%, #f8f6f0 100%);
                }
                .elegant-theme .document-title {
                    color: #8b4513;
                    font-family: "Times New Roman", serif;
                    font-style: italic;
                }
                .elegant-theme .divider {
                    border-top: 2px solid #d4c4a8;
                }
            `,
            
            formal: `
                /* 正式主题 */
                .formal-theme .document-container {
                    border: 3px solid #000;
                    background: #fff;
                }
                .formal-theme .document-title {
                    font-family: "Times New Roman", serif;
                    text-transform: uppercase;
                    letter-spacing: 2px;
                }
                .formal-theme table {
                    border: 2px solid #000;
                }
                .formal-theme th, .formal-theme td {
                    border: 1px solid #000;
                }
            `,
            
            compact: `
                /* 紧凑主题 */
                .compact-theme .document-container {
                    padding: 15mm;
                    font-size: 12px;
                }
                .compact-theme .document-title {
                    font-size: 18px;
                    margin-bottom: 20px;
                }
                .compact-theme table th,
                .compact-theme table td {
                    padding: 4px 8px;
                }
            `
        };
    }

    /**
     * 获取响应式CSS
     * @returns {string} 响应式CSS样式
     * @private
     */
    _getResponsiveCSS() {
        return `
            @media (max-width: 768px) {
                .smartoffice-document {
                    padding: 10px;
                    font-size: 12px;
                }
                
                .smartoffice-document table {
                    font-size: 11px;
                }
                
                .smartoffice-document th,
                .smartoffice-document td {
                    padding: 4px;
                }
            }
            
            @media (max-width: 480px) {
                .smartoffice-document {
                    padding: 5px;
                    font-size: 11px;
                }
            }
        `;
    }

    /**
     * 获取打印CSS
     * @returns {string} 打印CSS样式
     * @private
     */
    _getPrintCSS() {
        return `
            .smartoffice-document {
                padding: 0;
                max-width: none;
                font-size: 12pt;
                line-height: 1.4;
            }
            
            .smartoffice-document h1 { font-size: 18pt; }
            .smartoffice-document h2 { font-size: 16pt; }
            .smartoffice-document h3 { font-size: 14pt; }
            .smartoffice-document h4 { font-size: 12pt; }
            .smartoffice-document h5 { font-size: 11pt; }
            .smartoffice-document h6 { font-size: 10pt; }
            
            .smartoffice-document table {
                page-break-inside: avoid;
            }
            
            .smartoffice-document th,
            .smartoffice-document td {
                padding: 4pt;
            }
        `;
    }

    /**
     * 获取语言代码 - 将内部语言代码转换为HTML语言代码
     * @param {string} language - 内部语言代码
     * @returns {string} HTML语言代码
     * @private
     */
    _getLanguageCode(language) {
        const languageMap = {
            'zh-CN': 'zh-CN',
            'zh-TW': 'zh-TW',
            'en-US': 'en',
            'ms-MY': 'ms'
        };
        
        return languageMap[language] || 'zh-CN';
    }

    /**
     * 获取body类名 - 根据选项生成body元素的class属性
     * @param {Object} options - 渲染选项
     * @returns {string} body类名
     * @private
     */
    _getBodyClass(options) {
        const classes = [];
        
        // 添加主题类
        if (options.theme) {
            classes.push(`${options.theme}-theme`);
        }
        
        // 添加文档类型类
        if (options.documentType) {
            classes.push(`document-${options.documentType}`);
        }
        
        // 添加特殊配置类
        if (options.printFriendly) {
            classes.push('print-friendly');
        }
        
        if (options.enableResponsive) {
            classes.push('responsive');
        }
        
        return classes.join(' ');
    }

    /**
     * 获取body属性 - 生成body元素的额外属性
     * @param {Object} options - 渲染选项
     * @returns {string} body属性字符串
     * @private
     */
    _getBodyAttributes(options) {
        const attributes = [];
        
        // 添加数据属性
        if (options.documentType) {
            attributes.push(`data-document-type="${options.documentType}"`);
        }
        
        if (options.theme) {
            attributes.push(`data-theme="${options.theme}"`);
        }
        
        attributes.push(`data-renderer="html"`);
        attributes.push(`data-timestamp="${new Date().toISOString()}"`);
        
        return attributes.join(' ');
    }

    /**
     * 包装内容容器 - 为模板内容添加容器包装
     * @param {string} content - 模板内容
     * @param {Object} options - 渲染选项
     * @returns {string} 包装后的HTML内容
     * @private
     */
    _wrapContentWithContainer(content, options) {
        return `    <div class="document-container">
        ${content}
    </div>`;
    }

    /**
     * 获取交互脚本 - 生成支持交互功能的JavaScript代码
     * @param {Object} options - 渲染选项
     * @returns {string} JavaScript代码
     * @private
     */
    _getInteractionScripts(options) {
        if (!options.enableInteraction) {
            return '';
        }
        
        return `
    <script type="text/javascript">
        // 打印功能
        function printDocument() {
            window.print();
        }
        
        // 导出功能（如果有对应的API）
        function exportDocument(format) {
            // 这里可以调用导出API
            console.log('导出格式:', format);
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('SmartOffice文档已加载');
        });
    </script>`;
    }

    /**
     * 处理CSS
     * @param {string} html - HTML字符串
     * @param {Object} options - 选项
     * @returns {Promise<string>} 处理后的HTML
     * @private
     */
    async _processCSS(html, options) {
        if (!options.inlineCSS) {
            return html;
        }
        
        // 内联CSS处理逻辑
        // 提取<style>标签中的CSS
        const styleRegex = /<style[^>]*>([\s\S]*?)<\/style>/gi;
        const styles = [];
        let match;
        
        while ((match = styleRegex.exec(html)) !== null) {
            styles.push(match[1]);
        }
        
        if (styles.length > 0) {
            const combinedCSS = styles.join('\n');
            // 这里可以添加CSS内联的具体实现
            // 例如使用CSS解析器将样式内联到元素中
        }
        
        return html;
    }
    
    /**
     * 处理JavaScript
     * @param {string} html - HTML字符串
     * @param {Object} options - 选项
     * @returns {Promise<string>} 处理后的HTML
     * @private
     */
    async _processJavaScript(html, options) {
        if (!options.includeJS) {
            return html;
        }
        
        // JavaScript处理逻辑
        // 这里可以添加JavaScript注入或处理的具体实现
        
        return html;
    }
    
    /**
     * 优化HTML
     * @param {string} html - HTML字符串
     * @param {Object} options - 选项
     * @returns {Promise<string>} 优化后的HTML
     * @private
     */
    async _optimizeHTML(html, options) {
        let optimizedHTML = html;
        
        // 移除注释
        if (options.removeComments) {
            optimizedHTML = optimizedHTML.replace(/<!--[\s\S]*?-->/g, '');
        }
        
        // 压缩空白
        if (options.collapseWhitespace) {
            optimizedHTML = optimizedHTML.replace(/\s+/g, ' ');
            optimizedHTML = optimizedHTML.replace(/> </g, '><');
        }
        
        // 压缩HTML
        if (options.minifyHTML) {
            optimizedHTML = this._minifyHTML(optimizedHTML);
        }
        
        return optimizedHTML;
    }

    /**
     * 后处理HTML - 对生成的HTML进行最终处理
     * @param {string} html - 原始HTML内容
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} 处理后的HTML内容
     * @private
     */
    async _postprocessHTML(html, options) {
        let processedHTML = html;
        
        // 移除注释
        if (options.removeComments) {
            processedHTML = processedHTML.replace(/<!--[\s\S]*?-->/g, '');
        }
        
        // 压缩HTML
        if (options.minifyHTML) {
            processedHTML = this._minifyHTML(processedHTML);
        }
        
        // 格式化HTML（如果不压缩）
        if (!options.minifyHTML) {
            processedHTML = this._formatHTML(processedHTML);
        }
        
        return processedHTML;
    }

    /**
     * 压缩HTML - 移除多余的空白字符
     * @param {string} html - 原始HTML
     * @returns {string} 压缩后的HTML
     * @private
     */
    _minifyHTML(html) {
        return html
            .replace(/>\s+</g, '><') // 移除标签间的空白
            .replace(/\s{2,}/g, ' ') // 合并多个空格
            .replace(/^\s+|\s+$/gm, '') // 移除行首行尾空白
            .trim();
    }

    /**
     * 格式化HTML - 美化HTML代码格式
     * @param {string} html - 原始HTML
     * @returns {string} 格式化后的HTML
     * @private
     */
    _formatHTML(html) {
        // 简单的HTML格式化
        return html
            .replace(/></g, '>\n<')
            .split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0)
            .join('\n');
    }

    /**
     * 验证HTML内容 - 检查生成的HTML是否有效
     * @param {string} html - HTML内容
     * @returns {Object} 验证结果
     */
    validateHTML(html) {
        const errors = [];
        const warnings = [];
        
        // 检查基本HTML结构
        if (!html.includes('<html')) {
            errors.push('缺少html标签');
        }
        
        if (!html.includes('<head')) {
            errors.push('缺少head标签');
        }
        
        if (!html.includes('<body')) {
            errors.push('缺少body标签');
        }
        
        // 检查文档容器
        if (!html.includes('document-container')) {
            warnings.push('缺少文档容器类');
        }
        
        // 检查字符编码
        if (!html.includes('charset')) {
            warnings.push('缺少字符编码声明');
        }
        
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * 获取支持的主题列表 - 返回HTML渲染器支持的所有主题
     * @returns {Array<Object>} 主题信息数组
     */
    getSupportedThemes() {
        return [
            {
                id: 'classic',
                name: '经典',
                description: '传统的文档样式，适合正式场合'
            },
            {
                id: 'modern',
                name: '现代',
                description: '现代化设计，清新简洁'
            },
            {
                id: 'elegant',
                name: '优雅',
                description: '优雅精致，适合高端商务'
            },
            {
                id: 'formal',
                name: '正式',
                description: '正式商务样式，符合官方要求'
            },
            {
                id: 'compact',
                name: '紧凑',
                description: '紧凑布局，节省空间'
            }
        ];
    }

    /**
     * 创建HTML预览 - 生成用于预览的HTML片段
     * @param {Object} template - 模板对象
     * @param {Object} data - 渲染数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML预览片段
     */
    async createPreview(template, data, options = {}) {
        const previewOptions = {
            ...options,
            includeDoctype: false,
            includeMetaTags: false,
            minifyHTML: false,
            enableInteraction: false
        };
        
        // 只渲染body内容
        const templateHTML = await template.render(data, previewOptions);
        const css = await this._getFullCSS(data, previewOptions);
        
        return `${css}
<div class="document-container ${previewOptions.theme || 'classic'}-theme">
    ${templateHTML}
</div>`;
    }
}
// #endregion

// #region 工厂函数
/**
 * 创建HTML渲染器实例 - 工厂函数，方便创建HTML渲染器
 * @param {Object} config - 渲染器配置
 * @returns {HTMLRenderer} HTML渲染器实例
 */
export function createHTMLRenderer(config = {}) {
    return new HTMLRenderer(config);
}

/**
 * 创建预设HTML渲染器 - 创建具有预定义配置的HTML渲染器
 * @param {string} preset - 预设名称 ('default', 'print', 'preview', 'responsive')
 * @param {Object} config - 额外配置
 * @returns {HTMLRenderer} HTML渲染器实例
 */
export function createPresetHTMLRenderer(preset = 'default', config = {}) {
    const presetConfigs = {
        default: {
            renderConfig: {
                includeCSS: true,
                inlineCSS: true,
                enableResponsive: true,
                printFriendly: true
            }
        },
        
        print: {
            renderConfig: {
                includeCSS: true,
                inlineCSS: true,
                enableResponsive: false,
                printFriendly: true,
                removeComments: true,
                enableInteraction: false
            }
        },
        
        preview: {
            renderConfig: {
                includeDoctype: false,
                includeMetaTags: false,
                includeCSS: true,
                inlineCSS: true,
                enableResponsive: true,
                enableInteraction: false
            }
        },
        
        responsive: {
            renderConfig: {
                includeCSS: true,
                inlineCSS: true,
                enableResponsive: true,
                printFriendly: false,
                enableInteraction: true
            }
        }
    };
    
    const presetConfig = presetConfigs[preset] || presetConfigs.default;
    const mergedConfig = {
        ...presetConfig,
        ...config,
        renderConfig: {
            ...presetConfig.renderConfig,
            ...config.renderConfig
        }
    };
    
    return new HTMLRenderer(mergedConfig);
}
// #endregion