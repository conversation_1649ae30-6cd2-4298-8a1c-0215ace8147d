/**
 * @file 司机协议文档模型
 * @description 司机协议文档的数据模型和业务逻辑
 */

import { BaseDocument } from './base-document.js';
import { Validator, ValidationError } from '../core/utils/validation.js';
import { StringUtils } from '../core/utils/string-utils.js';
import { DateUtils } from '../core/utils/date-utils.js';
import { getCompanyImages } from '../core/utils/image-manager.js';

/**
 * @class DriverAgreementDocument
 * @description 司机协议文档类，继承自BaseDocument
 */
export class DriverAgreementDocument extends BaseDocument {
    /**
     * 创建司机协议文档实例
     * @param {Object} data - 协议数据
     */
    constructor(data = {}) {
        super({ ...data, type: 'driver-agreement' });
        
        // 协议特有字段
        this.agreementNumber = data.agreementNumber || '';
        this.agreementDate = data.agreementDate || new Date();
        this.effectiveDate = data.effectiveDate || new Date();
        this.expiryDate = data.expiryDate || null;
        this.agreementType = data.agreementType || 'employment'; // employment, contract, freelance
        
        // 司机信息
        this.driverInfo = data.driverInfo || {};
        
        // 公司信息
        this.companyInfo = data.companyInfo || {};
        
        // 车辆信息
        this.vehicleInfo = data.vehicleInfo || {};
        
        // 薪酬信息
        this.compensationInfo = data.compensationInfo || {};
        
        // 工作条件
        this.workConditions = data.workConditions || {};
        
        // 责任和义务
        this.responsibilities = data.responsibilities || {};
        
        // 条款和条件
        this.termsAndConditions = data.termsAndConditions || {};
        
        // 签名信息
        this.signatures = data.signatures || {};
        
        // 附件
        this.attachments = data.attachments || [];
        
        // 状态字段
        this.agreementStatus = data.agreementStatus || 'draft'; // draft, active, suspended, terminated, expired
        this.signatureStatus = data.signatureStatus || 'unsigned'; // unsigned, partial, signed
        
        // 初始化数据
        this._initializeData(data);
    }

    /**
     * 初始化协议数据
     * @param {Object} data - 原始数据
     * @protected
     */
    _initializeData(data) {
        // 确保日期是Date对象
        if (data.agreementDate && !(data.agreementDate instanceof Date)) {
            this.agreementDate = new Date(data.agreementDate);
        }
        
        if (data.effectiveDate && !(data.effectiveDate instanceof Date)) {
            this.effectiveDate = new Date(data.effectiveDate);
        }
        
        if (data.expiryDate && !(data.expiryDate instanceof Date)) {
            this.expiryDate = new Date(data.expiryDate);
        }
        
        // 初始化司机信息
        this.driverInfo = {
            name: data.driverInfo?.name || '',
            chineseName: data.driverInfo?.chineseName || '',
            icNumber: data.driverInfo?.icNumber || '',
            licenseNumber: data.driverInfo?.licenseNumber || '',
            licenseClass: data.driverInfo?.licenseClass || '',
            licenseExpiryDate: data.driverInfo?.licenseExpiryDate || null,
            dateOfBirth: data.driverInfo?.dateOfBirth || null,
            nationality: data.driverInfo?.nationality || '',
            address: data.driverInfo?.address || '',
            phone: data.driverInfo?.phone || '',
            email: data.driverInfo?.email || '',
            emergencyContact: data.driverInfo?.emergencyContact || {},
            medicalCertificate: data.driverInfo?.medicalCertificate || {},
            ...data.driverInfo
        };
        
        // 处理司机信息中的日期
        if (this.driverInfo.licenseExpiryDate && !(this.driverInfo.licenseExpiryDate instanceof Date)) {
            this.driverInfo.licenseExpiryDate = new Date(this.driverInfo.licenseExpiryDate);
        }
        
        if (this.driverInfo.dateOfBirth && !(this.driverInfo.dateOfBirth instanceof Date)) {
            this.driverInfo.dateOfBirth = new Date(this.driverInfo.dateOfBirth);
        }
        
        // 初始化公司信息
        const companyCode = data.companyCode || data.companyInfo?.companyCode || 'gomyhire';
        const companyImages = getCompanyImages(companyCode);
        
        this.companyInfo = {
            name: data.companyInfo?.name || '',
            registrationNumber: data.companyInfo?.registrationNumber || '',
            address: data.companyInfo?.address || '',
            phone: data.companyInfo?.phone || '',
            email: data.companyInfo?.email || '',
            website: data.companyInfo?.website || '',
            authorizedPerson: data.companyInfo?.authorizedPerson || '',
            position: data.companyInfo?.position || '',
            companyCode: companyCode,
            logo: data.companyInfo?.logo || companyImages.logo || '',
            stamp: data.companyInfo?.stamp || companyImages.stamp || '',
            footer: data.companyInfo?.footer || companyImages.footer || '',
            ...data.companyInfo
        };
        
        // 初始化车辆信息
        this.vehicleInfo = {
            make: data.vehicleInfo?.make || '',
            model: data.vehicleInfo?.model || '',
            year: data.vehicleInfo?.year || '',
            plateNumber: data.vehicleInfo?.plateNumber || '',
            chassisNumber: data.vehicleInfo?.chassisNumber || '',
            engineNumber: data.vehicleInfo?.engineNumber || '',
            color: data.vehicleInfo?.color || '',
            vehicleType: data.vehicleInfo?.vehicleType || '',
            roadTaxExpiry: data.vehicleInfo?.roadTaxExpiry || null,
            insuranceExpiry: data.vehicleInfo?.insuranceExpiry || null,
            inspectionExpiry: data.vehicleInfo?.inspectionExpiry || null,
            ...data.vehicleInfo
        };
        
        // 处理车辆信息中的日期
        ['roadTaxExpiry', 'insuranceExpiry', 'inspectionExpiry'].forEach(field => {
            if (this.vehicleInfo[field] && !(this.vehicleInfo[field] instanceof Date)) {
                this.vehicleInfo[field] = new Date(this.vehicleInfo[field]);
            }
        });
        
        // 初始化薪酬信息
        this.compensationInfo = {
            baseSalary: data.compensationInfo?.baseSalary || 0,
            currency: data.compensationInfo?.currency || 'RM',
            paymentFrequency: data.compensationInfo?.paymentFrequency || 'monthly', // daily, weekly, monthly
            overtimeRate: data.compensationInfo?.overtimeRate || 0,
            allowances: data.compensationInfo?.allowances || [],
            deductions: data.compensationInfo?.deductions || [],
            bonusStructure: data.compensationInfo?.bonusStructure || {},
            ...data.compensationInfo
        };
        
        // 初始化工作条件
        this.workConditions = {
            workingHours: data.workConditions?.workingHours || {},
            workingDays: data.workConditions?.workingDays || [],
            restDays: data.workConditions?.restDays || [],
            leaveEntitlement: data.workConditions?.leaveEntitlement || {},
            workLocation: data.workConditions?.workLocation || '',
            reportingStructure: data.workConditions?.reportingStructure || {},
            ...data.workConditions
        };
        
        // 初始化责任和义务
        this.responsibilities = {
            driverResponsibilities: data.responsibilities?.driverResponsibilities || [],
            companyResponsibilities: data.responsibilities?.companyResponsibilities || [],
            safetyRequirements: data.responsibilities?.safetyRequirements || [],
            maintenanceResponsibilities: data.responsibilities?.maintenanceResponsibilities || [],
            ...data.responsibilities
        };
        
        // 初始化条款和条件
        this.termsAndConditions = {
            probationPeriod: data.termsAndConditions?.probationPeriod || 0,
            noticePeriod: data.termsAndConditions?.noticePeriod || 0,
            terminationConditions: data.termsAndConditions?.terminationConditions || [],
            confidentialityClause: data.termsAndConditions?.confidentialityClause || '',
            nonCompeteClause: data.termsAndConditions?.nonCompeteClause || '',
            disputeResolution: data.termsAndConditions?.disputeResolution || '',
            governingLaw: data.termsAndConditions?.governingLaw || '',
            ...data.termsAndConditions
        };
        
        // 初始化签名信息
        this.signatures = {
            driverSignature: data.signatures?.driverSignature || null,
            driverSignatureDate: data.signatures?.driverSignatureDate || null,
            companySignature: data.signatures?.companySignature || null,
            companySignatureDate: data.signatures?.companySignatureDate || null,
            witnessSignature: data.signatures?.witnessSignature || null,
            witnessSignatureDate: data.signatures?.witnessSignatureDate || null,
            witnessName: data.signatures?.witnessName || '',
            ...data.signatures
        };
        
        // 处理签名日期
        ['driverSignatureDate', 'companySignatureDate', 'witnessSignatureDate'].forEach(field => {
            if (this.signatures[field] && !(this.signatures[field] instanceof Date)) {
                this.signatures[field] = new Date(this.signatures[field]);
            }
        });
        
        // 更新签名状态
        this._updateSignatureStatus();
    }

    /**
     * 更新协议数据
     * @param {Object} data - 要更新的数据
     * @protected
     */
    _updateData(data) {
        // 更新基本字段
        const updateFields = [
            'agreementNumber', 'agreementType', 'agreementStatus'
        ];
        
        updateFields.forEach(field => {
            if (data[field] !== undefined) {
                this[field] = data[field];
            }
        });
        
        // 更新日期
        if (data.agreementDate) {
            this.agreementDate = data.agreementDate instanceof Date ? data.agreementDate : new Date(data.agreementDate);
        }
        
        if (data.effectiveDate) {
            this.effectiveDate = data.effectiveDate instanceof Date ? data.effectiveDate : new Date(data.effectiveDate);
        }
        
        if (data.expiryDate) {
            this.expiryDate = data.expiryDate instanceof Date ? data.expiryDate : new Date(data.expiryDate);
        }
        
        // 更新复合对象
        const complexFields = [
            'driverInfo', 'companyInfo', 'vehicleInfo', 'compensationInfo',
            'workConditions', 'responsibilities', 'termsAndConditions', 'signatures'
        ];
        
        complexFields.forEach(field => {
            if (data[field]) {
                this[field] = { ...this[field], ...data[field] };
            }
        });
        
        // 更新附件
        if (data.attachments) {
            this.attachments = [...data.attachments];
        }
        
        // 重新初始化数据以处理日期转换
        this._initializeData(this);
    }

    /**
     * 更新签名状态
     * @private
     */
    _updateSignatureStatus() {
        const hasDriverSignature = !!this.signatures.driverSignature;
        const hasCompanySignature = !!this.signatures.companySignature;
        
        if (!hasDriverSignature && !hasCompanySignature) {
            this.signatureStatus = 'unsigned';
        } else if (hasDriverSignature && hasCompanySignature) {
            this.signatureStatus = 'signed';
        } else {
            this.signatureStatus = 'partial';
        }
    }

    /**
     * 特定验证
     * @protected
     */
    _validateSpecific() {
        // 验证协议号码
        if (!this.agreementNumber || this.agreementNumber.trim() === '') {
            throw new ValidationError('协议号码不能为空', 'agreementNumber', this.agreementNumber);
        }
        
        // 验证协议日期
        if (!Validator.isValidDate(this.agreementDate)) {
            throw new ValidationError('协议日期格式无效', 'agreementDate', this.agreementDate);
        }
        
        // 验证生效日期
        if (!Validator.isValidDate(this.effectiveDate)) {
            throw new ValidationError('生效日期格式无效', 'effectiveDate', this.effectiveDate);
        }
        
        // 验证到期日期
        if (this.expiryDate && !Validator.isValidDate(this.expiryDate)) {
            throw new ValidationError('到期日期格式无效', 'expiryDate', this.expiryDate);
        }
        
        // 验证到期日期不能早于生效日期
        if (this.expiryDate && this.expiryDate < this.effectiveDate) {
            throw new ValidationError('到期日期不能早于生效日期', 'expiryDate', this.expiryDate);
        }
        
        // 验证司机信息
        if (!this.driverInfo.name || this.driverInfo.name.trim() === '') {
            throw new ValidationError('司机姓名不能为空', 'driverInfo.name', this.driverInfo.name);
        }
        
        if (!this.driverInfo.icNumber || this.driverInfo.icNumber.trim() === '') {
            throw new ValidationError('司机身份证号码不能为空', 'driverInfo.icNumber', this.driverInfo.icNumber);
        }
        
        if (!this.driverInfo.licenseNumber || this.driverInfo.licenseNumber.trim() === '') {
            throw new ValidationError('司机驾照号码不能为空', 'driverInfo.licenseNumber', this.driverInfo.licenseNumber);
        }
        
        // 验证公司信息
        if (!this.companyInfo.name || this.companyInfo.name.trim() === '') {
            throw new ValidationError('公司名称不能为空', 'companyInfo.name', this.companyInfo.name);
        }
        
        // 验证车辆信息（如果提供）
        if (this.vehicleInfo.plateNumber && this.vehicleInfo.plateNumber.trim() !== '') {
            if (!this.vehicleInfo.make || this.vehicleInfo.make.trim() === '') {
                throw new ValidationError('车辆品牌不能为空', 'vehicleInfo.make', this.vehicleInfo.make);
            }
            
            if (!this.vehicleInfo.model || this.vehicleInfo.model.trim() === '') {
                throw new ValidationError('车辆型号不能为空', 'vehicleInfo.model', this.vehicleInfo.model);
            }
        }
        
        // 验证薪酬信息
        if (!Validator.isNonNegativeNumber(this.compensationInfo.baseSalary)) {
            throw new ValidationError('基本薪资不能为负数', 'compensationInfo.baseSalary', this.compensationInfo.baseSalary);
        }
        
        // 验证协议类型
        const validAgreementTypes = ['employment', 'contract', 'freelance'];
        if (!validAgreementTypes.includes(this.agreementType)) {
            throw new ValidationError(`无效的协议类型: ${this.agreementType}`, 'agreementType', this.agreementType);
        }
        
        // 验证协议状态
        const validStatuses = ['draft', 'active', 'suspended', 'terminated', 'expired'];
        if (!validStatuses.includes(this.agreementStatus)) {
            throw new ValidationError(`无效的协议状态: ${this.agreementStatus}`, 'agreementStatus', this.agreementStatus);
        }
    }

    /**
     * 获取特定数据
     * @returns {Object} 协议特定数据
     * @protected
     */
    _getSpecificData() {
        return {
            agreementNumber: this.agreementNumber,
            agreementDate: this.agreementDate.toISOString(),
            effectiveDate: this.effectiveDate.toISOString(),
            expiryDate: this.expiryDate ? this.expiryDate.toISOString() : null,
            agreementType: this.agreementType,
            driverInfo: {
                ...this.driverInfo,
                licenseExpiryDate: this.driverInfo.licenseExpiryDate ? this.driverInfo.licenseExpiryDate.toISOString() : null,
                dateOfBirth: this.driverInfo.dateOfBirth ? this.driverInfo.dateOfBirth.toISOString() : null
            },
            companyInfo: this.companyInfo,
            vehicleInfo: {
                ...this.vehicleInfo,
                roadTaxExpiry: this.vehicleInfo.roadTaxExpiry ? this.vehicleInfo.roadTaxExpiry.toISOString() : null,
                insuranceExpiry: this.vehicleInfo.insuranceExpiry ? this.vehicleInfo.insuranceExpiry.toISOString() : null,
                inspectionExpiry: this.vehicleInfo.inspectionExpiry ? this.vehicleInfo.inspectionExpiry.toISOString() : null
            },
            compensationInfo: this.compensationInfo,
            workConditions: this.workConditions,
            responsibilities: this.responsibilities,
            termsAndConditions: this.termsAndConditions,
            signatures: {
                ...this.signatures,
                driverSignatureDate: this.signatures.driverSignatureDate ? this.signatures.driverSignatureDate.toISOString() : null,
                companySignatureDate: this.signatures.companySignatureDate ? this.signatures.companySignatureDate.toISOString() : null,
                witnessSignatureDate: this.signatures.witnessSignatureDate ? this.signatures.witnessSignatureDate.toISOString() : null
            },
            attachments: this.attachments,
            agreementStatus: this.agreementStatus,
            signatureStatus: this.signatureStatus
        };
    }

    /**
     * 设置司机签名
     * @param {string} signature - 签名数据
     * @param {Date} date - 签名日期
     * @returns {DriverAgreementDocument} 当前实例
     */
    setDriverSignature(signature, date = new Date()) {
        this.signatures.driverSignature = signature;
        this.signatures.driverSignatureDate = date instanceof Date ? date : new Date(date);
        this._updateSignatureStatus();
        this.updatedAt = new Date();
        return this;
    }

    /**
     * 设置公司签名
     * @param {string} signature - 签名数据
     * @param {Date} date - 签名日期
     * @returns {DriverAgreementDocument} 当前实例
     */
    setCompanySignature(signature, date = new Date()) {
        this.signatures.companySignature = signature;
        this.signatures.companySignatureDate = date instanceof Date ? date : new Date(date);
        this._updateSignatureStatus();
        this.updatedAt = new Date();
        return this;
    }

    /**
     * 设置见证人签名
     * @param {string} signature - 签名数据
     * @param {string} witnessName - 见证人姓名
     * @param {Date} date - 签名日期
     * @returns {DriverAgreementDocument} 当前实例
     */
    setWitnessSignature(signature, witnessName, date = new Date()) {
        this.signatures.witnessSignature = signature;
        this.signatures.witnessName = witnessName;
        this.signatures.witnessSignatureDate = date instanceof Date ? date : new Date(date);
        this.updatedAt = new Date();
        return this;
    }

    /**
     * 激活协议
     * @returns {DriverAgreementDocument} 当前实例
     */
    activate() {
        if (this.signatureStatus !== 'signed') {
            throw new ValidationError('协议必须完全签署后才能激活', 'signatureStatus', this.signatureStatus);
        }
        
        this.agreementStatus = 'active';
        this.updatedAt = new Date();
        return this;
    }

    /**
     * 暂停协议
     * @returns {DriverAgreementDocument} 当前实例
     */
    suspend() {
        if (this.agreementStatus !== 'active') {
            throw new ValidationError('只有活跃状态的协议才能暂停', 'agreementStatus', this.agreementStatus);
        }
        
        this.agreementStatus = 'suspended';
        this.updatedAt = new Date();
        return this;
    }

    /**
     * 终止协议
     * @returns {DriverAgreementDocument} 当前实例
     */
    terminate() {
        const validStatuses = ['active', 'suspended'];
        if (!validStatuses.includes(this.agreementStatus)) {
            throw new ValidationError('只有活跃或暂停状态的协议才能终止', 'agreementStatus', this.agreementStatus);
        }
        
        this.agreementStatus = 'terminated';
        this.updatedAt = new Date();
        return this;
    }

    /**
     * 添加附件
     * @param {Object} attachment - 附件信息
     * @returns {DriverAgreementDocument} 当前实例
     */
    addAttachment(attachment) {
        const newAttachment = {
            id: StringUtils.generateUUID(),
            name: attachment.name || '',
            type: attachment.type || '',
            url: attachment.url || '',
            size: attachment.size || 0,
            uploadDate: new Date(),
            ...attachment
        };
        
        this.attachments.push(newAttachment);
        this.updatedAt = new Date();
        return this;
    }

    /**
     * 删除附件
     * @param {string} attachmentId - 附件ID
     * @returns {DriverAgreementDocument} 当前实例
     */
    removeAttachment(attachmentId) {
        const index = this.attachments.findIndex(att => att.id === attachmentId);
        
        if (index === -1) {
            throw new ValidationError(`未找到ID为 ${attachmentId} 的附件`, 'attachmentId', attachmentId);
        }
        
        this.attachments.splice(index, 1);
        this.updatedAt = new Date();
        return this;
    }

    /**
     * 获取司机年龄
     * @returns {number} 司机年龄
     */
    getDriverAge() {
        if (!this.driverInfo.dateOfBirth) {
            return null;
        }
        
        return DateUtils.calculateAge(this.driverInfo.dateOfBirth);
    }

    /**
     * 获取格式化的基本薪资
     * @returns {string} 格式化的基本薪资
     */
    getFormattedBaseSalary() {
        return StringUtils.formatCurrency(this.compensationInfo.baseSalary, this.compensationInfo.currency);
    }

    /**
     * 获取格式化的协议日期
     * @param {string} format - 日期格式
     * @returns {string} 格式化的协议日期
     */
    getFormattedAgreementDate(format = 'YYYY-MM-DD') {
        return DateUtils.format(this.agreementDate, format);
    }

    /**
     * 获取格式化的生效日期
     * @param {string} format - 日期格式
     * @returns {string} 格式化的生效日期
     */
    getFormattedEffectiveDate(format = 'YYYY-MM-DD') {
        return DateUtils.format(this.effectiveDate, format);
    }

    /**
     * 获取格式化的到期日期
     * @param {string} format - 日期格式
     * @returns {string} 格式化的到期日期
     */
    getFormattedExpiryDate(format = 'YYYY-MM-DD') {
        return this.expiryDate ? DateUtils.format(this.expiryDate, format) : '';
    }

    /**
     * 检查协议是否已过期
     * @returns {boolean} 是否已过期
     */
    isExpired() {
        if (!this.expiryDate) {
            return false;
        }
        
        return new Date() > this.expiryDate;
    }

    /**
     * 检查驾照是否即将过期
     * @param {number} days - 提前天数
     * @returns {boolean} 是否即将过期
     */
    isLicenseExpiringSoon(days = 30) {
        if (!this.driverInfo.licenseExpiryDate) {
            return false;
        }
        
        const warningDate = DateUtils.addDays(new Date(), days);
        return this.driverInfo.licenseExpiryDate <= warningDate;
    }

    /**
     * 检查车辆证件是否即将过期
     * @param {number} days - 提前天数
     * @returns {Object} 过期检查结果
     */
    getVehicleExpiryWarnings(days = 30) {
        const warningDate = DateUtils.addDays(new Date(), days);
        const warnings = {};
        
        if (this.vehicleInfo.roadTaxExpiry && this.vehicleInfo.roadTaxExpiry <= warningDate) {
            warnings.roadTax = {
                expiryDate: this.vehicleInfo.roadTaxExpiry,
                daysUntilExpiry: DateUtils.daysBetween(new Date(), this.vehicleInfo.roadTaxExpiry)
            };
        }
        
        if (this.vehicleInfo.insuranceExpiry && this.vehicleInfo.insuranceExpiry <= warningDate) {
            warnings.insurance = {
                expiryDate: this.vehicleInfo.insuranceExpiry,
                daysUntilExpiry: DateUtils.daysBetween(new Date(), this.vehicleInfo.insuranceExpiry)
            };
        }
        
        if (this.vehicleInfo.inspectionExpiry && this.vehicleInfo.inspectionExpiry <= warningDate) {
            warnings.inspection = {
                expiryDate: this.vehicleInfo.inspectionExpiry,
                daysUntilExpiry: DateUtils.daysBetween(new Date(), this.vehicleInfo.inspectionExpiry)
            };
        }
        
        return warnings;
    }

    /**
     * 检查协议是否完全签署
     * @returns {boolean} 是否完全签署
     */
    isFullySigned() {
        return this.signatureStatus === 'signed';
    }

    /**
     * 检查协议是否活跃
     * @returns {boolean} 是否活跃
     */
    isActive() {
        return this.agreementStatus === 'active';
    }

    /**
     * 检查协议是否已终止
     * @returns {boolean} 是否已终止
     */
    isTerminated() {
        return this.agreementStatus === 'terminated';
    }

    /**
     * 获取协议摘要
     * @returns {Object} 协议摘要
     */
    getSummary() {
        return {
            ...super.getSummary(),
            agreementNumber: this.agreementNumber,
            driverName: this.driverInfo.name,
            companyName: this.companyInfo.name,
            agreementType: this.agreementType,
            agreementStatus: this.agreementStatus,
            signatureStatus: this.signatureStatus,
            effectiveDate: this.getFormattedEffectiveDate(),
            expiryDate: this.getFormattedExpiryDate(),
            baseSalary: this.getFormattedBaseSalary(),
            isExpired: this.isExpired(),
            vehiclePlateNumber: this.vehicleInfo.plateNumber || 'N/A'
        };
    }

    /**
     * 生成协议号码
     * @param {string} prefix - 前缀
     * @returns {string} 生成的协议号码
     * @static
     */
    static generateAgreementNumber(prefix = 'DA') {
        const timestamp = DateUtils.format(new Date(), 'YYYYMMDD');
        const random = StringUtils.generateRandomString(4, true);
        return `${prefix}-${timestamp}-${random}`;
    }

    /**
     * 从JSON创建协议实例
     * @param {Object} json - JSON对象
     * @returns {DriverAgreementDocument} 协议实例
     * @static
     */
    static fromJSON(json) {
        const data = {
            ...json,
            agreementDate: new Date(json.agreementDate),
            effectiveDate: new Date(json.effectiveDate),
            expiryDate: json.expiryDate ? new Date(json.expiryDate) : null,
            createdAt: new Date(json.createdAt),
            updatedAt: new Date(json.updatedAt)
        };
        
        return new DriverAgreementDocument(data);
    }

    /**
     * 创建空白协议
     * @param {Object} defaults - 默认值
     * @returns {DriverAgreementDocument} 空白协议实例
     * @static
     */
    static createBlank(defaults = {}) {
        return new DriverAgreementDocument({
            agreementNumber: this.generateAgreementNumber(),
            agreementDate: new Date(),
            effectiveDate: new Date(),
            ...defaults
        });
    }

    /**
     * 转换为字符串表示
     * @returns {string} 字符串表示
     */
    toString() {
        return `DriverAgreementDocument(${this.agreementNumber}, ${this.driverInfo.name}, ${this.companyInfo.name})`;
    }
}
