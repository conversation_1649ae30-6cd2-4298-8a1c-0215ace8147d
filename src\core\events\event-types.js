/**
 * @file 事件类型定义
 * @description 定义应用程序中所有事件的类型和结构
 */

/**
 * 状态变更事件类型
 * @enum {string}
 */
export const StateEvents = {
    // 文档状态变更
    DOCUMENT_TYPE_CHANGED: 'document:type:changed',
    DOCUMENT_DATA_CHANGED: 'document:data:changed',
    DOCUMENT_TEMPLATE_CHANGED: 'document:template:changed',
    
    // 语言和本地化
    LANGUAGE_CHANGED: 'language:changed',
    BILINGUAL_MODE_CHANGED: 'bilingual:mode:changed',
    
    // 公司和配置
    COMPANY_CHANGED: 'company:changed',
    CURRENCY_CHANGED: 'currency:changed',
    PAYMENT_METHOD_CHANGED: 'payment:method:changed',
    
    // 表单数据变更
    FORM_DATA_CHANGED: 'form:data:changed',
    FORM_FIELD_CHANGED: 'form:field:changed',
    FORM_VALIDATION_CHANGED: 'form:validation:changed',
    
    // 项目列表变更
    ITEMS_ADDED: 'items:added',
    ITEMS_REMOVED: 'items:removed',
    ITEMS_UPDATED: 'items:updated',
    ITEMS_REORDERED: 'items:reordered'
};

/**
 * UI事件类型
 * @enum {string}
 */
export const UIEvents = {
    // 模态框事件
    MODAL_OPENED: 'modal:opened',
    MODAL_CLOSED: 'modal:closed',
    
    // 通知事件
    NOTIFICATION_SHOWN: 'notification:shown',
    NOTIFICATION_HIDDEN: 'notification:hidden',
    
    // 预览事件
    PREVIEW_UPDATED: 'preview:updated',
    PREVIEW_SCROLLED: 'preview:scrolled',
    PREVIEW_RESIZED: 'preview:resized',
    PREVIEW_ZOOMED: 'preview:zoomed',
    
    // 侧边栏事件
    SIDEBAR_OPENED: 'sidebar:opened',
    SIDEBAR_CLOSED: 'sidebar:closed',
    SIDEBAR_RESIZED: 'sidebar:resized',
    
    // 工具栏事件
    TOOLBAR_ACTION: 'toolbar:action',
    TOOLBAR_UPDATED: 'toolbar:updated',
    
    // 表单控件事件
    FORM_CONTROL_FOCUSED: 'form:control:focused',
    FORM_CONTROL_BLURRED: 'form:control:blurred',
    FORM_CONTROL_CHANGED: 'form:control:changed',
    
    // 组件事件
    COMPONENT_REGISTERED: 'component:registered',
    COMPONENT_UNREGISTERED: 'component:unregistered',
    COMPONENT_MOUNTED: 'component:mounted',
    COMPONENT_UNMOUNTED: 'component:unmounted',
    COMPONENT_UPDATED: 'component:updated',
    
    // 主题事件
    THEME_CHANGED: 'theme:changed',
    THEME_LOADED: 'theme:loaded'
};

/**
 * 渲染事件类型
 * @enum {string}
 */
export const RenderEvents = {
    // 渲染生命周期
    RENDER_STARTED: 'render:started',
    RENDER_PROGRESS: 'render:progress',
    RENDER_COMPLETED: 'render:completed',
    RENDER_FAILED: 'render:failed',
    RENDER_CANCELLED: 'render:cancelled',
    
    // 渲染阶段事件
    RENDER_STAGE_STARTED: 'render:stage:started',
    RENDER_STAGE_COMPLETED: 'render:stage:completed',
    RENDER_STAGE_FAILED: 'render:stage:failed',
    
    // 模板事件
    TEMPLATE_LOADED: 'template:loaded',
    TEMPLATE_RENDERED: 'template:rendered',
    TEMPLATE_ERROR: 'template:error',
    TEMPLATE_CACHED: 'template:cached',
    
    // 样式事件
    STYLES_APPLIED: 'styles:applied',
    STYLES_UPDATED: 'styles:updated',
    STYLES_COMPILED: 'styles:compiled',
    STYLES_CACHED: 'styles:cached',
    
    // 渲染引擎事件
    ENGINE_INITIALIZED: 'engine:initialized',
    ENGINE_DESTROYED: 'engine:destroyed',
    ENGINE_CONFIG_CHANGED: 'engine:config:changed'
};

/**
 * 导出事件类型
 * @enum {string}
 */
export const ExportEvents = {
    // 导出生命周期
    EXPORT_STARTED: 'export:started',
    EXPORT_PROGRESS: 'export:progress',
    EXPORT_COMPLETED: 'export:completed',
    EXPORT_FAILED: 'export:failed',
    
    // 导出类型
    EXPORT_PDF_STARTED: 'export:pdf:started',
    EXPORT_PDF_COMPLETED: 'export:pdf:completed',
    EXPORT_IMAGE_STARTED: 'export:image:started',
    EXPORT_IMAGE_COMPLETED: 'export:image:completed'
};

/**
 * 导出器系统事件类型 - 导出器管理和注册相关事件
 * @enum {string}
 */
export const EXPORT_EVENTS = {
    // 导出器生命周期
    EXPORTER_INITIALIZED: 'exporter:initialized',
    EXPORTER_ERROR: 'exporter:error',
    EXPORTER_CLEANUP: 'exporter:cleanup',
    
    // 导出器注册表事件
    EXPORTER_REGISTERED: 'exporter:registered',
    EXPORTER_UNREGISTERED: 'exporter:unregistered',
    
    // 导出流程事件
    EXPORT_STARTED: 'export:started',
    EXPORT_PROGRESS: 'export:progress',
    EXPORT_COMPLETED: 'export:completed',
    EXPORT_FAILED: 'export:failed',
    EXPORT_CANCELLED: 'export:cancelled',
    
    // 批量导出事件
    BATCH_EXPORT_STARTED: 'batch:export:started',
    BATCH_EXPORT_PROGRESS: 'batch:export:progress',
    BATCH_EXPORT_COMPLETED: 'batch:export:completed',
    BATCH_EXPORT_FAILED: 'batch:export:failed',
    
    // 导出引擎事件
    ENGINE_STARTED: 'engine:started',
    ENGINE_STOPPED: 'engine:stopped',
    ENGINE_QUEUE_UPDATED: 'engine:queue:updated'
};

/**
 * 历史记录事件类型
 * @enum {string}
 */
export const HistoryEvents = {
    // 历史记录操作
    HISTORY_SAVED: 'history:saved',
    HISTORY_LOADED: 'history:loaded',
    HISTORY_CLEARED: 'history:cleared',
    
    // 撤销/重做
    UNDO_PERFORMED: 'undo:performed',
    REDO_PERFORMED: 'redo:performed'
};

/**
 * API事件类型
 * @enum {string}
 */
export const APIEvents = {
    // API调用
    API_REQUEST_STARTED: 'api:request:started',
    API_REQUEST_COMPLETED: 'api:request:completed',
    API_REQUEST_FAILED: 'api:request:failed',
    
    // 自然语言处理
    NLP_PROCESSING_STARTED: 'nlp:processing:started',
    NLP_PROCESSING_COMPLETED: 'nlp:processing:completed',
    NLP_PROCESSING_FAILED: 'nlp:processing:failed'
};

/**
 * 错误事件类型
 * @enum {string}
 */
export const ErrorEvents = {
    // 应用程序错误
    APPLICATION_ERROR: 'error:application',
    VALIDATION_ERROR: 'error:validation',
    NETWORK_ERROR: 'error:network',
    RENDER_ERROR: 'error:render',
    EXPORT_ERROR: 'error:export'
};

/**
 * 事件数据接口
 * @typedef {Object} EventData
 * @property {string} type - 事件类型
 * @property {*} payload - 事件载荷
 * @property {number} timestamp - 时间戳
 * @property {string} source - 事件源
 */

/**
 * 事件监听器接口
 * @typedef {Function} EventListener
 * @param {EventData} eventData - 事件数据
 */

/**
 * 事件取消订阅函数接口
 * @typedef {Function} Unsubscribe
 */

/**
 * 应用程序事件类型
 * @enum {string}
 */
export const AppEvents = {
    // 应用生命周期
    APP_INITIALIZED: 'app:initialized',
    APP_READY: 'app:ready',
    APP_DESTROYED: 'app:destroyed',
    APP_SUSPENDED: 'app:suspended',
    APP_RESUMED: 'app:resumed',
    
    // 错误事件
    ERROR_OCCURRED: 'error:occurred',
    WARNING_OCCURRED: 'warning:occurred',
    CRITICAL_ERROR: 'error:critical',
    
    // 性能事件
    PERFORMANCE_MEASURED: 'performance:measured',
    PERFORMANCE_WARNING: 'performance:warning',
    MEMORY_USAGE_HIGH: 'memory:usage:high',
    
    // 配置事件
    CONFIG_LOADED: 'config:loaded',
    CONFIG_CHANGED: 'config:changed',
    CONFIG_SAVED: 'config:saved',
    
    // 插件事件
    PLUGIN_LOADED: 'plugin:loaded',
    PLUGIN_UNLOADED: 'plugin:unloaded',
    PLUGIN_ERROR: 'plugin:error'
};

/**
 * 缓存和性能事件类型
 * @enum {string}
 */
export const CacheEvents = {
    // 缓存操作
    CACHE_HIT: 'cache:hit',
    CACHE_MISS: 'cache:miss',
    CACHE_SET: 'cache:set',
    CACHE_CLEAR: 'cache:clear',
    CACHE_EXPIRED: 'cache:expired',
    
    // 缓存性能
    CACHE_SIZE_WARNING: 'cache:size:warning',
    CACHE_CLEANUP: 'cache:cleanup'
};

/**
 * 网络和API事件类型
 * @enum {string}
 */
export const NetworkEvents = {
    // 网络状态
    NETWORK_ONLINE: 'network:online',
    NETWORK_OFFLINE: 'network:offline',
    
    // API请求
    API_REQUEST_START: 'api:request:start',
    API_REQUEST_SUCCESS: 'api:request:success',
    API_REQUEST_ERROR: 'api:request:error',
    API_REQUEST_TIMEOUT: 'api:request:timeout'
};

/**
 * 统一事件类型导出
 * @description 将所有事件类型合并为一个对象，便于统一管理和使用
 */
export const ALL_EVENTS = {
    ...StateEvents,
    ...UIEvents,
    ...RenderEvents,
    ...ExportEvents,
    ...AppEvents,
    ...CacheEvents,
    ...NetworkEvents
};

/**
 * 事件优先级定义
 * @enum {number}
 */
export const EventPriority = {
    LOW: 1,
    NORMAL: 2,
    HIGH: 3,
    CRITICAL: 4
};

/**
 * 获取事件优先级
 * @param {string} eventType - 事件类型
 * @returns {number} 事件优先级
 */
export function getEventPriority(eventType) {
    // 关键错误事件
    if (eventType.includes('error') || eventType.includes('failed')) {
        return EventPriority.CRITICAL;
    }
    
    // 性能相关事件
    if (eventType.includes('performance') || eventType.includes('memory')) {
        return EventPriority.HIGH;
    }
    
    // 渲染和导出事件
    if (eventType.includes('render') || eventType.includes('export')) {
        return EventPriority.HIGH;
    }
    
    // UI交互事件
    if (eventType.includes('ui') || eventType.includes('component')) {
        return EventPriority.NORMAL;
    }
    
    // 默认优先级
    return EventPriority.NORMAL;
}

/**
 * UI组件系统事件类型 - UI组件管理和生命周期相关事件
 * @enum {string}
 */
export const UI_EVENTS = {
    // 组件生命周期
    COMPONENT_INITIALIZED: 'component:initialized',
    COMPONENT_BEFORE_MOUNT: 'component:before:mount',
    COMPONENT_MOUNTED: 'component:mounted',
    COMPONENT_BEFORE_UNMOUNT: 'component:before:unmount',
    COMPONENT_UNMOUNTED: 'component:unmounted',
    COMPONENT_BEFORE_UPDATE: 'component:before:update',
    COMPONENT_UPDATED: 'component:updated',
    COMPONENT_DESTROYED: 'component:destroyed',
    
    // 组件交互
    COMPONENT_CLICKED: 'component:clicked',
    COMPONENT_FOCUSED: 'component:focused',
    COMPONENT_BLURRED: 'component:blurred',
    COMPONENT_CHANGED: 'component:changed',
    COMPONENT_VALIDATED: 'component:validated',
    
    // 组件错误
    COMPONENT_ERROR: 'component:error',
    COMPONENT_WARNING: 'component:warning',
    
    // 组件注册表事件
    COMPONENT_REGISTERED: 'component:registered',
    COMPONENT_UNREGISTERED: 'component:unregistered',
    REGISTRY_CLEARED: 'registry:cleared',
    
    // 组件关系事件
    COMPONENT_CHILD_ADDED: 'component:child:added',
    COMPONENT_CHILD_REMOVED: 'component:child:removed',
    
    // 组件管理器事件
    MANAGER_CLEANED_UP: 'manager:cleaned:up',
    
    // 主题系统事件
    THEME_REGISTERED: 'theme:registered',
    THEME_CHANGED: 'theme:changed',
    
    // 表单系统事件
    FORM_SUBMIT: 'form:submit',
    FORM_RESET: 'form:reset',
    FORM_FIELD_ADDED: 'form:field:added',
    FORM_FIELD_REMOVED: 'form:field:removed',
    FORM_VALIDATION_FAILED: 'form:validation:failed',
    FORM_VALIDATION_PASSED: 'form:validation:passed',
    
    // 预览系统事件
    PREVIEW_REFRESH: 'preview:refresh',
    PREVIEW_ERROR: 'preview:error',
    
    // 导出控制事件
    EXPORT_CONFIG_CHANGED: 'export:config:changed',
    EXPORT_FORMAT_SELECTED: 'export:format:selected',
    
    // 对话框系统事件
    DIALOG_BEFORE_SHOW: 'dialog:before:show',
    DIALOG_SHOWN: 'dialog:shown',
    DIALOG_BEFORE_CLOSE: 'dialog:before:close',
    DIALOG_CLOSED: 'dialog:closed',
    DIALOG_DESTROYED: 'dialog:destroyed',
    DIALOG_CONTENT_UPDATED: 'dialog:content:updated',
    DIALOG_TITLE_UPDATED: 'dialog:title:updated',
    DIALOG_DRAG_START: 'dialog:drag:start',
    DIALOG_DRAG_MOVE: 'dialog:drag:move',
    DIALOG_DRAG_END: 'dialog:drag:end',
    DIALOG_RESIZE_START: 'dialog:resize:start',
    DIALOG_RESIZE_MOVE: 'dialog:resize:move',
    DIALOG_RESIZE_END: 'dialog:resize:end',
    
    // 通知系统事件
    NOTIFICATION_BEFORE_SHOW: 'notification:before:show',
    NOTIFICATION_SHOWN: 'notification:shown',
    NOTIFICATION_BEFORE_HIDE: 'notification:before:hide',
    NOTIFICATION_HIDDEN: 'notification:hidden',
    NOTIFICATION_DESTROYED: 'notification:destroyed',
    NOTIFICATION_CONTENT_UPDATED: 'notification:content:updated',
    NOTIFICATION_TYPE_UPDATED: 'notification:type:updated',
    NOTIFICATION_PAUSED: 'notification:paused',
    NOTIFICATION_RESUMED: 'notification:resumed',
    NOTIFICATION_ACTION_CLICKED: 'notification:action:clicked'
};
