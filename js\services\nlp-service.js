/**
 * @file NLP服务 - 自然语言处理服务模块
 * <AUTHOR> Team
 * @description 
 * 这个文件定义了NLP服务类，负责处理自然语言相关的功能
 * 从index.html中提取的NLP处理逻辑被重构到这里
 * 
 * 功能特性：
 * - 文本分析和处理
 * - 智能表单填充
 * - 语言检测和翻译
 * - 文本验证和格式化
 * - Gemini AI集成（可选）
 */

// #region NLP服务类定义
/**
 * @class NLPService - 自然语言处理服务类
 * @description 提供文本分析、智能填充等NLP功能
 */
export class NLPService {
    /**
     * 构造函数 - 初始化NLP服务
     * @param {Object} options - 服务配置选项
     */
    constructor(options = {}) {
        // 基础配置
        this.config = options.config;
        this.eventBus = options.eventBus;
        
        // NLP配置
        this.nlpConfig = {
            enabled: true,
            mode: 'local', // 'local', 'gemini'
            geminiEnabled: false,
            confidence: {
                minimum: 0.3,
                good: 0.6,
                excellent: 0.8
            },
            timeout: 30000,
            retryAttempts: 3,
            ...options.nlpConfig
        };
        
        // 服务状态
        this.isInitialized = false;
        this.isProcessing = false;
        
        // 处理器缓存
        this.processors = new Map();
        
        // 统计信息
        this.stats = {
            totalProcessed: 0,
            successCount: 0,
            errorCount: 0,
            averageProcessTime: 0
        };
        
        console.log('[NLPService] NLP服务已创建');
    }

    /**
     * 初始化NLP服务
     * @returns {Promise<void>}
     */
    async initialize() {
        try {
            console.log('[NLPService] 初始化NLP服务...');
            
            // 初始化文本处理器
            this._initializeProcessors();
            
            // 检查Gemini可用性
            if (this.nlpConfig.geminiEnabled) {
                await this._checkGeminiAvailability();
            }
            
            // 设置事件监听
            this._setupEventListeners();
            
            this.isInitialized = true;
            
            // 触发初始化完成事件
            if (this.eventBus) {
                this.eventBus.emit('nlp:initialized', {
                    service: 'NLPService',
                    timestamp: new Date()
                });
            }
            
            console.log('[NLPService] NLP服务初始化完成');
            
        } catch (error) {
            console.error('[NLPService] NLP服务初始化失败:', error);
            throw error;
        }
    }

    /**
     * 智能文本分析 - 分析文本内容并提取信息
     * @param {string} text - 要分析的文本
     * @param {Object} options - 分析选项
     * @returns {Promise<Object>} 分析结果
     */
    async analyzeText(text, options = {}) {
        if (!this.isInitialized) {
            throw new Error('NLP服务未初始化');
        }
        
        const startTime = Date.now();
        this.isProcessing = true;
        
        try {
            console.log('[NLPService] 开始文本分析...');
            
            // 基础文本清理
            const cleanedText = this._cleanText(text);
            
            // 执行分析
            const analysis = {
                originalText: text,
                cleanedText: cleanedText,
                language: this._detectLanguage(cleanedText),
                entities: this._extractEntities(cleanedText),
                keywords: this._extractKeywords(cleanedText),
                sentiment: this._analyzeSentiment(cleanedText),
                confidence: this._calculateConfidence(cleanedText),
                metadata: {
                    length: cleanedText.length,
                    wordCount: this._countWords(cleanedText),
                    processTime: Date.now() - startTime
                }
            };
            
            // 更新统计
            this._updateStats(Date.now() - startTime, true);
            
            console.log('[NLPService] 文本分析完成');
            return analysis;
            
        } catch (error) {
            this._updateStats(Date.now() - startTime, false);
            console.error('[NLPService] 文本分析失败:', error);
            throw error;
        } finally {
            this.isProcessing = false;
        }
    }

    /**
     * 智能表单填充 - 根据文本内容智能填充表单字段
     * @param {string} text - 输入文本
     * @param {Object} formSchema - 表单结构定义
     * @returns {Promise<Object>} 填充结果
     */
    async smartFormFill(text, formSchema) {
        try {
            console.log('[NLPService] 开始智能表单填充...');
            
            // 分析文本
            const analysis = await this.analyzeText(text);
            
            // 提取表单相关信息
            const formData = {};
            const confidence = {};
            
            // 遍历表单字段
            for (const [fieldName, fieldConfig] of Object.entries(formSchema)) {
                const result = this._extractFieldValue(analysis, fieldName, fieldConfig);
                if (result.value) {
                    formData[fieldName] = result.value;
                    confidence[fieldName] = result.confidence;
                }
            }
            
            console.log('[NLPService] 智能表单填充完成');
            
            return {
                formData,
                confidence,
                analysis,
                metadata: {
                    fieldsFound: Object.keys(formData).length,
                    totalFields: Object.keys(formSchema).length,
                    fillRate: Object.keys(formData).length / Object.keys(formSchema).length
                }
            };
            
        } catch (error) {
            console.error('[NLPService] 智能表单填充失败:', error);
            throw error;
        }
    }

    /**
     * 文本验证 - 验证文本内容的有效性
     * @param {string} text - 要验证的文本
     * @param {Object} rules - 验证规则
     * @returns {Object} 验证结果
     */
    validateText(text, rules = {}) {
        const errors = [];
        const warnings = [];
        
        // 基础验证
        if (!text || text.trim().length === 0) {
            errors.push('文本内容不能为空');
        }
        
        // 长度验证
        if (rules.minLength && text.length < rules.minLength) {
            errors.push(`文本长度不能少于${rules.minLength}个字符`);
        }
        
        if (rules.maxLength && text.length > rules.maxLength) {
            errors.push(`文本长度不能超过${rules.maxLength}个字符`);
        }
        
        // 格式验证
        if (rules.pattern && !rules.pattern.test(text)) {
            errors.push('文本格式不符合要求');
        }
        
        // 语言验证
        if (rules.language) {
            const detectedLanguage = this._detectLanguage(text);
            if (detectedLanguage !== rules.language) {
                warnings.push(`检测到的语言(${detectedLanguage})与要求的语言(${rules.language})不匹配`);
            }
        }
        
        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            metadata: {
                textLength: text.length,
                wordCount: this._countWords(text),
                detectedLanguage: this._detectLanguage(text)
            }
        };
    }

    /**
     * 获取服务统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.stats,
            isInitialized: this.isInitialized,
            isProcessing: this.isProcessing,
            config: {
                enabled: this.nlpConfig.enabled,
                mode: this.nlpConfig.mode,
                geminiEnabled: this.nlpConfig.geminiEnabled
            }
        };
    }

    /**
     * 初始化文本处理器
     * @private
     */
    _initializeProcessors() {
        // 实体提取器
        this.processors.set('entity', {
            email: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
            phone: /(\+?86)?[-\s]?1[3-9]\d{9}/g,
            date: /\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?/g,
            money: /[¥$€£]\s?\d+(?:,\d{3})*(?:\.\d{2})?/g,
            number: /\d+(?:\.\d+)?/g
        });
        
        // 关键词提取器
        this.processors.set('keyword', {
            stopWords: new Set(['的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这']),
            minLength: 2,
            maxKeywords: 10
        });
        
        console.log('[NLPService] 文本处理器初始化完成');
    }

    /**
     * 检查Gemini可用性
     * @private
     */
    async _checkGeminiAvailability() {
        // 这里可以实现Gemini API的可用性检查
        console.log('[NLPService] 检查Gemini可用性...');
        // 暂时设置为不可用
        this.nlpConfig.geminiEnabled = false;
    }

    /**
     * 设置事件监听
     * @private
     */
    _setupEventListeners() {
        if (this.eventBus) {
            this.eventBus.on('config:changed', (event) => {
                if (event.path.startsWith('nlp.')) {
                    this._handleConfigChange(event);
                }
            });
        }
    }

    /**
     * 清理文本
     * @param {string} text - 原始文本
     * @returns {string} 清理后的文本
     * @private
     */
    _cleanText(text) {
        return text
            .trim()
            .replace(/\s+/g, ' ')
            .replace(/[^\w\s\u4e00-\u9fa5]/g, ' ')
            .trim();
    }

    /**
     * 检测语言
     * @param {string} text - 文本内容
     * @returns {string} 语言代码
     * @private
     */
    _detectLanguage(text) {
        // 简单的语言检测逻辑
        const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
        const totalChars = text.length;
        
        if (chineseChars / totalChars > 0.3) {
            return 'zh-CN';
        } else {
            return 'en-US';
        }
    }

    /**
     * 提取实体
     * @param {string} text - 文本内容
     * @returns {Object} 提取的实体
     * @private
     */
    _extractEntities(text) {
        const entities = {};
        const processors = this.processors.get('entity');
        
        for (const [type, pattern] of Object.entries(processors)) {
            const matches = text.match(pattern);
            if (matches) {
                entities[type] = [...new Set(matches)]; // 去重
            }
        }
        
        return entities;
    }

    /**
     * 提取关键词
     * @param {string} text - 文本内容
     * @returns {Array} 关键词列表
     * @private
     */
    _extractKeywords(text) {
        const config = this.processors.get('keyword');
        const words = text.split(/\s+/);
        const wordCount = new Map();
        
        // 统计词频
        for (const word of words) {
            if (word.length >= config.minLength && !config.stopWords.has(word)) {
                wordCount.set(word, (wordCount.get(word) || 0) + 1);
            }
        }
        
        // 按频率排序并返回前N个
        return Array.from(wordCount.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, config.maxKeywords)
            .map(([word, count]) => ({ word, count }));
    }

    /**
     * 分析情感
     * @param {string} text - 文本内容
     * @returns {Object} 情感分析结果
     * @private
     */
    _analyzeSentiment(text) {
        // 简单的情感分析逻辑
        const positiveWords = ['好', '棒', '优秀', '满意', '喜欢', '赞'];
        const negativeWords = ['差', '坏', '糟糕', '不满', '讨厌', '烂'];
        
        let positiveScore = 0;
        let negativeScore = 0;
        
        for (const word of positiveWords) {
            positiveScore += (text.match(new RegExp(word, 'g')) || []).length;
        }
        
        for (const word of negativeWords) {
            negativeScore += (text.match(new RegExp(word, 'g')) || []).length;
        }
        
        const totalScore = positiveScore + negativeScore;
        
        if (totalScore === 0) {
            return { sentiment: 'neutral', confidence: 0.5 };
        }
        
        const sentiment = positiveScore > negativeScore ? 'positive' : 'negative';
        const confidence = Math.abs(positiveScore - negativeScore) / totalScore;
        
        return { sentiment, confidence };
    }

    /**
     * 计算置信度
     * @param {string} text - 文本内容
     * @returns {number} 置信度分数
     * @private
     */
    _calculateConfidence(text) {
        // 基于文本长度和内容质量计算置信度
        const length = text.length;
        const wordCount = this._countWords(text);
        
        let confidence = 0.5; // 基础置信度
        
        // 长度因子
        if (length > 50) confidence += 0.2;
        if (length > 200) confidence += 0.2;
        
        // 词汇丰富度因子
        if (wordCount > 10) confidence += 0.1;
        
        return Math.min(confidence, 1.0);
    }

    /**
     * 统计单词数量
     * @param {string} text - 文本内容
     * @returns {number} 单词数量
     * @private
     */
    _countWords(text) {
        return text.split(/\s+/).filter(word => word.length > 0).length;
    }

    /**
     * 提取字段值
     * @param {Object} analysis - 文本分析结果
     * @param {string} fieldName - 字段名称
     * @param {Object} fieldConfig - 字段配置
     * @returns {Object} 提取结果
     * @private
     */
    _extractFieldValue(analysis, fieldName, fieldConfig) {
        // 这里可以实现更复杂的字段值提取逻辑
        const entities = analysis.entities;
        
        // 根据字段类型提取值
        switch (fieldConfig.type) {
            case 'email':
                return {
                    value: entities.email?.[0] || null,
                    confidence: entities.email ? 0.9 : 0
                };
            case 'phone':
                return {
                    value: entities.phone?.[0] || null,
                    confidence: entities.phone ? 0.9 : 0
                };
            case 'date':
                return {
                    value: entities.date?.[0] || null,
                    confidence: entities.date ? 0.8 : 0
                };
            default:
                return { value: null, confidence: 0 };
        }
    }

    /**
     * 更新统计信息
     * @param {number} processTime - 处理时间
     * @param {boolean} success - 是否成功
     * @private
     */
    _updateStats(processTime, success) {
        this.stats.totalProcessed++;
        
        if (success) {
            this.stats.successCount++;
        } else {
            this.stats.errorCount++;
        }
        
        // 更新平均处理时间
        this.stats.averageProcessTime = 
            (this.stats.averageProcessTime * (this.stats.totalProcessed - 1) + processTime) / 
            this.stats.totalProcessed;
    }

    /**
     * 处理配置变更
     * @param {Object} event - 配置变更事件
     * @private
     */
    _handleConfigChange(event) {
        console.log('[NLPService] 配置已变更:', event.path);
        // 可以在这里处理配置变更的逻辑
    }

    /**
     * 销毁NLP服务
     */
    destroy() {
        this.processors.clear();
        this.isInitialized = false;
        this.isProcessing = false;
        
        console.log('[NLPService] NLP服务已销毁');
    }
}
// #endregion
