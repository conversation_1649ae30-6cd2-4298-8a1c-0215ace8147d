/**
 * @file SmartOffice主应用控制器
 * @description 整个SmartOffice系统的统一入口和核心管理器，负责协调所有模块的工作
 */

// #region 导入依赖模块
import { EventEmitter } from '../core/events/event-emitter.js';
import { UI_EVENTS, EXPORT_EVENTS, RenderEvents } from '../core/events/event-types.js';
import { ComponentManager } from '../ui/components/base-component.js';
import { ExportManager } from '../exporters/index.js';
import { RendererManager } from '../renderers/index.js';
import { TemplateManager } from '../templates/index.js';
import { WorkflowEngine } from '../workflow/workflow-engine.js';
import { ConfigurationManager } from '../config/configuration-manager.js';
import { GlobalStateManager } from '../state/global-state-manager.js';
import { PluginManager } from '../core/plugins/plugin-system.js';
import { createLogger, getLogger, LogLevel } from '../core/utils/logger.js';
import { getLocalNLPProcessor } from '../core/utils/local-nlp-processor.js';
import { getGeminiNLPProcessor } from '../core/utils/gemini-nlp-processor.js';
import { SmartNLPProcessor } from '../core/utils/smart-nlp-processor.js';
import { getOfflineConfigManager } from '../config/offline-config.js';
import { getLocalResourceManager } from '../core/utils/local-resources.js';
// 统一渲染架构
import { UnifiedRenderEngine } from '../core/rendering/unified-render-engine.js';
import { DocumentModel } from '../core/rendering/document-model.js';
import { StyleManager } from '../core/rendering/style-manager.js';
import { PositionManager } from '../core/rendering/position-manager.js';
// #endregion

// #region SmartOfficeApp 主应用类
/**
 * @class SmartOfficeApp - SmartOffice主应用控制器
 * @description 整个系统的核心管理器，提供统一的API接口和自动化工作流
 */
export class SmartOfficeApp extends EventEmitter {
    /**
     * 构造函数 - 初始化SmartOffice应用
     * @param {Object} config - 应用配置
     * @param {HTMLElement} container - 应用容器元素
     */
    constructor(config = {}, container = null) {
        super();
        
        // 首先初始化日志系统
        this.logger = createLogger({
            level: config.logLevel || LogLevel.DEBUG,
            enableConsole: true,
            enableStorage: true,
            enablePerformance: true,
            enableDebugPanel: config.enableDebugPanel || false
        });
        
        this.logger.info('SmartOfficeApp', 'constructor', '开始构造SmartOffice应用实例', {
            configKeys: Object.keys(config),
            hasContainer: !!container
        });
        
        // 应用基本信息
        this.name = 'SmartOffice';
        this.version = '2.0.0';
        this.description = '智能办公文档生成系统';
        
        this.logger.debug('SmartOfficeApp', 'constructor', '设置应用基本信息', {
            name: this.name,
            version: this.version,
            description: this.description
        });
        
        // 应用状态
        this.isInitialized = false;
        this.isReady = false;
        this.container = container;
        
        // 核心管理器实例
        this.managers = {
            config: null,           // 配置管理器
            offlineConfig: null,    // 离线配置管理器
            state: null,            // 状态管理器
            workflow: null,         // 工作流引擎
            components: null,       // UI组件管理器
            templates: null,        // 模板管理器
            renderers: null,        // 渲染器管理器
            exporters: null,        // 导出器管理器
            plugins: null,          // 插件系统
            nlp: null,              // 本地自然语言处理器
            resources: null,        // 本地资源管理器
            // 统一渲染架构组件
            renderEngine: null,     // 统一渲染引擎
            styleManager: null,     // 样式管理器
            positionManager: null   // 位置管理器
        };
        
        this.logger.debug('SmartOfficeApp', 'constructor', '初始化管理器容器', {
            managerKeys: Object.keys(this.managers)
        });
        
        // 应用配置
        this.config = {
            // 默认配置
            autoInit: true,
            enablePlugins: true,
            enableWorkflow: true,
            defaultLanguage: 'zh-CN',
            defaultTheme: 'default',
            defaultDocumentType: 'receipt',
            
            // 工作流配置
            workflow: {
                autoSave: true,
                autoPreview: true,
                smartTemplateSelection: true,
                batchProcessing: false
            },
            
            // UI配置
            ui: {
                showToolbar: true,
                showSidebar: true,
                showStatusBar: true,
                enableKeyboardShortcuts: true
            },
            
            // 导出配置
            export: {
                defaultFormat: 'pdf',
                autoDownload: true,
                showProgress: true,
                enableBatch: true
            },
            
            // 合并用户配置
            ...config
        };
        
        this.logger.info('SmartOfficeApp', 'constructor', '应用配置完成', {
            finalConfig: this.config
        });
        
        // 应用统计
        this.stats = {
            documentsGenerated: 0,
            templatesUsed: new Set(),
            exportFormats: new Map(),
            averageProcessingTime: 0,
            totalProcessingTime: 0,
            startTime: Date.now()
        };
        
        this.logger.debug('SmartOfficeApp', 'constructor', '统计数据初始化', {
            stats: this.stats
        });
        
        // 自动初始化（异步执行，不阻塞构造函数）
        if (this.config.autoInit) {
            this.logger.info('SmartOfficeApp', 'constructor', '启动自动初始化');
            // 使用 setTimeout 确保构造函数完成后再执行初始化
            setTimeout(() => {
                this._initializeApp().catch(error => {
                    this.logger.error('SmartOfficeApp', 'constructor', '自动初始化失败', { error });
                    this.emit('app:error', error);
                });
            }, 0);
        } else {
            this.logger.warn('SmartOfficeApp', 'constructor', '自动初始化已禁用，需要手动调用initialize()');
        }
        
        this.logger.info('SmartOfficeApp', 'constructor', 'SmartOffice应用实例构造完成');
    }

    /**
     * 初始化应用 - 设置所有管理器和系统组件
     * @private
     */
    async _initializeApp() {
        const initStartTime = performance.now();
        this.logger.startPerformanceMark('app_initialization', 'SmartOfficeApp', '_initializeApp');
        
        try {
            this.logger.info('SmartOfficeApp', '_initializeApp', `🚀 正在初始化 ${this.name} v${this.version}...`);
            
            // 1. 初始化离线配置管理器
            this.logger.debug('SmartOfficeApp', '_initializeApp', '步骤 1/9: 初始化离线配置管理器');
            await this._initializeOfflineConfigManager();
            
            // 2. 初始化本地资源管理器
            this.logger.debug('SmartOfficeApp', '_initializeApp', '步骤 2/9: 初始化本地资源管理器');
            await this._initializeLocalResourceManager();
            
            // 3. 初始化NLP处理器（支持本地和Gemini模式）
            this.logger.debug('SmartOfficeApp', '_initializeApp', '步骤 3/9: 初始化NLP处理器');
            await this._initializeNLPProcessor();
            
            // 4. 初始化配置管理器
            this.logger.debug('SmartOfficeApp', '_initializeApp', '步骤 4/9: 初始化配置管理器');
            await this._initializeConfigManager();
            
            // 5. 初始化状态管理器
            this.logger.debug('SmartOfficeApp', '_initializeApp', '步骤 5/9: 初始化状态管理器');
            await this._initializeStateManager();
            
            // 6. 初始化核心管理器
            this.logger.debug('SmartOfficeApp', '_initializeApp', '步骤 6/9: 初始化核心管理器');
            await this._initializeCoreManagers();
            
            // 7. 初始化工作流引擎
            this.logger.debug('SmartOfficeApp', '_initializeApp', '步骤 7/9: 初始化工作流引擎');
            await this._initializeWorkflowEngine();
            
            // 8. 初始化插件系统
            this.logger.debug('SmartOfficeApp', '_initializeApp', '步骤 8/9: 初始化插件系统');
            await this._initializePluginSystem();
            
            // 9. 设置事件监听器和UI界面
            this.logger.debug('SmartOfficeApp', '_initializeApp', '步骤 9/9: 设置事件监听器和UI界面');
            this._setupEventListeners();
            await this._initializeUI();
            
            // 标记为已初始化
            this.isInitialized = true;
            this.isReady = true;
            
            const initDuration = this.logger.endPerformanceMark('app_initialization', 'SmartOfficeApp', '_initializeApp');
            
            this.logger.info('SmartOfficeApp', '_initializeApp', `✅ ${this.name} 初始化完成！`, {
                initTime: `${initDuration?.toFixed(2)}ms`,
                totalStartupTime: `${(performance.now() - initStartTime).toFixed(2)}ms`,
                managersInitialized: Object.keys(this.managers).filter(key => this.managers[key] !== null)
            });
            
            // 触发初始化完成事件
            this.emit('app:initialized', {
                app: this.name,
                version: this.version,
                managers: Object.keys(this.managers),
                initTime: Date.now() - this.stats.startTime,
                performance: {
                    initDuration,
                    totalStartupTime: performance.now() - initStartTime
                }
            });
            
            // 记录初始化统计
            this.logger.incrementCounter('app_initializations', 'SmartOfficeApp');
            
        } catch (error) {
            this.logger.error('SmartOfficeApp', '_initializeApp', '❌ SmartOffice 初始化失败', {
                error: error.message,
                stack: error.stack,
                config: this.config
            });
            
            this.emit('app:error', {
                phase: 'initialization',
                error: error.message,
                timestamp: new Date().toISOString()
            });
            
            throw error;
        }
    }

    /**
     * 初始化离线配置管理器
     * @private
     */
    async _initializeOfflineConfigManager() {
        this.logger.startPerformanceMark('offline_config_init', 'SmartOfficeApp', '_initializeOfflineConfigManager');
        
        try {
            this.logger.info('SmartOfficeApp', '_initializeOfflineConfigManager', '🔧 初始化离线配置管理器');
            
            // 获取离线配置管理器实例
            this.managers.offlineConfig = getOfflineConfigManager();
            
            // 监听配置变更事件
            this.managers.offlineConfig.on('config:changed', (data) => {
                this.logger.debug('SmartOfficeApp', '_initializeOfflineConfigManager', '配置变更', data);
                this.emit('config:changed', data);
            });
            
            this.managers.offlineConfig.on('config:saved', (config) => {
                this.logger.debug('SmartOfficeApp', '_initializeOfflineConfigManager', '配置已保存');
            });
            
            const initDuration = this.logger.endPerformanceMark('offline_config_init', 'SmartOfficeApp', '_initializeOfflineConfigManager');
            this.logger.info('SmartOfficeApp', '_initializeOfflineConfigManager', '✅ 离线配置管理器初始化完成', {
                duration: `${initDuration?.toFixed(2)}ms`,
                mode: this.managers.offlineConfig.getMode()
            });
            
        } catch (error) {
            this.logger.error('SmartOfficeApp', '_initializeOfflineConfigManager', '离线配置管理器初始化失败', { error });
            throw error;
        }
    }

    /**
     * 初始化本地资源管理器
     * @private
     */
    async _initializeLocalResourceManager() {
        this.logger.startPerformanceMark('resource_manager_init', 'SmartOfficeApp', '_initializeLocalResourceManager');
        
        try {
            this.logger.info('SmartOfficeApp', '_initializeLocalResourceManager', '📦 初始化本地资源管理器');
            
            // 获取本地资源管理器实例
            this.managers.resources = getLocalResourceManager();
            
            // 获取资源状态
            const resourceStatus = this.managers.resources.getResourceStatus();
            
            const initDuration = this.logger.endPerformanceMark('resource_manager_init', 'SmartOfficeApp', '_initializeLocalResourceManager');
            this.logger.info('SmartOfficeApp', '_initializeLocalResourceManager', '✅ 本地资源管理器初始化完成', {
                duration: `${initDuration?.toFixed(2)}ms`,
                loadedResources: resourceStatus.loaded.length,
                cachedResources: resourceStatus.cached
            });
            
        } catch (error) {
            this.logger.error('SmartOfficeApp', '_initializeLocalResourceManager', '本地资源管理器初始化失败', { error });
            throw error;
        }
    }

    /**
     * 初始化NLP处理器（支持本地和Gemini模式）
     * @private
     */
    async _initializeNLPProcessor() {
        this.logger.startPerformanceMark('nlp_processor_init', 'SmartOfficeApp', '_initializeNLPProcessor');
        
        try {
            // 检测运行环境
            const isFileProtocol = typeof window !== 'undefined' && window.location.protocol === 'file:';
            
            // 获取NLP配置
            const nlpConfig = this.managers.offlineConfig.get('nlp');
            
            if (isFileProtocol) {
                // 在file://协议下，使用简化的本地NLP处理器
                this.logger.info('SmartOfficeApp', '_initializeNLPProcessor', '🏠 初始化本地NLP处理器（file://模式）', {
                    protocol: 'file://',
                    mode: 'local-only'
                });
                
                this.managers.nlp = getLocalNLPProcessor();
                
                // 测试本地处理器
                try {
                    const testResult = await this.managers.nlp.processText('测试文本：张三购买了2个苹果，单价5元', 'receipt');
                    this.logger.debug('SmartOfficeApp', '_initializeNLPProcessor', '本地NLP处理器测试成功', {
                        confidence: testResult.confidence,
                        extractedFields: Object.keys(testResult.extractedData || {}).length
                    });
                } catch (testError) {
                    this.logger.warn('SmartOfficeApp', '_initializeNLPProcessor', '本地NLP处理器测试失败', { error: testError.message });
                }
                
                const initDuration = this.logger.endPerformanceMark('nlp_processor_init', 'SmartOfficeApp', '_initializeNLPProcessor');
                this.logger.info('SmartOfficeApp', '_initializeNLPProcessor', '✅ 本地NLP处理器初始化完成', {
                    duration: `${initDuration?.toFixed(2)}ms`,
                    mode: 'local-only',
                    supportedFeatures: {
                        textProcessing: true,
                        imageProcessing: false,
                        multiModal: false,
                        fallbackSupport: false
                    }
                });
                
            } else {
                // 在HTTP协议下，使用智能NLP处理器
                this.logger.info('SmartOfficeApp', '_initializeNLPProcessor', '🧠 初始化智能NLP处理器（Gemini优先，本地备用）', {
                    geminiEnabled: nlpConfig.enabled,
                    hasApiKey: !!nlpConfig.apiKey,
                    enableImageProcessing: nlpConfig.enableImageProcessing,
                    enableMultiModal: nlpConfig.enableMultiModal
                });
                
                // 创建智能NLP处理器包装器
                this.managers.nlp = new SmartNLPProcessor({
                    geminiConfig: {
                        apiKey: nlpConfig.apiKey,
                        ...nlpConfig.geminiConfig
                    },
                    localConfig: nlpConfig.localProcessor,
                    logger: this.logger
                });
                
                // 测试智能处理器
                try {
                    const testResult = await this.managers.nlp.processText('测试文本：张三购买了2个苹果，单价5元', 'receipt');
                    this.logger.debug('SmartOfficeApp', '_initializeNLPProcessor', '智能NLP处理器测试成功', {
                        confidence: testResult.confidence,
                        extractedFields: Object.keys(testResult.extractedData || {}).length,
                        processorUsed: testResult.processorUsed || 'unknown'
                    });
                } catch (testError) {
                    this.logger.warn('SmartOfficeApp', '_initializeNLPProcessor', '智能NLP处理器测试失败', { error: testError.message });
                }
                
                const initDuration = this.logger.endPerformanceMark('nlp_processor_init', 'SmartOfficeApp', '_initializeNLPProcessor');
                this.logger.info('SmartOfficeApp', '_initializeNLPProcessor', '✅ 智能NLP处理器初始化完成', {
                    duration: `${initDuration?.toFixed(2)}ms`,
                    supportedFeatures: {
                        textProcessing: true,
                        imageProcessing: !!nlpConfig.apiKey && nlpConfig.enableImageProcessing,
                        multiModal: !!nlpConfig.apiKey && nlpConfig.enableMultiModal,
                        fallbackSupport: true
                    }
                });
            }
            
        } catch (error) {
            this.logger.error('SmartOfficeApp', '_initializeNLPProcessor', 'NLP处理器初始化失败', { error });
            throw error;
        }
    }

    /**
     * 初始化配置管理器
     * @private
     */
    async _initializeConfigManager() {
        this.logger.startPerformanceMark('config_manager_init', 'SmartOfficeApp', '_initializeConfigManager');
        
        try {
            this.logger.debug('SmartOfficeApp', '_initializeConfigManager', '正在创建配置管理器实例', {
                config: this.config
            });
            
            this.managers.config = new ConfigurationManager(this.config);
            
            this.logger.debug('SmartOfficeApp', '_initializeConfigManager', '正在初始化配置管理器');
            await this.managers.config.initialize();
            
            const duration = this.logger.endPerformanceMark('config_manager_init', 'SmartOfficeApp', '_initializeConfigManager');
            
            this.logger.info('SmartOfficeApp', '_initializeConfigManager', '📋 配置管理器初始化完成', {
                duration: `${duration?.toFixed(2)}ms`,
                configInfo: this.managers.config.getInfo()
            });
            
        } catch (error) {
            this.logger.error('SmartOfficeApp', '_initializeConfigManager', '配置管理器初始化失败', {
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }

    /**
     * 初始化状态管理器
     * @private
     */
    async _initializeStateManager() {
        this.logger.startPerformanceMark('state_manager_init', 'SmartOfficeApp', '_initializeStateManager');
        
        try {
            const stateConfig = {
                enablePersistence: true,
                enableHistory: true,
                maxHistorySize: 50
            };
            
            this.logger.debug('SmartOfficeApp', '_initializeStateManager', '正在创建状态管理器实例', {
                stateConfig
            });
            
            this.managers.state = new GlobalStateManager(stateConfig);
            
            this.logger.debug('SmartOfficeApp', '_initializeStateManager', '正在初始化状态管理器');
            await this.managers.state.initialize();
            
            const duration = this.logger.endPerformanceMark('state_manager_init', 'SmartOfficeApp', '_initializeStateManager');
            
            this.logger.info('SmartOfficeApp', '_initializeStateManager', '🔄 状态管理器初始化完成', {
                duration: `${duration?.toFixed(2)}ms`,
                stateInfo: this.managers.state.getInfo()
            });
            
        } catch (error) {
            this.logger.error('SmartOfficeApp', '_initializeStateManager', '状态管理器初始化失败', {
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }

    /**
     * 初始化核心管理器
     * @private
     */
    async _initializeCoreManagers() {
        this.logger.startPerformanceMark('core_managers_init', 'SmartOfficeApp', '_initializeCoreManagers');
        
        try {
            // 初始化统一渲染架构组件
            this.logger.debug('SmartOfficeApp', '_initializeCoreManagers', '正在初始化统一渲染架构组件');
            this.managers.styleManager = new StyleManager();
            this.managers.positionManager = new PositionManager();
            this.managers.renderEngine = new UnifiedRenderEngine({
                styleManager: this.managers.styleManager,
                positionManager: this.managers.positionManager
            });
            this.logger.trace('SmartOfficeApp', '_initializeCoreManagers', '统一渲染架构组件创建完成');
            
            // 初始化模板管理器
            this.logger.debug('SmartOfficeApp', '_initializeCoreManagers', '正在初始化模板管理器');
            this.managers.templates = new TemplateManager();
            this.logger.trace('SmartOfficeApp', '_initializeCoreManagers', '模板管理器实例创建完成');
            
            // 初始化渲染器管理器
            this.logger.debug('SmartOfficeApp', '_initializeCoreManagers', '正在初始化渲染器管理器');
            this.managers.renderers = new RendererManager();
            this.logger.trace('SmartOfficeApp', '_initializeCoreManagers', '渲染器管理器实例创建完成');
            
            // 初始化导出器管理器
            this.logger.debug('SmartOfficeApp', '_initializeCoreManagers', '正在初始化导出器管理器');
            this.managers.exporters = new ExportManager();
            this.logger.trace('SmartOfficeApp', '_initializeCoreManagers', '导出器管理器实例创建完成');
            
            // 初始化UI组件管理器
            this.logger.debug('SmartOfficeApp', '_initializeCoreManagers', '正在初始化UI组件管理器', {
                container: !!this.container,
                theme: this.config.defaultTheme
            });
            
            this.managers.components = new ComponentManager({
                container: this.container,
                theme: this.config.defaultTheme
            });
            this.logger.trace('SmartOfficeApp', '_initializeCoreManagers', 'UI组件管理器实例创建完成');
            
            const duration = this.logger.endPerformanceMark('core_managers_init', 'SmartOfficeApp', '_initializeCoreManagers');
            
            this.logger.info('SmartOfficeApp', '_initializeCoreManagers', '🔧 核心管理器初始化完成', {
                duration: `${duration?.toFixed(2)}ms`,
                managers: {
                    renderEngine: !!this.managers.renderEngine,
                    styleManager: !!this.managers.styleManager,
                    positionManager: !!this.managers.positionManager,
                    templates: !!this.managers.templates,
                    renderers: !!this.managers.renderers,
                    exporters: !!this.managers.exporters,
                    components: !!this.managers.components
                }
            });
            
        } catch (error) {
            this.logger.error('SmartOfficeApp', '_initializeCoreManagers', '核心管理器初始化失败', {
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }

    /**
     * 初始化工作流引擎
     * @private
     */
    async _initializeWorkflowEngine() {
        if (!this.config.enableWorkflow) {
            this.logger.warn('SmartOfficeApp', '_initializeWorkflowEngine', '工作流引擎已禁用，跳过初始化');
            return;
        }
        
        this.logger.startPerformanceMark('workflow_engine_init', 'SmartOfficeApp', '_initializeWorkflowEngine');
        
        try {
            const workflowConfig = {
                managers: this.managers,
                config: this.config.workflow
            };
            
            this.logger.debug('SmartOfficeApp', '_initializeWorkflowEngine', '正在创建工作流引擎实例', {
                workflowConfig: this.config.workflow
            });
            
            this.managers.workflow = new WorkflowEngine(workflowConfig);
            
            this.logger.debug('SmartOfficeApp', '_initializeWorkflowEngine', '正在初始化工作流引擎');
            await this.managers.workflow.initialize();
            
            const duration = this.logger.endPerformanceMark('workflow_engine_init', 'SmartOfficeApp', '_initializeWorkflowEngine');
            
            this.logger.info('SmartOfficeApp', '_initializeWorkflowEngine', '⚡ 工作流引擎初始化完成', {
                duration: `${duration?.toFixed(2)}ms`,
                workflowStats: this.managers.workflow.getStats()
            });
            
        } catch (error) {
            this.logger.error('SmartOfficeApp', '_initializeWorkflowEngine', '工作流引擎初始化失败', {
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }

    /**
     * 初始化插件系统
     * @private
     */
    async _initializePluginSystem() {
        if (!this.config.enablePlugins) {
            this.logger.warn('SmartOfficeApp', '_initializePluginSystem', '插件系统已禁用，跳过初始化');
            return;
        }
        
        this.logger.startPerformanceMark('plugin_system_init', 'SmartOfficeApp', '_initializePluginSystem');
        
        try {
            const pluginConfig = {
                app: this,
                managers: this.managers
            };
            
            this.logger.debug('SmartOfficeApp', '_initializePluginSystem', '正在创建插件系统实例');
            this.managers.plugins = new PluginManager(pluginConfig);
            
            this.logger.debug('SmartOfficeApp', '_initializePluginSystem', '正在初始化插件系统');
            await this.managers.plugins.initialize();
            
            const duration = this.logger.endPerformanceMark('plugin_system_init', 'SmartOfficeApp', '_initializePluginSystem');
            
            this.logger.info('SmartOfficeApp', '_initializePluginSystem', '🔌 插件系统初始化完成', {
                duration: `${duration?.toFixed(2)}ms`
            });
            
        } catch (error) {
            this.logger.error('SmartOfficeApp', '_initializePluginSystem', '插件系统初始化失败', {
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }

    /**
     * 设置事件监听器
     * @private
     */
    _setupEventListeners() {
        this.logger.startPerformanceMark('event_listeners_setup', 'SmartOfficeApp', '_setupEventListeners');
        
        try {
            this.logger.debug('SmartOfficeApp', '_setupEventListeners', '正在设置状态管理器事件监听');
            // 监听状态变更
            this.managers.state.on('state:changed', (data) => {
                this.logger.trace('SmartOfficeApp', '_setupEventListeners', '状态变更事件触发', data);
                this._handleStateChange(data);
            });
            
            // 监听工作流事件
            if (this.managers.workflow) {
                this.logger.debug('SmartOfficeApp', '_setupEventListeners', '正在设置工作流引擎事件监听');
                this.managers.workflow.on('workflow:completed', (data) => {
                    this.logger.trace('SmartOfficeApp', '_setupEventListeners', '工作流完成事件触发', data);
                    this._handleWorkflowCompleted(data);
                });
            }
            
            this.logger.debug('SmartOfficeApp', '_setupEventListeners', '正在设置导出器事件监听');
            // 监听导出事件
            this.managers.exporters.on(EXPORT_EVENTS.EXPORT_COMPLETED, (data) => {
                this.logger.trace('SmartOfficeApp', '_setupEventListeners', '导出完成事件触发', data);
                this._handleExportCompleted(data);
            });
            
            this.logger.debug('SmartOfficeApp', '_setupEventListeners', '正在设置UI组件事件监听');
            // 监听UI事件
            this.managers.components.on(UI_EVENTS.COMPONENT_CLICKED, (data) => {
                this.logger.trace('SmartOfficeApp', '_setupEventListeners', 'UI组件点击事件触发', data);
                this._handleUIInteraction(data);
            });
            
            const duration = this.logger.endPerformanceMark('event_listeners_setup', 'SmartOfficeApp', '_setupEventListeners');
            
            this.logger.info('SmartOfficeApp', '_setupEventListeners', '👂 事件监听器设置完成', {
                duration: `${duration?.toFixed(2)}ms`,
                listenersCount: 4
            });
            
        } catch (error) {
            this.logger.error('SmartOfficeApp', '_setupEventListeners', '事件监听器设置失败', {
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }

    /**
     * 初始化UI界面
     * @private
     */
    async _initializeUI() {
        if (!this.container) {
            this.logger.warn('SmartOfficeApp', '_initializeUI', '未提供容器元素，跳过UI初始化');
            return;
        }
        
        this.logger.startPerformanceMark('ui_initialization', 'SmartOfficeApp', '_initializeUI');
        
        try {
            this.logger.debug('SmartOfficeApp', '_initializeUI', '开始UI界面初始化', {
                containerTag: this.container.tagName,
                containerClasses: this.container.className
            });
            
            // 创建主界面布局
            this.logger.debug('SmartOfficeApp', '_initializeUI', '正在创建主界面布局');
            await this._createMainLayout();
            
            // 创建工具栏
            if (this.config.ui.showToolbar) {
                this.logger.debug('SmartOfficeApp', '_initializeUI', '正在创建工具栏');
                await this._createToolbar();
            } else {
                this.logger.debug('SmartOfficeApp', '_initializeUI', '工具栏已禁用，跳过创建');
            }
            
            // 创建侧边栏
            if (this.config.ui.showSidebar) {
                this.logger.debug('SmartOfficeApp', '_initializeUI', '正在创建侧边栏');
                await this._createSidebar();
            } else {
                this.logger.debug('SmartOfficeApp', '_initializeUI', '侧边栏已禁用，跳过创建');
            }
            
            // 创建状态栏
            if (this.config.ui.showStatusBar) {
                this.logger.debug('SmartOfficeApp', '_initializeUI', '正在创建状态栏');
                await this._createStatusBar();
            } else {
                this.logger.debug('SmartOfficeApp', '_initializeUI', '状态栏已禁用，跳过创建');
            }
            
            const duration = this.logger.endPerformanceMark('ui_initialization', 'SmartOfficeApp', '_initializeUI');
            
            this.logger.info('SmartOfficeApp', '_initializeUI', '🎨 UI界面初始化完成', {
                duration: `${duration?.toFixed(2)}ms`,
                components: {
                    toolbar: this.config.ui.showToolbar,
                    sidebar: this.config.ui.showSidebar,
                    statusBar: this.config.ui.showStatusBar
                }
            });
            
        } catch (error) {
            this.logger.error('SmartOfficeApp', '_initializeUI', 'UI界面初始化失败', {
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }

    /**
     * 创建主界面布局
     * @private
     */
    async _createMainLayout() {
        const { createLayout } = await import('../ui/components/layout.js');
        
        this.ui = {
            mainLayout: createLayout({
                name: 'main-layout',
                direction: 'vertical',
                className: 'smartoffice-main-layout'
            })
        };
        
        await this.ui.mainLayout.mount(this.container);
    }

    /**
     * 创建工具栏
     * @private
     */
    async _createToolbar() {
        const { createButton, createButtonGroup } = await import('../ui/components/button.js');
        
        // 创建工具栏按钮组
        this.ui.toolbar = createButtonGroup({
            name: 'main-toolbar',
            buttons: [
                { text: '新建文档', icon: 'plus', action: () => this.createDocument() },
                { text: '打开文档', icon: 'folder-open', action: () => this.openDocument() },
                { text: '保存文档', icon: 'save', action: () => this.saveDocument() },
                { text: '导出文档', icon: 'download', action: () => this.exportDocument() },
                { text: '设置', icon: 'settings', action: () => this.openSettings() }
            ],
            className: 'smartoffice-toolbar'
        });
        
        this.ui.mainLayout.addRegion('toolbar', this.ui.toolbar);
    }

    /**
     * 创建侧边栏
     * @private
     */
    async _createSidebar() {
        const { createPanel } = await import('../ui/components/layout.js');
        
        this.ui.sidebar = createPanel({
            name: 'main-sidebar',
            title: '文档模板',
            collapsible: true,
            className: 'smartoffice-sidebar'
        });
        
        this.ui.mainLayout.addRegion('sidebar', this.ui.sidebar);
    }

    /**
     * 创建状态栏
     * @private
     */
    async _createStatusBar() {
        const { createContainer } = await import('../ui/components/layout.js');
        
        this.ui.statusBar = createContainer({
            name: 'status-bar',
            className: 'smartoffice-status-bar'
        });
        
        this.ui.statusBar.addContent(`
            <div class="status-info">
                <span class="status-ready">就绪</span>
                <span class="status-docs">文档: ${this.stats.documentsGenerated}</span>
                <span class="status-version">v${this.version}</span>
            </div>
        `);
        
        this.ui.mainLayout.addRegion('statusBar', this.ui.statusBar);
    }

    // #region 公共API方法

    /**
     * 创建新文档 - 启动文档创建工作流
     * @param {string} documentType - 文档类型 ('receipt', 'invoice', 'driver-agreement')
     * @param {Object} data - 文档数据
     * @param {Object} options - 创建选项
     * @returns {Promise<Object>} 创建结果
     */
    async createDocument(documentType = null, data = {}, options = {}) {
        try {
            // 如果未指定文档类型，使用默认类型
            documentType = documentType || this.config.defaultDocumentType;
            
            console.log(`📄 正在创建 ${documentType} 文档...`);
            
            // 使用工作流引擎处理文档创建
            if (this.managers.workflow) {
                const result = await this.managers.workflow.executeWorkflow('create-document', {
                    documentType,
                    data,
                    options
                });
                
                // 更新统计信息
                this._updateStats('document-created', { documentType });
                
                return result;
            } else {
                // 手动处理文档创建流程
                return await this._manualCreateDocument(documentType, data, options);
            }
            
        } catch (error) {
            console.error('❌ 文档创建失败:', error);
            this.emit('app:error', {
                operation: 'create-document',
                error: error.message
            });
            throw error;
        }
    }

    /**
     * 手动文档创建流程
     * @param {string} documentType - 文档类型
     * @param {Object} data - 文档数据
     * @param {Object} options - 创建选项
     * @returns {Promise<Object>} 创建结果
     * @private
     */
    async _manualCreateDocument(documentType, data, options) {
        // 1. 选择模板
        const template = await this.managers.templates.getTemplate(documentType);
        
        // 2. 渲染文档
        const renderedContent = await this.managers.renderers.render(template, data);
        
        // 3. 如果启用自动预览，显示预览
        if (this.config.workflow.autoPreview) {
            await this.previewDocument(renderedContent);
        }
        
        return {
            documentType,
            template: template.name,
            content: renderedContent,
            data,
            timestamp: Date.now()
        };
    }

    /**
     * 预览文档
     * @param {Object} content - 文档内容
     * @param {Object} options - 预览选项
     * @returns {Promise<Object>} 预览结果
     */
    async previewDocument(content, options = {}) {
        try {
            console.log('👁️ 正在预览文档...');
            
            // 创建或更新预览组件
            if (!this.ui.preview) {
                const { createDocumentPreview } = await import('../ui/components/preview.js');
                
                this.ui.preview = createDocumentPreview({
                    name: 'main-preview',
                    className: 'smartoffice-preview'
                });
                
                this.ui.mainLayout.addRegion('content', this.ui.preview);
            }
            
            // 设置预览内容
            this.ui.preview.setDocument(content);
            await this.ui.preview.refresh();
            
            return {
                success: true,
                previewId: this.ui.preview.id
            };
            
        } catch (error) {
            console.error('❌ 文档预览失败:', error);
            throw error;
        }
    }

    /**
     * 导出文档
     * @param {Object} content - 文档内容
     * @param {string} format - 导出格式
     * @param {Object} options - 导出选项
     * @returns {Promise<Object>} 导出结果
     */
    async exportDocument(content = null, format = null, options = {}) {
        try {
            // 使用当前预览的文档内容
            content = content || this._getCurrentDocumentContent();
            format = format || this.config.export.defaultFormat;
            
            console.log(`📤 正在导出文档为 ${format} 格式...`);
            
            // 使用导出管理器导出
            const result = await this.managers.exporters.export(content, {
                format,
                ...options
            });
            
            // 更新统计信息
            this._updateStats('document-exported', { format });
            
            return result;
            
        } catch (error) {
            console.error('❌ 文档导出失败:', error);
            throw error;
        }
    }

    /**
     * 批量处理文档
     * @param {Array} documents - 文档数组
     * @param {Object} options - 处理选项
     * @returns {Promise<Array>} 处理结果数组
     */
    async batchProcess(documents, options = {}) {
        try {
            console.log(`📦 正在批量处理 ${documents.length} 个文档...`);
            
            if (this.managers.workflow) {
                return await this.managers.workflow.executeBatchWorkflow('batch-process', {
                    documents,
                    options
                });
            } else {
                // 手动批量处理
                const results = [];
                for (const doc of documents) {
                    const result = await this.createDocument(doc.type, doc.data, doc.options);
                    results.push(result);
                }
                return results;
            }
            
        } catch (error) {
            console.error('❌ 批量处理失败:', error);
            throw error;
        }
    }

    /**
     * 获取当前文档内容
     * @returns {Object} 当前文档内容
     * @private
     */
    _getCurrentDocumentContent() {
        if (this.ui.preview) {
            return this.ui.preview.getPreviewContent();
        }
        return null;
    }

    /**
     * 处理状态变更
     * @param {Object} data - 状态变更数据
     * @private
     */
    _handleStateChange(data) {
        // 更新UI状态
        if (this.ui.statusBar) {
            this._updateStatusBar(data);
        }
        
        // 触发状态变更事件
        this.emit('app:state:changed', data);
    }

    /**
     * 处理工作流完成
     * @param {Object} data - 工作流完成数据
     * @private
     */
    _handleWorkflowCompleted(data) {
        console.log('✅ 工作流完成:', data.workflowName);
        
        // 显示完成通知
        if (this.managers.components) {
            this._showNotification('success', `${data.workflowName} 完成`);
        }
    }

    /**
     * 处理导出完成
     * @param {Object} data - 导出完成数据
     * @private
     */
    _handleExportCompleted(data) {
        console.log('📤 导出完成:', data.format);
        
        // 显示完成通知
        this._showNotification('success', `文档已导出为 ${data.format} 格式`);
    }

    /**
     * 处理UI交互
     * @param {Object} data - UI交互数据
     * @private
     */
    _handleUIInteraction(data) {
        // 记录用户交互
        this.managers.state.setState('lastInteraction', {
            component: data.component,
            action: data.action,
            timestamp: Date.now()
        });
    }

    /**
     * 显示通知
     * @param {string} type - 通知类型
     * @param {string} message - 通知消息
     * @private
     */
    async _showNotification(type, message) {
        try {
            const { createNotification } = await import('../ui/components/notification.js');
            
            const notification = createNotification({
                type,
                message,
                autoHide: true,
                duration: 3000
            });
            
            notification.show();
            
        } catch (error) {
            console.warn('通知显示失败:', error);
        }
    }

    /**
     * 更新统计信息
     * @param {string} event - 事件类型
     * @param {Object} data - 事件数据
     * @private
     */
    _updateStats(event, data) {
        switch (event) {
            case 'document-created':
                this.stats.documentsGenerated++;
                this.stats.templatesUsed.add(data.documentType);
                break;
            case 'document-exported':
                const count = this.stats.exportFormats.get(data.format) || 0;
                this.stats.exportFormats.set(data.format, count + 1);
                break;
        }
        
        // 更新状态栏
        if (this.ui.statusBar) {
            this._updateStatusBar();
        }
    }

    /**
     * 更新状态栏
     * @private
     */
    _updateStatusBar() {
        if (this.ui.statusBar) {
            const statusContent = `
                <div class="status-info">
                    <span class="status-ready">就绪</span>
                    <span class="status-docs">文档: ${this.stats.documentsGenerated}</span>
                    <span class="status-version">v${this.version}</span>
                </div>
            `;
            this.ui.statusBar.setContent(statusContent);
        }
    }

    /**
     * 获取应用信息
     * @returns {Object} 应用信息
     */
    getAppInfo() {
        const managerCount = Object.values(this.managers).filter(manager => manager !== null).length;
        
        return {
            name: this.name,
            version: this.version,
            description: this.description,
            isInitialized: this.isInitialized,
            isReady: this.isReady,
            managersCount: managerCount,
            managers: Object.keys(this.managers).filter(key => this.managers[key] !== null),
            stats: { ...this.stats },
            config: { ...this.config },
            uptime: Date.now() - this.stats.startTime,
            container: !!this.container,
            mode: this.managers.offlineConfig?.getMode() || 'unknown',
            features: {
                offlineMode: this.managers.offlineConfig?.isOfflineMode() || false,
                localNLP: !!this.managers.nlp,
                localResources: !!this.managers.resources
            }
        };
    }

    /**
     * 获取管理器
     * @param {string} managerName - 管理器名称
     * @returns {Object} 管理器实例
     */
    getManager(managerName) {
        return this.managers[managerName] || null;
    }

    /**
     * 手动初始化应用 - 公共方法，用于手动初始化
     * @returns {Promise<void>}
     */
    async initialize() {
        this.logger.info('SmartOfficeApp', 'initialize', '手动初始化应用');
        
        if (this.isInitialized) {
            this.logger.warn('SmartOfficeApp', 'initialize', '应用已经初始化，跳过重复初始化');
            return;
        }
        
        return await this._initializeApp();
    }

    /**
     * 销毁应用
     */
    async destroy() {
        this.logger.info('SmartOfficeApp', 'destroy', '🔄 正在销毁 SmartOffice 应用...');
        
        // 销毁所有管理器
        for (const [name, manager] of Object.entries(this.managers)) {
            if (manager && typeof manager.destroy === 'function') {
                try {
                    await manager.destroy();
                    this.logger.debug('SmartOfficeApp', 'destroy', `管理器 ${name} 已销毁`);
                } catch (error) {
                    this.logger.error('SmartOfficeApp', 'destroy', `销毁管理器 ${name} 失败`, { error });
                }
            }
        }
        
        // 清理UI
        if (this.ui) {
            for (const [name, component] of Object.entries(this.ui)) {
                if (component && typeof component.destroy === 'function') {
                    try {
                        await component.destroy();
                        this.logger.debug('SmartOfficeApp', 'destroy', `UI组件 ${name} 已销毁`);
                    } catch (error) {
                        this.logger.error('SmartOfficeApp', 'destroy', `销毁UI组件 ${name} 失败`, { error });
                    }
                }
            }
        }
        
        // 清理事件监听器
        this.removeAllListeners();
        
        // 重置状态
        this.isInitialized = false;
        this.isReady = false;
        
        this.logger.info('SmartOfficeApp', 'destroy', '✅ SmartOffice 应用已销毁');
    }
    // #endregion
}
// #endregion

// #region 工厂函数和便捷方法
/**
 * 创建SmartOffice应用实例
 * @param {Object} config - 应用配置
 * @param {HTMLElement} container - 容器元素
 * @returns {SmartOfficeApp} 应用实例
 */
export function createSmartOfficeApp(config = {}, container = null) {
    return new SmartOfficeApp(config, container);
}

/**
 * 快速启动SmartOffice应用
 * @param {HTMLElement} container - 容器元素
 * @param {Object} config - 应用配置
 * @returns {Promise<SmartOfficeApp>} 应用实例
 */
export async function quickStart(container, config = {}) {
    const app = createSmartOfficeApp(config, container);
    
    // 如果应用已经初始化，直接返回
    if (app.isInitialized) {
        return app;
    }
    
    // 等待应用初始化完成
    return new Promise((resolve, reject) => {
        // 设置超时，防止无限等待
        const timeout = setTimeout(() => {
            reject(new Error('应用初始化超时'));
        }, 30000); // 30秒超时
        
        app.once('app:initialized', () => {
            clearTimeout(timeout);
            resolve(app);
        });
        
        app.once('app:error', (error) => {
            clearTimeout(timeout);
            reject(error);
        });
        
        // 如果自动初始化被禁用，手动初始化
        if (!config.autoInit) {
            app.initialize().catch(reject);
        }
    });
}

// 默认应用实例
export let defaultApp = null;

/**
 * 获取默认应用实例
 * @returns {SmartOfficeApp} 默认应用实例
 */
export function getDefaultApp() {
    return defaultApp;
}

/**
 * 设置默认应用实例
 * @param {SmartOfficeApp} app - 应用实例
 */
export function setDefaultApp(app) {
    defaultApp = app;
}

// 设置全局访问（如果在浏览器环境）
if (typeof window !== 'undefined') {
    window.SmartOffice = {
        App: SmartOfficeApp,
        createApp: createSmartOfficeApp,
        quickStart,
        getDefaultApp,
        setDefaultApp
    };
}
// #endregion