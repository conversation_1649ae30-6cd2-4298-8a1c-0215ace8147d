/**
 * @file 导出控制组件 - 基于统一渲染架构的导出功能控制界面UI组件
 * <AUTHOR> Team
 * @description 
 * 这个文件实现了导出控制组件，提供：
 * - ExportControl 导出控制主界面组件
 * - ExportFormatSelector 导出格式选择器
 * - ExportProgressDialog 导出进度对话框
 * - 导出选项配置和进度追踪功能
 * - 统一渲染架构集成
 */

// #region 导入依赖模块
import { BaseComponent } from './base-component.js';
import { Button, ButtonGroup } from './button.js';
import { Input, Select } from './input.js';
import { Form } from './form.js';
import { UI_EVENTS } from '../../core/events/event-types.js';
import { validateNonEmpty } from '../../core/utils/validation.js';
import { UnifiedRenderEngine } from '../../core/rendering/unified-render-engine.js';
import { DocumentModel } from '../../core/rendering/document-model.js';
import { StyleManager } from '../../core/rendering/style-manager.js';
import { PositionManager } from '../../core/rendering/position-manager.js';
import { HTMLRenderer } from '../../renderers/html-renderer.js';
import { PDFRenderer } from '../../renderers/pdf-renderer.js';
import { PrintRenderer } from '../../renderers/print-renderer.js';
// #endregion

// #region ExportControl 导出控制组件
/**
 * @class ExportControl - 导出控制组件
 * @description 提供文档导出功能的控制界面，包括格式选择、选项配置和导出执行
 */
export class ExportControl extends BaseComponent {
    /**
     * 构造函数 - 初始化导出控制组件
     * @param {Object} config - 导出控制配置
     * @param {Object} config.document - 要导出的文档对象
     * @param {Object} config.template - 模板对象
     * @param {Object} config.exportManager - 导出管理器
     * @param {Array<string>} config.supportedFormats - 支持的导出格式
     * @param {string} config.defaultFormat - 默认导出格式
     * @param {Object} config.defaultOptions - 默认导出选项
     * @param {Function} config.onExportStart - 导出开始回调
     * @param {Function} config.onExportComplete - 导出完成回调
     * @param {Function} config.onExportError - 导出错误回调
     */
    constructor(config = {}) {
        super({
            type: 'export-control',
            name: config.name || 'export-control',
            ...config
        });
        
        // 导出控制属性
        this.exportProps = {
            document: config.document || null,
            template: config.template || null,
            exportManager: config.exportManager || null,
            supportedFormats: config.supportedFormats || ['pdf', 'image', 'print'],
            defaultFormat: config.defaultFormat || 'pdf',
            defaultOptions: config.defaultOptions || {},
            
            // 统一渲染架构配置
            renderEngine: config.renderEngine || null,
            styleManager: config.styleManager || null,
            positionManager: config.positionManager || null,
            
            showFormatSelector: config.showFormatSelector !== false,
            showOptionsPanel: config.showOptionsPanel !== false,
            showPreviewButton: config.showPreviewButton !== false,
            showBatchExport: config.showBatchExport !== false,
            ...config.exportProps
        };
        
        // 当前导出配置
        this.exportConfig = {
            format: this.exportProps.defaultFormat,
            options: { ...this.exportProps.defaultOptions },
            filename: config.filename || 'document',
            quality: config.quality || 'default'
        };
        
        // 事件处理
        this.exportStartHandler = config.onExportStart || null;
        this.exportCompleteHandler = config.onExportComplete || null;
        this.exportErrorHandler = config.onExportError || null;
        this.exportProgressHandler = config.onExportProgress || null;
        
        // 导出状态
        this.exportState = {
            exporting: false,
            lastExportTime: null,
            exportCount: 0,
            hasError: false,
            errorMessage: '',
            ...config.exportState
        };
        
        // 合并到组件状态
        this.state = { ...this.state, ...this.exportState };
        
        // 统一渲染架构组件
        this.renderEngine = null;
        this.styleManager = null;
        this.positionManager = null;
        this.renderers = {
            html: null,
            pdf: null,
            print: null
        };
        this.documentModel = null;
        
        // 子组件
        this.formatSelector = null;
        this.optionsForm = null;
        this.exportButtons = null;
        this.progressDialog = null;
        
        // 验证配置
        this._validateExportConfig();
        
        // 初始化统一渲染架构
        this._initializeRenderingArchitecture();
    }

    /**
     * 验证导出配置 - 验证导出控制特有的配置
     * @private
     */
    _validateExportConfig() {
        // 验证支持的格式
        const validFormats = ['pdf', 'image', 'print'];
        this.exportProps.supportedFormats = this.exportProps.supportedFormats.filter(
            format => validFormats.includes(format)
        );
        
        if (this.exportProps.supportedFormats.length === 0) {
            console.warn('没有有效的导出格式，使用默认格式');
            this.exportProps.supportedFormats = ['pdf'];
        }
        
        // 验证默认格式
        if (!this.exportProps.supportedFormats.includes(this.exportProps.defaultFormat)) {
            console.warn(`默认格式 ${this.exportProps.defaultFormat} 不支持，使用第一个支持的格式`);
            this.exportProps.defaultFormat = this.exportProps.supportedFormats[0];
            this.exportConfig.format = this.exportProps.defaultFormat;
        }
    }
    
    /**
     * 初始化统一渲染架构
     * @private
     */
    async _initializeRenderingArchitecture() {
        try {
            // 初始化样式管理器
            this.styleManager = this.exportProps.styleManager || new StyleManager({
                theme: 'default',
                format: 'multi' // 支持多种格式
            });
            
            // 初始化位置管理器
            this.positionManager = this.exportProps.positionManager || new PositionManager({
                coordinateSystem: 'universal', // 通用坐标系统
                unit: 'px'
            });
            
            // 初始化各种渲染器
            this.renderers.html = new HTMLRenderer({
                styleManager: this.styleManager,
                positionManager: this.positionManager
            });
            
            this.renderers.pdf = new PDFRenderer({
                styleManager: this.styleManager,
                positionManager: this.positionManager
            });
            
            this.renderers.print = new PrintRenderer({
                styleManager: this.styleManager,
                positionManager: this.positionManager
            });
            
            // 初始化统一渲染引擎
            this.renderEngine = this.exportProps.renderEngine || new UnifiedRenderEngine({
                renderers: this.renderers,
                styleManager: this.styleManager,
                positionManager: this.positionManager,
                defaultFormat: this.exportConfig.format
            });
            
            console.log('[ExportControl] 统一渲染架构初始化完成');
            
        } catch (error) {
            console.error('[ExportControl] 统一渲染架构初始化失败:', error);
            // 不抛出错误，允许组件继续使用传统导出方式
        }
    }

    /**
     * 渲染导出控制 - 创建导出控制DOM结构
     * @returns {Promise<HTMLElement>} 导出控制容器元素
     */
    async render() {
        // 创建导出控制容器
        const container = document.createElement('div');
        container.id = this.id;
        
        // 创建标题区域
        const header = this._createHeader();
        container.appendChild(header);
        
        // 创建格式选择器
        if (this.exportProps.showFormatSelector) {
            const formatSection = await this._createFormatSection();
            container.appendChild(formatSection);
        }
        
        // 创建选项面板
        if (this.exportProps.showOptionsPanel) {
            const optionsSection = await this._createOptionsSection();
            container.appendChild(optionsSection);
        }
        
        // 创建导出按钮区域
        const buttonsSection = await this._createButtonsSection();
        container.appendChild(buttonsSection);
        
        // 创建进度对话框
        this.progressDialog = new ExportProgressDialog({
            onCancel: () => this._cancelExport()
        });
        
        await this.progressDialog.mount(container);
        this.addChild('progress-dialog', this.progressDialog);
        
        return container;
    }

    /**
     * 创建标题区域 - 创建导出控制的标题
     * @returns {HTMLElement} 标题区域元素
     * @private
     */
    _createHeader() {
        const header = document.createElement('div');
        header.className = 'export-header';
        
        const title = document.createElement('h3');
        title.className = 'export-title';
        title.textContent = '导出文档';
        header.appendChild(title);
        
        const description = document.createElement('p');
        description.className = 'export-description';
        description.textContent = '选择导出格式和配置选项，然后点击导出按钮生成文档。';
        header.appendChild(description);
        
        return header;
    }

    /**
     * 创建格式选择区域 - 创建导出格式选择器
     * @returns {Promise<HTMLElement>} 格式选择区域
     * @private
     */
    async _createFormatSection() {
        const section = document.createElement('div');
        section.className = 'export-section format-section';
        
        const sectionTitle = document.createElement('h4');
        sectionTitle.className = 'section-title';
        sectionTitle.textContent = '导出格式';
        section.appendChild(sectionTitle);
        
        // 创建格式选择器
        this.formatSelector = new ExportFormatSelector({
            supportedFormats: this.exportProps.supportedFormats,
            selectedFormat: this.exportConfig.format,
            onFormatChange: (format) => this._handleFormatChange(format)
        });
        
        await this.formatSelector.mount(section);
        this.addChild('format-selector', this.formatSelector);
        
        return section;
    }

    /**
     * 创建选项配置区域 - 创建导出选项配置表单
     * @returns {Promise<HTMLElement>} 选项配置区域
     * @private
     */
    async _createOptionsSection() {
        const section = document.createElement('div');
        section.className = 'export-section options-section';
        
        const sectionTitle = document.createElement('h4');
        sectionTitle.className = 'section-title';
        sectionTitle.textContent = '导出选项';
        section.appendChild(sectionTitle);
        
        // 创建选项表单
        this.optionsForm = new Form({
            fields: this._getOptionsFields(),
            layout: 'vertical',
            data: this.exportConfig.options,
            onChange: (fieldName, value) => this._handleOptionChange(fieldName, value)
        });
        
        await this.optionsForm.mount(section);
        this.addChild('options-form', this.optionsForm);
        
        return section;
    }

    /**
     * 创建按钮区域 - 创建导出和控制按钮
     * @returns {Promise<HTMLElement>} 按钮区域
     * @private
     */
    async _createButtonsSection() {
        const section = document.createElement('div');
        section.className = 'export-section buttons-section';
        
        // 创建导出按钮组
        const exportButtonConfigs = [
            {
                name: 'export',
                text: '开始导出',
                icon: 'fas fa-download',
                variant: 'primary',
                size: 'lg',
                onClick: () => this._handleExport()
            }
        ];
        
        // 添加预览按钮
        if (this.exportProps.showPreviewButton) {
            exportButtonConfigs.unshift({
                name: 'preview',
                text: '预览',
                icon: 'fas fa-eye',
                variant: 'secondary',
                size: 'lg',
                onClick: () => this._handlePreview()
            });
        }
        
        // 添加批量导出按钮
        if (this.exportProps.showBatchExport) {
            exportButtonConfigs.push({
                name: 'batch-export',
                text: '批量导出',
                icon: 'fas fa-layer-group',
                variant: 'info',
                size: 'lg',
                onClick: () => this._handleBatchExport()
            });
        }
        
        this.exportButtons = new ButtonGroup({
            orientation: 'horizontal',
            buttons: exportButtonConfigs
        });
        
        await this.exportButtons.mount(section);
        this.addChild('export-buttons', this.exportButtons);
        
        return section;
    }

    /**
     * 获取选项字段配置 - 根据当前格式获取选项字段
     * @returns {Array<Object>} 选项字段配置
     * @private
     */
    _getOptionsFields() {
        const commonFields = [
            {
                name: 'filename',
                type: 'text',
                label: '文件名',
                placeholder: '请输入文件名',
                value: this.exportConfig.filename,
                required: true
            },
            {
                name: 'quality',
                type: 'select',
                label: '导出质量',
                value: this.exportConfig.quality,
                options: [
                    { value: 'low', text: '低质量（文件小）' },
                    { value: 'default', text: '默认质量' },
                    { value: 'high', text: '高质量（文件大）' }
                ]
            }
        ];
        
        // 根据格式添加特定字段
        switch (this.exportConfig.format) {
            case 'pdf':
                return [
                    ...commonFields,
                    {
                        name: 'pageSize',
                        type: 'select',
                        label: '页面大小',
                        value: 'A4',
                        options: [
                            { value: 'A4', text: 'A4' },
                            { value: 'A3', text: 'A3' },
                            { value: 'A5', text: 'A5' },
                            { value: 'Letter', text: 'Letter' },
                            { value: 'Legal', text: 'Legal' }
                        ]
                    },
                    {
                        name: 'orientation',
                        type: 'select',
                        label: '页面方向',
                        value: 'portrait',
                        options: [
                            { value: 'portrait', text: '纵向' },
                            { value: 'landscape', text: '横向' }
                        ]
                    },
                    {
                        name: 'margin',
                        type: 'number',
                        label: '页边距 (mm)',
                        value: 20,
                        min: 0,
                        max: 50
                    }
                ];
                
            case 'image':
                return [
                    ...commonFields,
                    {
                        name: 'format',
                        type: 'select',
                        label: '图片格式',
                        value: 'png',
                        options: [
                            { value: 'png', text: 'PNG' },
                            { value: 'jpeg', text: 'JPEG' },
                            { value: 'webp', text: 'WebP' }
                        ]
                    },
                    {
                        name: 'width',
                        type: 'number',
                        label: '图片宽度 (px)',
                        value: 800,
                        min: 100,
                        max: 4000
                    },
                    {
                        name: 'dpi',
                        type: 'number',
                        label: 'DPI',
                        value: 150,
                        min: 72,
                        max: 300
                    }
                ];
                
            case 'print':
                return [
                    {
                        name: 'pageSize',
                        type: 'select',
                        label: '页面大小',
                        value: 'A4',
                        options: [
                            { value: 'A4', text: 'A4' },
                            { value: 'A3', text: 'A3' },
                            { value: 'A5', text: 'A5' },
                            { value: 'Letter', text: 'Letter' }
                        ]
                    },
                    {
                        name: 'orientation',
                        type: 'select',
                        label: '页面方向',
                        value: 'portrait',
                        options: [
                            { value: 'portrait', text: '纵向' },
                            { value: 'landscape', text: '横向' }
                        ]
                    },
                    {
                        name: 'copies',
                        type: 'number',
                        label: '打印份数',
                        value: 1,
                        min: 1,
                        max: 10
                    }
                ];
                
            default:
                return commonFields;
        }
    }

    /**
     * 应用样式 - 应用导出控制特有的样式类
     * @private
     */
    _applyStyles() {
        if (!this.element) {
            return;
        }
        
        // 构建样式类列表
        const styleClasses = [
            'smartoffice-export-control',
            'export-control',
            `export-format-${this.exportConfig.format}`,
            this.state.exporting ? 'export-exporting' : '',
            this.state.hasError ? 'export-error' : '',
            this.state.enabled ? '' : 'export-disabled',
            this.props.className
        ].filter(Boolean);
        
        // 应用样式类
        this.element.className = styleClasses.join(' ');
        
        // 设置自定义样式
        if (this.props.style) {
            Object.assign(this.element.style, this.props.style);
        }
    }

    /**
     * 处理格式改变 - 处理导出格式改变
     * @param {string} format - 新的导出格式
     * @private
     */
    _handleFormatChange(format) {
        this.exportConfig.format = format;
        this._applyStyles();
        
        // 重新创建选项表单
        if (this.optionsForm) {
            this._updateOptionsForm();
        }
        
        this.emit(UI_EVENTS.EXPORT_FORMAT_SELECTED, {
            component: this,
            format,
            config: { ...this.exportConfig }
        });
    }

    /**
     * 处理选项改变 - 处理导出选项改变
     * @param {string} fieldName - 字段名称
     * @param {*} value - 字段值
     * @private
     */
    _handleOptionChange(fieldName, value) {
        this.exportConfig.options[fieldName] = value;
        
        // 特殊处理文件名
        if (fieldName === 'filename') {
            this.exportConfig.filename = value;
        }
        
        // 特殊处理质量
        if (fieldName === 'quality') {
            this.exportConfig.quality = value;
        }
        
        this.emit(UI_EVENTS.EXPORT_CONFIG_CHANGED, {
            component: this,
            fieldName,
            value,
            config: { ...this.exportConfig }
        });
    }

    /**
     * 处理导出 - 处理导出按钮点击
     * @private
     */
    async _handleExport() {
        if (this.state.exporting || !this.exportProps.document) {
            return;
        }
        
        try {
            // 验证导出配置
            if (!this._validateExportSettings()) {
                return;
            }
            
            // 更新导出状态
            this.update({}, { exporting: true, hasError: false, errorMessage: '' });
            
            // 显示进度对话框
            if (this.progressDialog) {
                this.progressDialog.show();
                this.progressDialog.updateProgress(0, '准备导出...');
            }
            
            // 禁用导出按钮
            this._setButtonsEnabled(false);
            
            // 触发导出开始事件
            this.emit(UI_EVENTS.EXPORT_STARTED, {
                component: this,
                config: { ...this.exportConfig }
            });
            
            // 执行导出开始回调
            if (this.exportStartHandler) {
                this.exportStartHandler({ ...this.exportConfig }, this);
            }
            
            let exportResult;
            
            // 使用统一渲染架构或传统导出管理器
            if (this.renderEngine && this.exportProps.template) {
                // 使用统一渲染架构导出
                exportResult = await this._exportWithUnifiedArchitecture();
            } else if (this.exportProps.exportManager) {
                // 使用传统导出管理器
                exportResult = await this.exportProps.exportManager.export(
                    this.exportProps.document,
                    this.exportConfig.format,
                    {
                        ...this.exportConfig.options,
                        filename: this.exportConfig.filename,
                        quality: this.exportConfig.quality,
                        template: this.exportProps.template,
                        onProgress: (progress, message) => this._handleExportProgress(progress, message)
                    }
                );
            } else {
                throw new Error('未配置导出管理器或统一渲染引擎');
            }
            
            // 导出成功
            this._handleExportSuccess(exportResult);
            
        } catch (error) {
            // 导出失败
            this._handleExportError(error);
        }
    }
    
    /**
     * 使用统一渲染架构导出
     * @returns {Promise<Blob|string>} 导出结果
     * @private
     */
    async _exportWithUnifiedArchitecture() {
        try {
            // 更新进度
            this._handleExportProgress(10, '创建文档模型...');
            
            // 创建文档模型
            this.documentModel = await this._createDocumentModel(
                this.exportProps.document,
                this.exportProps.template,
                {
                    format: this.exportConfig.format,
                    ...this.exportConfig.options,
                    filename: this.exportConfig.filename,
                    quality: this.exportConfig.quality
                }
            );
            
            // 更新进度
            this._handleExportProgress(30, '渲染文档内容...');
            
            // 使用统一渲染引擎渲染
            const renderResult = await this.renderEngine.render(this.documentModel, {
                format: this.exportConfig.format,
                target: 'export',
                onProgress: (progress) => {
                    this._handleExportProgress(30 + progress * 0.6, '渲染中...');
                }
            });
            
            // 更新进度
            this._handleExportProgress(90, '处理导出结果...');
            
            // 处理导出结果
            const exportResult = await this._processExportResult(
                renderResult,
                this.exportConfig.format,
                this.exportConfig.options
            );
            
            // 更新进度
            this._handleExportProgress(100, '导出完成');
            
            return exportResult;
            
        } catch (error) {
            console.error('[ExportControl] 统一渲染架构导出失败:', error);
            throw error;
        }
    }
    
    /**
     * 创建文档模型
     * @param {Object} document - 文档数据
     * @param {Object} template - 模板对象
     * @param {Object} options - 选项
     * @returns {Promise<DocumentModel>} 文档模型
     * @private
     */
    async _createDocumentModel(document, template, options) {
        try {
            // 创建文档模型实例
            const documentModel = new DocumentModel({
                id: `export-${Date.now()}`,
                title: document.title || options.filename || '导出文档',
                format: options.format || 'pdf',
                theme: options.theme || 'default'
            });
            
            // 设置模板
            await documentModel.setTemplate(template);
            
            // 设置数据
            await documentModel.setData(document);
            
            // 设置选项
            await documentModel.setOptions(options);
            
            // 解析模板
            await documentModel.parseTemplate();
            
            // 绑定数据
            await documentModel.bindData();
            
            console.log('[ExportControl] 文档模型创建完成');
            return documentModel;
            
        } catch (error) {
            console.error('[ExportControl] 文档模型创建失败:', error);
            throw error;
        }
    }
    
    /**
     * 处理导出结果
     * @param {Object} renderResult - 渲染结果
     * @param {string} format - 导出格式
     * @param {Object} options - 导出选项
     * @returns {Promise<Blob|string>} 处理后的导出结果
     * @private
     */
    async _processExportResult(renderResult, format, options) {
        try {
            let exportResult = renderResult;
            
            // 根据格式处理结果
            switch (format) {
                case 'html':
                    exportResult = renderResult.content || renderResult;
                    if (options.standalone) {
                        exportResult = this._wrapHTMLStandalone(exportResult, options);
                    }
                    break;
                    
                case 'pdf':
                    // PDF渲染器已经返回Blob
                    exportResult = renderResult.blob || renderResult;
                    break;
                    
                case 'image':
                    // 图片渲染器已经返回Blob
                    exportResult = renderResult.blob || renderResult;
                    break;
                    
                case 'print':
                    // 打印渲染器返回打印就绪的HTML
                    exportResult = renderResult.content || renderResult;
                    if (options.autoTrigger !== false) {
                        this._triggerPrint(exportResult);
                    }
                    break;
                    
                default:
                    exportResult = renderResult.content || renderResult;
            }
            
            return exportResult;
            
        } catch (error) {
            console.error('[ExportControl] 导出结果处理失败:', error);
            throw error;
        }
    }
    
    /**
     * 包装HTML为独立文档
     * @param {string} htmlContent - HTML内容
     * @param {Object} options - 选项
     * @returns {string} 独立HTML文档
     * @private
     */
    _wrapHTMLStandalone(htmlContent, options) {
        const title = options.title || options.filename || '导出文档';
        const charset = options.charset || 'UTF-8';
        
        return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="${charset}">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    ${options.includeCSS ? this._getExportCSS() : ''}
</head>
<body>
    ${htmlContent}
    ${options.includeJS ? this._getExportJS() : ''}
</body>
</html>`;
    }
    
    /**
     * 获取导出用CSS
     * @returns {string} CSS样式
     * @private
     */
    _getExportCSS() {
        return this.styleManager ? this.styleManager.generateCSS() : '';
    }
    
    /**
     * 获取导出用JavaScript
     * @returns {string} JavaScript代码
     * @private
     */
    _getExportJS() {
        return ''; // 根据需要添加必要的JavaScript
    }
    
    /**
     * 触发打印
     * @param {string} content - 打印内容
     * @private
     */
    _triggerPrint(content) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(content);
        printWindow.document.close();
        printWindow.print();
    }

    /**
     * 处理预览 - 处理预览按钮点击
     * @private
     */
    _handlePreview() {
        this.emit(UI_EVENTS.EXPORT_PREVIEW_REQUESTED, {
            component: this,
            config: { ...this.exportConfig }
        });
    }

    /**
     * 处理批量导出 - 处理批量导出按钮点击
     * @private
     */
    _handleBatchExport() {
        this.emit(UI_EVENTS.EXPORT_BATCH_REQUESTED, {
            component: this,
            config: { ...this.exportConfig }
        });
    }

    /**
     * 处理导出进度 - 处理导出进度更新
     * @param {number} progress - 进度百分比 (0-100)
     * @param {string} message - 进度消息
     * @private
     */
    _handleExportProgress(progress, message) {
        // 更新进度对话框
        if (this.progressDialog) {
            this.progressDialog.updateProgress(progress, message);
        }
        
        // 触发进度事件
        this.emit(UI_EVENTS.EXPORT_PROGRESS, {
            component: this,
            progress,
            message
        });
        
        // 执行进度回调
        if (this.exportProgressHandler) {
            this.exportProgressHandler(progress, message, this);
        }
    }

    /**
     * 处理导出成功 - 处理导出完成
     * @param {Object} result - 导出结果
     * @private
     */
    _handleExportSuccess(result) {
        // 更新导出状态
        this.update({}, {
            exporting: false,
            lastExportTime: new Date(),
            exportCount: this.state.exportCount + 1
        });
        
        // 隐藏进度对话框
        if (this.progressDialog) {
            this.progressDialog.hide();
        }
        
        // 启用导出按钮
        this._setButtonsEnabled(true);
        
        // 触发导出完成事件
        this.emit(UI_EVENTS.EXPORT_COMPLETED, {
            component: this,
            result,
            config: { ...this.exportConfig },
            documentModel: this.documentModel,
            renderEngine: this.renderEngine
        });
        
        // 执行完成回调
        if (this.exportCompleteHandler) {
            this.exportCompleteHandler(result, { ...this.exportConfig }, this);
        }
    }

    /**
     * 处理导出错误 - 处理导出失败
     * @param {Error} error - 错误对象
     * @private
     */
    _handleExportError(error) {
        const errorMessage = error.message || '导出失败';
        
        // 更新错误状态
        this.update({}, {
            exporting: false,
            hasError: true,
            errorMessage
        });
        
        // 更新进度对话框显示错误
        if (this.progressDialog) {
            this.progressDialog.showError(errorMessage);
        }
        
        // 启用导出按钮
        this._setButtonsEnabled(true);
        
        // 触发导出错误事件
        this.emit(UI_EVENTS.EXPORT_FAILED, {
            component: this,
            error: errorMessage,
            originalError: error,
            config: { ...this.exportConfig }
        });
        
        // 执行错误回调
        if (this.exportErrorHandler) {
            this.exportErrorHandler(error, { ...this.exportConfig }, this);
        }
        
        console.error('导出失败:', error);
    }

    /**
     * 取消导出 - 取消当前导出操作
     * @private
     */
    _cancelExport() {
        // 更新状态
        this.update({}, { exporting: false });
        
        // 隐藏进度对话框
        if (this.progressDialog) {
            this.progressDialog.hide();
        }
        
        // 启用导出按钮
        this._setButtonsEnabled(true);
        
        // 触发取消事件
        this.emit(UI_EVENTS.EXPORT_CANCELLED, {
            component: this,
            config: { ...this.exportConfig }
        });
    }

    /**
     * 验证导出设置 - 验证当前导出配置
     * @returns {boolean} 是否有效
     * @private
     */
    _validateExportSettings() {
        // 验证文件名
        if (!this.exportConfig.filename || this.exportConfig.filename.trim() === '') {
            this._showValidationError('请输入文件名');
            return false;
        }
        
        // 验证文档和模板
        if (!this.exportProps.document) {
            this._showValidationError('没有可导出的文档');
            return false;
        }
        
        if (!this.exportProps.template) {
            this._showValidationError('没有选择文档模板');
            return false;
        }
        
        // 验证导出管理器
        if (!this.exportProps.exportManager) {
            this._showValidationError('导出管理器未初始化');
            return false;
        }
        
        return true;
    }

    /**
     * 显示验证错误 - 显示验证错误消息
     * @param {string} message - 错误消息
     * @private
     */
    _showValidationError(message) {
        // 更新错误状态
        this.update({}, { hasError: true, errorMessage: message });
        
        // 显示错误提示
        this.emit(UI_EVENTS.COMPONENT_ERROR, {
            component: this,
            error: message,
            phase: 'validation'
        });
    }

    /**
     * 更新选项表单 - 根据当前格式更新选项表单
     * @private
     */
    async _updateOptionsForm() {
        if (!this.optionsForm) {
            return;
        }
        
        // 获取新的字段配置
        const newFields = this._getOptionsFields();
        
        // 重新创建表单
        await this.optionsForm.unmount();
        
        this.optionsForm = new Form({
            fields: newFields,
            layout: 'vertical',
            data: this.exportConfig.options,
            onChange: (fieldName, value) => this._handleOptionChange(fieldName, value)
        });
        
        const optionsSection = this.element.querySelector('.options-section');
        if (optionsSection) {
            await this.optionsForm.mount(optionsSection);
        }
        
        this.addChild('options-form', this.optionsForm);
    }

    /**
     * 设置按钮启用状态 - 设置导出按钮的启用状态
     * @param {boolean} enabled - 是否启用
     * @private
     */
    _setButtonsEnabled(enabled) {
        if (this.exportButtons) {
            if (enabled) {
                this.exportButtons.enable();
            } else {
                this.exportButtons.disable();
            }
        }
    }

    /**
     * 设置文档 - 设置要导出的文档
     * @param {Object} document - 文档对象
     */
    setDocument(document) {
        this.exportProps.document = document;
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            property: 'document',
            value: document
        });
    }

    /**
     * 设置模板 - 设置导出使用的模板
     * @param {Object} template - 模板对象
     */
    setTemplate(template) {
        this.exportProps.template = template;
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            property: 'template',
            value: template
        });
    }

    /**
     * 设置导出管理器 - 设置导出管理器
     * @param {Object} exportManager - 导出管理器对象
     */
    setExportManager(exportManager) {
        this.exportProps.exportManager = exportManager;
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            property: 'exportManager',
            value: exportManager
        });
    }

    /**
     * 获取导出配置 - 获取当前导出配置
     * @returns {Object} 导出配置
     */
    getExportConfig() {
        return { ...this.exportConfig };
    }

    /**
     * 设置导出配置 - 设置导出配置
     * @param {Object} config - 导出配置
     */
    setExportConfig(config) {
        this.exportConfig = { ...this.exportConfig, ...config };
        
        // 更新格式选择器
        if (this.formatSelector && config.format) {
            this.formatSelector.setSelectedFormat(config.format);
        }
        
        // 更新选项表单
        if (this.optionsForm && config.options) {
            this.optionsForm.setFormData(config.options);
        }
        
        this.emit(UI_EVENTS.EXPORT_CONFIG_CHANGED, {
            component: this,
            config: { ...this.exportConfig }
        });
    }

    /**
     * 获取导出控制信息 - 获取导出控制的详细信息
     * @returns {Object} 导出控制信息
     */
    getExportControlInfo() {
        return {
            ...this._getComponentInfo(),
            exportProps: { ...this.exportProps },
            exportConfig: { ...this.exportConfig },
            exportState: {
                exporting: this.state.exporting,
                lastExportTime: this.state.lastExportTime,
                exportCount: this.state.exportCount,
                hasError: this.state.hasError,
                errorMessage: this.state.errorMessage
            }
        };
    }
}
// #endregion

// #region ExportFormatSelector 导出格式选择器
/**
 * @class ExportFormatSelector - 导出格式选择器组件
 * @description 提供导出格式选择的UI组件
 */
export class ExportFormatSelector extends BaseComponent {
    /**
     * 构造函数 - 初始化格式选择器
     * @param {Object} config - 格式选择器配置
     * @param {Array<string>} config.supportedFormats - 支持的格式列表
     * @param {string} config.selectedFormat - 当前选中的格式
     * @param {Function} config.onFormatChange - 格式改变回调
     */
    constructor(config = {}) {
        super({
            type: 'export-format-selector',
            name: config.name || 'export-format-selector',
            ...config
        });
        
        // 格式选择器属性
        this.selectorProps = {
            supportedFormats: config.supportedFormats || ['pdf', 'image', 'print'],
            selectedFormat: config.selectedFormat || 'pdf',
            showIcons: config.showIcons !== false,
            showDescriptions: config.showDescriptions !== false,
            ...config.selectorProps
        };
        
        // 事件处理
        this.formatChangeHandler = config.onFormatChange || null;
        
        // 格式信息
        this.formatInfo = {
            'pdf': {
                icon: 'fas fa-file-pdf',
                label: 'PDF文档',
                description: '生成便携式文档格式，适合打印和分享'
            },
            'image': {
                icon: 'fas fa-image',
                label: '图片文件',
                description: '生成图片格式，适合网页展示和插图使用'
            },
            'print': {
                icon: 'fas fa-print',
                label: '打印文档',
                description: '优化打印输出，直接发送到打印机'
            }
        };
    }

    /**
     * 渲染格式选择器 - 创建格式选择器DOM结构
     * @returns {Promise<HTMLElement>} 格式选择器元素
     */
    async render() {
        // 创建格式选择器容器
        const container = document.createElement('div');
        container.id = this.id;
        
        // 创建格式选项
        for (const format of this.selectorProps.supportedFormats) {
            const formatOption = this._createFormatOption(format);
            container.appendChild(formatOption);
        }
        
        return container;
    }

    /**
     * 创建格式选项 - 创建单个格式选项
     * @param {string} format - 格式名称
     * @returns {HTMLElement} 格式选项元素
     * @private
     */
    _createFormatOption(format) {
        const option = document.createElement('div');
        option.className = 'format-option';
        option.dataset.format = format;
        
        if (format === this.selectorProps.selectedFormat) {
            option.classList.add('selected');
        }
        
        const formatInfo = this.formatInfo[format];
        if (!formatInfo) {
            return option;
        }
        
        // 创建格式图标
        if (this.selectorProps.showIcons) {
            const icon = document.createElement('i');
            icon.className = `format-icon ${formatInfo.icon}`;
            option.appendChild(icon);
        }
        
        // 创建格式信息
        const info = document.createElement('div');
        info.className = 'format-info';
        
        const label = document.createElement('div');
        label.className = 'format-label';
        label.textContent = formatInfo.label;
        info.appendChild(label);
        
        if (this.selectorProps.showDescriptions) {
            const description = document.createElement('div');
            description.className = 'format-description';
            description.textContent = formatInfo.description;
            info.appendChild(description);
        }
        
        option.appendChild(info);
        
        return option;
    }

    /**
     * 应用样式 - 应用格式选择器特有的样式类
     * @private
     */
    _applyStyles() {
        if (!this.element) {
            return;
        }
        
        // 构建样式类列表
        const styleClasses = [
            'smartoffice-export-format-selector',
            'export-format-selector',
            this.state.enabled ? '' : 'selector-disabled',
            this.props.className
        ].filter(Boolean);
        
        // 应用样式类
        this.element.className = styleClasses.join(' ');
        
        // 设置自定义样式
        if (this.props.style) {
            Object.assign(this.element.style, this.props.style);
        }
    }

    /**
     * 绑定组件特定事件 - 绑定格式选择器特有的事件
     * @protected
     */
    _bindComponentEvents() {
        if (!this.element) {
            return;
        }
        
        // 绑定格式选项点击事件
        this._addEventListener(this.element, 'click', (event) => {
            this._handleFormatClick(event);
        });
    }

    /**
     * 处理格式点击 - 处理格式选项点击
     * @param {Event} event - 点击事件
     * @private
     */
    _handleFormatClick(event) {
        const formatOption = event.target.closest('.format-option');
        if (!formatOption) {
            return;
        }
        
        const format = formatOption.dataset.format;
        if (format && format !== this.selectorProps.selectedFormat) {
            this.setSelectedFormat(format);
            
            if (this.formatChangeHandler) {
                this.formatChangeHandler(format);
            }
        }
    }

    /**
     * 设置选中格式 - 设置当前选中的格式
     * @param {string} format - 格式名称
     */
    setSelectedFormat(format) {
        if (!this.selectorProps.supportedFormats.includes(format)) {
            console.warn(`不支持的格式: ${format}`);
            return;
        }
        
        // 更新选中状态
        const options = this.element?.querySelectorAll('.format-option');
        if (options) {
            options.forEach(option => {
                option.classList.remove('selected');
                if (option.dataset.format === format) {
                    option.classList.add('selected');
                }
            });
        }
        
        this.selectorProps.selectedFormat = format;
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            property: 'selectedFormat',
            value: format
        });
    }

    /**
     * 获取选中格式 - 获取当前选中的格式
     * @returns {string} 选中的格式
     */
    getSelectedFormat() {
        return this.selectorProps.selectedFormat;
    }
}
// #endregion

// #region ExportProgressDialog 导出进度对话框
/**
 * @class ExportProgressDialog - 导出进度对话框组件
 * @description 显示导出进度的对话框组件
 */
export class ExportProgressDialog extends BaseComponent {
    /**
     * 构造函数 - 初始化进度对话框
     * @param {Object} config - 进度对话框配置
     * @param {Function} config.onCancel - 取消回调
     */
    constructor(config = {}) {
        super({
            type: 'export-progress-dialog',
            name: config.name || 'export-progress-dialog',
            ...config
        });
        
        // 对话框属性
        this.dialogProps = {
            title: config.title || '导出进度',
            cancellable: config.cancellable !== false,
            ...config.dialogProps
        };
        
        // 事件处理
        this.cancelHandler = config.onCancel || null;
        
        // 进度状态
        this.progressState = {
            visible: false,
            progress: 0,
            message: '',
            hasError: false,
            errorMessage: '',
            ...config.progressState
        };
        
        // 合并到组件状态
        this.state = { ...this.state, ...this.progressState };
    }

    /**
     * 渲染进度对话框 - 创建进度对话框DOM结构
     * @returns {Promise<HTMLElement>} 进度对话框元素
     */
    async render() {
        // 创建对话框遮罩
        const overlay = document.createElement('div');
        overlay.className = 'progress-overlay';
        overlay.style.display = 'none';
        
        // 创建对话框容器
        const dialog = document.createElement('div');
        dialog.className = 'progress-dialog';
        dialog.id = this.id;
        
        // 创建对话框标题
        const header = this._createDialogHeader();
        dialog.appendChild(header);
        
        // 创建进度内容
        const content = this._createDialogContent();
        dialog.appendChild(content);
        
        // 创建对话框底部
        const footer = this._createDialogFooter();
        dialog.appendChild(footer);
        
        overlay.appendChild(dialog);
        
        return overlay;
    }

    /**
     * 创建对话框标题 - 创建对话框标题区域
     * @returns {HTMLElement} 标题区域元素
     * @private
     */
    _createDialogHeader() {
        const header = document.createElement('div');
        header.className = 'dialog-header';
        
        const title = document.createElement('h3');
        title.className = 'dialog-title';
        title.textContent = this.dialogProps.title;
        header.appendChild(title);
        
        return header;
    }

    /**
     * 创建对话框内容 - 创建对话框主要内容
     * @returns {HTMLElement} 内容区域元素
     * @private
     */
    _createDialogContent() {
        const content = document.createElement('div');
        content.className = 'dialog-content';
        
        // 创建进度条
        const progressContainer = document.createElement('div');
        progressContainer.className = 'progress-container';
        
        const progressBar = document.createElement('div');
        progressBar.className = 'progress-bar';
        
        const progressFill = document.createElement('div');
        progressFill.className = 'progress-fill';
        progressFill.style.width = '0%';
        
        progressBar.appendChild(progressFill);
        progressContainer.appendChild(progressBar);
        
        // 创建进度文本
        const progressText = document.createElement('div');
        progressText.className = 'progress-text';
        progressText.textContent = '准备中...';
        
        // 创建错误消息区域
        const errorArea = document.createElement('div');
        errorArea.className = 'error-area';
        errorArea.style.display = 'none';
        
        const errorIcon = document.createElement('i');
        errorIcon.className = 'error-icon fas fa-exclamation-triangle';
        errorArea.appendChild(errorIcon);
        
        const errorMessage = document.createElement('div');
        errorMessage.className = 'error-message';
        errorArea.appendChild(errorMessage);
        
        content.appendChild(progressContainer);
        content.appendChild(progressText);
        content.appendChild(errorArea);
        
        return content;
    }

    /**
     * 创建对话框底部 - 创建对话框按钮区域
     * @returns {HTMLElement} 底部区域元素
     * @private
     */
    _createDialogFooter() {
        const footer = document.createElement('div');
        footer.className = 'dialog-footer';
        
        if (this.dialogProps.cancellable) {
            const cancelButton = new Button({
                text: '取消',
                variant: 'secondary',
                onClick: () => this._handleCancel()
            });
            
            cancelButton.mount(footer);
            this.addChild('cancel-button', cancelButton);
        }
        
        return footer;
    }

    /**
     * 应用样式 - 应用进度对话框特有的样式类
     * @private
     */
    _applyStyles() {
        const overlay = this.element;
        if (!overlay) {
            return;
        }
        
        // 构建样式类列表
        const styleClasses = [
            'smartoffice-export-progress',
            'export-progress-overlay',
            this.state.visible ? 'visible' : 'hidden',
            this.state.hasError ? 'has-error' : '',
            this.props.className
        ].filter(Boolean);
        
        // 应用样式类
        overlay.className = styleClasses.join(' ');
        
        // 设置自定义样式
        if (this.props.style) {
            Object.assign(overlay.style, this.props.style);
        }
    }

    /**
     * 处理取消 - 处理取消按钮点击
     * @private
     */
    _handleCancel() {
        this.hide();
        
        if (this.cancelHandler) {
            this.cancelHandler();
        }
    }

    /**
     * 显示对话框 - 显示进度对话框
     */
    show() {
        this.update({}, { visible: true, hasError: false });
        
        if (this.element) {
            this.element.style.display = 'flex';
        }
    }

    /**
     * 隐藏对话框 - 隐藏进度对话框
     */
    hide() {
        this.update({}, { visible: false });
        
        if (this.element) {
            this.element.style.display = 'none';
        }
    }

    /**
     * 更新进度 - 更新进度显示
     * @param {number} progress - 进度百分比 (0-100)
     * @param {string} message - 进度消息
     */
    updateProgress(progress, message) {
        this.update({}, { progress, message, hasError: false });
        
        // 更新进度条
        const progressFill = this.element?.querySelector('.progress-fill');
        if (progressFill) {
            progressFill.style.width = `${Math.max(0, Math.min(100, progress))}%`;
        }
        
        // 更新进度文本
        const progressText = this.element?.querySelector('.progress-text');
        if (progressText) {
            progressText.textContent = message || `${Math.round(progress)}%`;
        }
        
        // 隐藏错误区域
        const errorArea = this.element?.querySelector('.error-area');
        if (errorArea) {
            errorArea.style.display = 'none';
        }
    }

    /**
     * 显示错误 - 显示错误消息
     * @param {string} errorMessage - 错误消息
     */
    showError(errorMessage) {
        this.update({}, { hasError: true, errorMessage });
        
        // 显示错误区域
        const errorArea = this.element?.querySelector('.error-area');
        const errorMsg = this.element?.querySelector('.error-message');
        
        if (errorArea && errorMsg) {
            errorMsg.textContent = errorMessage;
            errorArea.style.display = 'flex';
        }
        
        // 隐藏进度条
        const progressContainer = this.element?.querySelector('.progress-container');
        const progressText = this.element?.querySelector('.progress-text');
        
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }
        
        if (progressText) {
            progressText.style.display = 'none';
        }
    }
}
// #endregion

// #region 工厂函数
/**
 * 创建导出控制 - 工厂函数，创建导出控制实例
 * @param {Object} config - 导出控制配置
 * @returns {ExportControl} 导出控制实例
 */
export function createExportControl(config = {}) {
    return new ExportControl(config);
}

/**
 * 创建格式选择器 - 工厂函数，创建格式选择器实例
 * @param {Object} config - 格式选择器配置
 * @returns {ExportFormatSelector} 格式选择器实例
 */
export function createExportFormatSelector(config = {}) {
    return new ExportFormatSelector(config);
}

/**
 * 创建进度对话框 - 工厂函数，创建进度对话框实例
 * @param {Object} config - 进度对话框配置
 * @returns {ExportProgressDialog} 进度对话框实例
 */
export function createExportProgressDialog(config = {}) {
    return new ExportProgressDialog(config);
}
// #endregion