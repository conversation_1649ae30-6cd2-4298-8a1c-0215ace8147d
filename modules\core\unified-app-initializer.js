/**
 * @file 统一应用初始化器 - 重构后的应用初始化接口
 * <AUTHOR> Team
 * @description 
 * 重构后的统一应用初始化器，使用新的UnifiedApp类
 * 整合js/app-initializer.js、js/services/app-initializer.js、js/main.js、src/app/smart-office-app.js的功能
 * 提供统一的应用初始化和管理接口
 * 
 * 重构说明：
 * - 使用新的UnifiedApp类作为核心
 * - 保持向后兼容的API接口
 * - 消除重复的初始化代码
 * - 提供便捷的工厂函数
 */

import { getLogger } from '../../js/utils/logger.js';
import { 
    UnifiedApp, 
    createUnifiedApp, 
    quickStartUnified, 
    getUnifiedApp, 
    setUnifiedApp, 
    resetUnifiedApp 
} from './unified-app.js';

// #region 统一应用初始化器类
/**
 * @class UnifiedAppInitializer - 统一应用初始化器（向后兼容包装器）
 * @description 为了向后兼容而保留的初始化器类，内部使用新的UnifiedApp
 * @deprecated 建议直接使用UnifiedApp类或工厂函数
 */
export class UnifiedAppInitializer {
    /**
     * 构造函数
     * @param {Object} config - 应用配置
     * @param {HTMLElement} container - 容器元素
     */
    constructor(config = {}, container = null) {
        this.logger = getLogger();
        this.logger.warn('UnifiedAppInitializer', 'constructor', 
            'UnifiedAppInitializer已废弃，建议使用UnifiedApp类或工厂函数');

        // 创建新的UnifiedApp实例
        this.app = createUnifiedApp(container, config);

        // 代理属性以保持向后兼容
        this.name = this.app.name;
        this.version = this.app.version;
        this.description = this.app.description;
        this.container = this.app.container;

        // 代理状态属性
        Object.defineProperty(this, 'isInitialized', {
            get: () => this.app.isInitialized
        });

        Object.defineProperty(this, 'isReady', {
            get: () => this.app.isReady
        });

        // 代理管理器
        Object.defineProperty(this, 'managers', {
            get: () => this.app.managers
        });

        // 代理统计信息
        Object.defineProperty(this, 'stats', {
            get: () => this.app.stats
        });

        // 代理配置
        Object.defineProperty(this, 'config', {
            get: () => this.app.config
        });

        this.logger.info('UnifiedAppInitializer', 'constructor', '🚀 统一应用初始化器创建（兼容模式）');
    }
    
    /**
     * 初始化应用 - 代理到UnifiedApp
     * @returns {Promise<Object>} 初始化结果
     */
    async initialize() {
        this.logger.info('UnifiedAppInitializer', 'initialize', '代理初始化到UnifiedApp');
        return await this.app.initialize();
    }

    /**
     * 获取应用信息 - 代理到UnifiedApp
     * @returns {Object} 应用信息
     */
    getAppInfo() {
        return this.app.getAppInfo();
    }

    /**
     * 获取管理器 - 代理到UnifiedApp
     * @param {string} managerName - 管理器名称
     * @returns {Object|null} 管理器实例
     */
    getManager(managerName) {
        return this.app.getManager(managerName);
    }

    /**
     * 检查应用是否就绪 - 代理到UnifiedApp
     * @returns {boolean} 是否就绪
     */
    isAppReady() {
        return this.app.isAppReady();
    }

    /**
     * 销毁应用 - 代理到UnifiedApp
     * @returns {Promise<void>}
     */
    async destroy() {
        return await this.app.destroy();
    }
}
// #endregion

// #region 工厂函数和便捷方法
/**
 * 创建统一应用初始化器（向后兼容）
 * @param {Object} config - 应用配置
 * @param {HTMLElement} container - 容器元素
 * @returns {UnifiedAppInitializer} 初始化器实例
 * @deprecated 建议使用createUnifiedApp
 */
export function createUnifiedAppInitializer(config = {}, container = null) {
    return new UnifiedAppInitializer(config, container);
}

/**
 * 快速启动统一应用（向后兼容）
 * @param {Object} options - 启动选项
 * @returns {Promise<UnifiedAppInitializer>} 初始化完成的应用实例
 * @deprecated 建议使用quickStartUnified
 */
export async function quickStartUnifiedApp(options = {}) {
    const {
        container = null,
        config = {},
        autoInit = true
    } = options;
    
    const initializer = createUnifiedAppInitializer(config, container);
    
    if (autoInit && !initializer.isAppReady()) {
        await initializer.initialize();
    }
    
    return initializer;
}
// #endregion

// #region 导出
export {
    // 新的统一应用类和工厂函数
    UnifiedApp,
    createUnifiedApp,
    quickStartUnified,
    getUnifiedApp,
    setUnifiedApp,
    resetUnifiedApp,
    
    // 向后兼容的初始化器（已废弃）
    UnifiedAppInitializer
};

// 默认导出新的UnifiedApp
export default UnifiedApp;
// #endregion
