# SmartOffice 2.0 项目规则

> 🎯 **核心目标**：确保代码质量，防范开发错误，维护架构一致性

## 📋 强制性规则

每次执行代码更新，必须在完成新的代码之后，审视旧代码的关联性 并 删除无用代码。

### 1. 架构规则

#### 1.1 事件驱动通信
- **规则**：所有组件间通信必须通过 EventEmitter 进行
- **禁止**：直接调用其他组件的方法或访问内部状态
- **示例**：
  ```javascript
  // ✅ 正确
  this.emit('document:changed', data);
  
  // ❌ 错误
  otherComponent.updateData(data);
  ```

#### 1.2 模块依赖
- **规则**：严禁循环依赖
- **要求**：所有依赖在文件头部显式声明
- **检查**：使用 `madge --circular` 检测

#### 1.3 状态管理
- **规则**：全局状态只能通过 GlobalStateManager 访问和修改
- **禁止**：直接修改全局变量或组件内部状态
- **示例**：
  ```javascript
  // ✅ 正确
  this.stateManager.set('document.type', 'receipt');
  
  // ❌ 错误
  window.globalState.document.type = 'receipt';
  ```

### 2. 组件规则

#### 2.1 组件继承
- **规则**：所有UI组件必须继承 BaseComponent
- **规则**：所有模板必须继承 BaseTemplate
- **规则**：所有文档模型必须继承 BaseDocument

#### 2.2 组件生命周期
- **规则**：组件销毁时必须清理所有事件监听器
- **规则**：DOM操作前必须检查组件挂载状态
- **示例**：
  ```javascript
  destroy() {
      this._listeners.forEach(unsubscribe => unsubscribe());
      super.destroy();
  }
  ```

#### 2.3 异步操作
- **规则**：避免竞态条件，使用队列或锁机制
- **规则**：所有异步操作必须有错误处理

### 3. 代码质量规则

#### 3.1 命名规范
- **类名**：PascalCase（如 `DocumentPreview`）
- **函数名**：camelCase（如 `renderTemplate`）
- **变量名**：camelCase（如 `documentData`）
- **常量名**：UPPER_SNAKE_CASE（如 `UI_EVENTS`）
- **文件名**：kebab-case（如 `base-component.js`）
- **事件名**：namespace:action（如 `ui:form:changed`）

#### 3.2 函数规范
- **规则**：函数长度不超过50行
- **规则**：函数职责单一
- **规则**：所有函数必须有 @function 注释

#### 3.3 注释要求
- **规则**：所有文件必须有 @file 头注释
- **规则**：所有类必须有 @class 注释
- **规则**：所有公共函数必须有完整的 JSDoc 注释
- **格式**：
  ```javascript
  /**
   * @function functionName - 函数简短描述
   * @param {Type} param - 参数描述
   * @returns {Type} 返回值描述
   */
  ```

### 4. 错误处理规则

#### 4.1 异常处理
- **规则**：所有可能失败的操作必须有 try-catch
- **规则**：错误必须记录到日志系统
- **规则**：错误必须通过事件系统通知

#### 4.2 数据验证
- **规则**：所有外部输入必须验证
- **规则**：模板渲染前必须验证数据
- **规则**：状态更新前必须验证数据格式

### 5. 运行日志规则

#### 5.1 日志级别要求
- **要求**：错误和异常必须使用 ERROR 级别记录
- **要求**：关键业务流程必须使用 INFO 级别记录
- **要求**：调试信息使用 DEBUG 级别记录

#### 5.2 日志结构规范
- **规则**：日志必须包含时间戳和上下文信息
- **规则**：错误日志必须记录完整的堆栈信息
- **规则**：敏感信息必须脱敏后记录
## 📝 开发流程规则

### 6. 开发前检查
- **必须**：阅读 Memory Bank 相关文档
- **必须**：检查组件接口文档
- **必须**：确认修改不破坏现有调用关系
- **必须**：准备测试用例

### 7. 代码提交规则
- **必须**：所有测试通过
- **必须**：代码符合规范检查
- **必须**：更新相关文档
- **必须**：记录变更日志

### 8. 文档维护规则
- **规则**：代码修改时同步更新注释
- **规则**：新增组件时更新 Memory Bank
- **规则**：架构变更时更新系统文档
- **规则**：定期审查文档准确性

## ⚠️ 禁止行为

### 9. 严格禁止
- ❌ 绕过事件系统直接调用组件方法
- ❌ 直接修改全局状态变量
- ❌ 在组件未挂载时操作DOM
- ❌ 硬编码配置值
- ❌ 忽略错误处理
- ❌ 创建循环依赖
- ❌ 使用重复或模糊的命名
- ❌ 跳过数据验证
- ❌ 不清理事件监听器
- ❌ 提交未测试的代码

## 🔍 检查清单

### 10. 代码审查清单
- [ ] 架构一致性检查
- [ ] 命名规范检查
- [ ] 注释完整性检查
- [ ] 错误处理检查
- [ ] 测试覆盖检查
- [ ] 文档更新检查
- [ ] 性能影响评估
- [ ] 兼容性检查

### 11. 提交前清单
- [ ] 所有测试通过
- [ ] 代码规范检查通过
- [ ] Memory Bank 已更新
- [ ] README 已更新
- [ ] 变更日志已记录
- [ ] 相关文档已同步

## 📞 违规处理

### 12. 发现违规时
1. **立即停止**：停止当前开发工作
2. **分析影响**：评估违规代码的影响范围
3. **制定修复计划**：按照规则要求重构代码
4. **更新文档**：同步更新相关文档
5. **验证修复**：确保修复后符合所有规则

### 13. 规则更新
- **触发条件**：发现新的常见错误模式
- **更新流程**：提交PR → AI审核 → 自动更新规则文件 → 自动通知开发者
- **生效时间**：规则合并到主分支后自动生效

---

> 📝 **重要提醒**：这些规则是强制性的，所有开发人员和AI IDE必须严格遵守。违反规则的代码不得提交到主分支。AI IDE将自动检查并阻止不符合规则的代码提交。

> 🎯 **目标**：通过严格执行这些规则，确保 SmartOffice 2.0 项目的代码质量、架构一致性和长期可维护性。