/**
 * @fileoverview 组件管理器 - 统一管理组件的创建、销毁和生命周期
 * <AUTHOR> Office Team
 * @version 1.0.0
 */

// 导入依赖模块
import { EventEmitter } from '../../core/utils/event-emitter.js';
import { UI_EVENTS } from '../../core/utils/event-types.js';
import { getLogger } from '../../core/utils/logger.js';
import { defaultComponentRegistry } from './component-registry.js';
import { BaseComponent } from './base-component.js';

// #region ComponentManager 组件管理器
/**
 * @class ComponentManager - 组件管理器
 * @description 统一管理组件的创建、销毁和生命周期，提供主题管理和批量操作功能
 */
export class ComponentManager extends EventEmitter {
    /**
     * 构造函数 - 初始化组件管理器
     * @param {Object} config - 管理器配置
     */
    constructor(config = {}) {
        super();
        
        // 初始化日志记录器
        this.logger = getLogger();
        this.logger.startPerformanceMark('component_manager_init', 'ComponentManager', 'constructor');
        this.logger.info('ComponentManager', 'constructor', '开始初始化组件管理器', {
            configKeys: Object.keys(config)
        });
        
        // 配置管理器
        this.config = {
            container: null,
            theme: 'default',
            autoCleanup: true,
            enableGlobalAccess: true,
            batchSize: 50,
            ...config
        };
        
        // 组件注册表
        this.registry = defaultComponentRegistry;
        
        // 主题管理
        this.themes = new Map();
        this.currentTheme = this.config.theme;
        
        // 容器引用
        this.container = this.config.container;
        
        this.logger.debug('ComponentManager', 'constructor', '基础配置完成', {
            config: this.config,
            hasContainer: !!this.container,
            currentTheme: this.currentTheme
        });
        
        // 初始化管理器
        this._initializeManager();
        
        const initDuration = this.logger.endPerformanceMark('component_manager_init', 'ComponentManager', 'constructor');
        this.logger.info('ComponentManager', 'constructor', '✅ 组件管理器初始化完成', {
            duration: `${initDuration?.toFixed(2)}ms`,
            themesCount: this.themes.size,
            registryReady: !!this.registry
        });
    }

    /**
     * 初始化管理器 - 设置主题、全局访问等
     * @private
     */
    _initializeManager() {
        this.logger.debug('ComponentManager', '_initializeManager', '开始初始化管理器功能');
        
        try {
            // 初始化主题系统
            this.logger.trace('ComponentManager', '_initializeManager', '初始化主题系统');
            this._initializeThemes();
            
            // 设置全局访问
            if (this.config.enableGlobalAccess) {
                this.logger.trace('ComponentManager', '_initializeManager', '设置全局访问');
                this._setupGlobalAccess();
            }
            
            // 设置自动清理
            if (this.config.autoCleanup) {
                this.logger.trace('ComponentManager', '_initializeManager', '设置自动清理');
                this._setupAutoCleanup();
            }
            
            // 监听注册表事件
            this.logger.trace('ComponentManager', '_initializeManager', '绑定注册表事件');
            this.registry.on('component:registered', (data) => {
                this.logger.trace('ComponentManager', '_initializeManager', '注册表组件注册事件', {
                    componentId: data.component.id
                });
                this.emit('component:created', data);
            });
            
            this.registry.on('component:unregistered', (data) => {
                this.logger.trace('ComponentManager', '_initializeManager', '注册表组件取消注册事件', {
                    componentId: data.componentId
                });
                this.emit('component:destroyed', data);
            });
            
            this.logger.debug('ComponentManager', '_initializeManager', '管理器功能初始化完成');
            
        } catch (error) {
            this.logger.error('ComponentManager', '_initializeManager', '管理器初始化失败', {
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }

    /**
     * 设置全局访问 - 在window对象上暴露组件管理器
     * @private
     */
    _setupGlobalAccess() {
        if (typeof window !== 'undefined') {
            window.SmartOfficeComponentManager = this;
            window.SmartOfficeComponents = {
                manager: this,
                registry: this.registry,
                BaseComponent
            };
        }
    }

    /**
     * 设置自动清理 - 设置组件自动清理机制
     * @private
     */
    _setupAutoCleanup() {
        // 监听页面卸载事件
        if (typeof window !== 'undefined') {
            window.addEventListener('beforeunload', () => {
                this.cleanup();
            });
        }
    }

    /**
     * 初始化主题系统 - 设置默认主题
     * @private
     */
    _initializeThemes() {
        // 注册默认主题
        this.registerTheme('default', {
            name: 'Default',
            colors: {
                primary: '#007bff',
                secondary: '#6c757d',
                success: '#28a745',
                danger: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8'
            },
            fonts: {
                family: 'Arial, sans-serif',
                size: '14px'
            },
            spacing: {
                xs: '4px',
                sm: '8px',
                md: '16px',
                lg: '24px',
                xl: '32px'
            }
        });
    }

    /**
     * 创建组件 - 创建并注册新的组件实例
     * @param {Class} ComponentClass - 组件类
     * @param {Object} config - 组件配置
     * @returns {BaseComponent} 创建的组件实例
     */
    createComponent(ComponentClass, config = {}) {
        this.logger.startPerformanceMark('component_creation', 'ComponentManager', 'createComponent');
        this.logger.info('ComponentManager', 'createComponent', '开始创建组件', {
            componentClass: ComponentClass.name,
            configKeys: Object.keys(config)
        });
        
        try {
            // 合并配置
            const finalConfig = {
                theme: this.currentTheme,
                container: this.container,
                ...config
            };
            
            this.logger.debug('ComponentManager', 'createComponent', '配置合并完成', {
                finalConfigKeys: Object.keys(finalConfig),
                theme: finalConfig.theme
            });
            
            // 创建组件实例
            this.logger.trace('ComponentManager', 'createComponent', '实例化组件');
            const component = new ComponentClass(finalConfig);
            
            // 注册组件
            this.logger.trace('ComponentManager', 'createComponent', '注册组件到注册表');
            const registered = this.registry.register(component);
            
            if (!registered) {
                const error = new Error('组件注册失败');
                this.logger.error('ComponentManager', 'createComponent', '组件创建失败：注册失败', {
                    componentClass: ComponentClass.name,
                    componentId: component.id
                });
                throw error;
            }
            
            const creationDuration = this.logger.endPerformanceMark('component_creation', 'ComponentManager', 'createComponent');
            this.logger.info('ComponentManager', 'createComponent', '✅ 组件创建成功', {
                componentClass: ComponentClass.name,
                componentId: component.id,
                componentName: component.name,
                duration: `${creationDuration?.toFixed(2)}ms`
            });
            
            // 记录创建统计
            this.logger.incrementCounter(`component_manager_${ComponentClass.name}_created`, 'ComponentManager');
            
            return component;
            
        } catch (error) {
            this.logger.error('ComponentManager', 'createComponent', '组件创建失败', {
                componentClass: ComponentClass.name,
                error: error.message,
                stack: error.stack,
                config
            });
            throw error;
        }
    }

    /**
     * 注册主题 - 注册新的主题
     * @param {string} name - 主题名称
     * @param {Object} theme - 主题配置
     */
    registerTheme(name, theme) {
        this.themes.set(name, theme);
        
        this.emit(UI_EVENTS.THEME_REGISTERED, { name, theme });
    }

    /**
     * 设置主题 - 切换全局主题
     * @param {string} themeName - 主题名称
     */
    setTheme(themeName) {
        if (!this.themes.has(themeName)) {
            console.warn(`主题 ${themeName} 未注册`);
            return;
        }
        
        this.currentTheme = themeName;
        
        // 更新所有组件的主题
        this.registry.getAll().forEach(component => {
            component.update({ theme: themeName });
        });
        
        this.emit(UI_EVENTS.THEME_CHANGED, { theme: themeName });
    }

    /**
     * 获取主题 - 获取指定主题配置
     * @param {string} themeName - 主题名称
     * @returns {Object|null} 主题配置
     */
    getTheme(themeName = this.currentTheme) {
        return this.themes.get(themeName) || null;
    }

    /**
     * 批量操作 - 对多个组件执行批量操作
     * @param {Array<string>|Function} selector - 组件ID数组或选择器函数
     * @param {Function} operation - 要执行的操作
     * @returns {Promise<Array>} 操作结果数组
     */
    async batchOperation(selector, operation) {
        let components;
        
        if (Array.isArray(selector)) {
            components = selector.map(id => this.registry.get(id)).filter(Boolean);
        } else if (typeof selector === 'function') {
            components = this.registry.find(selector);
        } else {
            throw new Error('选择器必须是组件ID数组或函数');
        }
        
        const results = [];
        for (const component of components) {
            try {
                const result = await operation(component);
                results.push({ success: true, result, component: component.id });
            } catch (error) {
                results.push({ success: false, error: error.message, component: component.id });
            }
        }
        
        return results;
    }

    /**
     * 获取管理器统计 - 获取管理器和注册表的统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.registry.getStats(),
            themes: this.themes.size,
            currentTheme: this.currentTheme,
            config: { ...this.config }
        };
    }

    /**
     * 清理资源 - 清理管理器和所有组件
     * @returns {Promise<void>}
     */
    async cleanup() {
        await this.registry.clear();
        
        // 清理主题
        this.themes.clear();
        
        // 清理全局引用
        if (typeof window !== 'undefined') {
            delete window.SmartOfficeComponentManager;
            delete window.SmartOfficeComponents;
        }
        
        this.emit(UI_EVENTS.MANAGER_CLEANED_UP);
    }
}
// #endregion

// #region 创建默认实例
/**
 * 创建默认组件管理器
 */
export const defaultComponentManager = new ComponentManager({
    registry: defaultComponentRegistry
});
// #endregion

// #region 便捷函数
/**
 * 快速创建组件 - 使用默认管理器快速创建组件
 * @param {Function} ComponentClass - 组件类
 * @param {Object} config - 组件配置
 * @returns {BaseComponent} 组件实例
 */
export function createComponent(ComponentClass, config = {}) {
    return defaultComponentManager.createComponent(ComponentClass, config);
}

/**
 * 快速挂载组件 - 创建并挂载组件
 * @param {Function} ComponentClass - 组件类
 * @param {HTMLElement|string} container - 容器元素或选择器
 * @param {Object} config - 组件配置
 * @returns {Promise<BaseComponent>} 组件实例
 */
export async function mountComponent(ComponentClass, container, config = {}) {
    const component = createComponent(ComponentClass, { ...config, container });
    await component.mount();
    return component;
}
// #endregion