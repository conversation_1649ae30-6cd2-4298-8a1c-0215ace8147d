/**
 * @file 文档编辑器模块
 * <AUTHOR> Team
 * @description 
 * 从index.html中提取的文档编辑功能
 * 提供文档类型切换、表单处理、NLP解析等功能
 */

import { getLogger } from '../utils/logger.js';
import { getDOMHelpers } from '../utils/dom-helpers.js';
import { showErrorNotification, showSuccessNotification } from '../utils/notification-manager.js';

// #region 文档编辑器类
/**
 * @class DocumentEditor - 文档编辑器
 * @description 提供文档编辑相关功能
 */
export class DocumentEditor {
    /**
     * 构造函数
     * @param {Object} config - 编辑器配置
     */
    constructor(config = {}) {
        this.logger = getLogger();
        this.dom = getDOMHelpers();
        this.config = config;
        this.isInitialized = false;
        
        this._initialize();
    }

    /**
     * 初始化文档编辑器
     * @private
     */
    _initialize() {
        if (this.isInitialized) return;
        
        this.logger.info('DocumentEditor', '_initialize', '初始化文档编辑器');
        
        // 设置事件监听器
        this._setupEventListeners();
        
        this.isInitialized = true;
        this.logger.info('DocumentEditor', '_initialize', '文档编辑器初始化完成');
    }

    /**
     * 设置事件监听器
     * @private
     */
    _setupEventListeners() {
        this.logger.debug('DocumentEditor', '_setupEventListeners', '设置事件监听器');
        
        // 文档类型切换
        this._setupDocumentTypeHandler();
        
        // 解析按钮
        this._setupParseButtonHandler();
        
        // 更新预览按钮
        this._setupUpdatePreviewHandler();
        
        // 重置按钮
        this._setupResetButtonHandler();
        
        // 移动端操作按钮
        this._setupMobileActionHandler();
        
        // 公司选择器
        this._setupCompanySelectorHandler();
        
        // Gemini配置
        this._setupGeminiConfigHandlers();
        
        // 图片上传
        this._setupImageUploadHandlers();
    }

    /**
     * 设置文档类型切换处理器
     * @private
     */
    _setupDocumentTypeHandler() {
        this.dom.addEventListener('#document-type', 'change', (e) => {
            const documentType = e.target.value;
            this.logger.info('DocumentEditor', 'documentTypeChange', '📄 文档类型切换', {
                oldType: e.target.dataset.previousValue || 'unknown',
                newType: documentType,
                timestamp: new Date().toISOString()
            });
            
            // 记录切换统计
            this.logger.incrementCounter(`document_type_${documentType}`, 'DocumentEditor');
            
            // 存储当前值以供下次使用
            e.target.dataset.previousValue = documentType;
            
            // 调用新架构的文档类型切换方法
            if (window.smartOfficeApp && window.smartOfficeApp.getManager) {
                const rendererManager = window.smartOfficeApp.getManager('renderers');
                if (rendererManager && rendererManager.switchTemplate) {
                    rendererManager.switchTemplate(documentType);
                }
            }
            
            this.logger.debug('DocumentEditor', 'documentTypeChange', '已集成新架构的文档类型切换方法');
            this.updatePreviewAndFormFields(documentType);
            this.adjustContentPadding();
        });
    }

    /**
     * 设置解析按钮处理器
     * @private
     */
    _setupParseButtonHandler() {
        this.dom.addEventListener('#parse-button', 'click', async (e) => {
            const input = this.dom.getElement('#natural-language-input');
            
            this.logger.debug('DocumentEditor', 'parseButtonClick', '解析按钮被点击', {
                hasInput: !!input,
                inputLength: input?.value?.length || 0
            });
            
            if (input && input.value.trim()) {
                this.logger.startPerformanceMark('nlp_parsing', 'DocumentEditor', 'parseButtonClick');
                this.logger.info('DocumentEditor', 'parseButtonClick', '🔍 开始自然语言解析', {
                    inputText: input.value.substring(0, 100) + (input.value.length > 100 ? '...' : ''),
                    inputLength: input.value.length
                });
                
                // 禁用按钮防止重复点击
                const parseButton = this.dom.getElement('#parse-button');
                this.dom.disable('#parse-button');
                if (parseButton) {
                    parseButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> 解析中...';
                }
                
                try {
                    // 调用新架构的自然语言处理方法
                    const nlpManager = window.smartOfficeApp?.getManager('nlp');
                    if (!nlpManager) {
                        throw new Error('NLP处理器未初始化');
                    }
                    
                    // 获取当前文档类型
                    const documentType = this.dom.getValue('#document-type', 'receipt');
                    
                    // 检查是否有上传的图片
                    const hasImage = window.uploadedImageFile;
                    const inputText = input.value.trim();
                    
                    let result;
                    
                    if (hasImage && nlpManager.processImage) {
                        // 多模态处理（文字+图片）
                        this.logger.info('DocumentEditor', 'parseButtonClick', '🖼️ 开始多模态分析（文字+图片）');
                        result = await nlpManager.processImage(window.uploadedImageFile, inputText, documentType);
                    } else if (hasImage) {
                        // 纯图片处理（如果NLP管理器不支持processImage方法）
                        this.logger.warn('DocumentEditor', 'parseButtonClick', '⚠️ NLP管理器不支持图片处理，仅处理文本');
                        result = await nlpManager.processText(inputText, documentType);
                    } else {
                        // 纯文本处理
                        this.logger.info('DocumentEditor', 'parseButtonClick', '📝 开始文本分析');
                        result = await nlpManager.processText(inputText, documentType);
                    }
                    
                    const parseDuration = this.logger.endPerformanceMark('nlp_parsing', 'DocumentEditor', 'parseButtonClick');
                    
                    if (result && result.extractedData) {
                        // 填充表单字段
                        this.fillFormFromNLPResult(result.extractedData);
                        
                        this.logger.info('DocumentEditor', 'parseButtonClick', '✅ 自然语言解析完成', {
                            duration: `${parseDuration?.toFixed(2)}ms`,
                            confidence: result.confidence,
                            extractedFields: Object.keys(result.extractedData).length,
                            hasImage: !!hasImage,
                            documentType
                        });
                        
                        // 显示成功通知
                        showSuccessNotification(
                            '解析成功', 
                            `已提取${Object.keys(result.extractedData).length}个字段，置信度：${(result.confidence * 100).toFixed(1)}%`
                        );
                    } else {
                        this.logger.warn('DocumentEditor', 'parseButtonClick', '解析结果为空或无效');
                        showErrorNotification('解析失败', '未能从输入中提取有效信息');
                    }
                    
                    // 记录解析统计
                    this.logger.incrementCounter('nlp_parsing_attempts', 'DocumentEditor');
                    if (hasImage) {
                        this.logger.incrementCounter('nlp_image_processing_attempts', 'DocumentEditor');
                    }
                    
                } catch (error) {
                    this.logger.error('DocumentEditor', 'parseButtonClick', '自然语言解析失败', {
                        error: error.message,
                        inputText: input.value
                    });
                    showErrorNotification('解析失败', error.message);
                } finally {
                    // 恢复按钮状态
                    this.dom.enable('#parse-button');
                    const parseButton = this.dom.getElement('#parse-button');
                    if (parseButton) {
                        parseButton.innerHTML = '<i class="fas fa-magic mr-2"></i> 智能解析';
                    }
                }
            } else {
                this.logger.warn('DocumentEditor', 'parseButtonClick', '解析按钮点击但输入为空');
                showErrorNotification('输入为空', '请输入要解析的内容');
            }
        });
    }

    /**
     * 设置更新预览处理器
     * @private
     */
    _setupUpdatePreviewHandler() {
        this.dom.addEventListener('#update-preview-button', 'click', () => {
            this.logger.info('DocumentEditor', 'updatePreviewClick', '🔄 更新文档预览');
            this.logger.startPerformanceMark('preview_update', 'DocumentEditor', 'updatePreviewClick');
            
            // 调用新架构的文档预览方法
            if (window.smartOfficeApp && window.smartOfficeApp.previewDocument) {
                const currentContent = window.smartOfficeApp._getCurrentDocumentContent();
                window.smartOfficeApp.previewDocument(currentContent);
            }
            this.logger.debug('DocumentEditor', 'updatePreviewClick', '已集成新架构的文档预览方法');
            
            // 记录预览更新统计
            this.logger.incrementCounter('preview_updates', 'DocumentEditor');
            this.adjustContentPadding();
            
            const updateDuration = this.logger.endPerformanceMark('preview_update', 'DocumentEditor', 'updatePreviewClick');
            this.logger.debug('DocumentEditor', 'updatePreviewClick', '预览更新完成', {
                duration: `${updateDuration?.toFixed(2)}ms`
            });
        });
    }

    /**
     * 设置重置按钮处理器
     * @private
     */
    _setupResetButtonHandler() {
        this.dom.addEventListener('#reset-form-button', 'click', () => {
            this.logger.info('DocumentEditor', 'resetButtonClick', '🔄 重置表单');
            this.logger.startPerformanceMark('form_reset', 'DocumentEditor', 'resetButtonClick');
            
            // 调用新架构的表单重置方法
            if (window.smartOfficeApp && window.smartOfficeApp.getManager) {
                const stateManager = window.smartOfficeApp.getManager('state');
                if (stateManager && stateManager.reset) {
                    stateManager.reset('form');
                }
            }
            this.logger.debug('DocumentEditor', 'resetButtonClick', '已集成新架构的表单重置方法');
            
            // 记录重置统计
            this.logger.incrementCounter('form_resets', 'DocumentEditor');
            
            const resetDuration = this.logger.endPerformanceMark('form_reset', 'DocumentEditor', 'resetButtonClick');
            this.logger.debug('DocumentEditor', 'resetButtonClick', '表单重置完成', {
                duration: `${resetDuration?.toFixed(2)}ms`
            });
        });
    }

    /**
     * 设置移动端操作处理器
     * @private
     */
    _setupMobileActionHandler() {
        this.dom.addEventListener('#mobile-action-button', 'click', () => {
            const wasHidden = this.dom.hasClass('#mobile-action-menu', 'hidden');
            this.dom.toggleClass('#mobile-action-menu', 'hidden');
            
            this.logger.debug('DocumentEditor', 'mobileActionClick', '移动端操作菜单切换', {
                action: wasHidden ? 'show' : 'hide',
                menuVisible: !this.dom.hasClass('#mobile-action-menu', 'hidden')
            });
            
            // 记录移动端交互统计
            this.logger.incrementCounter('mobile_menu_toggles', 'DocumentEditor');
        });
    }

    /**
     * 设置公司选择器处理器
     * @private
     */
    _setupCompanySelectorHandler() {
        this.dom.addEventListener('#company-selector', 'change', (e) => {
            const companyCode = e.target.value;
            if (window.smartOfficeApp && typeof window.smartOfficeApp.updateCompanyImages === 'function') {
                window.smartOfficeApp.updateCompanyImages(companyCode);
            } else if (typeof updateCompanyImages === 'function') {
                updateCompanyImages(companyCode);
            }
            this.adjustContentPadding();
        });
    }

    /**
     * 设置Gemini配置处理器
     * @private
     */
    _setupGeminiConfigHandlers() {
        this.logger.debug('DocumentEditor', '_setupGeminiConfigHandlers', '开始设置Gemini配置事件处理器');
        
        // NLP模式切换
        this.dom.addEventListener('#nlp-mode-selector', 'change', (e) => {
            const mode = e.target.value;
            this.logger.info('DocumentEditor', 'nlpModeChange', '🧠 NLP模式切换', { mode });
            
            const imageUploadLabel = this.dom.getElement('label[for="image-upload-input"]');
            
            if (mode === 'gemini') {
                if (imageUploadLabel) {
                    this.dom.removeClass('label[for="image-upload-input"]', 'opacity-50');
                    this.dom.removeClass('label[for="image-upload-input"]', 'cursor-not-allowed');
                    this.dom.enable('label[for="image-upload-input"] input');
                }
            } else {
                if (imageUploadLabel) {
                    this.dom.addClass('label[for="image-upload-input"]', 'opacity-50');
                    this.dom.addClass('label[for="image-upload-input"]', 'cursor-not-allowed');
                    this.dom.disable('label[for="image-upload-input"] input');
                }
            }
            
            // 更新配置
            this.updateNLPConfig();
        });
        
        // 初始化状态 - 默认选择Gemini模式
        this.dom.setValue('#nlp-mode-selector', 'gemini');
        
        // API密钥已硬植入，直接设置为已配置状态
        this.logger.info('DocumentEditor', '_setupGeminiConfigHandlers', '✅ API密钥已硬植入，设置为已配置状态');
    }

    /**
     * 设置图片上传处理器
     * @private
     */
    _setupImageUploadHandlers() {
        this.logger.debug('DocumentEditor', '_setupImageUploadHandlers', '开始设置图片上传事件处理器');
        
        this.dom.addEventListener('#image-upload-input', 'change', (e) => {
            const file = e.target.files[0];
            
            if (file) {
                this.logger.info('DocumentEditor', 'imageUpload', '📷 图片上传', {
                    fileName: file.name,
                    fileSize: file.size,
                    fileType: file.type
                });
                
                // 验证文件类型
                if (!file.type.startsWith('image/')) {
                    showErrorNotification('文件类型错误', '请选择图片文件');
                    return;
                }
                
                // 验证文件大小（10MB限制）
                if (file.size > 10 * 1024 * 1024) {
                    showErrorNotification('文件过大', '图片文件大小不能超过10MB');
                    return;
                }
                
                // 处理图片上传
                this.handleImageUpload(file);
            }
        });
    }

    /**
     * 处理图片上传
     * @param {File} file - 上传的文件
     */
    handleImageUpload(file) {
        const reader = new FileReader();
        
        reader.onload = (e) => {
            const imageData = e.target.result;
            
            // 存储到全局变量
            window.uploadedImageFile = file;
            window.uploadedImageData = imageData;
            
            // 更新预览
            this.updateImagePreview(imageData, file.name);
            
            this.logger.info('DocumentEditor', 'handleImageUpload', '图片上传处理完成', {
                fileName: file.name,
                dataLength: imageData.length
            });
        };
        
        reader.onerror = (error) => {
            this.logger.error('DocumentEditor', 'handleImageUpload', '图片读取失败', error);
            showErrorNotification('图片读取失败', '无法读取选择的图片文件');
        };
        
        reader.readAsDataURL(file);
    }

    /**
     * 更新图片预览
     * @param {string} imageData - 图片数据
     * @param {string} fileName - 文件名
     */
    updateImagePreview(imageData, fileName) {
        const previewContainer = this.dom.getElement('#image-preview-container');
        const preview = this.dom.getElement('#image-preview');
        const placeholder = this.dom.getElement('#image-preview-placeholder');
        const filenameElement = this.dom.getElement('#image-upload-filename');
        
        if (preview) {
            preview.src = imageData;
            this.dom.removeClass('#image-preview', 'hidden');
        }
        
        if (placeholder) {
            this.dom.addClass('#image-preview-placeholder', 'hidden');
        }
        
        if (filenameElement) {
            filenameElement.textContent = fileName;
        }
        
        if (previewContainer) {
            this.dom.removeClass('#image-preview-container', 'hidden');
        }
    }

    /**
     * 更新预览和表单字段
     * @param {string} documentType - 文档类型
     */
    updatePreviewAndFormFields(documentType) {
        this.logger.debug('DocumentEditor', 'updatePreviewAndFormFields', '更新预览和表单字段', { documentType });
        
        // 这里可以添加具体的更新逻辑
        // 目前保持与现有架构的兼容性
    }

    /**
     * 调整内容边距
     */
    adjustContentPadding() {
        this.logger.trace('DocumentEditor', 'adjustContentPadding', '调整内容边距');
        
        // 这里可以添加具体的边距调整逻辑
        // 目前保持与现有架构的兼容性
    }

    /**
     * 从NLP结果填充表单
     * @param {Object} extractedData - 提取的数据
     */
    fillFormFromNLPResult(extractedData) {
        this.logger.debug('DocumentEditor', 'fillFormFromNLPResult', '从NLP结果填充表单', {
            fieldsCount: Object.keys(extractedData).length
        });
        
        // 这里可以添加具体的表单填充逻辑
        // 目前保持与现有架构的兼容性
    }

    /**
     * 更新NLP配置
     */
    updateNLPConfig() {
        this.logger.debug('DocumentEditor', 'updateNLPConfig', '更新NLP配置');
        
        // 这里可以添加具体的配置更新逻辑
        // 目前保持与现有架构的兼容性
    }

    /**
     * 销毁文档编辑器
     */
    destroy() {
        this.logger.info('DocumentEditor', 'destroy', '销毁文档编辑器');
        
        this.isInitialized = false;
    }
}
// #endregion

// #region 全局文档编辑器实例
let globalDocumentEditor = null;

/**
 * 获取全局文档编辑器实例
 * @returns {DocumentEditor} 文档编辑器实例
 */
export function getDocumentEditor() {
    if (!globalDocumentEditor) {
        globalDocumentEditor = new DocumentEditor();
    }
    return globalDocumentEditor;
}
// #endregion
