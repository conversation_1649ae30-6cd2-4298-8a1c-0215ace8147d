/**
 * @file 通知管理器模块
 * <AUTHOR> Team
 * @description 
 * 从index.html中提取的通知功能
 * 提供错误通知、成功通知等用户反馈功能
 */

import { getLogger } from './logger.js';

// #region 通知管理器类
/**
 * @class NotificationManager - 通知管理器
 * @description 提供统一的用户通知功能
 */
export class NotificationManager {
    /**
     * 构造函数
     * @param {Object} config - 通知配置
     */
    constructor(config = {}) {
        this.logger = getLogger();
        this.config = {
            autoRemoveDelay: config.autoRemoveDelay || 5000,
            maxNotifications: config.maxNotifications || 5,
            position: config.position || 'top-right'
        };
        
        this.notifications = new Set();
        this._initialize();
    }

    /**
     * 初始化通知管理器
     * @private
     */
    _initialize() {
        this.logger.info('NotificationManager', '_initialize', '通知管理器初始化');
        
        // 创建通知容器
        this._createNotificationContainer();
    }

    /**
     * 创建通知容器
     * @private
     */
    _createNotificationContainer() {
        let container = document.getElementById('notification-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notification-container';
            container.className = 'fixed top-4 right-4 z-50 space-y-2';
            document.body.appendChild(container);
        }
        this.container = container;
    }

    /**
     * 显示错误通知
     * @param {string} title - 错误标题
     * @param {string} message - 错误消息
     * @param {Object} options - 通知选项
     */
    showError(title, message, options = {}) {
        this.logger.debug('NotificationManager', 'showError', '显示错误通知', {
            title,
            message
        });
        
        this._showNotification('error', title, message, options);
    }

    /**
     * 显示成功通知
     * @param {string} title - 成功标题
     * @param {string} message - 成功消息
     * @param {Object} options - 通知选项
     */
    showSuccess(title, message, options = {}) {
        this.logger.debug('NotificationManager', 'showSuccess', '显示成功通知', {
            title,
            message
        });
        
        this._showNotification('success', title, message, options);
    }

    /**
     * 显示警告通知
     * @param {string} title - 警告标题
     * @param {string} message - 警告消息
     * @param {Object} options - 通知选项
     */
    showWarning(title, message, options = {}) {
        this.logger.debug('NotificationManager', 'showWarning', '显示警告通知', {
            title,
            message
        });
        
        this._showNotification('warning', title, message, options);
    }

    /**
     * 显示信息通知
     * @param {string} title - 信息标题
     * @param {string} message - 信息消息
     * @param {Object} options - 通知选项
     */
    showInfo(title, message, options = {}) {
        this.logger.debug('NotificationManager', 'showInfo', '显示信息通知', {
            title,
            message
        });
        
        this._showNotification('info', title, message, options);
    }

    /**
     * 内部通知显示方法
     * @private
     * @param {string} type - 通知类型
     * @param {string} title - 通知标题
     * @param {string} message - 通知消息
     * @param {Object} options - 通知选项
     */
    _showNotification(type, title, message, options = {}) {
        // 检查通知数量限制
        if (this.notifications.size >= this.config.maxNotifications) {
            this._removeOldestNotification();
        }

        // 创建通知元素
        const notification = this._createNotificationElement(type, title, message, options);
        
        // 添加到容器
        this.container.appendChild(notification);
        this.notifications.add(notification);
        
        // 设置自动移除
        const delay = options.autoRemove !== false ? 
            (options.delay || this.config.autoRemoveDelay) : 0;
            
        if (delay > 0) {
            setTimeout(() => {
                this._removeNotification(notification);
            }, delay);
        }
        
        this.logger.trace('NotificationManager', '_showNotification', '通知已显示');
    }

    /**
     * 创建通知元素
     * @private
     * @param {string} type - 通知类型
     * @param {string} title - 通知标题
     * @param {string} message - 通知消息
     * @param {Object} options - 通知选项
     * @returns {HTMLElement} 通知元素
     */
    _createNotificationElement(type, title, message, options) {
        const notification = document.createElement('div');
        
        // 设置基础样式
        const baseClasses = 'p-4 rounded-lg shadow-lg max-w-sm w-full';
        const typeClasses = this._getTypeClasses(type);
        notification.className = `${baseClasses} ${typeClasses}`;
        
        // 设置内容
        const icon = this._getTypeIcon(type);
        notification.innerHTML = `
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i class="${icon} mr-2"></i>
                </div>
                <div class="ml-3 w-0 flex-1">
                    <h4 class="font-bold text-sm">${this._escapeHtml(title)}</h4>
                    <p class="text-sm mt-1">${this._escapeHtml(message)}</p>
                </div>
                <div class="ml-4 flex-shrink-0 flex">
                    <button class="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none" 
                            onclick="this.closest('.p-4').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
        
        // 添加点击关闭功能
        const closeButton = notification.querySelector('button');
        closeButton.addEventListener('click', () => {
            this._removeNotification(notification);
        });
        
        return notification;
    }

    /**
     * 获取类型对应的CSS类
     * @private
     * @param {string} type - 通知类型
     * @returns {string} CSS类名
     */
    _getTypeClasses(type) {
        const typeMap = {
            error: 'bg-red-500 text-white',
            success: 'bg-green-500 text-white',
            warning: 'bg-yellow-500 text-white',
            info: 'bg-blue-500 text-white'
        };
        
        return typeMap[type] || typeMap.info;
    }

    /**
     * 获取类型对应的图标
     * @private
     * @param {string} type - 通知类型
     * @returns {string} 图标类名
     */
    _getTypeIcon(type) {
        const iconMap = {
            error: 'fas fa-exclamation-triangle',
            success: 'fas fa-check-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        
        return iconMap[type] || iconMap.info;
    }

    /**
     * HTML转义
     * @private
     * @param {string} text - 要转义的文本
     * @returns {string} 转义后的文本
     */
    _escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 移除通知
     * @private
     * @param {HTMLElement} notification - 通知元素
     */
    _removeNotification(notification) {
        if (notification && notification.parentElement) {
            notification.remove();
            this.notifications.delete(notification);
        }
    }

    /**
     * 移除最旧的通知
     * @private
     */
    _removeOldestNotification() {
        const oldest = this.notifications.values().next().value;
        if (oldest) {
            this._removeNotification(oldest);
        }
    }

    /**
     * 清除所有通知
     */
    clearAll() {
        this.logger.debug('NotificationManager', 'clearAll', '清除所有通知');
        
        this.notifications.forEach(notification => {
            this._removeNotification(notification);
        });
        
        this.notifications.clear();
    }

    /**
     * 销毁通知管理器
     */
    destroy() {
        this.logger.info('NotificationManager', 'destroy', '销毁通知管理器');
        
        this.clearAll();
        
        if (this.container && this.container.parentElement) {
            this.container.remove();
        }
    }
}
// #endregion

// #region 全局通知实例
let globalNotificationManager = null;

/**
 * 获取全局通知管理器实例
 * @returns {NotificationManager} 通知管理器实例
 */
export function getNotificationManager() {
    if (!globalNotificationManager) {
        globalNotificationManager = new NotificationManager();
    }
    return globalNotificationManager;
}

/**
 * 显示错误通知（便捷函数）
 * @param {string} title - 错误标题
 * @param {string} message - 错误消息
 */
export function showErrorNotification(title, message) {
    getNotificationManager().showError(title, message);
}

/**
 * 显示成功通知（便捷函数）
 * @param {string} title - 成功标题
 * @param {string} message - 成功消息
 */
export function showSuccessNotification(title, message) {
    getNotificationManager().showSuccess(title, message);
}

/**
 * 显示警告通知（便捷函数）
 * @param {string} title - 警告标题
 * @param {string} message - 警告消息
 */
export function showWarningNotification(title, message) {
    getNotificationManager().showWarning(title, message);
}

/**
 * 显示信息通知（便捷函数）
 * @param {string} title - 信息标题
 * @param {string} message - 信息消息
 */
export function showInfoNotification(title, message) {
    getNotificationManager().showInfo(title, message);
}
// #endregion
