/**
 * @file 工具模块导出
 * @description 统一导出所有工具类和函数
 */

// 导出验证工具
export { Validator, ValidationError, DocumentValidationRules } from './validation.js';

// 导出日期工具
export { DateUtils } from './date-utils.js';

// 导出字符串工具
export { StringUtils } from './string-utils.js';

// 导出NLP处理器
export { getLocalNLPProcessor, LocalNLPProcessor } from './local-nlp-processor.js';
export { getGeminiNLPProcessor, GeminiNLPProcessor } from './gemini-nlp-processor.js';
export { SmartNLPProcessor, getSmartNLPProcessor, createSmartNLPProcessor } from './smart-nlp-processor.js';

// 导出图片管理器
export { ImageManager, getImageManager, getCompanyImages, getCompanyLogo, getCompanyStamp, getCompanyFooter } from './image-manager.js';

// 导出日志系统
export { getLogger, createLogger, LogLevel } from './logger.js';

// 导出调试面板
export { DebugPanel, createDebugPanel } from './debug-panel.js';

/**
 * 通用工具函数
 */

/**
 * 深度克隆对象
 * @param {*} obj - 要克隆的对象
 * @returns {*} 克隆后的对象
 */
export function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    
    if (obj instanceof Date) {
        return new Date(obj.getTime());
    }
    
    if (obj instanceof Array) {
        return obj.map(item => deepClone(item));
    }
    
    if (typeof obj === 'object') {
        const cloned = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                cloned[key] = deepClone(obj[key]);
            }
        }
        return cloned;
    }
    
    return obj;
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay) {
    let timeoutId;
    
    return function(...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 时间间隔（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
    let inThrottle;
    
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 检查是否为空值
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为空
 */
export function isEmpty(value) {
    if (value === null || value === undefined) {
        return true;
    }
    
    if (typeof value === 'string') {
        return value.trim().length === 0;
    }
    
    if (Array.isArray(value)) {
        return value.length === 0;
    }
    
    if (typeof value === 'object') {
        return Object.keys(value).length === 0;
    }
    
    return false;
}

/**
 * 获取对象的嵌套属性值
 * @param {Object} obj - 对象
 * @param {string} path - 属性路径，如 'user.profile.name'
 * @param {*} defaultValue - 默认值
 * @returns {*} 属性值或默认值
 */
export function getNestedValue(obj, path, defaultValue = undefined) {
    if (!obj || typeof obj !== 'object') {
        return defaultValue;
    }
    
    const keys = path.split('.');
    let current = obj;
    
    for (const key of keys) {
        if (current === null || current === undefined || !(key in current)) {
            return defaultValue;
        }
        current = current[key];
    }
    
    return current;
}

/**
 * 设置对象的嵌套属性值
 * @param {Object} obj - 对象
 * @param {string} path - 属性路径，如 'user.profile.name'
 * @param {*} value - 要设置的值
 * @returns {Object} 修改后的对象
 */
export function setNestedValue(obj, path, value) {
    if (!obj || typeof obj !== 'object') {
        return obj;
    }
    
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        
        if (!(key in current) || typeof current[key] !== 'object') {
            current[key] = {};
        }
        
        current = current[key];
    }
    
    current[keys[keys.length - 1]] = value;
    return obj;
}

/**
 * 合并多个对象
 * @param {Object} target - 目标对象
 * @param {...Object} sources - 源对象
 * @returns {Object} 合并后的对象
 */
export function merge(target, ...sources) {
    if (!target || typeof target !== 'object') {
        target = {};
    }
    
    for (const source of sources) {
        if (source && typeof source === 'object') {
            for (const key in source) {
                if (source.hasOwnProperty(key)) {
                    if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
                        target[key] = merge(target[key] || {}, source[key]);
                    } else {
                        target[key] = source[key];
                    }
                }
            }
        }
    }
    
    return target;
}

/**
 * 数组去重
 * @param {Array} array - 输入数组
 * @param {string|Function} key - 去重键或比较函数
 * @returns {Array} 去重后的数组
 */
export function unique(array, key = null) {
    if (!Array.isArray(array)) {
        return [];
    }
    
    if (key === null) {
        return [...new Set(array)];
    }
    
    const seen = new Set();
    const result = [];
    
    for (const item of array) {
        const keyValue = typeof key === 'function' ? key(item) : item[key];
        
        if (!seen.has(keyValue)) {
            seen.add(keyValue);
            result.push(item);
        }
    }
    
    return result;
}

/**
 * 数组分组
 * @param {Array} array - 输入数组
 * @param {string|Function} key - 分组键或分组函数
 * @returns {Object} 分组后的对象
 */
export function groupBy(array, key) {
    if (!Array.isArray(array)) {
        return {};
    }
    
    return array.reduce((groups, item) => {
        const groupKey = typeof key === 'function' ? key(item) : item[key];
        
        if (!groups[groupKey]) {
            groups[groupKey] = [];
        }
        
        groups[groupKey].push(item);
        return groups;
    }, {});
}

/**
 * 数组排序
 * @param {Array} array - 输入数组
 * @param {string|Function} key - 排序键或比较函数
 * @param {string} order - 排序顺序，'asc' 或 'desc'
 * @returns {Array} 排序后的数组
 */
export function sortBy(array, key, order = 'asc') {
    if (!Array.isArray(array)) {
        return [];
    }
    
    const sorted = [...array].sort((a, b) => {
        let valueA, valueB;
        
        if (typeof key === 'function') {
            valueA = key(a);
            valueB = key(b);
        } else {
            valueA = a[key];
            valueB = b[key];
        }
        
        if (valueA < valueB) {
            return order === 'asc' ? -1 : 1;
        }
        
        if (valueA > valueB) {
            return order === 'asc' ? 1 : -1;
        }
        
        return 0;
    });
    
    return sorted;
}

/**
 * 延迟执行
 * @param {number} ms - 延迟时间（毫秒）
 * @returns {Promise} Promise对象
 */
export function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 重试函数
 * @param {Function} func - 要重试的函数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} delay - 重试间隔（毫秒）
 * @returns {Promise} Promise对象
 */
export async function retry(func, maxRetries = 3, delay = 1000) {
    let lastError;
    
    for (let i = 0; i <= maxRetries; i++) {
        try {
            return await func();
        } catch (error) {
            lastError = error;
            
            if (i < maxRetries) {
                await sleep(delay);
            }
        }
    }
    
    throw lastError;
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes, decimals = 2) {
    if (bytes === 0) {
        return '0 Bytes';
    }
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * 生成范围内的随机数
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {number} 随机数
 */
export function randomBetween(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * 检查是否为有效的URL
 * @param {string} url - URL字符串
 * @returns {boolean} 是否为有效URL
 */
export function isValidUrl(url) {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

/**
 * 获取URL参数
 * @param {string} url - URL字符串
 * @returns {Object} 参数对象
 */
export function getUrlParams(url) {
    try {
        const urlObj = new URL(url);
        const params = {};
        
        for (const [key, value] of urlObj.searchParams) {
            params[key] = value;
        }
        
        return params;
    } catch {
        return {};
    }
}

/**
 * 构建URL查询字符串
 * @param {Object} params - 参数对象
 * @returns {string} 查询字符串
 */
export function buildQueryString(params) {
    if (!params || typeof params !== 'object') {
        return '';
    }
    
    const searchParams = new URLSearchParams();
    
    for (const [key, value] of Object.entries(params)) {
        if (value !== null && value !== undefined) {
            searchParams.append(key, String(value));
        }
    }
    
    return searchParams.toString();
}

// 导出图片管理器
export { 
    ImageManager, 
    createImageManager, 
    getImageManager, 
    setImageManager,
    getCompanyImages,
    getCompanyLogo,
    getCompanyStamp,
    getCompanyFooter
} from './image-manager.js';

// 导出本地自然语言处理器
export { 
    LocalNLPProcessor, 
    createLocalNLPProcessor, 
    getLocalNLPProcessor 
} from './local-nlp-processor.js';

// 导出Gemini自然语言处理器
export {
    GeminiNLPProcessor,
    createGeminiNLPProcessor,
    getGeminiNLPProcessor,
    setGeminiNLPProcessor
} from './gemini-nlp-processor.js';

// 导出日志系统
export { 
    Logger,
    LogLevel,
    createLogger,
    getLogger
} from './logger.js';

// 导出本地资源管理器
export { 
    LocalResourceManager,
    createLocalResourceManager,
    getLocalResourceManager,
    loadOfflineResources
} from './local-resources.js';
