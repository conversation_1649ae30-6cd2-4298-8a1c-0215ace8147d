<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片转 Base64 工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select, input[type="file"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            background-color: #f9f9f9;
        }
        .preview {
            margin-top: 20px;
            text-align: center;
        }
        .preview img {
            max-width: 100%;
            max-height: 300px;
            border: 1px solid #ddd;
        }
        textarea {
            width: 100%;
            height: 100px;
            margin-top: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            font-family: monospace;
        }
        .copy-btn {
            background-color: #2196F3;
            margin-top: 10px;
        }
        .copy-btn:hover {
            background-color: #0b7dda;
        }
    </style>
</head>
<body>
    <h1>图片转 Base64 工具</h1>
    <div class="container">
        <div class="form-group">
            <label for="image-type">图片类型</label>
            <select id="image-type">
                <option value="logo">公司标志 (Logo)</option>
                <option value="stamp">公司盖章 (Stamp)</option>
                <option value="footer">页脚图片 (Footer)</option>
                <option value="content">内容图片 (Content)</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="company">公司</label>
            <select id="company">
                <option value="sky-mirror">Sky Mirror World Tour</option>
                <option value="gomyhire">GoMyHire Travel</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="image-file">选择图片文件</label>
            <input type="file" id="image-file" accept="image/*">
        </div>
        
        <button id="convert-btn">转换为 Base64</button>
        
        <div class="preview" id="preview-container" style="display: none;">
            <h3>图片预览</h3>
            <img id="preview-image" src="" alt="预览">
        </div>
        
        <div class="result" id="result-container" style="display: none;">
            <h3>Base64 编码结果</h3>
            <textarea id="base64-result" readonly></textarea>
            <button id="copy-btn" class="copy-btn">复制到剪贴板</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const imageFileInput = document.getElementById('image-file');
            const convertBtn = document.getElementById('convert-btn');
            const previewContainer = document.getElementById('preview-container');
            const previewImage = document.getElementById('preview-image');
            const resultContainer = document.getElementById('result-container');
            const base64Result = document.getElementById('base64-result');
            const copyBtn = document.getElementById('copy-btn');
            
            // 转换按钮点击事件
            convertBtn.addEventListener('click', function() {
                const file = imageFileInput.files[0];
                if (!file) {
                    alert('请先选择图片文件');
                    return;
                }
                
                const reader = new FileReader();
                reader.onload = function(e) {
                    const base64Data = e.target.result;
                    
                    // 显示预览
                    previewImage.src = base64Data;
                    previewContainer.style.display = 'block';
                    
                    // 显示结果
                    base64Result.value = base64Data;
                    resultContainer.style.display = 'block';
                };
                
                reader.readAsDataURL(file);
            });
            
            // 复制按钮点击事件
            copyBtn.addEventListener('click', function() {
                base64Result.select();
                document.execCommand('copy');
                alert('已复制到剪贴板');
            });
            
            // 文件选择变化事件
            imageFileInput.addEventListener('change', function() {
                // 隐藏之前的结果
                previewContainer.style.display = 'none';
                resultContainer.style.display = 'none';
            });
        });
    </script>
</body>
</html>
