/**
 * @file 通知组件 - SmartOffice 通知和消息系统
 * <AUTHOR> Team
 * @description 
 * 提供完整的通知功能，包括：
 * - Notification 基础通知组件
 * - NotificationManager 通知管理器
 * - 多种通知类型：成功、警告、错误、信息
 * - 自动消失、手动关闭、持久化通知
 * - 位置配置、动画效果、堆叠管理
 * - 完整的事件系统和生命周期管理
 */

import { BaseComponent } from './base-component.js';
import { UI_EVENTS } from '../../core/events/event-types.js';

// #region Notification主组件
/**
 * @class Notification - 通知主组件
 * @extends BaseComponent
 * @description 可配置的通知组件，支持多种类型和位置
 */
export class Notification extends BaseComponent {
    /**
     * 构造函数 - 初始化通知组件
     * @param {Object} config - 通知配置
     * @param {string} config.type - 通知类型
     * @param {string} config.title - 通知标题
     * @param {string} config.message - 通知内容
     * @param {number} config.duration - 自动关闭时间
     * @param {boolean} config.closable - 是否可手动关闭
     * @param {boolean} config.persistent - 是否持久化显示
     * @param {string} config.position - 显示位置
     */
    constructor(config = {}) {
        super(config);
        
        // 通知属性
        this.notificationProps = {
            type: config.type || 'info', // success, warning, error, info
            title: config.title || '',
            message: config.message || '',
            duration: config.duration !== undefined ? config.duration : 4000, // 自动关闭时间(毫秒)
            closable: config.closable !== false, // 默认可关闭
            persistent: config.persistent || false, // 持久化显示
            position: config.position || 'top-right', // top-left, top-right, bottom-left, bottom-right, center
            showProgress: config.showProgress !== false, // 显示进度条
            icon: config.icon || this._getDefaultIcon(config.type), // 图标
            actions: config.actions || [], // 操作按钮
            className: config.className || '',
            zIndex: config.zIndex || 5000,
            animation: config.animation !== false, // 动画效果
            sound: config.sound || false, // 声音提示
            onAction: config.onAction || null, // 操作回调
            onClose: config.onClose || null // 关闭回调
        };
        
        // 通知状态
        this.notificationState = {
            visible: false,
            showing: false,
            hiding: false,
            timeLeft: this.notificationProps.duration,
            progressWidth: 100,
            autoCloseTimer: null,
            progressTimer: null,
            pausedTime: null
        };
        
        // DOM元素引用
        this.elements = {
            notification: null,
            icon: null,
            content: null,
            title: null,
            message: null,
            closeButton: null,
            progressBar: null,
            progressFill: null,
            actions: null
        };
        
        // 事件处理器
        this.handlers = {
            closeClick: this._handleCloseClick.bind(this),
            mouseEnter: this._handleMouseEnter.bind(this),
            mouseLeave: this._handleMouseLeave.bind(this),
            actionClick: this._handleActionClick.bind(this)
        };
        
        // 初始化组件
        this._initializeNotification();
    }

    /**
     * 获取默认图标 - 根据类型获取默认图标
     * @param {string} type - 通知类型
     * @returns {string} 图标HTML
     * @private
     */
    _getDefaultIcon(type) {
        const iconMap = {
            success: '✓',
            warning: '⚠',
            error: '✕',
            info: 'ℹ'
        };
        return iconMap[type] || iconMap.info;
    }

    /**
     * 初始化通知 - 创建DOM结构和设置事件
     * @private
     */
    _initializeNotification() {
        this._createNotificationStructure();
        this._applyNotificationStyles();
        this._bindNotificationEvents();
        
        // 发射初始化完成事件
        this.emit(UI_EVENTS.COMPONENT_INITIALIZED, {
            component: this,
            type: 'notification'
        });
    }

    /**
     * 创建通知DOM结构 - 构建完整的通知HTML
     * @private
     */
    _createNotificationStructure() {
        // 创建通知容器
        this.elements.notification = document.createElement('div');
        this.elements.notification.className = `smartoffice-notification smartoffice-notification-${this.notificationProps.type} ${this.notificationProps.className}`;
        this.elements.notification.setAttribute('role', 'alert');
        this.elements.notification.setAttribute('aria-live', 'polite');
        
        // 创建图标
        this._createNotificationIcon();
        
        // 创建内容
        this._createNotificationContent();
        
        // 创建关闭按钮
        if (this.notificationProps.closable) {
            this._createCloseButton();
        }
        
        // 创建操作按钮
        if (this.notificationProps.actions.length > 0) {
            this._createActionButtons();
        }
        
        // 创建进度条
        if (this.notificationProps.showProgress && this.notificationProps.duration > 0) {
            this._createProgressBar();
        }
        
        this.container = this.elements.notification;
    }

    /**
     * 创建通知图标 - 图标区域
     * @private
     */
    _createNotificationIcon() {
        this.elements.icon = document.createElement('div');
        this.elements.icon.className = 'smartoffice-notification-icon';
        this.elements.icon.innerHTML = this.notificationProps.icon;
        
        this.elements.notification.appendChild(this.elements.icon);
    }

    /**
     * 创建通知内容 - 标题和消息内容
     * @private
     */
    _createNotificationContent() {
        this.elements.content = document.createElement('div');
        this.elements.content.className = 'smartoffice-notification-content';
        
        // 标题
        if (this.notificationProps.title) {
            this.elements.title = document.createElement('div');
            this.elements.title.className = 'smartoffice-notification-title';
            this.elements.title.textContent = this.notificationProps.title;
            this.elements.content.appendChild(this.elements.title);
        }
        
        // 消息内容
        if (this.notificationProps.message) {
            this.elements.message = document.createElement('div');
            this.elements.message.className = 'smartoffice-notification-message';
            this.elements.message.textContent = this.notificationProps.message;
            this.elements.content.appendChild(this.elements.message);
        }
        
        this.elements.notification.appendChild(this.elements.content);
    }

    /**
     * 创建关闭按钮 - 手动关闭按钮
     * @private
     */
    _createCloseButton() {
        this.elements.closeButton = document.createElement('button');
        this.elements.closeButton.className = 'smartoffice-notification-close';
        this.elements.closeButton.innerHTML = '×';
        this.elements.closeButton.setAttribute('aria-label', '关闭通知');
        this.elements.closeButton.type = 'button';
        
        this.elements.notification.appendChild(this.elements.closeButton);
    }

    /**
     * 创建操作按钮 - 自定义操作按钮
     * @private
     */
    _createActionButtons() {
        this.elements.actions = document.createElement('div');
        this.elements.actions.className = 'smartoffice-notification-actions';
        
        this.notificationProps.actions.forEach((action, index) => {
            const button = document.createElement('button');
            button.className = `smartoffice-btn smartoffice-btn-${action.variant || 'secondary'} smartoffice-btn-sm`;
            button.textContent = action.text || `操作${index + 1}`;
            button.type = 'button';
            button.dataset.actionIndex = index;
            
            this.elements.actions.appendChild(button);
        });
        
        this.elements.notification.appendChild(this.elements.actions);
    }

    /**
     * 创建进度条 - 自动关闭进度指示
     * @private
     */
    _createProgressBar() {
        this.elements.progressBar = document.createElement('div');
        this.elements.progressBar.className = 'smartoffice-notification-progress';
        
        this.elements.progressFill = document.createElement('div');
        this.elements.progressFill.className = 'smartoffice-notification-progress-fill';
        
        this.elements.progressBar.appendChild(this.elements.progressFill);
        this.elements.notification.appendChild(this.elements.progressBar);
    }

    /**
     * 应用通知样式 - 设置CSS样式
     * @private
     */
    _applyNotificationStyles() {
        // 基础样式
        Object.assign(this.elements.notification.style, {
            position: 'fixed',
            zIndex: this.notificationProps.zIndex.toString(),
            backgroundColor: this._getTypeColor(),
            color: 'white',
            borderRadius: '6px',
            padding: '16px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            display: 'flex',
            alignItems: 'flex-start',
            gap: '12px',
            maxWidth: '400px',
            minWidth: '300px',
            margin: '8px',
            opacity: '0',
            transform: this._getInitialTransform(),
            transition: this.notificationProps.animation ? 'all 0.3s ease' : 'none'
        });
        
        // 图标样式
        if (this.elements.icon) {
            Object.assign(this.elements.icon.style, {
                fontSize: '18px',
                lineHeight: '1',
                flexShrink: '0',
                marginTop: '2px'
            });
        }
        
        // 内容样式
        if (this.elements.content) {
            Object.assign(this.elements.content.style, {
                flex: '1',
                lineHeight: '1.4'
            });
        }
        
        // 标题样式
        if (this.elements.title) {
            Object.assign(this.elements.title.style, {
                fontWeight: 'bold',
                fontSize: '14px',
                marginBottom: '4px'
            });
        }
        
        // 消息样式
        if (this.elements.message) {
            Object.assign(this.elements.message.style, {
                fontSize: '13px',
                opacity: '0.9'
            });
        }
        
        // 关闭按钮样式
        if (this.elements.closeButton) {
            Object.assign(this.elements.closeButton.style, {
                background: 'none',
                border: 'none',
                color: 'white',
                fontSize: '18px',
                cursor: 'pointer',
                padding: '0',
                width: '20px',
                height: '20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '3px',
                opacity: '0.8',
                flexShrink: '0'
            });
        }
        
        // 进度条样式
        if (this.elements.progressBar) {
            Object.assign(this.elements.progressBar.style, {
                position: 'absolute',
                bottom: '0',
                left: '0',
                right: '0',
                height: '3px',
                backgroundColor: 'rgba(255, 255, 255, 0.3)',
                borderRadius: '0 0 6px 6px',
                overflow: 'hidden'
            });
            
            Object.assign(this.elements.progressFill.style, {
                width: '100%',
                height: '100%',
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
                transition: 'width linear'
            });
        }
        
        // 操作按钮样式
        if (this.elements.actions) {
            Object.assign(this.elements.actions.style, {
                marginTop: '8px',
                display: 'flex',
                gap: '8px'
            });
        }
        
        // 设置位置
        this._setPosition();
    }

    /**
     * 获取类型颜色 - 根据通知类型获取背景色
     * @returns {string} 颜色值
     * @private
     */
    _getTypeColor() {
        const colorMap = {
            success: '#28a745',
            warning: '#ffc107',
            error: '#dc3545',
            info: '#17a2b8'
        };
        return colorMap[this.notificationProps.type] || colorMap.info;
    }

    /**
     * 获取初始变换 - 根据位置获取初始动画变换
     * @returns {string} CSS变换值
     * @private
     */
    _getInitialTransform() {
        const position = this.notificationProps.position;
        if (position.includes('top')) {
            return 'translateY(-100px)';
        } else if (position.includes('bottom')) {
            return 'translateY(100px)';
        }
        return 'scale(0.8)';
    }

    /**
     * 设置位置 - 根据配置设置通知位置
     * @private
     */
    _setPosition() {
        const style = this.elements.notification.style;
        
        switch (this.notificationProps.position) {
            case 'top-left':
                style.top = '20px';
                style.left = '20px';
                break;
            case 'top-right':
                style.top = '20px';
                style.right = '20px';
                break;
            case 'bottom-left':
                style.bottom = '20px';
                style.left = '20px';
                break;
            case 'bottom-right':
                style.bottom = '20px';
                style.right = '20px';
                break;
            case 'center':
                style.top = '50%';
                style.left = '50%';
                style.transform = 'translate(-50%, -50%)';
                break;
            default:
                style.top = '20px';
                style.right = '20px';
        }
    }

    /**
     * 绑定通知事件 - 设置所有事件监听器
     * @private
     */
    _bindNotificationEvents() {
        // 关闭按钮事件
        if (this.elements.closeButton) {
            this.elements.closeButton.addEventListener('click', this.handlers.closeClick);
        }
        
        // 鼠标悬停事件（暂停自动关闭）
        if (!this.notificationProps.persistent && this.notificationProps.duration > 0) {
            this.elements.notification.addEventListener('mouseenter', this.handlers.mouseEnter);
            this.elements.notification.addEventListener('mouseleave', this.handlers.mouseLeave);
        }
        
        // 操作按钮事件
        if (this.elements.actions) {
            this.elements.actions.addEventListener('click', this.handlers.actionClick);
        }
    }

    // #region 公开方法
    /**
     * 显示通知 - 显示并启动自动关闭计时器
     */
    show() {
        if (this.notificationState.visible || this.notificationState.showing) {
            return;
        }
        
        this.notificationState.showing = true;
        
        // 发射显示前事件
        const beforeShowEvent = {
            component: this,
            cancel: false
        };
        this.emit(UI_EVENTS.NOTIFICATION_BEFORE_SHOW, beforeShowEvent);
        
        if (beforeShowEvent.cancel) {
            this.notificationState.showing = false;
            return;
        }
        
        // 添加到DOM
        document.body.appendChild(this.container);
        
        // 播放声音
        if (this.notificationProps.sound) {
            this._playSound();
        }
        
        // 显示动画
        if (this.notificationProps.animation) {
            this._showWithAnimation();
        } else {
            this._showInstantly();
        }
        
        // 启动自动关闭
        if (!this.notificationProps.persistent && this.notificationProps.duration > 0) {
            this._startAutoClose();
        }
    }

    /**
     * 动画显示 - 带动画效果的显示
     * @private
     */
    _showWithAnimation() {
        // 强制重绘
        this.elements.notification.offsetHeight;
        
        // 动画到最终状态
        this.elements.notification.style.opacity = '1';
        this.elements.notification.style.transform = this.notificationProps.position === 'center' ? 
            'translate(-50%, -50%) scale(1)' : 'translateY(0)';
        
        // 动画完成
        setTimeout(() => {
            this.notificationState.showing = false;
            this.notificationState.visible = true;
            
            this.emit(UI_EVENTS.NOTIFICATION_SHOWN, { component: this });
        }, 300);
    }

    /**
     * 立即显示 - 无动画效果的显示
     * @private
     */
    _showInstantly() {
        this.elements.notification.style.opacity = '1';
        this.elements.notification.style.transform = this.notificationProps.position === 'center' ? 
            'translate(-50%, -50%)' : 'translateY(0)';
        
        this.notificationState.showing = false;
        this.notificationState.visible = true;
        
        this.emit(UI_EVENTS.NOTIFICATION_SHOWN, { component: this });
    }

    /**
     * 关闭通知 - 隐藏并可选择性销毁通知
     * @param {string} reason - 关闭原因
     */
    hide(reason = 'manual') {
        if (!this.notificationState.visible || this.notificationState.hiding) {
            return;
        }
        
        this.notificationState.hiding = true;
        
        // 发射关闭前事件
        const beforeHideEvent = {
            component: this,
            reason,
            cancel: false
        };
        this.emit(UI_EVENTS.NOTIFICATION_BEFORE_HIDE, beforeHideEvent);
        
        if (beforeHideEvent.cancel) {
            this.notificationState.hiding = false;
            return;
        }
        
        // 停止计时器
        this._stopAutoClose();
        
        // 关闭动画
        if (this.notificationProps.animation) {
            this._hideWithAnimation(reason);
        } else {
            this._hideInstantly(reason);
        }
    }

    /**
     * 动画关闭 - 带动画效果的关闭
     * @param {string} reason - 关闭原因
     * @private
     */
    _hideWithAnimation(reason) {
        this.elements.notification.style.opacity = '0';
        this.elements.notification.style.transform = this._getInitialTransform();
        
        setTimeout(() => {
            this._hideInstantly(reason);
        }, 300);
    }

    /**
     * 立即关闭 - 无动画效果的关闭
     * @param {string} reason - 关闭原因
     * @private
     */
    _hideInstantly(reason) {
        if (this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
        
        this.notificationState.hiding = false;
        this.notificationState.visible = false;
        
        // 发射关闭事件
        this.emit(UI_EVENTS.NOTIFICATION_HIDDEN, { 
            component: this, 
            reason 
        });
        
        // 执行关闭回调
        if (this.notificationProps.onClose) {
            this.notificationProps.onClose(reason);
        }
        
        // 自动销毁
        setTimeout(() => {
            this.destroy();
        }, 100);
    }

    /**
     * 启动自动关闭 - 启动自动关闭计时器
     * @private
     */
    _startAutoClose() {
        this.notificationState.timeLeft = this.notificationProps.duration;
        
        // 自动关闭计时器
        this.notificationState.autoCloseTimer = setTimeout(() => {
            this.hide('auto');
        }, this.notificationProps.duration);
        
        // 进度条动画
        if (this.elements.progressFill) {
            this.elements.progressFill.style.transitionDuration = this.notificationProps.duration + 'ms';
            this.elements.progressFill.style.width = '0%';
        }
    }

    /**
     * 停止自动关闭 - 停止自动关闭计时器
     * @private
     */
    _stopAutoClose() {
        if (this.notificationState.autoCloseTimer) {
            clearTimeout(this.notificationState.autoCloseTimer);
            this.notificationState.autoCloseTimer = null;
        }
        
        if (this.notificationState.progressTimer) {
            clearInterval(this.notificationState.progressTimer);
            this.notificationState.progressTimer = null;
        }
    }

    /**
     * 暂停自动关闭 - 暂停自动关闭计时器
     * @private
     */
    _pauseAutoClose() {
        if (this.notificationState.autoCloseTimer) {
            clearTimeout(this.notificationState.autoCloseTimer);
            this.notificationState.pausedTime = Date.now();
            
            // 暂停进度条动画
            if (this.elements.progressFill) {
                const computedStyle = getComputedStyle(this.elements.progressFill);
                this.elements.progressFill.style.width = computedStyle.width;
                this.elements.progressFill.style.transitionDuration = '0s';
            }
        }
    }

    /**
     * 恢复自动关闭 - 恢复自动关闭计时器
     * @private
     */
    _resumeAutoClose() {
        if (this.notificationState.pausedTime && !this.notificationState.autoCloseTimer) {
            const elapsedTime = Date.now() - this.notificationState.pausedTime;
            this.notificationState.timeLeft = Math.max(0, this.notificationState.timeLeft - elapsedTime);
            
            if (this.notificationState.timeLeft > 0) {
                // 重新启动计时器
                this.notificationState.autoCloseTimer = setTimeout(() => {
                    this.hide('auto');
                }, this.notificationState.timeLeft);
                
                // 恢复进度条动画
                if (this.elements.progressFill) {
                    this.elements.progressFill.style.transitionDuration = this.notificationState.timeLeft + 'ms';
                    this.elements.progressFill.style.width = '0%';
                }
            } else {
                this.hide('auto');
            }
            
            this.notificationState.pausedTime = null;
        }
    }

    /**
     * 播放声音 - 播放通知声音
     * @private
     */
    _playSound() {
        try {
            // 创建音频上下文（如果需要）
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            // 设置音频参数
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.1);
        } catch (error) {
            console.warn('无法播放通知声音:', error);
        }
    }

    /**
     * 更新内容 - 动态更新通知内容
     * @param {Object} content - 新内容
     */
    updateContent(content) {
        if (content.title && this.elements.title) {
            this.notificationProps.title = content.title;
            this.elements.title.textContent = content.title;
        }
        
        if (content.message && this.elements.message) {
            this.notificationProps.message = content.message;
            this.elements.message.textContent = content.message;
        }
        
        this.emit(UI_EVENTS.NOTIFICATION_CONTENT_UPDATED, { 
            component: this, 
            content 
        });
    }

    /**
     * 更新类型 - 动态更新通知类型
     * @param {string} type - 新类型
     */
    updateType(type) {
        // 移除旧类型样式
        this.elements.notification.classList.remove(`smartoffice-notification-${this.notificationProps.type}`);
        
        // 应用新类型
        this.notificationProps.type = type;
        this.elements.notification.classList.add(`smartoffice-notification-${type}`);
        this.elements.notification.style.backgroundColor = this._getTypeColor();
        
        // 更新图标
        this.notificationProps.icon = this._getDefaultIcon(type);
        this.elements.icon.innerHTML = this.notificationProps.icon;
        
        this.emit(UI_EVENTS.NOTIFICATION_TYPE_UPDATED, { 
            component: this, 
            type 
        });
    }
    // #endregion

    // #region 事件处理方法
    /**
     * 处理关闭按钮点击 - 关闭按钮事件
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleCloseClick(e) {
        e.preventDefault();
        this.hide('button');
    }

    /**
     * 处理鼠标进入 - 暂停自动关闭
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleMouseEnter(e) {
        this._pauseAutoClose();
        this.emit(UI_EVENTS.NOTIFICATION_PAUSED, { component: this });
    }

    /**
     * 处理鼠标离开 - 恢复自动关闭
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleMouseLeave(e) {
        this._resumeAutoClose();
        this.emit(UI_EVENTS.NOTIFICATION_RESUMED, { component: this });
    }

    /**
     * 处理操作点击 - 操作按钮事件
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleActionClick(e) {
        if (e.target.tagName === 'BUTTON') {
            const actionIndex = parseInt(e.target.dataset.actionIndex);
            const action = this.notificationProps.actions[actionIndex];
            
            if (action && action.handler) {
                const result = action.handler(e, this);
                
                // 发射操作事件
                this.emit(UI_EVENTS.NOTIFICATION_ACTION_CLICKED, { 
                    component: this, 
                    action, 
                    actionIndex,
                    result
                });
                
                // 执行全局操作回调
                if (this.notificationProps.onAction) {
                    this.notificationProps.onAction(action, actionIndex, result);
                }
                
                // 如果操作没有返回false，自动关闭通知
                if (result !== false && action.autoClose !== false) {
                    this.hide('action');
                }
            }
        }
    }
    // #endregion

    // #region 生命周期方法
    /**
     * 组件销毁 - 清理资源
     */
    destroy() {
        // 如果正在显示，先隐藏
        if (this.notificationState.visible) {
            this.hide('destroy');
            return; // 会在隐藏后自动销毁
        }
        
        // 停止所有计时器
        this._stopAutoClose();
        
        // 调用父类销毁方法
        super.destroy();
        
        this.emit(UI_EVENTS.NOTIFICATION_DESTROYED, { component: this });
    }
    // #endregion
}

/**
 * @class NotificationManager - 通知管理器
 * @description 管理多个通知的创建、显示和堆叠
 */
export class NotificationManager {
    constructor() {
        this.notifications = new Map(); // 通知实例映射
        this.containers = new Map(); // 位置容器映射
        this.zIndexCounter = 5000; // Z-index计数器
        this.maxNotifications = 5; // 最大通知数量
        this.positions = ['top-left', 'top-right', 'bottom-left', 'bottom-right']; // 支持的位置
    }

    /**
     * 创建通知 - 创建新的通知实例
     * @param {Object} config - 通知配置
     * @returns {Notification} 通知实例
     */
    createNotification(config = {}) {
        const notification = new Notification({
            ...config,
            zIndex: this.zIndexCounter++
        });
        
        this.notifications.set(notification.id, notification);
        
        // 监听通知事件
        notification.on(UI_EVENTS.NOTIFICATION_SHOWN, () => {
            this._addToContainer(notification);
        });
        
        notification.on(UI_EVENTS.NOTIFICATION_HIDDEN, () => {
            this._removeFromContainer(notification);
        });
        
        notification.on(UI_EVENTS.NOTIFICATION_DESTROYED, () => {
            this.notifications.delete(notification.id);
        });
        
        return notification;
    }

    /**
     * 显示成功通知 - 创建并显示成功通知
     * @param {string|Object} options - 通知选项或消息文本
     * @returns {Notification} 通知实例
     */
    success(options) {
        const config = typeof options === 'string' ? { message: options } : options;
        const notification = this.createNotification({
            ...config,
            type: 'success'
        });
        notification.show();
        return notification;
    }

    /**
     * 显示警告通知 - 创建并显示警告通知
     * @param {string|Object} options - 通知选项或消息文本
     * @returns {Notification} 通知实例
     */
    warning(options) {
        const config = typeof options === 'string' ? { message: options } : options;
        const notification = this.createNotification({
            ...config,
            type: 'warning'
        });
        notification.show();
        return notification;
    }

    /**
     * 显示错误通知 - 创建并显示错误通知
     * @param {string|Object} options - 通知选项或消息文本
     * @returns {Notification} 通知实例
     */
    error(options) {
        const config = typeof options === 'string' ? { message: options } : options;
        const notification = this.createNotification({
            ...config,
            type: 'error',
            duration: 6000 // 错误通知显示更长时间
        });
        notification.show();
        return notification;
    }

    /**
     * 显示信息通知 - 创建并显示信息通知
     * @param {string|Object} options - 通知选项或消息文本
     * @returns {Notification} 通知实例
     */
    info(options) {
        const config = typeof options === 'string' ? { message: options } : options;
        const notification = this.createNotification({
            ...config,
            type: 'info'
        });
        notification.show();
        return notification;
    }

    /**
     * 添加到容器 - 将通知添加到位置容器
     * @param {Notification} notification - 通知实例
     * @private
     */
    _addToContainer(notification) {
        const position = notification.notificationProps.position;
        
        if (!this.containers.has(position)) {
            this._createContainer(position);
        }
        
        const container = this.containers.get(position);
        const notificationsInPosition = this._getNotificationsByPosition(position);
        
        // 限制通知数量
        while (notificationsInPosition.length >= this.maxNotifications) {
            const oldestNotification = notificationsInPosition.shift();
            oldestNotification.hide('overflow');
        }
        
        // 重新排列位置
        this._arrangeNotifications(position);
    }

    /**
     * 从容器移除 - 将通知从位置容器移除
     * @param {Notification} notification - 通知实例
     * @private
     */
    _removeFromContainer(notification) {
        const position = notification.notificationProps.position;
        this._arrangeNotifications(position);
    }

    /**
     * 创建容器 - 为指定位置创建通知容器
     * @param {string} position - 位置
     * @private
     */
    _createContainer(position) {
        const container = document.createElement('div');
        container.className = `smartoffice-notification-container smartoffice-notification-container-${position}`;
        
        // 设置容器样式
        Object.assign(container.style, {
            position: 'fixed',
            zIndex: '4999',
            pointerEvents: 'none',
            display: 'flex',
            flexDirection: 'column',
            gap: '8px'
        });
        
        // 设置位置
        this._setContainerPosition(container, position);
        
        document.body.appendChild(container);
        this.containers.set(position, container);
    }

    /**
     * 设置容器位置 - 设置通知容器的位置
     * @param {HTMLElement} container - 容器元素
     * @param {string} position - 位置
     * @private
     */
    _setContainerPosition(container, position) {
        switch (position) {
            case 'top-left':
                container.style.top = '20px';
                container.style.left = '20px';
                break;
            case 'top-right':
                container.style.top = '20px';
                container.style.right = '20px';
                break;
            case 'bottom-left':
                container.style.bottom = '20px';
                container.style.left = '20px';
                container.style.flexDirection = 'column-reverse';
                break;
            case 'bottom-right':
                container.style.bottom = '20px';
                container.style.right = '20px';
                container.style.flexDirection = 'column-reverse';
                break;
        }
    }

    /**
     * 排列通知 - 重新排列指定位置的通知
     * @param {string} position - 位置
     * @private
     */
    _arrangeNotifications(position) {
        const notifications = this._getNotificationsByPosition(position);
        const container = this.containers.get(position);
        
        if (!container) return;
        
        notifications.forEach((notification, index) => {
            const offset = index * 80; // 每个通知间隔80px
            
            if (position.includes('top')) {
                notification.elements.notification.style.top = (20 + offset) + 'px';
            } else {
                notification.elements.notification.style.bottom = (20 + offset) + 'px';
            }
        });
    }

    /**
     * 获取指定位置的通知 - 获取在指定位置的所有通知
     * @param {string} position - 位置
     * @returns {Array<Notification>} 通知数组
     * @private
     */
    _getNotificationsByPosition(position) {
        return Array.from(this.notifications.values())
            .filter(notification => 
                notification.notificationProps.position === position && 
                notification.notificationState.visible
            )
            .sort((a, b) => a.id.localeCompare(b.id)); // 按ID排序
    }

    /**
     * 关闭所有通知 - 关闭当前所有显示的通知
     * @param {string} position - 可选，只关闭指定位置的通知
     */
    closeAll(position = null) {
        for (const notification of this.notifications.values()) {
            if (notification.notificationState.visible) {
                if (!position || notification.notificationProps.position === position) {
                    notification.hide('manager_close_all');
                }
            }
        }
    }

    /**
     * 设置最大通知数 - 设置每个位置的最大通知数量
     * @param {number} max - 最大数量
     */
    setMaxNotifications(max) {
        this.maxNotifications = Math.max(1, max);
    }

    /**
     * 获取所有通知 - 获取所有通知实例
     * @returns {Array<Notification>} 通知实例数组
     */
    getAllNotifications() {
        return Array.from(this.notifications.values());
    }

    /**
     * 获取可见通知 - 获取当前可见的通知
     * @param {string} position - 可选，只获取指定位置的通知
     * @returns {Array<Notification>} 可见通知数组
     */
    getVisibleNotifications(position = null) {
        return this.getAllNotifications().filter(notification => {
            const isVisible = notification.notificationState.visible;
            const matchesPosition = !position || notification.notificationProps.position === position;
            return isVisible && matchesPosition;
        });
    }

    /**
     * 销毁所有通知 - 销毁所有通知实例
     */
    destroyAll() {
        for (const notification of this.notifications.values()) {
            notification.destroy();
        }
        this.notifications.clear();
        
        // 清理容器
        for (const container of this.containers.values()) {
            if (container.parentNode) {
                container.parentNode.removeChild(container);
            }
        }
        this.containers.clear();
    }
}
// #endregion

// #region 工厂函数
/**
 * 创建通知 - 便捷函数创建通知实例
 * @param {Object} config - 通知配置
 * @returns {Notification} 通知实例
 */
export function createNotification(config = {}) {
    return new Notification(config);
}

/**
 * 创建成功通知 - 便捷函数创建成功通知
 * @param {string} message - 通知消息
 * @param {Object} options - 额外选项
 * @returns {Notification} 通知实例
 */
export function createSuccessNotification(message, options = {}) {
    const manager = new NotificationManager();
    return manager.success({ ...options, message });
}

/**
 * 创建警告通知 - 便捷函数创建警告通知
 * @param {string} message - 通知消息
 * @param {Object} options - 额外选项
 * @returns {Notification} 通知实例
 */
export function createWarningNotification(message, options = {}) {
    const manager = new NotificationManager();
    return manager.warning({ ...options, message });
}

/**
 * 创建错误通知 - 便捷函数创建错误通知
 * @param {string} message - 通知消息
 * @param {Object} options - 额外选项
 * @returns {Notification} 通知实例
 */
export function createErrorNotification(message, options = {}) {
    const manager = new NotificationManager();
    return manager.error({ ...options, message });
}

/**
 * 创建信息通知 - 便捷函数创建信息通知
 * @param {string} message - 通知消息
 * @param {Object} options - 额外选项
 * @returns {Notification} 通知实例
 */
export function createInfoNotification(message, options = {}) {
    const manager = new NotificationManager();
    return manager.info({ ...options, message });
}
// #endregion

// #region 默认实例导出
/**
 * 默认通知管理器实例
 */
export const notificationManager = new NotificationManager();
// #endregion 