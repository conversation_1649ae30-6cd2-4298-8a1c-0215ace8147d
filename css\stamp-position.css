/* @file 印章定位样式 */
/* @project SmartOffice */
/* 此文件包含印章的绝对定位相关样式 */

/* #region 印章绝对定位样式 */
.company-stamp {
    /* 改回绝对定位，实现固定位置需求 */
    position: absolute !important;
    /* 基于A4纸张的固定尺寸和位置 (A4: 210mm×297mm) */
    width: 80px !important; /* 印章容器宽度 */
    height: 80px !important; /* 印章容器高度 */
    /* 计算基于A4纸张的80%位置 */
    top: calc(var(--a4-height-px) * 0.8 - 80px) !important; /* A4高度的80%位置减去印章高度 */
    /* 计算基于A4纸张宽度的右侧位置 */
    right: 40px !important; /* 距离右边40px */
    /* 移除内边距和外边距，确保精确定位 */
    padding: 0 !important;
    margin: 0 !important;
    /* 设置最高层级，确保显示在所有内容之上 */
    z-index: 10000 !important;
    /* 允许点击穿透，不影响下方元素的交互 */
    pointer-events: none !important;
    /* 显示为块级元素，占据一行 */
    display: block !important;
    text-align: right; /* 用于内部 block 图片的靠右对齐 */
    /* 确保可见 */
    visibility: visible !important;
    /* 确保打印时不被分页 */
    page-break-inside: avoid !important;
}

/* 印章图片样式 */
.company-stamp img,
img.stamp-image,
img.company-stamp-gmh,
img.company-stamp-smw,
img.driver-agreement-stamp-image {
    /* 使用块级显示 */
    display: block !important;
    /* 印章图片设置为相对定位，在容器内居中 */
    position: relative !important;
    top: 0 !important;
    right: 0 !important;
    /* 设置固定大小 */
    width: 80px !important;
    height: 80px !important;
    max-width: 80px !important;
    max-height: 80px !important;
    /* 边框圆角，使印章呈圆形 */
    border-radius: 50% !important;
    /* 确保图片内容适应容器 */
    object-fit: contain !important;
    /* 确保印章显示在其他元素之上 */
    z-index: 10000 !important;
    /* 透明度设置，使印章看起来更真实 */
    opacity: 0.85 !important;
    /* 添加旋转效果，增加真实感 */
    transform: rotate(-5deg) !important;
    /* 移除外边距，提高定位精确度 */
    margin: 0 auto !important;
    /* 显式指定背景色，防止透明背景导致不可见 */
    background-color: transparent !important;
    /* 手动指定可见性 */
    visibility: visible !important;
}



/* 导出文档中的印章样式确保一致 */
.pdf-export-container .company-stamp,
.image-export-container .company-stamp {
    /* 确保导出时印章位置一致 */
    position: absolute !important;
    top: 158mm !important; /* 与主设置保持一致 */
    right: 22mm !important; /* 与主设置保持一致 */
    width: 50mm !important;
    height: 30mm !important;
    z-index: 10000 !important;
}

.pdf-export-container .company-stamp img,
.pdf-export-container img.stamp-image,
.pdf-export-container img.company-stamp-gmh,
.pdf-export-container img.company-stamp-smw,
.pdf-export-container img.driver-agreement-stamp-image,
.image-export-container .company-stamp img,
.image-export-container img.stamp-image,
.image-export-container img.company-stamp-gmh,
.image-export-container img.company-stamp-smw,
.image-export-container img.driver-agreement-stamp-image {
    position: absolute !important;
    top: 2.5mm !important;
    right: 12.5mm !important;
    width: 25mm !important;
    height: 25mm !important;
    border-radius: 50% !important;
    object-fit: contain !important;
    z-index: 10000 !important;
    opacity: 0.85 !important;
    /* 确保可见 */
    visibility: visible !important;
    display: block !important;
}

/* 响应式调整 - 仅调整预览时的显示，打印和导出不受影响 */
@media (max-width: 768px) {
    /* 预览状态下的响应式调整 */
    #document-preview .company-stamp {
        /* 在预览中保持相对定位，以适应不同屏幕大小 */
        position: relative !important;
        top: auto !important;
        right: auto !important;
        margin-left: auto !important;
        margin-right: 20px !important;
        text-align: right !important;
    }
    
    #document-preview .company-stamp img,
    #document-preview img.stamp-image,
    #document-preview img.company-stamp-gmh,
    #document-preview img.company-stamp-smw,
    #document-preview img.driver-agreement-stamp-image {
        position: relative !important;
        top: auto !important;
        right: auto !important;
        width: 90px !important;
        height: 90px !important;
        margin-left: auto !important;
    }
}

@media (max-width: 480px) {
    #document-preview .company-stamp img,
    #document-preview img.stamp-image,
    #document-preview img.company-stamp-gmh,
    #document-preview img.company-stamp-smw,
    #document-preview img.driver-agreement-stamp-image {
        width: 70px !important;
        height: 70px !important;
    }
}
/* #endregion 印章绝对定位样式 */

/* 确保 #document-preview-content 在打印时能正确分页 */
@media print {
    #document-preview-content {
        overflow: visible !important; /* 允许内容溢出并分页 */
    }

    .company-stamp {
        /* 打印时固定在A4页面特定位置 */
        position: absolute !important;
        top: 208mm !important; /* A4高度297mm的70% */
        right: 42mm !important; /* A4宽度210mm的20% */
        page-break-inside: avoid !important; /* 尽量避免印章被分割到两页 */
        min-height: 30mm; /* 确保打印时印章区域有高度 */
    }

    .document-footer-note,
    .electronic-generated-notice {
        page-break-inside: avoid !important; /* 尽量避免这些元素被分割 */
    }
    
    .document-footer { /* 页脚区域 */
        page-break-before: auto; /* 根据内容自动决定是否在新的一页开始 */
        position: fixed !important; /* 打印时页脚固定 */
        bottom: 0 !important;
    }
}

/* 移除重复的样式定义，保持文件简洁 */ 

/* End of migrated rules from driver-agreement-template.js - Now removed */