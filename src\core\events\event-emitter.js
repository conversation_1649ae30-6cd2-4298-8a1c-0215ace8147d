/**
 * @file 事件发射器
 * @description 提供事件发布订阅功能，支持事件的注册、触发和取消订阅
 */

// #region 导入依赖模块
import { getLogger } from '../utils/logger.js';
// #endregion

/**
 * @class EventEmitter
 * @description 事件发射器类，实现观察者模式，提供事件的发布订阅功能
 */
export class EventEmitter {
    /**
     * 创建事件发射器实例
     */
    constructor() {
        // 初始化日志记录器
        this.logger = getLogger();
        this.logger.startPerformanceMark('event_emitter_construction', 'EventEmitter', 'constructor');
        this.logger.debug('EventEmitter', 'constructor', '开始构造事件发射器');
        
        /** @private {Map<string, Set<Function>>} 事件监听器映射表 */
        this._listeners = new Map();
        
        /** @private {Map<string, Set<Function>>} 一次性事件监听器映射表 */
        this._onceListeners = new Map();
        
        /** @private {boolean} 是否启用调试模式 */
        this._debug = false;
        
        /** @private {Object} 事件统计信息 */
        this._stats = {
            totalEvents: 0,
            totalListeners: 0,
            eventCounts: new Map(),
            lastEvent: null
        };
        
        const constructionDuration = this.logger.endPerformanceMark('event_emitter_construction', 'EventEmitter', 'constructor');
        this.logger.info('EventEmitter', 'constructor', '✅ 事件发射器构造完成', {
            duration: `${constructionDuration?.toFixed(2)}ms`
        });
        
        // 记录构造统计
        this.logger.incrementCounter('event_emitter_instances', 'EventEmitter');
    }

    /**
     * 更新事件统计信息
     * @param {string} eventType - 事件类型
     * @param {number} listenerCount - 监听器数量
     * @param {number} duration - 执行时长
     * @private
     */
    _updateEventStats(eventType, listenerCount, duration) {
        this._stats.totalEvents++;
        this._stats.lastEvent = {
            type: eventType,
            timestamp: Date.now(),
            listenerCount,
            duration
        };
        
        // 更新事件计数
        const currentCount = this._stats.eventCounts.get(eventType) || 0;
        this._stats.eventCounts.set(eventType, currentCount + 1);
    }

    /**
     * 获取事件统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this._stats,
            totalListeners: this._getTotalListenerCount(),
            eventTypes: Array.from(this._stats.eventCounts.keys()),
            topEvents: this._getTopEvents()
        };
    }

    /**
     * 获取总监听器数量
     * @returns {number} 总监听器数量
     * @private
     */
    _getTotalListenerCount() {
        let total = 0;
        for (const listeners of this._listeners.values()) {
            total += listeners.size;
        }
        for (const listeners of this._onceListeners.values()) {
            total += listeners.size;
        }
        return total;
    }

    /**
     * 获取最频繁的事件
     * @param {number} limit - 限制数量
     * @returns {Array} 事件列表
     * @private
     */
    _getTopEvents(limit = 10) {
        return Array.from(this._stats.eventCounts.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, limit)
            .map(([type, count]) => ({ type, count }));
    }

    /**
     * 注册事件监听器
     * @param {string} eventType - 事件类型
     * @param {Function} listener - 事件监听器函数
     * @returns {Function} 取消订阅函数
     */
    on(eventType, listener) {
        if (typeof eventType !== 'string') {
            throw new Error('事件类型必须是字符串');
        }
        
        if (typeof listener !== 'function') {
            throw new Error('事件监听器必须是函数');
        }

        // 获取或创建事件监听器集合
        if (!this._listeners.has(eventType)) {
            this._listeners.set(eventType, new Set());
        }
        
        const listeners = this._listeners.get(eventType);
        listeners.add(listener);

        if (this._debug) {
            console.log(`[EventEmitter] 注册事件监听器: ${eventType}`);
        }

        // 返回取消订阅函数
        return () => this.off(eventType, listener);
    }

    /**
     * 注册一次性事件监听器
     * @param {string} eventType - 事件类型
     * @param {Function} listener - 事件监听器函数
     * @returns {Function} 取消订阅函数
     */
    once(eventType, listener) {
        if (typeof eventType !== 'string') {
            throw new Error('事件类型必须是字符串');
        }
        
        if (typeof listener !== 'function') {
            throw new Error('事件监听器必须是函数');
        }

        // 获取或创建一次性事件监听器集合
        if (!this._onceListeners.has(eventType)) {
            this._onceListeners.set(eventType, new Set());
        }
        
        const onceListeners = this._onceListeners.get(eventType);
        onceListeners.add(listener);

        if (this._debug) {
            console.log(`[EventEmitter] 注册一次性事件监听器: ${eventType}`);
        }

        // 返回取消订阅函数
        return () => this.off(eventType, listener);
    }

    /**
     * 取消事件监听器
     * @param {string} eventType - 事件类型
     * @param {Function} listener - 事件监听器函数
     * @returns {boolean} 是否成功取消订阅
     */
    off(eventType, listener) {
        let removed = false;

        // 从普通监听器中移除
        if (this._listeners.has(eventType)) {
            const listeners = this._listeners.get(eventType);
            removed = listeners.delete(listener);
            
            // 如果集合为空，删除整个映射
            if (listeners.size === 0) {
                this._listeners.delete(eventType);
            }
        }

        // 从一次性监听器中移除
        if (this._onceListeners.has(eventType)) {
            const onceListeners = this._onceListeners.get(eventType);
            removed = onceListeners.delete(listener) || removed;
            
            // 如果集合为空，删除整个映射
            if (onceListeners.size === 0) {
                this._onceListeners.delete(eventType);
            }
        }

        if (this._debug && removed) {
            console.log(`[EventEmitter] 取消事件监听器: ${eventType}`);
        }

        return removed;
    }

    /**
     * 触发事件
     * @param {string} eventType - 事件类型
     * @param {...any} args - 事件参数
     * @returns {boolean} 是否有监听器处理了该事件
     */
    emit(eventType, ...args) {
        if (typeof eventType !== 'string') {
            throw new Error('事件类型必须是字符串');
        }

        const startTime = performance.now();
        let hasListeners = false;
        let listenerCount = 0;

        try {
            // 触发普通监听器
            if (this._listeners.has(eventType)) {
                const listeners = this._listeners.get(eventType);
                for (const listener of listeners) {
                    try {
                        listener(...args);
                        hasListeners = true;
                        listenerCount++;
                    } catch (error) {
                        this.logger.error('EventEmitter', 'emit', `事件监听器执行错误 (${eventType})`, {
                            error: error.message,
                            stack: error.stack
                        });
                    }
                }
            }

            // 触发一次性监听器
            if (this._onceListeners.has(eventType)) {
                const onceListeners = this._onceListeners.get(eventType);
                for (const listener of onceListeners) {
                    try {
                        listener(...args);
                        hasListeners = true;
                        listenerCount++;
                    } catch (error) {
                        this.logger.error('EventEmitter', 'emit', `一次性事件监听器执行错误 (${eventType})`, {
                            error: error.message,
                            stack: error.stack
                        });
                    }
                }
                // 清除一次性监听器
                this._onceListeners.delete(eventType);
            }

            // 更新统计信息
            this._updateEventStats(eventType, listenerCount, performance.now() - startTime);

            if (this._debug) {
                this.logger.debug('EventEmitter', 'emit', `触发事件: ${eventType}`, {
                    args,
                    listenerCount,
                    duration: `${(performance.now() - startTime).toFixed(2)}ms`
                });
            }

            return hasListeners;
            
        } catch (error) {
            this.logger.error('EventEmitter', 'emit', `事件触发失败: ${eventType}`, {
                error: error.message,
                args
            });
            throw error;
        }
    }

    /**
     * 移除指定事件类型的所有监听器
     * @param {string} eventType - 事件类型
     * @returns {number} 移除的监听器数量
     */
    removeAllListeners(eventType) {
        let removedCount = 0;

        if (this._listeners.has(eventType)) {
            removedCount += this._listeners.get(eventType).size;
            this._listeners.delete(eventType);
        }

        if (this._onceListeners.has(eventType)) {
            removedCount += this._onceListeners.get(eventType).size;
            this._onceListeners.delete(eventType);
        }

        if (this._debug && removedCount > 0) {
            console.log(`[EventEmitter] 移除所有监听器: ${eventType}, 数量: ${removedCount}`);
        }

        return removedCount;
    }

    /**
     * 获取指定事件类型的监听器数量
     * @param {string} eventType - 事件类型
     * @returns {number} 监听器数量
     */
    listenerCount(eventType) {
        let count = 0;

        if (this._listeners.has(eventType)) {
            count += this._listeners.get(eventType).size;
        }

        if (this._onceListeners.has(eventType)) {
            count += this._onceListeners.get(eventType).size;
        }

        return count;
    }

    /**
     * 获取所有事件类型
     * @returns {string[]} 事件类型数组
     */
    eventNames() {
        const eventTypes = new Set();
        
        for (const eventType of this._listeners.keys()) {
            eventTypes.add(eventType);
        }
        
        for (const eventType of this._onceListeners.keys()) {
            eventTypes.add(eventType);
        }
        
        return Array.from(eventTypes);
    }

    /**
     * 清空所有监听器
     */
    clear() {
        const totalCount = this._listeners.size + this._onceListeners.size;
        
        this._listeners.clear();
        this._onceListeners.clear();

        if (this._debug && totalCount > 0) {
            console.log(`[EventEmitter] 清空所有监听器, 数量: ${totalCount}`);
        }
    }

    /**
     * 清除所有事件监听器
     */
    removeAllListeners() {
        const totalListeners = this._getTotalListenerCount();
        this._listeners.clear();
        this._onceListeners.clear();
        
        this.logger.info('EventEmitter', 'removeAllListeners', '所有事件监听器已清除', {
            removedListeners: totalListeners
        });
        
        if (this._debug) {
            this.logger.debug('EventEmitter', 'removeAllListeners', '调试: 所有事件监听器已清除');
        }
    }

    /**
     * 销毁事件发射器
     */
    destroy() {
        this.removeAllListeners();
        this._stats.eventCounts.clear();
        this._stats = null;
        this.logger.info('EventEmitter', 'destroy', '事件发射器已销毁');
    }

    /**
     * 启用或禁用调试模式
     * @param {boolean} enabled - 是否启用调试模式
     */
    setDebug(enabled) {
        this._debug = Boolean(enabled);
    }

    /**
     * 启用调试模式
     */
    enableDebug() {
        this._debug = true;
        this.logger.info('EventEmitter', 'enableDebug', '调试模式已启用');
    }

    /**
     * 禁用调试模式
     */
    disableDebug() {
        this._debug = false;
        this.logger.info('EventEmitter', 'disableDebug', '调试模式已禁用');
    }

    /**
     * 异步触发事件（下一个事件循环）
     * @param {string} eventType - 事件类型
     * @param {...any} args - 事件参数
     * @returns {Promise<boolean>} 是否有监听器处理了该事件
     */
    async emitAsync(eventType, ...args) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const result = this.emit(eventType, ...args);
                resolve(result);
            }, 0);
        });
    }

    /**
     * 批量触发事件
     * @param {Array<{type: string, args: Array}>} events - 事件列表
     * @returns {Array<boolean>} 每个事件的处理结果
     */
    emitBatch(events) {
        if (!Array.isArray(events)) {
            throw new Error('事件列表必须是数组');
        }

        const results = [];
        for (const event of events) {
            if (!event.type) {
                throw new Error('事件必须包含type属性');
            }
            const result = this.emit(event.type, ...(event.args || []));
            results.push(result);
        }
        return results;
    }

    /**
     * 等待特定事件触发
     * @param {string} eventType - 事件类型
     * @param {number} timeout - 超时时间（毫秒）
     * @returns {Promise<Array>} 事件参数
     */
    waitFor(eventType, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const timer = setTimeout(() => {
                this.off(eventType, listener);
                reject(new Error(`等待事件 ${eventType} 超时`));
            }, timeout);

            const listener = (...args) => {
                clearTimeout(timer);
                resolve(args);
            };

            this.once(eventType, listener);
        });
    }

    /**
     * 获取调试信息
     * @returns {Object} 调试信息对象
     */
    getDebugInfo() {
        const info = {
            totalListeners: 0,
            totalOnceListeners: 0,
            eventTypes: [],
            details: {}
        };

        // 统计普通监听器
        for (const [eventType, listeners] of this._listeners) {
            info.totalListeners += listeners.size;
            info.eventTypes.push(eventType);
            info.details[eventType] = {
                listeners: listeners.size,
                onceListeners: 0
            };
        }

        // 统计一次性监听器
        for (const [eventType, onceListeners] of this._onceListeners) {
            info.totalOnceListeners += onceListeners.size;
            
            if (!info.eventTypes.includes(eventType)) {
                info.eventTypes.push(eventType);
                info.details[eventType] = {
                    listeners: 0,
                    onceListeners: onceListeners.size
                };
            } else {
                info.details[eventType].onceListeners = onceListeners.size;
            }
        }

        return info;
    }
}

// 创建全局事件发射器实例
export const globalEventEmitter = new EventEmitter();
