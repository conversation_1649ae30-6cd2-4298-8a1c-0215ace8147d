/**
 * @file 收据文档模型
 * @description 收据文档的数据模型和业务逻辑
 */

import { BaseDocument } from './base-document.js';
import { Validator, ValidationError } from '../core/utils/validation.js';
import { StringUtils } from '../core/utils/string-utils.js';
import { DateUtils } from '../core/utils/date-utils.js';
import { getCompanyImages } from '../core/utils/image-manager.js';

/**
 * @class ReceiptDocument
 * @description 收据文档类，继承自BaseDocument
 */
export class ReceiptDocument extends BaseDocument {
    /**
     * 创建收据文档实例
     * @param {Object} data - 收据数据
     */
    constructor(data = {}) {
        super({ ...data, type: 'receipt' });
        
        // 收据特有字段
        this.receiptNumber = data.receiptNumber || '';
        this.date = data.date || new Date();
        this.receivedFrom = data.receivedFrom || '';
        this.amount = data.amount || 0;
        this.currency = data.currency || 'RM';
        this.paymentMethod = data.paymentMethod || 'cash'; // cash, card, transfer, cheque
        this.description = data.description || '';
        this.purpose = data.purpose || '';
        this.issuedBy = data.issuedBy || '';
        this.additionalNotes = data.additionalNotes || '';
        
        // 状态字段
        this.isPaid = data.isPaid !== undefined ? data.isPaid : true;
        this.isVoided = data.isVoided || false;
        this.voidReason = data.voidReason || '';
        
        // 初始化数据（包括图片数据）
        this._initializeData(data);
    }

    /**
     * 初始化收据数据
     * @param {Object} data - 原始数据
     * @protected
     */
    _initializeData(data) {
        // 确保日期是Date对象
        if (data.date && !(data.date instanceof Date)) {
            this.date = new Date(data.date);
        }
        
        // 初始化公司信息
        const companyCode = data.companyCode || data.companyInfo?.companyCode || 'gomyhire';
        const companyImages = getCompanyImages(companyCode);
        
        this.companyInfo = {
            name: data.companyInfo?.name || '',
            address: data.companyInfo?.address || '',
            phone: data.companyInfo?.phone || '',
            email: data.companyInfo?.email || '',
            registrationNumber: data.companyInfo?.registrationNumber || '',
            taxNumber: data.companyInfo?.taxNumber || '',
            companyCode: companyCode,
            logo: data.companyInfo?.logo || companyImages.logo || '',
            stamp: data.companyInfo?.stamp || companyImages.stamp || '',
            footer: data.companyInfo?.footer || companyImages.footer || '',
            ...data.companyInfo
        };
        
        // 初始化客户信息
        this.customerInfo = {
            name: data.customerInfo?.name || '',
            address: data.customerInfo?.address || '',
            phone: data.customerInfo?.phone || '',
            email: data.customerInfo?.email || '',
            idNumber: data.customerInfo?.idNumber || '',
            ...data.customerInfo
        };
        
        // 初始化税务信息
        this.taxInfo = {
            taxRate: data.taxInfo?.taxRate || 0,
            taxType: data.taxInfo?.taxType || 'none', // none, gst, sst, vat
            taxNumber: data.taxInfo?.taxNumber || '',
            isExempt: data.taxInfo?.isExempt || false,
            ...data.taxInfo
        };
        
        // 重新计算总金额
        this._calculateTotals();
    }

    /**
     * 更新收据数据
     * @param {Object} data - 要更新的数据
     * @protected
     */
    _updateData(data) {
        // 更新基本字段
        const updateFields = [
            'receiptNumber', 'receivedFrom', 'amount', 'currency',
            'paymentMethod', 'description', 'purpose', 'issuedBy',
            'additionalNotes', 'isPaid', 'isVoided', 'voidReason'
        ];
        
        updateFields.forEach(field => {
            if (data[field] !== undefined) {
                this[field] = data[field];
            }
        });
        
        // 更新日期
        if (data.date) {
            this.date = data.date instanceof Date ? data.date : new Date(data.date);
        }
        
        // 更新复合对象
        if (data.companyInfo) {
            this.companyInfo = { ...this.companyInfo, ...data.companyInfo };
        }
        
        if (data.customerInfo) {
            this.customerInfo = { ...this.customerInfo, ...data.customerInfo };
        }
        
        if (data.taxInfo) {
            this.taxInfo = { ...this.taxInfo, ...data.taxInfo };
        }
        
        // 重新计算总金额
        this._calculateTotals();
    }

    /**
     * 计算税额和总金额
     * @private
     */
    _calculateTotals() {
        if (this.taxInfo.isExempt || this.taxInfo.taxRate === 0) {
            this.taxAmount = 0;
            this.totalAmount = this.amount;
        } else {
            this.taxAmount = this.amount * (this.taxInfo.taxRate / 100);
            this.totalAmount = this.amount + this.taxAmount;
        }
    }

    /**
     * 特定验证
     * @protected
     */
    _validateSpecific() {
        // 验证收据号码
        if (!this.receiptNumber || this.receiptNumber.trim() === '') {
            throw new ValidationError('收据号码不能为空', 'receiptNumber', this.receiptNumber);
        }
        
        // 验证金额
        if (!Validator.isPositiveNumber(this.amount)) {
            throw new ValidationError('金额必须为正数', 'amount', this.amount);
        }
        
        // 验证收款人
        if (!this.receivedFrom || this.receivedFrom.trim() === '') {
            throw new ValidationError('收款人不能为空', 'receivedFrom', this.receivedFrom);
        }
        
        // 验证日期
        if (!Validator.isValidDate(this.date)) {
            throw new ValidationError('日期格式无效', 'date', this.date);
        }
        
        // 验证货币代码
        const validCurrencies = ['RM', 'USD', 'EUR', 'SGD', 'CNY'];
        if (!validCurrencies.includes(this.currency)) {
            throw new ValidationError(`无效的货币代码: ${this.currency}`, 'currency', this.currency);
        }
        
        // 验证支付方式
        const validPaymentMethods = ['cash', 'card', 'transfer', 'cheque', 'online'];
        if (!validPaymentMethods.includes(this.paymentMethod)) {
            throw new ValidationError(`无效的支付方式: ${this.paymentMethod}`, 'paymentMethod', this.paymentMethod);
        }
        
        // 验证税率
        if (this.taxInfo.taxRate < 0 || this.taxInfo.taxRate > 100) {
            throw new ValidationError('税率必须在0-100之间', 'taxInfo.taxRate', this.taxInfo.taxRate);
        }
        
        // 如果已作废，必须有作废原因
        if (this.isVoided && (!this.voidReason || this.voidReason.trim() === '')) {
            throw new ValidationError('已作废的收据必须提供作废原因', 'voidReason', this.voidReason);
        }
    }

    /**
     * 获取特定数据
     * @returns {Object} 收据特定数据
     * @protected
     */
    _getSpecificData() {
        return {
            receiptNumber: this.receiptNumber,
            date: this.date.toISOString(),
            receivedFrom: this.receivedFrom,
            amount: this.amount,
            currency: this.currency,
            paymentMethod: this.paymentMethod,
            description: this.description,
            purpose: this.purpose,
            issuedBy: this.issuedBy,
            companyInfo: this.companyInfo,
            customerInfo: this.customerInfo,
            taxInfo: this.taxInfo,
            additionalNotes: this.additionalNotes,
            taxAmount: this.taxAmount,
            totalAmount: this.totalAmount,
            isPaid: this.isPaid,
            isVoided: this.isVoided,
            voidReason: this.voidReason
        };
    }

    /**
     * 设置收据号码
     * @param {string} receiptNumber - 收据号码
     * @returns {ReceiptDocument} 当前实例
     */
    setReceiptNumber(receiptNumber) {
        this.receiptNumber = receiptNumber;
        this.updatedAt = new Date();
        return this;
    }

    /**
     * 设置金额
     * @param {number} amount - 金额
     * @returns {ReceiptDocument} 当前实例
     */
    setAmount(amount) {
        if (!Validator.isPositiveNumber(amount)) {
            throw new ValidationError('金额必须为正数', 'amount', amount);
        }
        
        this.amount = amount;
        this._calculateTotals();
        this.updatedAt = new Date();
        return this;
    }

    /**
     * 设置税率
     * @param {number} taxRate - 税率（百分比）
     * @returns {ReceiptDocument} 当前实例
     */
    setTaxRate(taxRate) {
        if (taxRate < 0 || taxRate > 100) {
            throw new ValidationError('税率必须在0-100之间', 'taxRate', taxRate);
        }
        
        this.taxInfo.taxRate = taxRate;
        this._calculateTotals();
        this.updatedAt = new Date();
        return this;
    }

    /**
     * 设置税务豁免状态
     * @param {boolean} isExempt - 是否豁免税务
     * @returns {ReceiptDocument} 当前实例
     */
    setTaxExempt(isExempt) {
        this.taxInfo.isExempt = isExempt;
        this._calculateTotals();
        this.updatedAt = new Date();
        return this;
    }

    /**
     * 作废收据
     * @param {string} reason - 作废原因
     * @returns {ReceiptDocument} 当前实例
     */
    void(reason) {
        if (!reason || reason.trim() === '') {
            throw new ValidationError('作废原因不能为空', 'reason', reason);
        }
        
        this.isVoided = true;
        this.voidReason = reason;
        this.updatedAt = new Date();
        return this;
    }

    /**
     * 取消作废
     * @returns {ReceiptDocument} 当前实例
     */
    unvoid() {
        this.isVoided = false;
        this.voidReason = '';
        this.updatedAt = new Date();
        return this;
    }

    /**
     * 标记为已支付
     * @returns {ReceiptDocument} 当前实例
     */
    markAsPaid() {
        this.isPaid = true;
        this.updatedAt = new Date();
        return this;
    }

    /**
     * 标记为未支付
     * @returns {ReceiptDocument} 当前实例
     */
    markAsUnpaid() {
        this.isPaid = false;
        this.updatedAt = new Date();
        return this;
    }

    /**
     * 获取格式化的金额
     * @returns {string} 格式化的金额
     */
    getFormattedAmount() {
        return StringUtils.formatCurrency(this.amount, this.currency);
    }

    /**
     * 获取格式化的税额
     * @returns {string} 格式化的税额
     */
    getFormattedTaxAmount() {
        return StringUtils.formatCurrency(this.taxAmount, this.currency);
    }

    /**
     * 获取格式化的总金额
     * @returns {string} 格式化的总金额
     */
    getFormattedTotalAmount() {
        return StringUtils.formatCurrency(this.totalAmount, this.currency);
    }

    /**
     * 获取格式化的日期
     * @param {string} format - 日期格式
     * @returns {string} 格式化的日期
     */
    getFormattedDate(format = 'YYYY-MM-DD') {
        return DateUtils.format(this.date, format);
    }

    /**
     * 获取支付方式的中文描述
     * @returns {string} 支付方式描述
     */
    getPaymentMethodDescription() {
        const descriptions = {
            cash: '现金',
            card: '银行卡',
            transfer: '银行转账',
            cheque: '支票',
            online: '在线支付'
        };
        
        return descriptions[this.paymentMethod] || this.paymentMethod;
    }

    /**
     * 获取税务类型的中文描述
     * @returns {string} 税务类型描述
     */
    getTaxTypeDescription() {
        const descriptions = {
            none: '无税',
            gst: '商品及服务税',
            sst: '销售及服务税',
            vat: '增值税'
        };
        
        return descriptions[this.taxInfo.taxType] || this.taxInfo.taxType;
    }

    /**
     * 检查是否包含税务
     * @returns {boolean} 是否包含税务
     */
    hasTax() {
        return !this.taxInfo.isExempt && this.taxInfo.taxRate > 0;
    }

    /**
     * 检查是否为现金支付
     * @returns {boolean} 是否为现金支付
     */
    isCashPayment() {
        return this.paymentMethod === 'cash';
    }

    /**
     * 检查是否为电子支付
     * @returns {boolean} 是否为电子支付
     */
    isElectronicPayment() {
        return ['card', 'transfer', 'online'].includes(this.paymentMethod);
    }

    /**
     * 获取收据摘要
     * @returns {Object} 收据摘要
     */
    getSummary() {
        return {
            ...super.getSummary(),
            receiptNumber: this.receiptNumber,
            receivedFrom: this.receivedFrom,
            amount: this.getFormattedAmount(),
            totalAmount: this.getFormattedTotalAmount(),
            date: this.getFormattedDate(),
            paymentMethod: this.getPaymentMethodDescription(),
            isPaid: this.isPaid,
            isVoided: this.isVoided
        };
    }

    /**
     * 生成收据号码
     * @param {string} prefix - 前缀
     * @returns {string} 生成的收据号码
     * @static
     */
    static generateReceiptNumber(prefix = 'RCP') {
        const timestamp = DateUtils.format(new Date(), 'YYYYMMDD');
        const random = StringUtils.generateRandomString(4, true);
        return `${prefix}-${timestamp}-${random}`;
    }

    /**
     * 从JSON创建收据实例
     * @param {Object} json - JSON对象
     * @returns {ReceiptDocument} 收据实例
     * @static
     */
    static fromJSON(json) {
        const data = {
            ...json,
            date: new Date(json.date),
            createdAt: new Date(json.createdAt),
            updatedAt: new Date(json.updatedAt)
        };
        
        return new ReceiptDocument(data);
    }

    /**
     * 创建空白收据
     * @param {Object} defaults - 默认值
     * @returns {ReceiptDocument} 空白收据实例
     * @static
     */
    static createBlank(defaults = {}) {
        return new ReceiptDocument({
            receiptNumber: this.generateReceiptNumber(),
            date: new Date(),
            ...defaults
        });
    }

    /**
     * 转换为字符串表示
     * @returns {string} 字符串表示
     */
    toString() {
        return `ReceiptDocument(${this.receiptNumber}, ${this.getFormattedAmount()}, ${this.receivedFrom})`;
    }
}
