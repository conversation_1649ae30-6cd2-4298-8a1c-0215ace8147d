/**
 * @file 司机协议模板 - 实现司机协议文档的模板渲染功能
 * <AUTHOR> Team
 * @description 
 * 这个文件定义了司机协议文档的模板类，包括：
 * - DriverAgreementTemplate 类，继承自 BaseTemplate
 * - 司机协议专用的渲染逻辑和样式配置
 * - 协议字段验证和格式化处理
 * - 支持多种协议样式主题（标准、正式、简约、详细）
 */

// #region 导入依赖模块
import { BaseTemplate } from './base-template.js';
import { DriverAgreementDocument } from '../models/driver-agreement-document.js';
import { StringUtils } from '../core/utils/string-utils.js';
import { DateUtils } from '../core/utils/date-utils.js';
// #endregion

// #region 司机协议模板类定义
/**
 * @class DriverAgreementTemplate - 司机协议模板类
 * @description 专门用于渲染司机协议文档的模板类，继承自BaseTemplate
 */
export class DriverAgreementTemplate extends BaseTemplate {
    /**
     * 构造函数 - 初始化司机协议模板实例
     * @param {Object} config - 模板配置对象
     */
    constructor(config = {}) {
        // 设置司机协议模板的默认配置
        const defaultConfig = {
            name: config.name || 'driver-agreement-template',
            type: 'driver-agreement',
            version: config.version || '1.0.0',
            metadata: {
                title: '司机协议模板',
                description: '用于生成司机服务协议文档的标准模板',
                author: 'SmartOffice Team',
                created: new Date(),
                ...config.metadata
            },
            
            // 司机协议专用的渲染配置
            renderConfig: {
                theme: 'standard', // 默认主题：标准、正式、简约、详细
                language: 'zh-CN', // 默认语言
                dateFormat: 'YYYY年MM月DD日', // 中文日期格式
                showSignatures: true, // 是否显示签名区域
                showStamp: true, // 是否显示印章位置
                showWitness: false, // 是否显示见证人
                includeLegalTerms: true, // 是否包含法律条款
                pageSize: 'A4', // 页面大小
                ...config.renderConfig
            },
            
            // 司机协议专用样式配置
            styles: {
                // 页面容器样式
                container: {
                    width: '210mm',
                    minHeight: '297mm',
                    margin: '0 auto',
                    padding: '25mm 20mm',
                    fontFamily: '"Times New Roman", "Microsoft YaHei", serif',
                    fontSize: '14px',
                    lineHeight: '1.8',
                    color: '#000',
                    backgroundColor: '#fff'
                },
                
                // 标题样式
                title: {
                    fontSize: '20px',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    marginBottom: '30px',
                    color: '#000'
                },
                
                // 协议信息样式
                agreementInfo: {
                    marginBottom: '25px',
                    textAlign: 'center',
                    fontSize: '12px'
                },
                
                // 当事人信息样式
                partyInfo: {
                    marginBottom: '20px',
                    textIndent: '2em'
                },
                
                // 条款样式
                clause: {
                    marginBottom: '15px',
                    textIndent: '2em'
                },
                
                // 条款标题样式
                clauseTitle: {
                    fontWeight: 'bold',
                    marginBottom: '8px',
                    textIndent: '0'
                },
                
                // 条款内容样式
                clauseContent: {
                    marginLeft: '1em',
                    textIndent: '2em'
                },
                
                // 子条款样式
                subClause: {
                    marginBottom: '8px',
                    marginLeft: '2em',
                    textIndent: '2em'
                },
                
                // 签名区域样式
                signatureSection: {
                    marginTop: '40px',
                    display: 'flex',
                    justifyContent: 'space-between'
                },
                
                // 签名框样式
                signatureBox: {
                    width: '45%',
                    textAlign: 'center'
                },
                
                // 签名线样式
                signatureLine: {
                    borderBottom: '1px solid #000',
                    marginBottom: '5px',
                    height: '40px'
                },
                
                // 日期样式
                dateSection: {
                    marginTop: '30px',
                    textAlign: 'center'
                },
                
                // 印章样式
                stampSection: {
                    marginTop: '20px',
                    textAlign: 'right',
                    fontSize: '12px'
                },
                
                // 页脚样式
                footer: {
                    marginTop: '30px',
                    borderTop: '1px solid #ccc',
                    paddingTop: '15px',
                    fontSize: '10px',
                    color: '#666',
                    textAlign: 'center'
                },
                
                ...config.styles
            },
            
            // 司机协议字段验证规则
            validationRules: {
                agreementNumber: {
                    type: 'string',
                    minLength: 1,
                    maxLength: 50
                },
                agreementDate: {
                    validator: (value) => value instanceof Date || !isNaN(Date.parse(value))
                },
                driverInfo: {
                    type: 'object',
                    validator: (value) => value && value.name && value.idNumber
                },
                companyInfo: {
                    type: 'object',
                    validator: (value) => value && value.name
                },
                ...config.validationRules
            },
            
            // 必填字段列表
            requiredFields: [
                'agreementNumber',
                'agreementDate',
                'driverInfo',
                'companyInfo',
                ...config.requiredFields || []
            ]
        };
        
        // 调用父类构造函数
        super(defaultConfig);
        
        // 司机协议专用属性
        this.supportedThemes = ['standard', 'formal', 'simple', 'detailed'];
        this.supportedLanguages = ['zh-CN', 'en-US', 'ms-MY'];
        
        // 协议条款模板
        this.clauseTemplates = {
            serviceScope: '服务范围',
            responsibilities: '双方责任',
            compensation: '报酬标准',
            workTime: '工作时间',
            safety: '安全要求',
            termination: '终止条件',
            legal: '法律条款'
        };
    }

    /**
     * 执行实际渲染 - 实现基类的抽象方法
     * @param {Object} data - 预处理后的数据对象
     * @param {Object} options - 渲染选项配置
     * @returns {Promise<string>} 渲染结果字符串
     * @protected
     */
    async _doRender(data, options) {
        try {
            // 确保数据完整性
            const agreementData = await this._ensureDataCompleteness(data);
            
            // 根据主题选择渲染方法
            switch (options.theme) {
                case 'formal':
                    return await this._renderFormalTheme(agreementData, options);
                case 'simple':
                    return await this._renderSimpleTheme(agreementData, options);
                case 'detailed':
                    return await this._renderDetailedTheme(agreementData, options);
                default:
                    return await this._renderStandardTheme(agreementData, options);
            }
        } catch (error) {
            throw new Error(`司机协议模板渲染失败: ${error.message}`);
        }
    }

    /**
     * 确保数据完整性 - 补充缺失的司机协议数据
     * @param {Object} data - 原始数据
     * @returns {Promise<Object>} 完整的司机协议数据
     * @private
     */
    async _ensureDataCompleteness(data) {
        const agreementData = { ...data };
        
        // 确保有协议号
        if (!agreementData.agreementNumber) {
            agreementData.agreementNumber = this._generateAgreementNumber();
        }
        
        // 确保有协议日期
        if (!agreementData.agreementDate) {
            agreementData.agreementDate = new Date();
        }
        
        // 格式化日期
        agreementData.formattedAgreementDate = DateUtils.format(
            new Date(agreementData.agreementDate),
            this.renderConfig.dateFormat,
            this.renderConfig.language
        );
        
        // 确保有公司信息
        agreementData.companyInfo = {
            name: '智能办公系统',
            address: '',
            phone: '',
            email: '',
            legalRepresentative: '',
            registrationNumber: '',
            ...agreementData.companyInfo
        };
        
        // 确保有司机信息
        agreementData.driverInfo = {
            name: '司机姓名',
            idNumber: '',
            phone: '',
            address: '',
            licenseNumber: '',
            licenseType: '',
            experience: '',
            ...agreementData.driverInfo
        };
        
        // 确保有车辆信息
        agreementData.vehicleInfo = {
            plateNumber: '',
            model: '',
            brand: '',
            color: '',
            year: '',
            ...agreementData.vehicleInfo
        };
        
        // 确保有服务条款
        agreementData.serviceTerms = {
            serviceType: '旅游包车服务',
            workArea: '',
            compensation: '',
            paymentMethod: '',
            ...agreementData.serviceTerms
        };
        
        // 确保有协议条款
        if (!agreementData.clauses || !Array.isArray(agreementData.clauses)) {
            agreementData.clauses = this._getDefaultClauses();
        }
        
        return agreementData;
    }

    /**
     * 获取默认协议条款 - 返回标准的司机协议条款
     * @returns {Array} 协议条款数组
     * @private
     */
    _getDefaultClauses() {
        return [
            {
                title: '第一条 服务范围',
                content: '甲方委托乙方提供旅游包车服务，具体服务内容包括但不限于：车辆驾驶、路线规划、安全驾驶等。乙方应按照甲方要求，在约定时间和地点提供服务。'
            },
            {
                title: '第二条 双方责任',
                content: '甲方负责提供准确的行程信息和必要的证件，支付约定的服务费用。乙方负责安全驾驶，维护车辆状况良好，遵守交通法规。',
                subClauses: [
                    '甲方应提前告知行程变更',
                    '乙方应保证车辆安全性能',
                    '双方应相互尊重，文明服务'
                ]
            },
            {
                title: '第三条 报酬标准',
                content: '服务费用按照约定标准计算，包括基本服务费和可能产生的额外费用。费用支付方式为现金或转账，具体以双方约定为准。'
            },
            {
                title: '第四条 工作时间',
                content: '工作时间以实际服务时间为准，包括行驶时间和等待时间。超出约定时间的服务按加班标准计费。'
            },
            {
                title: '第五条 安全要求',
                content: '乙方必须持有有效驾驶证，具备相应的驾驶经验。行驶过程中严格遵守交通法规，确保乘客安全。如发生交通事故，按照相关法律法规处理。'
            },
            {
                title: '第六条 协议终止',
                content: '本协议在服务完成后自动终止。如遇特殊情况需要提前终止，双方应协商解决。'
            },
            {
                title: '第七条 争议解决',
                content: '如发生争议，双方应首先协商解决。协商不成的，可向有管辖权的人民法院提起诉讼。'
            }
        ];
    }

    /**
     * 渲染标准主题协议 - 标准的司机协议样式
     * @param {Object} data - 协议数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderStandardTheme(data, options) {
        const html = `
            <div class="agreement-container standard-theme">
                ${await this._renderTitle(data, options)}
                ${await this._renderAgreementInfo(data, options)}
                ${await this._renderParties(data, options)}
                ${await this._renderClauses(data, options)}
                ${await this._renderSignatures(data, options)}
                ${await this._renderFooter(data, options)}
            </div>
        `;
        
        return html;
    }

    /**
     * 渲染正式主题协议 - 正式的司机协议样式
     * @param {Object} data - 协议数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderFormalTheme(data, options) {
        const html = `
            <div class="agreement-container formal-theme">
                <div class="formal-header">
                    ${await this._renderTitle(data, options)}
                    ${await this._renderAgreementInfo(data, options)}
                </div>
                <div class="formal-content">
                    ${await this._renderParties(data, options)}
                    <div class="formal-divider"></div>
                    ${await this._renderClauses(data, options)}
                </div>
                <div class="formal-footer">
                    ${await this._renderSignatures(data, options)}
                    ${await this._renderFooter(data, options)}
                </div>
            </div>
        `;
        
        return html;
    }

    /**
     * 渲染简约主题协议 - 简约的司机协议样式
     * @param {Object} data - 协议数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderSimpleTheme(data, options) {
        const html = `
            <div class="agreement-container simple-theme">
                ${await this._renderTitle(data, options)}
                ${await this._renderBasicInfo(data, options)}
                ${await this._renderSimpleClauses(data, options)}
                ${await this._renderSimpleSignatures(data, options)}
            </div>
        `;
        
        return html;
    }

    /**
     * 渲染详细主题协议 - 详细的司机协议样式
     * @param {Object} data - 协议数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderDetailedTheme(data, options) {
        const html = `
            <div class="agreement-container detailed-theme">
                <div class="detailed-header">
                    ${await this._renderTitle(data, options)}
                    ${await this._renderAgreementInfo(data, options)}
                </div>
                <div class="detailed-parties">
                    ${await this._renderDetailedParties(data, options)}
                </div>
                <div class="detailed-vehicle">
                    ${await this._renderVehicleInfo(data, options)}
                </div>
                <div class="detailed-terms">
                    ${await this._renderServiceTerms(data, options)}
                </div>
                <div class="detailed-clauses">
                    ${await this._renderClauses(data, options)}
                </div>
                <div class="detailed-signatures">
                    ${await this._renderSignatures(data, options)}
                </div>
                <div class="detailed-footer">
                    ${await this._renderFooter(data, options)}
                </div>
            </div>
        `;
        
        return html;
    }

    /**
     * 渲染协议标题 - 生成协议标题部分
     * @param {Object} data - 协议数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderTitle(data, options) {
        const company = data.companyInfo || {};
        
        return `
            <div class="agreement-title-section">
                ${options.showLogo && company.logo ? `
                <div class="company-logo-header">
                    <img src="${company.logo}" alt="${company.name}" class="logo-image">
                </div>
                ` : ''}
                <h1 class="agreement-title">司机服务协议书</h1>
            </div>
        `;
    }

    /**
     * 渲染协议信息 - 生成协议号、日期等基本信息
     * @param {Object} data - 协议数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderAgreementInfo(data, options) {
        return `
            <div class="agreement-info-section">
                <div class="agreement-number">协议编号：${data.agreementNumber}</div>
                <div class="agreement-date">签署日期：${data.formattedAgreementDate}</div>
            </div>
        `;
    }

    /**
     * 渲染当事人信息 - 生成甲乙双方信息
     * @param {Object} data - 协议数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderParties(data, options) {
        const company = data.companyInfo;
        const driver = data.driverInfo;
        
        return `
            <div class="parties-section">
                <div class="party-info">
                    <p><strong>甲方（委托方）：</strong>${company.name}</p>
                    ${company.address ? `<p><strong>地址：</strong>${company.address}</p>` : ''}
                    ${company.phone ? `<p><strong>联系电话：</strong>${company.phone}</p>` : ''}
                    ${company.legalRepresentative ? `<p><strong>法定代表人：</strong>${company.legalRepresentative}</p>` : ''}
                </div>
                
                <div class="party-info">
                    <p><strong>乙方（服务方）：</strong>${driver.name}</p>
                    ${driver.idNumber ? `<p><strong>身份证号：</strong>${driver.idNumber}</p>` : ''}
                    ${driver.phone ? `<p><strong>联系电话：</strong>${driver.phone}</p>` : ''}
                    ${driver.address ? `<p><strong>住址：</strong>${driver.address}</p>` : ''}
                    ${driver.licenseNumber ? `<p><strong>驾驶证号：</strong>${driver.licenseNumber}</p>` : ''}
                    ${driver.licenseType ? `<p><strong>准驾车型：</strong>${driver.licenseType}</p>` : ''}
                </div>
                
                <p class="agreement-intro">经甲乙双方友好协商，就司机服务事宜达成如下协议：</p>
            </div>
        `;
    }

    /**
     * 渲染详细当事人信息 - 详细主题的当事人信息
     * @param {Object} data - 协议数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderDetailedParties(data, options) {
        const company = data.companyInfo;
        const driver = data.driverInfo;
        
        return `
            <div class="detailed-parties-section">
                <div class="party-section">
                    <h3>甲方（委托方）信息</h3>
                    <table class="party-table">
                        <tr><td>公司名称：</td><td>${company.name}</td></tr>
                        ${company.registrationNumber ? `<tr><td>注册号：</td><td>${company.registrationNumber}</td></tr>` : ''}
                        ${company.address ? `<tr><td>注册地址：</td><td>${company.address}</td></tr>` : ''}
                        ${company.phone ? `<tr><td>联系电话：</td><td>${company.phone}</td></tr>` : ''}
                        ${company.email ? `<tr><td>电子邮箱：</td><td>${company.email}</td></tr>` : ''}
                        ${company.legalRepresentative ? `<tr><td>法定代表人：</td><td>${company.legalRepresentative}</td></tr>` : ''}
                    </table>
                </div>
                
                <div class="party-section">
                    <h3>乙方（服务方）信息</h3>
                    <table class="party-table">
                        <tr><td>姓名：</td><td>${driver.name}</td></tr>
                        ${driver.idNumber ? `<tr><td>身份证号：</td><td>${driver.idNumber}</td></tr>` : ''}
                        ${driver.phone ? `<tr><td>联系电话：</td><td>${driver.phone}</td></tr>` : ''}
                        ${driver.address ? `<tr><td>住址：</td><td>${driver.address}</td></tr>` : ''}
                        ${driver.licenseNumber ? `<tr><td>驾驶证号：</td><td>${driver.licenseNumber}</td></tr>` : ''}
                        ${driver.licenseType ? `<tr><td>准驾车型：</td><td>${driver.licenseType}</td></tr>` : ''}
                        ${driver.experience ? `<tr><td>驾驶经验：</td><td>${driver.experience}</td></tr>` : ''}
                    </table>
                </div>
            </div>
        `;
    }

    /**
     * 渲染车辆信息 - 生成车辆详细信息
     * @param {Object} data - 协议数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderVehicleInfo(data, options) {
        const vehicle = data.vehicleInfo;
        
        if (!vehicle.plateNumber && !vehicle.model) {
            return '';
        }
        
        return `
            <div class="vehicle-info-section">
                <h3>车辆信息</h3>
                <table class="vehicle-table">
                    ${vehicle.plateNumber ? `<tr><td>车牌号码：</td><td>${vehicle.plateNumber}</td></tr>` : ''}
                    ${vehicle.brand ? `<tr><td>车辆品牌：</td><td>${vehicle.brand}</td></tr>` : ''}
                    ${vehicle.model ? `<tr><td>车辆型号：</td><td>${vehicle.model}</td></tr>` : ''}
                    ${vehicle.color ? `<tr><td>车身颜色：</td><td>${vehicle.color}</td></tr>` : ''}
                    ${vehicle.year ? `<tr><td>出厂年份：</td><td>${vehicle.year}</td></tr>` : ''}
                </table>
            </div>
        `;
    }

    /**
     * 渲染服务条款 - 生成服务条款信息
     * @param {Object} data - 协议数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderServiceTerms(data, options) {
        const terms = data.serviceTerms;
        
        return `
            <div class="service-terms-section">
                <h3>服务条款</h3>
                <table class="terms-table">
                    <tr><td>服务类型：</td><td>${terms.serviceType}</td></tr>
                    ${terms.workArea ? `<tr><td>服务区域：</td><td>${terms.workArea}</td></tr>` : ''}
                    ${terms.compensation ? `<tr><td>报酬标准：</td><td>${terms.compensation}</td></tr>` : ''}
                    ${terms.paymentMethod ? `<tr><td>支付方式：</td><td>${terms.paymentMethod}</td></tr>` : ''}
                </table>
            </div>
        `;
    }

    /**
     * 渲染协议条款 - 生成详细的协议条款
     * @param {Object} data - 协议数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderClauses(data, options) {
        const clausesHtml = data.clauses.map(clause => {
            let clauseHtml = `
                <div class="clause">
                    <div class="clause-title">${clause.title}</div>
                    <div class="clause-content">${clause.content}</div>
            `;
            
            if (clause.subClauses && clause.subClauses.length > 0) {
                clauseHtml += '<div class="sub-clauses">';
                clause.subClauses.forEach(subClause => {
                    clauseHtml += `<div class="sub-clause">${subClause}</div>`;
                });
                clauseHtml += '</div>';
            }
            
            clauseHtml += '</div>';
            return clauseHtml;
        }).join('');
        
        return `
            <div class="clauses-section">
                ${clausesHtml}
            </div>
        `;
    }

    /**
     * 渲染基本信息 - 简约主题的基本信息
     * @param {Object} data - 协议数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderBasicInfo(data, options) {
        return `
            <div class="basic-info-section">
                <p><strong>协议编号：</strong>${data.agreementNumber}</p>
                <p><strong>签署日期：</strong>${data.formattedAgreementDate}</p>
                <p><strong>甲方：</strong>${data.companyInfo.name}</p>
                <p><strong>乙方：</strong>${data.driverInfo.name}</p>
                ${data.driverInfo.phone ? `<p><strong>联系电话：</strong>${data.driverInfo.phone}</p>` : ''}
                ${data.vehicleInfo.plateNumber ? `<p><strong>车牌号：</strong>${data.vehicleInfo.plateNumber}</p>` : ''}
            </div>
        `;
    }

    /**
     * 渲染简约条款 - 简约主题的协议条款
     * @param {Object} data - 协议数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderSimpleClauses(data, options) {
        const simpleClauses = data.clauses.slice(0, 3); // 只显示前3条重要条款
        
        const clausesHtml = simpleClauses.map(clause => `
            <div class="simple-clause">
                <strong>${clause.title}</strong>
                <p>${clause.content}</p>
            </div>
        `).join('');
        
        return `
            <div class="simple-clauses-section">
                ${clausesHtml}
                <p class="clause-note">其他条款按照标准服务协议执行。</p>
            </div>
        `;
    }

    /**
     * 渲染签名区域 - 生成双方签名区域
     * @param {Object} data - 协议数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderSignatures(data, options) {
        if (!options.showSignatures) {
            return '';
        }
        
        return `
            <div class="signatures-section">
                <div class="signature-boxes">
                    <div class="signature-box">
                        <div class="signature-label">甲方（委托方）：</div>
                        <div class="signature-line"></div>
                        <div class="signature-info">
                            <div>签名：________________</div>
                            <div>日期：________________</div>
                        </div>
                        ${options.showStamp ? '<div class="stamp-area">（盖章）</div>' : ''}
                    </div>
                    
                    <div class="signature-box">
                        <div class="signature-label">乙方（服务方）：</div>
                        <div class="signature-line"></div>
                        <div class="signature-info">
                            <div>签名：________________</div>
                            <div>日期：________________</div>
                        </div>
                    </div>
                </div>
                
                ${options.showWitness ? `
                <div class="witness-section">
                    <div class="witness-box">
                        <div class="witness-label">见证人：</div>
                        <div class="witness-line"></div>
                        <div class="witness-info">
                            <div>签名：________________</div>
                            <div>日期：________________</div>
                        </div>
                    </div>
                </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * 渲染简约签名 - 简约主题的签名区域
     * @param {Object} data - 协议数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderSimpleSignatures(data, options) {
        return `
            <div class="simple-signatures-section">
                <div class="simple-signature-row">
                    <span>甲方：________________</span>
                    <span>乙方：________________</span>
                </div>
                <div class="simple-date-row">
                    <span>日期：${data.formattedAgreementDate}</span>
                </div>
            </div>
        `;
    }

    /**
     * 渲染页脚 - 生成协议页脚信息
     * @param {Object} data - 协议数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderFooter(data, options) {
        const currentTime = DateUtils.format(new Date(), 'YYYY-MM-DD HH:mm:ss', this.renderConfig.language);
        const company = data.companyInfo || {};
        
        return `
            <div class="agreement-footer">
                ${options.showFooterImage && company.footer ? `
                <div class="company-footer-image">
                    <img src="${company.footer}" alt="公司页脚" class="footer-image">
                </div>
                ` : ''}
                <div class="footer-content">
                    <div class="print-time">生成时间: ${currentTime}</div>
                    <div class="footer-note">本协议一式两份，甲乙双方各执一份，具有同等法律效力。</div>
                </div>
                ${options.showStamp && company.stamp ? `
                <div class="company-stamp-footer">
                    <img src="${company.stamp}" alt="公司印章" class="stamp-image">
                </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * 生成协议号 - 创建唯一的协议编号
     * @returns {string} 协议号
     * @private
     */
    _generateAgreementNumber() {
        const date = new Date();
        const dateStr = DateUtils.format(date, 'YYYYMMDD');
        const timeStr = DateUtils.format(date, 'HHmmss');
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `DA${dateStr}${timeStr}${random}`;
    }

    /**
     * 获取支持的主题列表 - 返回司机协议模板支持的所有主题
     * @returns {Array<Object>} 主题信息数组
     */
    getSupportedThemes() {
        return [
            {
                id: 'standard',
                name: '标准',
                description: '标准的司机协议样式，适合大多数场景'
            },
            {
                id: 'formal',
                name: '正式',
                description: '正式的法律文档样式，条款清晰'
            },
            {
                id: 'simple',
                name: '简约',
                description: '简约样式，突出重点信息'
            },
            {
                id: 'detailed',
                name: '详细',
                description: '详细信息展示，包含完整的当事人和车辆信息'
            }
        ];
    }

    /**
     * 验证司机协议数据 - 检查协议数据的完整性和有效性
     * @param {Object} data - 要验证的协议数据
     * @returns {Object} 验证结果 {isValid: boolean, errors: Array}
     */
    validateAgreementData(data) {
        const errors = [];
        
        // 检查必填字段
        if (!data.agreementNumber) {
            errors.push('缺少协议编号');
        }
        
        if (!data.agreementDate) {
            errors.push('缺少协议日期');
        }
        
        if (!data.companyInfo || !data.companyInfo.name) {
            errors.push('缺少公司信息');
        }
        
        if (!data.driverInfo || !data.driverInfo.name) {
            errors.push('缺少司机姓名');
        }
        
        if (!data.driverInfo || !data.driverInfo.idNumber) {
            errors.push('缺少司机身份证号');
        }
        
        // 验证身份证号格式
        if (data.driverInfo && data.driverInfo.idNumber) {
            const idPattern = /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
            if (!idPattern.test(data.driverInfo.idNumber)) {
                errors.push('身份证号格式不正确');
            }
        }
        
        // 验证电话号码格式
        if (data.driverInfo && data.driverInfo.phone) {
            const phonePattern = /^1[3-9]\d{9}$/;
            if (!phonePattern.test(data.driverInfo.phone)) {
                errors.push('手机号码格式不正确');
            }
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 创建默认协议数据 - 生成一个包含默认值的协议数据对象
     * @param {Object} overrides - 要覆盖的默认值
     * @returns {Object} 默认协议数据
     */
    createDefaultData(overrides = {}) {
        return {
            agreementNumber: this._generateAgreementNumber(),
            agreementDate: new Date(),
            companyInfo: {
                name: '智能办公系统',
                address: '',
                phone: '',
                email: '',
                legalRepresentative: ''
            },
            driverInfo: {
                name: '司机姓名',
                idNumber: '',
                phone: '',
                address: '',
                licenseNumber: '',
                licenseType: 'C1',
                experience: ''
            },
            vehicleInfo: {
                plateNumber: '',
                brand: '',
                model: '',
                color: '',
                year: ''
            },
            serviceTerms: {
                serviceType: '旅游包车服务',
                workArea: '',
                compensation: '',
                paymentMethod: '现金'
            },
            clauses: this._getDefaultClauses(),
            ...overrides
        };
    }
}
// #endregion

// #region 工厂函数
/**
 * 创建司机协议模板实例 - 工厂函数，方便创建司机协议模板
 * @param {Object} config - 模板配置
 * @returns {DriverAgreementTemplate} 司机协议模板实例
 */
export function createDriverAgreementTemplate(config = {}) {
    return new DriverAgreementTemplate(config);
}

/**
 * 创建预定义的司机协议模板 - 创建具有预定义样式的司机协议模板
 * @param {string} theme - 主题名称
 * @param {Object} config - 额外配置
 * @returns {DriverAgreementTemplate} 司机协议模板实例
 */
export function createPresetDriverAgreementTemplate(theme = 'standard', config = {}) {
    const presetConfig = {
        ...config,
        renderConfig: {
            theme,
            ...config.renderConfig
        }
    };
    
    return new DriverAgreementTemplate(presetConfig);
}
// #endregion 