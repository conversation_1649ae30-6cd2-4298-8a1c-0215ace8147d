/**
 * @file 对话框组件 - SmartOffice 对话框和模态框系统
 * <AUTHOR> Team
 * @description 
 * 提供完整的对话框功能，包括：
 * - Dialog 基础对话框组件
 * - DialogManager 对话框管理器
 * - 模态和非模态对话框支持
 * - 确认、提示、信息等预设对话框
 * - 拖拽、缩放、键盘控制
 * - 完整的事件系统和生命周期管理
 */

import { BaseComponent } from './base-component.js';
import { UI_EVENTS } from '../../core/events/event-types.js';

// #region Dialog主组件
/**
 * @class Dialog - 对话框主组件
 * @extends BaseComponent
 * @description 可配置的对话框组件，支持模态和非模态模式
 */
export class Dialog extends BaseComponent {
    /**
     * 构造函数 - 初始化对话框组件
     * @param {Object} config - 对话框配置
     * @param {string} config.title - 对话框标题
     * @param {string|HTMLElement} config.content - 对话框内容
     * @param {Array} config.buttons - 按钮配置数组
     * @param {boolean} config.modal - 是否为模态对话框
     * @param {boolean} config.closable - 是否可关闭
     * @param {boolean} config.draggable - 是否可拖拽
     * @param {boolean} config.resizable - 是否可缩放
     * @param {string} config.size - 对话框尺寸
     */
    constructor(config = {}) {
        super(config);
        
        // 对话框属性
        this.dialogProps = {
            title: config.title || '对话框',
            content: config.content || '',
            buttons: config.buttons || [],
            modal: config.modal !== false, // 默认为模态
            closable: config.closable !== false, // 默认可关闭
            draggable: config.draggable !== false, // 默认可拖拽
            resizable: config.resizable || false, // 默认不可缩放
            size: config.size || 'medium', // 尺寸: small, medium, large, fullscreen
            width: config.width || null,
            height: config.height || null,
            maxWidth: config.maxWidth || '90vw',
            maxHeight: config.maxHeight || '90vh',
            position: config.position || 'center', // center, top, bottom
            animation: config.animation !== false, // 默认启用动画
            closeOnEscape: config.closeOnEscape !== false, // ESC键关闭
            closeOnOverlay: config.closeOnOverlay !== false, // 点击遮罩关闭
            destroyOnClose: config.destroyOnClose || false, // 关闭时销毁
            zIndex: config.zIndex || 1000,
            className: config.className || ''
        };
        
        // 对话框状态
        this.dialogState = {
            visible: false,
            opening: false,
            closing: false,
            dragging: false,
            resizing: false,
            position: { x: 0, y: 0 },
            size: { width: 0, height: 0 },
            originalPosition: null,
            originalSize: null
        };
        
        // DOM元素引用
        this.elements = {
            overlay: null,
            dialog: null,
            header: null,
            title: null,
            closeButton: null,
            content: null,
            footer: null,
            dragHandle: null,
            resizeHandle: null
        };
        
        // 事件处理器
        this.handlers = {
            keydown: this._handleKeydown.bind(this),
            overlayClick: this._handleOverlayClick.bind(this),
            closeClick: this._handleCloseClick.bind(this),
            dragStart: this._handleDragStart.bind(this),
            dragMove: this._handleDragMove.bind(this),
            dragEnd: this._handleDragEnd.bind(this),
            resizeStart: this._handleResizeStart.bind(this),
            resizeMove: this._handleResizeMove.bind(this),
            resizeEnd: this._handleResizeEnd.bind(this)
        };
        
        // 初始化组件
        this._initializeDialog();
    }

    /**
     * 初始化对话框 - 创建DOM结构和设置事件
     * @private
     */
    _initializeDialog() {
        this._createDialogStructure();
        this._applyDialogStyles();
        this._bindDialogEvents();
        this._setInitialPosition();
        
        // 发射初始化完成事件
        this.emit(UI_EVENTS.COMPONENT_INITIALIZED, {
            component: this,
            type: 'dialog'
        });
    }

    /**
     * 创建对话框DOM结构 - 构建完整的对话框HTML
     * @private
     */
    _createDialogStructure() {
        // 创建遮罩层
        this.elements.overlay = document.createElement('div');
        this.elements.overlay.className = `smartoffice-dialog-overlay ${this.dialogProps.className}`;
        this.elements.overlay.style.display = 'none';
        
        // 创建对话框容器
        this.elements.dialog = document.createElement('div');
        this.elements.dialog.className = `smartoffice-dialog smartoffice-dialog-${this.dialogProps.size}`;
        this.elements.dialog.setAttribute('role', 'dialog');
        this.elements.dialog.setAttribute('aria-modal', this.dialogProps.modal.toString());
        this.elements.dialog.setAttribute('aria-labelledby', `dialog-title-${this.id}`);
        
        // 创建对话框头部
        this._createDialogHeader();
        
        // 创建对话框内容
        this._createDialogContent();
        
        // 创建对话框底部
        this._createDialogFooter();
        
        // 添加拖拽和缩放控制
        if (this.dialogProps.draggable) {
            this._createDragHandle();
        }
        
        if (this.dialogProps.resizable) {
            this._createResizeHandle();
        }
        
        // 组装结构
        this.elements.overlay.appendChild(this.elements.dialog);
        this.container = this.elements.overlay;
    }

    /**
     * 创建对话框头部 - 标题和关闭按钮
     * @private
     */
    _createDialogHeader() {
        this.elements.header = document.createElement('div');
        this.elements.header.className = 'smartoffice-dialog-header';
        
        // 标题
        this.elements.title = document.createElement('h3');
        this.elements.title.className = 'smartoffice-dialog-title';
        this.elements.title.id = `dialog-title-${this.id}`;
        this.elements.title.textContent = this.dialogProps.title;
        
        // 关闭按钮
        if (this.dialogProps.closable) {
            this.elements.closeButton = document.createElement('button');
            this.elements.closeButton.className = 'smartoffice-dialog-close';
            this.elements.closeButton.innerHTML = '×';
            this.elements.closeButton.setAttribute('aria-label', '关闭对话框');
            this.elements.closeButton.type = 'button';
        }
        
        this.elements.header.appendChild(this.elements.title);
        if (this.elements.closeButton) {
            this.elements.header.appendChild(this.elements.closeButton);
        }
        
        this.elements.dialog.appendChild(this.elements.header);
    }

    /**
     * 创建对话框内容 - 主要内容区域
     * @private
     */
    _createDialogContent() {
        this.elements.content = document.createElement('div');
        this.elements.content.className = 'smartoffice-dialog-content';
        
        // 设置内容
        if (typeof this.dialogProps.content === 'string') {
            this.elements.content.innerHTML = this.dialogProps.content;
        } else if (this.dialogProps.content instanceof HTMLElement) {
            this.elements.content.appendChild(this.dialogProps.content);
        }
        
        this.elements.dialog.appendChild(this.elements.content);
    }

    /**
     * 创建对话框底部 - 按钮区域
     * @private
     */
    _createDialogFooter() {
        if (!this.dialogProps.buttons || this.dialogProps.buttons.length === 0) {
            return;
        }
        
        this.elements.footer = document.createElement('div');
        this.elements.footer.className = 'smartoffice-dialog-footer';
        
        // 创建按钮
        this.dialogProps.buttons.forEach((buttonConfig, index) => {
            const button = document.createElement('button');
            button.className = `smartoffice-btn smartoffice-btn-${buttonConfig.variant || 'secondary'}`;
            button.textContent = buttonConfig.text || `按钮${index + 1}`;
            button.type = 'button';
            
            // 按钮事件
            if (buttonConfig.handler) {
                button.addEventListener('click', (e) => {
                    const result = buttonConfig.handler(e, this);
                    // 如果返回 false，不自动关闭对话框
                    if (result !== false && buttonConfig.autoClose !== false) {
                        this.close();
                    }
                });
            }
            
            // 特殊按钮类型
            if (buttonConfig.type === 'primary') {
                button.classList.add('smartoffice-btn-primary');
            }
            
            this.elements.footer.appendChild(button);
        });
        
        this.elements.dialog.appendChild(this.elements.footer);
    }

    /**
     * 创建拖拽控制 - 拖拽手柄
     * @private
     */
    _createDragHandle() {
        this.elements.dragHandle = this.elements.header; // 使用头部作为拖拽区域
        this.elements.dragHandle.style.cursor = 'move';
        this.elements.dragHandle.setAttribute('title', '拖拽移动对话框');
    }

    /**
     * 创建缩放控制 - 缩放手柄
     * @private
     */
    _createResizeHandle() {
        this.elements.resizeHandle = document.createElement('div');
        this.elements.resizeHandle.className = 'smartoffice-dialog-resize-handle';
        this.elements.resizeHandle.style.cursor = 'se-resize';
        this.elements.resizeHandle.setAttribute('title', '拖拽调整对话框大小');
        
        this.elements.dialog.appendChild(this.elements.resizeHandle);
    }

    /**
     * 应用对话框样式 - 设置CSS样式
     * @private
     */
    _applyDialogStyles() {
        // 遮罩层样式
        Object.assign(this.elements.overlay.style, {
            position: 'fixed',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: this.dialogProps.zIndex.toString(),
            display: 'flex',
            alignItems: this._getVerticalAlignment(),
            justifyContent: 'center'
        });
        
        // 对话框样式
        Object.assign(this.elements.dialog.style, {
            position: 'relative',
            backgroundColor: 'white',
            borderRadius: '8px',
            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)',
            maxWidth: this.dialogProps.maxWidth,
            maxHeight: this.dialogProps.maxHeight,
            width: this.dialogProps.width || this._getSizeWidth(),
            height: this.dialogProps.height || 'auto',
            display: 'flex',
            flexDirection: 'column',
            outline: 'none'
        });
        
        // 动画样式
        if (this.dialogProps.animation) {
            this.elements.dialog.style.transition = 'transform 0.3s ease, opacity 0.3s ease';
            this.elements.overlay.style.transition = 'opacity 0.3s ease';
        }
    }

    /**
     * 获取垂直对齐方式 - 根据位置配置
     * @returns {string} CSS对齐值
     * @private
     */
    _getVerticalAlignment() {
        switch (this.dialogProps.position) {
            case 'top': return 'flex-start';
            case 'bottom': return 'flex-end';
            default: return 'center';
        }
    }

    /**
     * 获取尺寸宽度 - 根据尺寸配置
     * @returns {string} 宽度值
     * @private
     */
    _getSizeWidth() {
        const sizeMap = {
            small: '400px',
            medium: '600px',
            large: '800px',
            fullscreen: '100vw'
        };
        return sizeMap[this.dialogProps.size] || sizeMap.medium;
    }

    /**
     * 绑定对话框事件 - 设置所有事件监听器
     * @private
     */
    _bindDialogEvents() {
        // 关闭按钮事件
        if (this.elements.closeButton) {
            this.elements.closeButton.addEventListener('click', this.handlers.closeClick);
        }
        
        // 遮罩层点击事件
        if (this.dialogProps.closeOnOverlay) {
            this.elements.overlay.addEventListener('click', this.handlers.overlayClick);
        }
        
        // 拖拽事件
        if (this.dialogProps.draggable && this.elements.dragHandle) {
            this.elements.dragHandle.addEventListener('mousedown', this.handlers.dragStart);
        }
        
        // 缩放事件
        if (this.dialogProps.resizable && this.elements.resizeHandle) {
            this.elements.resizeHandle.addEventListener('mousedown', this.handlers.resizeStart);
        }
    }

    /**
     * 设置初始位置 - 计算并设置对话框位置
     * @private
     */
    _setInitialPosition() {
        // 记录初始位置用于拖拽
        this.dialogState.originalPosition = { x: 0, y: 0 };
        this.dialogState.position = { x: 0, y: 0 };
    }

    // #region 公开方法
    /**
     * 显示对话框 - 打开并显示对话框
     * @param {Object} options - 显示选项
     */
    show(options = {}) {
        if (this.dialogState.visible || this.dialogState.opening) {
            return;
        }
        
        this.dialogState.opening = true;
        
        // 发射显示前事件
        const beforeShowEvent = {
            component: this,
            options,
            cancel: false
        };
        this.emit(UI_EVENTS.DIALOG_BEFORE_SHOW, beforeShowEvent);
        
        if (beforeShowEvent.cancel) {
            this.dialogState.opening = false;
            return;
        }
        
        // 添加到DOM
        document.body.appendChild(this.container);
        
        // 设置焦点
        this._setFocus();
        
        // 键盘事件
        if (this.dialogProps.closeOnEscape) {
            document.addEventListener('keydown', this.handlers.keydown);
        }
        
        // 显示动画
        if (this.dialogProps.animation) {
            this._showWithAnimation();
        } else {
            this._showInstantly();
        }
    }

    /**
     * 动画显示 - 带动画效果的显示
     * @private
     */
    _showWithAnimation() {
        // 初始状态
        this.elements.overlay.style.opacity = '0';
        this.elements.dialog.style.transform = 'scale(0.7) translateY(-50px)';
        this.elements.dialog.style.opacity = '0';
        this.elements.overlay.style.display = 'flex';
        
        // 强制重绘
        this.elements.overlay.offsetHeight;
        
        // 动画到最终状态
        this.elements.overlay.style.opacity = '1';
        this.elements.dialog.style.transform = 'scale(1) translateY(0)';
        this.elements.dialog.style.opacity = '1';
        
        // 动画完成
        setTimeout(() => {
            this.dialogState.opening = false;
            this.dialogState.visible = true;
            
            this.emit(UI_EVENTS.DIALOG_SHOWN, { component: this });
        }, 300);
    }

    /**
     * 立即显示 - 无动画效果的显示
     * @private
     */
    _showInstantly() {
        this.elements.overlay.style.display = 'flex';
        this.elements.overlay.style.opacity = '1';
        this.elements.dialog.style.opacity = '1';
        
        this.dialogState.opening = false;
        this.dialogState.visible = true;
        
        this.emit(UI_EVENTS.DIALOG_SHOWN, { component: this });
    }

    /**
     * 关闭对话框 - 隐藏并可选择性销毁对话框
     * @param {*} result - 关闭结果
     */
    close(result = null) {
        if (!this.dialogState.visible || this.dialogState.closing) {
            return;
        }
        
        this.dialogState.closing = true;
        
        // 发射关闭前事件
        const beforeCloseEvent = {
            component: this,
            result,
            cancel: false
        };
        this.emit(UI_EVENTS.DIALOG_BEFORE_CLOSE, beforeCloseEvent);
        
        if (beforeCloseEvent.cancel) {
            this.dialogState.closing = false;
            return;
        }
        
        // 移除键盘事件
        document.removeEventListener('keydown', this.handlers.keydown);
        
        // 关闭动画
        if (this.dialogProps.animation) {
            this._hideWithAnimation(result);
        } else {
            this._hideInstantly(result);
        }
    }

    /**
     * 动画关闭 - 带动画效果的关闭
     * @param {*} result - 关闭结果
     * @private
     */
    _hideWithAnimation(result) {
        this.elements.overlay.style.opacity = '0';
        this.elements.dialog.style.transform = 'scale(0.7) translateY(-50px)';
        this.elements.dialog.style.opacity = '0';
        
        setTimeout(() => {
            this._hideInstantly(result);
        }, 300);
    }

    /**
     * 立即关闭 - 无动画效果的关闭
     * @param {*} result - 关闭结果
     * @private
     */
    _hideInstantly(result) {
        this.elements.overlay.style.display = 'none';
        
        if (this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
        
        this.dialogState.closing = false;
        this.dialogState.visible = false;
        
        // 发射关闭事件
        this.emit(UI_EVENTS.DIALOG_CLOSED, { 
            component: this, 
            result 
        });
        
        // 销毁组件
        if (this.dialogProps.destroyOnClose) {
            this.destroy();
        }
    }

    /**
     * 设置焦点 - 设置对话框焦点
     * @private
     */
    _setFocus() {
        // 优先聚焦第一个可聚焦元素
        const focusableElements = this.elements.dialog.querySelectorAll(
            'button, input, select, textarea, a[href], [tabindex]:not([tabindex="-1"])'
        );
        
        if (focusableElements.length > 0) {
            focusableElements[0].focus();
        } else {
            this.elements.dialog.focus();
        }
    }

    /**
     * 更新内容 - 动态更新对话框内容
     * @param {string|HTMLElement} content - 新内容
     */
    updateContent(content) {
        this.dialogProps.content = content;
        
        if (typeof content === 'string') {
            this.elements.content.innerHTML = content;
        } else if (content instanceof HTMLElement) {
            this.elements.content.innerHTML = '';
            this.elements.content.appendChild(content);
        }
        
        this.emit(UI_EVENTS.DIALOG_CONTENT_UPDATED, { 
            component: this, 
            content 
        });
    }

    /**
     * 更新标题 - 动态更新对话框标题
     * @param {string} title - 新标题
     */
    updateTitle(title) {
        this.dialogProps.title = title;
        this.elements.title.textContent = title;
        
        this.emit(UI_EVENTS.DIALOG_TITLE_UPDATED, { 
            component: this, 
            title 
        });
    }
    // #endregion

    // #region 事件处理方法
    /**
     * 处理键盘事件 - ESC键关闭等
     * @param {KeyboardEvent} e - 键盘事件
     * @private
     */
    _handleKeydown(e) {
        if (e.key === 'Escape' && this.dialogProps.closeOnEscape) {
            e.preventDefault();
            this.close('escape');
        }
    }

    /**
     * 处理遮罩点击 - 点击遮罩关闭对话框
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleOverlayClick(e) {
        if (e.target === this.elements.overlay && this.dialogProps.closeOnOverlay) {
            this.close('overlay');
        }
    }

    /**
     * 处理关闭按钮点击 - 关闭按钮事件
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleCloseClick(e) {
        e.preventDefault();
        this.close('button');
    }

    /**
     * 处理拖拽开始 - 开始拖拽对话框
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleDragStart(e) {
        if (!this.dialogProps.draggable) return;
        
        e.preventDefault();
        this.dialogState.dragging = true;
        
        const rect = this.elements.dialog.getBoundingClientRect();
        this.dialogState.dragOffset = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        };
        
        document.addEventListener('mousemove', this.handlers.dragMove);
        document.addEventListener('mouseup', this.handlers.dragEnd);
        
        this.elements.dialog.style.cursor = 'move';
        this.emit(UI_EVENTS.DIALOG_DRAG_START, { component: this });
    }

    /**
     * 处理拖拽移动 - 拖拽过程中的移动
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleDragMove(e) {
        if (!this.dialogState.dragging) return;
        
        e.preventDefault();
        
        const x = e.clientX - this.dialogState.dragOffset.x;
        const y = e.clientY - this.dialogState.dragOffset.y;
        
        // 限制在视窗内
        const maxX = window.innerWidth - this.elements.dialog.offsetWidth;
        const maxY = window.innerHeight - this.elements.dialog.offsetHeight;
        
        const constrainedX = Math.max(0, Math.min(x, maxX));
        const constrainedY = Math.max(0, Math.min(y, maxY));
        
        this.elements.dialog.style.position = 'fixed';
        this.elements.dialog.style.left = constrainedX + 'px';
        this.elements.dialog.style.top = constrainedY + 'px';
        
        this.dialogState.position = { x: constrainedX, y: constrainedY };
        
        this.emit(UI_EVENTS.DIALOG_DRAG_MOVE, { 
            component: this, 
            position: this.dialogState.position 
        });
    }

    /**
     * 处理拖拽结束 - 结束拖拽
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleDragEnd(e) {
        if (!this.dialogState.dragging) return;
        
        this.dialogState.dragging = false;
        
        document.removeEventListener('mousemove', this.handlers.dragMove);
        document.removeEventListener('mouseup', this.handlers.dragEnd);
        
        this.elements.dialog.style.cursor = '';
        this.emit(UI_EVENTS.DIALOG_DRAG_END, { component: this });
    }

    /**
     * 处理缩放开始 - 开始缩放对话框
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleResizeStart(e) {
        if (!this.dialogProps.resizable) return;
        
        e.preventDefault();
        e.stopPropagation();
        this.dialogState.resizing = true;
        
        const rect = this.elements.dialog.getBoundingClientRect();
        this.dialogState.resizeStart = {
            x: e.clientX,
            y: e.clientY,
            width: rect.width,
            height: rect.height
        };
        
        document.addEventListener('mousemove', this.handlers.resizeMove);
        document.addEventListener('mouseup', this.handlers.resizeEnd);
        
        this.emit(UI_EVENTS.DIALOG_RESIZE_START, { component: this });
    }

    /**
     * 处理缩放移动 - 缩放过程中的调整
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleResizeMove(e) {
        if (!this.dialogState.resizing) return;
        
        e.preventDefault();
        
        const deltaX = e.clientX - this.dialogState.resizeStart.x;
        const deltaY = e.clientY - this.dialogState.resizeStart.y;
        
        const newWidth = Math.max(300, this.dialogState.resizeStart.width + deltaX);
        const newHeight = Math.max(200, this.dialogState.resizeStart.height + deltaY);
        
        this.elements.dialog.style.width = newWidth + 'px';
        this.elements.dialog.style.height = newHeight + 'px';
        
        this.dialogState.size = { width: newWidth, height: newHeight };
        
        this.emit(UI_EVENTS.DIALOG_RESIZE_MOVE, { 
            component: this, 
            size: this.dialogState.size 
        });
    }

    /**
     * 处理缩放结束 - 结束缩放
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleResizeEnd(e) {
        if (!this.dialogState.resizing) return;
        
        this.dialogState.resizing = false;
        
        document.removeEventListener('mousemove', this.handlers.resizeMove);
        document.removeEventListener('mouseup', this.handlers.resizeEnd);
        
        this.emit(UI_EVENTS.DIALOG_RESIZE_END, { component: this });
    }
    // #endregion

    // #region 生命周期方法
    /**
     * 组件销毁 - 清理资源
     */
    destroy() {
        // 如果正在显示，先关闭
        if (this.dialogState.visible) {
            this.close();
        }
        
        // 移除所有事件监听器
        document.removeEventListener('keydown', this.handlers.keydown);
        document.removeEventListener('mousemove', this.handlers.dragMove);
        document.removeEventListener('mouseup', this.handlers.dragEnd);
        document.removeEventListener('mousemove', this.handlers.resizeMove);
        document.removeEventListener('mouseup', this.handlers.resizeEnd);
        
        // 调用父类销毁方法
        super.destroy();
        
        this.emit(UI_EVENTS.DIALOG_DESTROYED, { component: this });
    }
    // #endregion
}

/**
 * @class DialogManager - 对话框管理器
 * @description 管理多个对话框的创建、显示和销毁
 */
export class DialogManager {
    constructor() {
        this.dialogs = new Map(); // 对话框实例映射
        this.zIndexCounter = 1000; // Z-index计数器
        this.activeDialog = null; // 当前活跃对话框
    }

    /**
     * 创建对话框 - 创建新的对话框实例
     * @param {Object} config - 对话框配置
     * @returns {Dialog} 对话框实例
     */
    createDialog(config = {}) {
        const dialog = new Dialog({
            ...config,
            zIndex: this.zIndexCounter++
        });
        
        this.dialogs.set(dialog.id, dialog);
        
        // 监听对话框事件
        dialog.on(UI_EVENTS.DIALOG_SHOWN, () => {
            this.activeDialog = dialog;
        });
        
        dialog.on(UI_EVENTS.DIALOG_CLOSED, () => {
            if (this.activeDialog === dialog) {
                this.activeDialog = null;
            }
        });
        
        dialog.on(UI_EVENTS.DIALOG_DESTROYED, () => {
            this.dialogs.delete(dialog.id);
        });
        
        return dialog;
    }

    /**
     * 显示确认对话框 - 创建并显示确认对话框
     * @param {Object} options - 确认对话框选项
     * @returns {Promise} Promise，resolve确认结果
     */
    confirm(options = {}) {
        return new Promise((resolve) => {
            const dialog = this.createDialog({
                title: options.title || '确认',
                content: options.message || '确定要执行此操作吗？',
                size: options.size || 'small',
                modal: true,
                closable: false,
                buttons: [
                    {
                        text: options.cancelText || '取消',
                        variant: 'secondary',
                        handler: () => {
                            resolve(false);
                        }
                    },
                    {
                        text: options.confirmText || '确定',
                        variant: 'primary',
                        type: 'primary',
                        handler: () => {
                            resolve(true);
                        }
                    }
                ],
                destroyOnClose: true
            });
            
            dialog.show();
        });
    }

    /**
     * 显示提示对话框 - 创建并显示提示对话框
     * @param {Object} options - 提示对话框选项
     * @returns {Promise} Promise，resolve关闭结果
     */
    alert(options = {}) {
        return new Promise((resolve) => {
            const dialog = this.createDialog({
                title: options.title || '提示',
                content: options.message || '提示信息',
                size: options.size || 'small',
                modal: true,
                closable: true,
                buttons: [
                    {
                        text: options.buttonText || '确定',
                        variant: 'primary',
                        type: 'primary',
                        handler: () => {
                            resolve(true);
                        }
                    }
                ],
                destroyOnClose: true
            });
            
            dialog.show();
        });
    }

    /**
     * 显示输入对话框 - 创建并显示输入对话框
     * @param {Object} options - 输入对话框选项
     * @returns {Promise} Promise，resolve输入结果
     */
    prompt(options = {}) {
        return new Promise((resolve) => {
            const inputId = `prompt-input-${Date.now()}`;
            const content = document.createElement('div');
            content.innerHTML = `
                <p>${options.message || '请输入内容：'}</p>
                <input type="text" id="${inputId}" class="smartoffice-input" 
                       value="${options.defaultValue || ''}" 
                       placeholder="${options.placeholder || ''}">
            `;
            
            const dialog = this.createDialog({
                title: options.title || '输入',
                content: content,
                size: options.size || 'small',
                modal: true,
                closable: true,
                buttons: [
                    {
                        text: options.cancelText || '取消',
                        variant: 'secondary',
                        handler: () => {
                            resolve(null);
                        }
                    },
                    {
                        text: options.confirmText || '确定',
                        variant: 'primary',
                        type: 'primary',
                        handler: () => {
                            const input = dialog.elements.content.querySelector(`#${inputId}`);
                            resolve(input.value);
                        }
                    }
                ],
                destroyOnClose: true
            });
            
            dialog.show();
            
            // 聚焦输入框
            setTimeout(() => {
                const input = dialog.elements.content.querySelector(`#${inputId}`);
                if (input) {
                    input.focus();
                    input.select();
                }
            }, 100);
        });
    }

    /**
     * 关闭所有对话框 - 关闭当前所有打开的对话框
     */
    closeAll() {
        for (const dialog of this.dialogs.values()) {
            if (dialog.dialogState.visible) {
                dialog.close('manager_close_all');
            }
        }
    }

    /**
     * 获取活跃对话框 - 获取当前活跃的对话框
     * @returns {Dialog|null} 活跃对话框实例
     */
    getActiveDialog() {
        return this.activeDialog;
    }

    /**
     * 获取所有对话框 - 获取所有对话框实例
     * @returns {Array<Dialog>} 对话框实例数组
     */
    getAllDialogs() {
        return Array.from(this.dialogs.values());
    }

    /**
     * 销毁所有对话框 - 销毁所有对话框实例
     */
    destroyAll() {
        for (const dialog of this.dialogs.values()) {
            dialog.destroy();
        }
        this.dialogs.clear();
        this.activeDialog = null;
    }
}
// #endregion

// #region 工厂函数
/**
 * 创建对话框 - 便捷函数创建对话框实例
 * @param {Object} config - 对话框配置
 * @returns {Dialog} 对话框实例
 */
export function createDialog(config = {}) {
    return new Dialog(config);
}

/**
 * 创建模态对话框 - 便捷函数创建模态对话框
 * @param {Object} config - 对话框配置
 * @returns {Dialog} 对话框实例
 */
export function createModalDialog(config = {}) {
    return new Dialog({
        ...config,
        modal: true,
        closable: true,
        draggable: true
    });
}

/**
 * 创建确认对话框 - 便捷函数创建确认对话框
 * @param {string} message - 确认消息
 * @param {string} title - 对话框标题
 * @returns {Promise<boolean>} 确认结果
 */
export function createConfirmDialog(message, title = '确认') {
    const manager = new DialogManager();
    return manager.confirm({ message, title });
}

/**
 * 创建提示对话框 - 便捷函数创建提示对话框
 * @param {string} message - 提示消息
 * @param {string} title - 对话框标题
 * @returns {Promise<boolean>} 关闭结果
 */
export function createAlertDialog(message, title = '提示') {
    const manager = new DialogManager();
    return manager.alert({ message, title });
}

/**
 * 创建输入对话框 - 便捷函数创建输入对话框
 * @param {string} message - 输入提示
 * @param {string} title - 对话框标题
 * @param {string} defaultValue - 默认值
 * @returns {Promise<string|null>} 输入结果
 */
export function createPromptDialog(message, title = '输入', defaultValue = '') {
    const manager = new DialogManager();
    return manager.prompt({ message, title, defaultValue });
}
// #endregion

// #region 默认实例导出
/**
 * 默认对话框管理器实例
 */
export const dialogManager = new DialogManager();
// #endregion 