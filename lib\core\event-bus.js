/**
 * @file 事件总线模块 - 传统架构版本
 * <AUTHOR> Team
 * @description 
 * 应用程序的事件通信中心，提供发布-订阅模式的事件系统
 * 转换自ES6模块系统到传统script标签架构
 * 
 * 功能特性：
 * - 事件发布和订阅
 * - 事件命名空间
 * - 一次性事件监听
 * - 事件优先级
 * - 事件历史记录
 * - 异步事件处理
 * - 错误处理和恢复
 */

(function(SmartOffice) {
    'use strict';
    
    // 检查命名空间和依赖
    if (!SmartOffice || !SmartOffice.Core) {
        throw new Error('SmartOffice.Core命名空间未初始化');
    }
    
    // 注册模块
    SmartOffice.Modules.register('Core.EventBus', []);
    
    // #region 事件总线类定义
    /**
     * @class EventBus - 事件总线类
     * @description 管理应用程序的事件通信
     */
    class EventBus {
        /**
         * 构造函数 - 初始化事件总线
         */
        constructor() {
            // 事件监听器存储
            this.listeners = new Map();
            
            // 一次性监听器存储
            this.onceListeners = new Map();
            
            // 事件历史记录
            this.eventHistory = [];
            this.maxHistorySize = 1000;
            
            // 事件统计
            this.stats = {
                totalEvents: 0,
                totalListeners: 0,
                errorCount: 0
            };
            
            // 调试模式
            this.debugMode = false;
            
            // 事件处理队列
            this.eventQueue = [];
            this.isProcessingQueue = false;
            
            console.log('[EventBus] 事件总线已初始化');
        }

        /**
         * 添加事件监听器
         * @param {string} event - 事件名称
         * @param {Function} listener - 监听器函数
         * @param {Object} [options] - 选项
         * @param {number} [options.priority=0] - 优先级（数字越大优先级越高）
         * @param {boolean} [options.once=false] - 是否只监听一次
         * @param {Object} [options.context] - 执行上下文
         * @returns {Function} 取消监听的函数
         */
        on(event, listener, options = {}) {
            if (typeof event !== 'string' || typeof listener !== 'function') {
                throw new Error('事件名称必须是字符串，监听器必须是函数');
            }
            
            const {
                priority = 0,
                once = false,
                context = null
            } = options;
            
            // 创建监听器对象
            const listenerObj = {
                listener,
                priority,
                context,
                id: this._generateListenerId(),
                addedAt: new Date()
            };
            
            // 选择存储位置
            const storage = once ? this.onceListeners : this.listeners;
            
            // 添加到对应的事件监听器列表
            if (!storage.has(event)) {
                storage.set(event, []);
            }
            
            const eventListeners = storage.get(event);
            eventListeners.push(listenerObj);
            
            // 按优先级排序（优先级高的在前）
            eventListeners.sort((a, b) => b.priority - a.priority);
            
            // 更新统计
            this.stats.totalListeners++;
            
            if (this.debugMode) {
                console.log(`[EventBus] 添加监听器: ${event} (优先级: ${priority}, 一次性: ${once})`);
            }
            
            // 返回取消监听的函数
            return () => this.off(event, listener);
        }

        /**
         * 添加一次性事件监听器
         * @param {string} event - 事件名称
         * @param {Function} listener - 监听器函数
         * @param {Object} [options] - 选项
         * @returns {Function} 取消监听的函数
         */
        once(event, listener, options = {}) {
            return this.on(event, listener, { ...options, once: true });
        }

        /**
         * 移除事件监听器
         * @param {string} event - 事件名称
         * @param {Function} [listener] - 监听器函数，不提供则移除所有监听器
         */
        off(event, listener) {
            if (typeof event !== 'string') {
                throw new Error('事件名称必须是字符串');
            }
            
            // 从普通监听器中移除
            this._removeFromStorage(this.listeners, event, listener);
            
            // 从一次性监听器中移除
            this._removeFromStorage(this.onceListeners, event, listener);
            
            if (this.debugMode) {
                console.log(`[EventBus] 移除监听器: ${event}`);
            }
        }

        /**
         * 发布事件
         * @param {string} event - 事件名称
         * @param {*} [data] - 事件数据
         * @param {Object} [options] - 选项
         * @param {boolean} [options.async=false] - 是否异步处理
         * @param {number} [options.delay=0] - 延迟时间（毫秒）
         * @returns {Promise<Array>} 所有监听器的执行结果
         */
        async emit(event, data, options = {}) {
            if (typeof event !== 'string') {
                throw new Error('事件名称必须是字符串');
            }
            
            const {
                async = false,
                delay = 0
            } = options;
            
            // 创建事件对象
            const eventObj = {
                name: event,
                data,
                timestamp: new Date(),
                id: this._generateEventId()
            };
            
            // 添加到历史记录
            this._addToHistory(eventObj);
            
            // 更新统计
            this.stats.totalEvents++;
            
            if (this.debugMode) {
                console.log(`[EventBus] 发布事件: ${event}`, data);
            }
            
            // 延迟处理
            if (delay > 0) {
                await this._delay(delay);
            }
            
            // 异步处理
            if (async) {
                this._addToQueue(eventObj);
                return [];
            }
            
            // 同步处理
            return await this._processEvent(eventObj);
        }

        /**
         * 发布异步事件
         * @param {string} event - 事件名称
         * @param {*} [data] - 事件数据
         * @param {number} [delay=0] - 延迟时间（毫秒）
         */
        emitAsync(event, data, delay = 0) {
            return this.emit(event, data, { async: true, delay });
        }

        /**
         * 等待特定事件
         * @param {string} event - 事件名称
         * @param {number} [timeout=0] - 超时时间（毫秒），0表示不超时
         * @returns {Promise} 事件数据的Promise
         */
        waitFor(event, timeout = 0) {
            return new Promise((resolve, reject) => {
                let timeoutId = null;
                
                // 设置超时
                if (timeout > 0) {
                    timeoutId = setTimeout(() => {
                        this.off(event, listener);
                        reject(new Error(`等待事件 ${event} 超时`));
                    }, timeout);
                }
                
                // 监听事件
                const listener = (data) => {
                    if (timeoutId) {
                        clearTimeout(timeoutId);
                    }
                    resolve(data);
                };
                
                this.once(event, listener);
            });
        }

        /**
         * 获取事件监听器数量
         * @param {string} [event] - 事件名称，不提供则返回所有事件的监听器总数
         * @returns {number} 监听器数量
         */
        listenerCount(event) {
            if (event) {
                const normalCount = this.listeners.has(event) ? this.listeners.get(event).length : 0;
                const onceCount = this.onceListeners.has(event) ? this.onceListeners.get(event).length : 0;
                return normalCount + onceCount;
            } else {
                return this.stats.totalListeners;
            }
        }

        /**
         * 获取所有事件名称
         * @returns {Array<string>} 事件名称数组
         */
        eventNames() {
            const normalEvents = Array.from(this.listeners.keys());
            const onceEvents = Array.from(this.onceListeners.keys());
            return [...new Set([...normalEvents, ...onceEvents])];
        }

        /**
         * 清除所有监听器
         * @param {string} [event] - 事件名称，不提供则清除所有事件的监听器
         */
        removeAllListeners(event) {
            if (event) {
                this.listeners.delete(event);
                this.onceListeners.delete(event);
            } else {
                this.listeners.clear();
                this.onceListeners.clear();
                this.stats.totalListeners = 0;
            }
            
            if (this.debugMode) {
                console.log(`[EventBus] 清除监听器: ${event || 'all'}`);
            }
        }

        /**
         * 获取事件历史
         * @param {number} [limit=100] - 返回的事件数量限制
         * @returns {Array} 事件历史数组
         */
        getHistory(limit = 100) {
            return this.eventHistory.slice(-limit);
        }

        /**
         * 清除事件历史
         */
        clearHistory() {
            this.eventHistory = [];
            console.log('[EventBus] 事件历史已清除');
        }

        /**
         * 获取统计信息
         * @returns {Object} 统计信息对象
         */
        getStats() {
            return {
                ...this.stats,
                activeEvents: this.eventNames().length,
                historySize: this.eventHistory.length,
                queueSize: this.eventQueue.length
            };
        }

        /**
         * 启用调试模式
         * @param {boolean} [enabled=true] - 是否启用
         */
        setDebugMode(enabled = true) {
            this.debugMode = enabled;
            console.log(`[EventBus] 调试模式: ${enabled ? '启用' : '禁用'}`);
        }

        /**
         * 处理事件
         * @param {Object} eventObj - 事件对象
         * @returns {Promise<Array>} 监听器执行结果数组
         * @private
         */
        async _processEvent(eventObj) {
            const { name, data } = eventObj;
            const results = [];

            try {
                // 获取普通监听器
                const normalListeners = this.listeners.get(name) || [];

                // 获取一次性监听器
                const onceListeners = this.onceListeners.get(name) || [];

                // 合并并按优先级排序
                const allListeners = [...normalListeners, ...onceListeners]
                    .sort((a, b) => b.priority - a.priority);

                // 执行监听器
                for (const listenerObj of allListeners) {
                    try {
                        const { listener, context } = listenerObj;
                        const result = context ? listener.call(context, data) : listener(data);

                        // 处理Promise结果
                        if (result instanceof Promise) {
                            results.push(await result);
                        } else {
                            results.push(result);
                        }
                    } catch (error) {
                        console.error(`[EventBus] 监听器执行失败: ${name}`, error);
                        this.stats.errorCount++;
                        results.push({ error: error.message });
                    }
                }

                // 清除一次性监听器
                if (onceListeners.length > 0) {
                    this.onceListeners.delete(name);
                    this.stats.totalListeners -= onceListeners.length;
                }

            } catch (error) {
                console.error(`[EventBus] 事件处理失败: ${name}`, error);
                this.stats.errorCount++;
            }

            return results;
        }

        /**
         * 从存储中移除监听器
         * @param {Map} storage - 存储对象
         * @param {string} event - 事件名称
         * @param {Function} [listener] - 监听器函数
         * @private
         */
        _removeFromStorage(storage, event, listener) {
            if (!storage.has(event)) {
                return;
            }

            const eventListeners = storage.get(event);

            if (listener) {
                // 移除特定监听器
                const index = eventListeners.findIndex(obj => obj.listener === listener);
                if (index > -1) {
                    eventListeners.splice(index, 1);
                    this.stats.totalListeners--;
                }
            } else {
                // 移除所有监听器
                this.stats.totalListeners -= eventListeners.length;
                eventListeners.length = 0;
            }

            // 如果没有监听器了，删除事件
            if (eventListeners.length === 0) {
                storage.delete(event);
            }
        }

        /**
         * 添加事件到历史记录
         * @param {Object} eventObj - 事件对象
         * @private
         */
        _addToHistory(eventObj) {
            this.eventHistory.push(eventObj);

            // 限制历史记录大小
            if (this.eventHistory.length > this.maxHistorySize) {
                this.eventHistory.shift();
            }
        }

        /**
         * 添加事件到处理队列
         * @param {Object} eventObj - 事件对象
         * @private
         */
        _addToQueue(eventObj) {
            this.eventQueue.push(eventObj);

            if (!this.isProcessingQueue) {
                this._processQueue();
            }
        }

        /**
         * 处理事件队列
         * @private
         */
        async _processQueue() {
            this.isProcessingQueue = true;

            while (this.eventQueue.length > 0) {
                const eventObj = this.eventQueue.shift();
                await this._processEvent(eventObj);
            }

            this.isProcessingQueue = false;
        }

        /**
         * 延迟函数
         * @param {number} ms - 延迟毫秒数
         * @returns {Promise}
         * @private
         */
        _delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        /**
         * 生成监听器ID
         * @returns {string} 监听器ID
         * @private
         */
        _generateListenerId() {
            return `listener_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        /**
         * 生成事件ID
         * @returns {string} 事件ID
         * @private
         */
        _generateEventId() {
            return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        /**
         * 销毁事件总线
         */
        destroy() {
            // 清除所有监听器
            this.removeAllListeners();

            // 清除历史记录
            this.clearHistory();

            // 清除队列
            this.eventQueue = [];

            // 重置统计
            this.stats = {
                totalEvents: 0,
                totalListeners: 0,
                errorCount: 0
            };

            console.log('[EventBus] 事件总线已销毁');
        }
    }
    // #endregion

    // #region 全局事件总线实例管理
    let globalEventBus = null;

    /**
     * 创建事件总线实例
     * @returns {EventBus} 事件总线实例
     */
    function createEventBus() {
        return new EventBus();
    }

    /**
     * 获取全局事件总线实例
     * @returns {EventBus} 全局事件总线
     */
    function getEventBus() {
        if (!globalEventBus) {
            globalEventBus = new EventBus();
        }
        return globalEventBus;
    }
    // #endregion

    // #region 暴露到命名空间
    // 暴露事件总线类
    SmartOffice.Core.EventBus = EventBus;

    // 暴露工厂函数
    SmartOffice.Core.createEventBus = createEventBus;
    SmartOffice.Core.getEventBus = getEventBus;

    // 暴露默认实例
    SmartOffice.Core.Events = getEventBus();

    // 标记模块已加载
    SmartOffice.Modules.markLoaded('Core.EventBus');

    // 输出加载信息
    console.info('🚌 事件总线模块加载完成 (传统架构)');
    // #endregion

})(window.SmartOffice);
