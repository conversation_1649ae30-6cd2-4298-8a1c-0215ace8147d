/**
 * @file 全局状态管理器
 * @description 统一管理SmartOffice应用的全局状态，提供状态持久化和历史管理
 */

// #region 导入依赖模块
import { EventEmitter } from '../core/events/event-emitter.js';
import { deepClone } from '../core/utils/index.js';
import { getLogger } from '../core/utils/logger.js';
// #endregion

// #region StateSnapshot 状态快照类
/**
 * @class StateSnapshot - 状态快照
 * @description 保存特定时刻的状态信息，支持状态回滚和历史查看
 */
export class StateSnapshot {
    /**
     * 构造函数 - 创建状态快照
     * @param {Object} state - 状态数据
     * @param {Object} metadata - 元数据
     */
    constructor(state, metadata = {}) {
        this.id = `snapshot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        this.timestamp = Date.now();
        this.state = deepClone(state);
        this.metadata = {
            description: '',
            tags: [],
            source: 'manual',
            ...metadata
        };
    }

    /**
     * 获取快照信息
     * @returns {Object} 快照信息
     */
    getInfo() {
        return {
            id: this.id,
            timestamp: this.timestamp,
            description: this.metadata.description,
            tags: this.metadata.tags,
            source: this.metadata.source,
            stateKeys: Object.keys(this.state)
        };
    }

    /**
     * 比较与另一个快照的差异
     * @param {StateSnapshot} otherSnapshot - 另一个快照
     * @returns {Object} 差异信息
     */
    diff(otherSnapshot) {
        const differences = {};
        const allKeys = new Set([
            ...Object.keys(this.state),
            ...Object.keys(otherSnapshot.state)
        ]);

        for (const key of allKeys) {
            const thisValue = this.state[key];
            const otherValue = otherSnapshot.state[key];

            if (JSON.stringify(thisValue) !== JSON.stringify(otherValue)) {
                differences[key] = {
                    from: otherValue,
                    to: thisValue
                };
            }
        }

        return {
            snapshotId: this.id,
            comparedWith: otherSnapshot.id,
            differences,
            changeCount: Object.keys(differences).length
        };
    }
}
// #endregion

// #region GlobalStateManager 全局状态管理器类
/**
 * @class GlobalStateManager - 全局状态管理器
 * @description 管理应用的全局状态，支持状态持久化、历史管理和状态订阅
 */
export class GlobalStateManager extends EventEmitter {
    /**
     * 构造函数 - 创建全局状态管理器
     * @param {Object} options - 配置选项
     */
    constructor(options = {}) {
        super();
        
        // 初始化日志记录器
        this.logger = getLogger();
        this.logger.startPerformanceMark('state_manager_construction', 'GlobalStateManager', 'constructor');
        this.logger.info('GlobalStateManager', 'constructor', '开始构造全局状态管理器', {
            optionsKeys: Object.keys(options || {})
        });
        
        this.name = 'GlobalStateManager';
        this.version = '1.0.0';
        this.isInitialized = false;
        
        // 状态存储
        this.state = {};
        this.initialState = {};
        
        // 历史管理
        this.history = [];
        this.currentHistoryIndex = -1;
        this.snapshots = new Map();
        
        this.logger.trace('GlobalStateManager', 'constructor', '基础存储结构初始化完成');
        
        // 配置选项
        this.options = {
            enablePersistence: true,
            enableHistory: true,
            enableSnapshots: true,
            maxHistorySize: 50,
            maxSnapshotSize: 20,
            autoSnapshot: true,
            snapshotInterval: 300000, // 5分钟
            storageKey: 'smartoffice_state',
            enableDeepWatch: true,
            enableValidation: false,
            ...options
        };
        
        this.logger.debug('GlobalStateManager', 'constructor', '配置选项设置完成', {
            options: this.options
        });
        
        // 状态订阅者
        this.subscribers = new Map();
        this.watchers = new Map();
        this.validators = new Map();
        
        // 状态统计
        this.stats = {
            totalStateChanges: 0,
            totalSubscribers: 0,
            totalWatchers: 0,
            lastChangeTime: null,
            averageChangeInterval: 0
        };
        
        this.logger.trace('GlobalStateManager', 'constructor', '订阅者和统计信息初始化完成');
        
        // 设置初始状态
        this.logger.debug('GlobalStateManager', 'constructor', '开始设置初始状态');
        this._setupInitialState();
        
        const constructionDuration = this.logger.endPerformanceMark('state_manager_construction', 'GlobalStateManager', 'constructor');
        this.logger.info('GlobalStateManager', 'constructor', '✅ 全局状态管理器构造完成', {
            duration: `${constructionDuration?.toFixed(2)}ms`,
            stateKeys: Object.keys(this.state),
            enabledFeatures: {
                persistence: this.options.enablePersistence,
                history: this.options.enableHistory,
                snapshots: this.options.enableSnapshots,
                validation: this.options.enableValidation
            }
        });
        
        // 记录构造统计
        this.logger.incrementCounter('state_manager_instances', 'GlobalStateManager');
    }

    /**
     * 初始化状态管理器
     */
    async initialize() {
        try {
            console.log('🔧 正在初始化全局状态管理器...');
            
            // 加载持久化状态
            if (this.options.enablePersistence) {
                await this._loadPersistedState();
            }
            
            // 设置自动快照
            if (this.options.autoSnapshot) {
                this._setupAutoSnapshot();
            }
            
            // 创建初始快照
            if (this.options.enableSnapshots) {
                this.createSnapshot('初始状态', { source: 'initialization' });
            }
            
            this.isInitialized = true;
            
            this.emit('state:initialized', {
                manager: this.name,
                stateKeys: Object.keys(this.state),
                options: this.options
            });
            
            console.log('✅ 全局状态管理器初始化完成');
            
        } catch (error) {
            console.error('❌ 全局状态管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 设置初始状态
     * @private
     */
    _setupInitialState() {
        this.initialState = {
            // 应用状态
            app: {
                isReady: false,
                currentView: 'home',
                theme: 'default',
                language: 'zh-CN',
                lastActivity: Date.now()
            },
            
            // 用户状态
            user: {
                preferences: {},
                recentDocuments: [],
                favoriteTemplates: [],
                settings: {}
            },
            
            // 文档状态
            document: {
                current: null,
                isModified: false,
                lastSaved: null,
                autoSaveEnabled: true,
                versions: []
            },
            
            // UI状态
            ui: {
                sidebarVisible: true,
                toolbarVisible: true,
                statusBarVisible: true,
                activePanel: null,
                modals: [],
                notifications: []
            },
            
            // 工作流状态
            workflow: {
                activeWorkflows: [],
                completedWorkflows: [],
                failedWorkflows: [],
                queuedWorkflows: []
            },
            
            // 导出状态
            export: {
                activeExports: [],
                completedExports: [],
                exportHistory: [],
                defaultSettings: {}
            },
            
            // 性能状态
            performance: {
                memoryUsage: 0,
                renderTime: 0,
                loadTime: 0,
                errorCount: 0
            },
            
            // 网络状态
            network: {
                isOnline: navigator.onlineStatus || true,
                lastSync: null,
                pendingRequests: 0,
                failedRequests: []
            }
        };
        
        this.state = deepClone(this.initialState);
    }

    /**
     * 获取状态值
     * @param {string} path - 状态路径，支持点号分隔
     * @param {*} defaultValue - 默认值
     * @returns {*} 状态值
     */
    getState(path = null, defaultValue = undefined) {
        if (!path) {
            return deepClone(this.state);
        }
        
        const value = this._getNestedValue(this.state, path);
        return value !== undefined ? deepClone(value) : defaultValue;
    }

    /**
     * 设置状态值
     * @param {string|Object} path - 状态路径或状态对象
     * @param {*} value - 状态值
     * @param {Object} options - 设置选项
     * @returns {boolean} 是否设置成功
     */
    setState(path, value = undefined, options = {}) {
        try {
            let changes = {};
            
            if (typeof path === 'object') {
                // 批量设置
                changes = path;
                options = value || {};
            } else {
                // 单个设置
                changes[path] = value;
            }
            
            // 验证状态变更
            if (this.options.enableValidation && !options.skipValidation) {
                this._validateStateChanges(changes);
            }
            
            // 记录历史
            if (this.options.enableHistory && !options.skipHistory) {
                this._recordHistory();
            }
            
            // 应用状态变更
            const oldState = deepClone(this.state);
            const actualChanges = {};
            
            for (const [changePath, changeValue] of Object.entries(changes)) {
                const oldValue = this._getNestedValue(this.state, changePath);
                
                // 只有值真正改变时才记录
                if (JSON.stringify(oldValue) !== JSON.stringify(changeValue)) {
                    this._setNestedValue(this.state, changePath, changeValue);
                    actualChanges[changePath] = {
                        oldValue,
                        newValue: changeValue
                    };
                }
            }
            
            // 如果有实际变更，触发事件
            if (Object.keys(actualChanges).length > 0) {
                this._emitStateChanges(actualChanges, oldState);
                
                // 更新统计信息
                this._updateStats();
                
                // 自动保存
                if (this.options.enablePersistence && !options.skipPersistence) {
                    this._saveState();
                }
            }
            
            return true;
            
        } catch (error) {
            console.error('状态设置失败:', error);
            this.emit('state:error', {
                operation: 'setState',
                path,
                value,
                error: error.message
            });
            return false;
        }
    }

    /**
     * 重置状态
     * @param {string} path - 状态路径，如果为空则重置所有状态
     * @param {Object} options - 重置选项
     */
    resetState(path = null, options = {}) {
        try {
            if (path) {
                // 重置特定状态
                const initialValue = this._getNestedValue(this.initialState, path);
                this.setState(path, initialValue, options);
            } else {
                // 重置所有状态
                const oldState = deepClone(this.state);
                this.state = deepClone(this.initialState);
                
                this.emit('state:reset', {
                    oldState,
                    newState: this.state,
                    timestamp: Date.now()
                });
                
                if (this.options.enablePersistence && !options.skipPersistence) {
                    this._saveState();
                }
            }
            
        } catch (error) {
            console.error('状态重置失败:', error);
            this.emit('state:error', {
                operation: 'resetState',
                path,
                error: error.message
            });
        }
    }

    /**
     * 订阅状态变更
     * @param {string} path - 状态路径
     * @param {Function} callback - 回调函数
     * @param {Object} options - 订阅选项
     * @returns {string} 订阅ID
     */
    subscribe(path, callback, options = {}) {
        const subscriptionId = `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        this.subscribers.set(subscriptionId, {
            path,
            callback,
            options: {
                immediate: false,
                deep: true,
                ...options
            },
            created: Date.now()
        });
        
        // 如果设置了立即执行，立即调用一次回调
        if (options.immediate) {
            const currentValue = this.getState(path);
            try {
                callback({
                    path,
                    value: currentValue,
                    oldValue: undefined,
                    timestamp: Date.now(),
                    type: 'immediate'
                });
            } catch (error) {
                console.error(`订阅回调执行失败 (${subscriptionId}):`, error);
            }
        }
        
        this.stats.totalSubscribers++;
        
        this.emit('state:subscribed', {
            subscriptionId,
            path,
            subscriberCount: this.subscribers.size
        });
        
        return subscriptionId;
    }

    /**
     * 取消订阅
     * @param {string} subscriptionId - 订阅ID
     * @returns {boolean} 是否成功取消
     */
    unsubscribe(subscriptionId) {
        const removed = this.subscribers.delete(subscriptionId);
        
        if (removed) {
            this.emit('state:unsubscribed', {
                subscriptionId,
                subscriberCount: this.subscribers.size
            });
        }
        
        return removed;
    }

    /**
     * 监听状态变更（类似于watch）
     * @param {string} path - 状态路径
     * @param {Function} callback - 回调函数
     * @param {Object} options - 监听选项
     * @returns {string} 监听器ID
     */
    watch(path, callback, options = {}) {
        const watcherId = `watch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        this.watchers.set(watcherId, {
            path,
            callback,
            options: {
                deep: this.options.enableDeepWatch,
                immediate: false,
                ...options
            },
            created: Date.now()
        });
        
        this.stats.totalWatchers++;
        
        this.emit('state:watched', {
            watcherId,
            path,
            watcherCount: this.watchers.size
        });
        
        return watcherId;
    }

    /**
     * 取消监听
     * @param {string} watcherId - 监听器ID
     * @returns {boolean} 是否成功取消
     */
    unwatch(watcherId) {
        const removed = this.watchers.delete(watcherId);
        
        if (removed) {
            this.emit('state:unwatched', {
                watcherId,
                watcherCount: this.watchers.size
            });
        }
        
        return removed;
    }

    /**
     * 创建状态快照
     * @param {string} description - 快照描述
     * @param {Object} metadata - 元数据
     * @returns {StateSnapshot} 状态快照
     */
    createSnapshot(description = '', metadata = {}) {
        if (!this.options.enableSnapshots) {
            return null;
        }
        
        const snapshot = new StateSnapshot(this.state, {
            description,
            ...metadata
        });
        
        this.snapshots.set(snapshot.id, snapshot);
        
        // 限制快照数量
        if (this.snapshots.size > this.options.maxSnapshotSize) {
            const oldestSnapshot = Array.from(this.snapshots.values())
                .sort((a, b) => a.timestamp - b.timestamp)[0];
            this.snapshots.delete(oldestSnapshot.id);
        }
        
        this.emit('state:snapshot:created', {
            snapshotId: snapshot.id,
            description,
            snapshotCount: this.snapshots.size
        });
        
        return snapshot;
    }

    /**
     * 恢复到快照状态
     * @param {string} snapshotId - 快照ID
     * @param {Object} options - 恢复选项
     * @returns {boolean} 是否恢复成功
     */
    restoreSnapshot(snapshotId, options = {}) {
        try {
            const snapshot = this.snapshots.get(snapshotId);
            if (!snapshot) {
                throw new Error(`Snapshot ${snapshotId} not found`);
            }
            
            const oldState = deepClone(this.state);
            this.state = deepClone(snapshot.state);
            
            this.emit('state:snapshot:restored', {
                snapshotId,
                oldState,
                newState: this.state,
                timestamp: Date.now()
            });
            
            // 通知所有订阅者
            this._notifyAllSubscribers('snapshot-restore');
            
            if (this.options.enablePersistence && !options.skipPersistence) {
                this._saveState();
            }
            
            return true;
            
        } catch (error) {
            console.error('快照恢复失败:', error);
            this.emit('state:error', {
                operation: 'restoreSnapshot',
                snapshotId,
                error: error.message
            });
            return false;
        }
    }

    /**
     * 获取状态历史
     * @param {number} limit - 限制数量
     * @returns {Array} 状态历史
     */
    getHistory(limit = 20) {
        return this.history.slice(-limit);
    }

    /**
     * 回滚到历史状态
     * @param {number} steps - 回滚步数
     * @returns {boolean} 是否回滚成功
     */
    rollback(steps = 1) {
        try {
            if (!this.options.enableHistory) {
                throw new Error('History is disabled');
            }
            
            if (steps <= 0 || steps > this.history.length) {
                throw new Error('Invalid rollback steps');
            }
            
            const targetIndex = this.history.length - steps;
            const targetState = this.history[targetIndex];
            
            const oldState = deepClone(this.state);
            this.state = deepClone(targetState.state);
            
            this.emit('state:rollback', {
                steps,
                targetTimestamp: targetState.timestamp,
                oldState,
                newState: this.state,
                timestamp: Date.now()
            });
            
            // 通知所有订阅者
            this._notifyAllSubscribers('rollback');
            
            if (this.options.enablePersistence) {
                this._saveState();
            }
            
            return true;
            
        } catch (error) {
            console.error('状态回滚失败:', error);
            this.emit('state:error', {
                operation: 'rollback',
                steps,
                error: error.message
            });
            return false;
        }
    }

    /**
     * 导出状态
     * @param {Array<string>} paths - 要导出的状态路径
     * @returns {Object} 导出的状态
     */
    exportState(paths = null) {
        if (!paths) {
            return deepClone(this.state);
        }
        
        const exported = {};
        
        for (const path of paths) {
            const value = this.getState(path);
            if (value !== undefined) {
                this._setNestedValue(exported, path, value);
            }
        }
        
        return exported;
    }

    /**
     * 导入状态
     * @param {Object} state - 要导入的状态
     * @param {Object} options - 导入选项
     * @returns {boolean} 是否导入成功
     */
    importState(state, options = {}) {
        try {
            if (!state || typeof state !== 'object') {
                throw new Error('Invalid state object');
            }
            
            // 验证导入的状态
            if (this.options.enableValidation && !options.skipValidation) {
                this._validateState(state);
            }
            
            // 合并或替换状态
            if (options.merge !== false) {
                this.state = this._mergeState(this.state, state);
            } else {
                this.state = deepClone(state);
            }
            
            this.emit('state:imported', {
                importedPaths: Object.keys(state),
                merged: options.merge !== false,
                timestamp: Date.now()
            });
            
            // 通知所有订阅者
            this._notifyAllSubscribers('import');
            
            if (this.options.enablePersistence && !options.skipPersistence) {
                this._saveState();
            }
            
            return true;
            
        } catch (error) {
            console.error('状态导入失败:', error);
            this.emit('state:error', {
                operation: 'importState',
                error: error.message
            });
            return false;
        }
    }

    // #region 私有方法

    /**
     * 获取嵌套值
     * @param {Object} obj - 对象
     * @param {string} path - 路径
     * @returns {*} 值
     * @private
     */
    _getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : undefined;
        }, obj);
    }

    /**
     * 设置嵌套值
     * @param {Object} obj - 对象
     * @param {string} path - 路径
     * @param {*} value - 值
     * @private
     */
    _setNestedValue(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        
        const target = keys.reduce((current, key) => {
            if (!current[key] || typeof current[key] !== 'object') {
                current[key] = {};
            }
            return current[key];
        }, obj);
        
        target[lastKey] = value;
    }

    /**
     * 合并状态
     * @param {Object} target - 目标状态
     * @param {Object} source - 源状态
     * @returns {Object} 合并后的状态
     * @private
     */
    _mergeState(target, source) {
        const result = deepClone(target);
        
        for (const [key, value] of Object.entries(source)) {
            if (value && typeof value === 'object' && !Array.isArray(value)) {
                result[key] = this._mergeState(result[key] || {}, value);
            } else {
                result[key] = value;
            }
        }
        
        return result;
    }

    /**
     * 验证状态变更
     * @param {Object} changes - 状态变更
     * @private
     */
    _validateStateChanges(changes) {
        for (const [path, value] of Object.entries(changes)) {
            if (this.validators.has(path)) {
                const validators = this.validators.get(path);
                for (const validator of validators) {
                    if (!validator(value, this.state)) {
                        throw new Error(`State validation failed for ${path}`);
                    }
                }
            }
        }
    }

    /**
     * 验证状态
     * @param {Object} state - 要验证的状态
     * @private
     */
    _validateState(state) {
        // 这里可以添加状态结构验证逻辑
        // 例如检查必需的字段、数据类型等
    }

    /**
     * 记录状态历史
     * @private
     */
    _recordHistory() {
        if (!this.options.enableHistory) {
            return;
        }
        
        this.history.push({
            timestamp: Date.now(),
            state: deepClone(this.state)
        });
        
        // 限制历史记录大小
        if (this.history.length > this.options.maxHistorySize) {
            this.history.shift();
        }
    }

    /**
     * 触发状态变更事件
     * @param {Object} changes - 状态变更
     * @param {Object} oldState - 旧状态
     * @private
     */
    _emitStateChanges(changes, oldState) {
        // 触发全局状态变更事件
        this.emit('state:changed', {
            changes,
            oldState,
            newState: this.state,
            timestamp: Date.now()
        });
        
        // 触发特定路径的状态变更事件
        for (const [path, change] of Object.entries(changes)) {
            this.emit(`state:changed:${path}`, {
                path,
                oldValue: change.oldValue,
                newValue: change.newValue,
                timestamp: Date.now()
            });
            
            // 通知订阅者和监听器
            this._notifySubscribers(path, change.newValue, change.oldValue);
            this._notifyWatchers(path, change.newValue, change.oldValue);
        }
    }

    /**
     * 通知订阅者
     * @param {string} path - 状态路径
     * @param {*} newValue - 新值
     * @param {*} oldValue - 旧值
     * @private
     */
    _notifySubscribers(path, newValue, oldValue) {
        for (const [subscriptionId, subscription] of this.subscribers) {
            if (this._pathMatches(path, subscription.path)) {
                try {
                    subscription.callback({
                        path,
                        value: newValue,
                        oldValue,
                        timestamp: Date.now(),
                        type: 'change'
                    });
                } catch (error) {
                    console.error(`订阅者回调执行失败 (${subscriptionId}):`, error);
                }
            }
        }
    }

    /**
     * 通知监听器
     * @param {string} path - 状态路径
     * @param {*} newValue - 新值
     * @param {*} oldValue - 旧值
     * @private
     */
    _notifyWatchers(path, newValue, oldValue) {
        for (const [watcherId, watcher] of this.watchers) {
            if (this._pathMatches(path, watcher.path)) {
                try {
                    watcher.callback({
                        path,
                        value: newValue,
                        oldValue,
                        timestamp: Date.now(),
                        type: 'watch'
                    });
                } catch (error) {
                    console.error(`监听器回调执行失败 (${watcherId}):`, error);
                }
            }
        }
    }

    /**
     * 通知所有订阅者
     * @param {string} type - 通知类型
     * @private
     */
    _notifyAllSubscribers(type) {
        for (const [subscriptionId, subscription] of this.subscribers) {
            try {
                const currentValue = this.getState(subscription.path);
                subscription.callback({
                    path: subscription.path,
                    value: currentValue,
                    oldValue: undefined,
                    timestamp: Date.now(),
                    type
                });
            } catch (error) {
                console.error(`订阅者回调执行失败 (${subscriptionId}):`, error);
            }
        }
    }

    /**
     * 检查路径是否匹配
     * @param {string} path - 实际路径
     * @param {string} pattern - 模式路径
     * @returns {boolean} 是否匹配
     * @private
     */
    _pathMatches(path, pattern) {
        if (path === pattern) {
            return true;
        }
        
        // 支持通配符匹配
        if (pattern.includes('*')) {
            const regex = new RegExp(pattern.replace(/\*/g, '.*'));
            return regex.test(path);
        }
        
        // 支持前缀匹配
        return path.startsWith(pattern + '.');
    }

    /**
     * 更新统计信息
     * @private
     */
    _updateStats() {
        this.stats.totalStateChanges++;
        
        const now = Date.now();
        if (this.stats.lastChangeTime) {
            const interval = now - this.stats.lastChangeTime;
            this.stats.averageChangeInterval = 
                (this.stats.averageChangeInterval * (this.stats.totalStateChanges - 1) + interval) / 
                this.stats.totalStateChanges;
        }
        
        this.stats.lastChangeTime = now;
    }

    /**
     * 加载持久化状态
     * @private
     */
    async _loadPersistedState() {
        try {
            if (typeof localStorage !== 'undefined') {
                const stored = localStorage.getItem(this.options.storageKey);
                if (stored) {
                    const persistedState = JSON.parse(stored);
                    this.state = this._mergeState(this.state, persistedState);
                    
                    this.emit('state:loaded', {
                        source: 'localStorage',
                        paths: Object.keys(persistedState)
                    });
                }
            }
        } catch (error) {
            console.warn('加载持久化状态失败:', error);
        }
    }

    /**
     * 保存状态
     * @private
     */
    _saveState() {
        try {
            if (typeof localStorage !== 'undefined') {
                localStorage.setItem(this.options.storageKey, JSON.stringify(this.state));
                
                this.emit('state:saved', {
                    destination: 'localStorage',
                    timestamp: Date.now()
                });
            }
        } catch (error) {
            console.warn('保存状态失败:', error);
        }
    }

    /**
     * 设置自动快照
     * @private
     */
    _setupAutoSnapshot() {
        if (this.options.snapshotInterval > 0) {
            this._snapshotTimer = setInterval(() => {
                this.createSnapshot('自动快照', { source: 'auto' });
            }, this.options.snapshotInterval);
        }
    }
    // #endregion

    /**
     * 获取状态管理器信息
     * @returns {Object} 管理器信息
     */
    getInfo() {
        return {
            name: this.name,
            version: this.version,
            isInitialized: this.isInitialized,
            stateKeys: Object.keys(this.state),
            subscriberCount: this.subscribers.size,
            watcherCount: this.watchers.size,
            snapshotCount: this.snapshots.size,
            historySize: this.history.length,
            stats: { ...this.stats },
            options: { ...this.options }
        };
    }

    /**
     * 获取所有快照信息
     * @returns {Array} 快照信息数组
     */
    getSnapshots() {
        return Array.from(this.snapshots.values())
            .map(snapshot => snapshot.getInfo())
            .sort((a, b) => b.timestamp - a.timestamp);
    }

    /**
     * 删除快照
     * @param {string} snapshotId - 快照ID
     * @returns {boolean} 是否删除成功
     */
    deleteSnapshot(snapshotId) {
        const deleted = this.snapshots.delete(snapshotId);
        
        if (deleted) {
            this.emit('state:snapshot:deleted', {
                snapshotId,
                snapshotCount: this.snapshots.size
            });
        }
        
        return deleted;
    }

    /**
     * 添加状态验证器
     * @param {string} path - 状态路径
     * @param {Function} validator - 验证函数
     */
    addValidator(path, validator) {
        if (!this.validators.has(path)) {
            this.validators.set(path, []);
        }
        
        this.validators.get(path).push(validator);
        
        this.emit('state:validator:added', {
            path,
            validatorCount: this.validators.get(path).length
        });
    }

    /**
     * 移除状态验证器
     * @param {string} path - 状态路径
     * @param {Function} validator - 验证函数
     * @returns {boolean} 是否成功移除
     */
    removeValidator(path, validator) {
        if (!this.validators.has(path)) {
            return false;
        }
        
        const validators = this.validators.get(path);
        const index = validators.indexOf(validator);
        
        if (index !== -1) {
            validators.splice(index, 1);
            
            if (validators.length === 0) {
                this.validators.delete(path);
            }
            
            this.emit('state:validator:removed', {
                path,
                validatorCount: validators.length
            });
            
            return true;
        }
        
        return false;
    }

    /**
     * 销毁状态管理器
     */
    async destroy() {
        // 清理定时器
        if (this._snapshotTimer) {
            clearInterval(this._snapshotTimer);
        }
        
        // 清理订阅者和监听器
        this.subscribers.clear();
        this.watchers.clear();
        this.validators.clear();
        
        // 清理快照和历史
        this.snapshots.clear();
        this.history = [];
        
        // 清理事件监听器
        this.removeAllListeners();
        
        // 最后保存状态
        if (this.options.enablePersistence) {
            this._saveState();
        }
        
        this.isInitialized = false;
        
        console.log('🔄 全局状态管理器已销毁');
    }
}
// #endregion

// #region 工厂函数和便捷方法
/**
 * 创建全局状态管理器
 * @param {Object} options - 配置选项
 * @returns {GlobalStateManager} 状态管理器实例
 */
export function createGlobalStateManager(options = {}) {
    return new GlobalStateManager(options);
}

/**
 * 创建状态快照
 * @param {Object} state - 状态数据
 * @param {Object} metadata - 元数据
 * @returns {StateSnapshot} 状态快照实例
 */
export function createStateSnapshot(state, metadata = {}) {
    return new StateSnapshot(state, metadata);
}
// #endregion 