/**
 * @file 应用初始化器模块
 * <AUTHOR> Team
 * @description 
 * 从index.html中提取的应用初始化功能
 * 负责整个应用的启动、配置、模块加载等
 */

import { getLogger } from './utils/logger.js';
import { getResourceManager } from './utils/resource-manager.js';
import { getNotificationManager } from './utils/notification-manager.js';
import { getDOMHelpers } from './utils/dom-helpers.js';
import { getDocumentEditor } from './ui/document-editor.js';

// #region 应用初始化器类
/**
 * @class AppInitializer - 应用初始化器
 * @description 负责整个应用的初始化和启动
 */
export class AppInitializer {
    /**
     * 构造函数
     * @param {Object} config - 应用配置
     */
    constructor(config = {}) {
        this.logger = getLogger();
        this.config = {
            autoStart: config.autoStart !== false,
            enableResourceFallback: config.enableResourceFallback !== false,
            enablePerformanceMonitoring: config.enablePerformanceMonitoring !== false,
            ...config
        };
        
        this.isInitialized = false;
        this.startTime = performance.now();
        this.modules = new Map();
        
        this.logger.info('AppInitializer', 'constructor', '🚀 SmartOffice应用初始化器创建');
    }

    /**
     * 初始化应用
     * @returns {Promise} 初始化完成的Promise
     */
    async initialize() {
        if (this.isInitialized) {
            this.logger.warn('AppInitializer', 'initialize', '应用已经初始化，跳过重复初始化');
            return this;
        }

        try {
            this.logger.info('AppInitializer', 'initialize', '🔄 开始初始化SmartOffice应用');
            this.logger.startPerformanceMark('app_initialization', 'AppInitializer', 'initialize');

            // 1. 等待DOM就绪
            await this.waitForDOM();

            // 2. 初始化资源管理器
            await this.initializeResourceManager();

            // 3. 初始化核心模块
            await this.initializeCoreModules();

            // 4. 初始化UI组件
            await this.initializeUIComponents();

            // 5. 设置全局错误处理
            this.setupGlobalErrorHandling();

            // 6. 启动应用
            await this.startApplication();

            const initDuration = this.logger.endPerformanceMark('app_initialization', 'AppInitializer', 'initialize');
            this.isInitialized = true;

            this.logger.info('AppInitializer', 'initialize', '✅ SmartOffice应用初始化完成', {
                duration: `${initDuration?.toFixed(2)}ms`,
                totalTime: `${(performance.now() - this.startTime).toFixed(2)}ms`,
                modulesCount: this.modules.size
            });

            return this;

        } catch (error) {
            this.logger.error('AppInitializer', 'initialize', '❌ 应用初始化失败', error);
            throw error;
        }
    }

    /**
     * 等待DOM就绪
     * @private
     * @returns {Promise} DOM就绪的Promise
     */
    async waitForDOM() {
        this.logger.debug('AppInitializer', 'waitForDOM', '等待DOM就绪');
        
        const dom = getDOMHelpers();
        await dom.ready();
        
        this.logger.debug('AppInitializer', 'waitForDOM', '✅ DOM已就绪');
    }

    /**
     * 初始化资源管理器
     * @private
     * @returns {Promise} 初始化完成的Promise
     */
    async initializeResourceManager() {
        this.logger.debug('AppInitializer', 'initializeResourceManager', '初始化资源管理器');
        
        const resourceManager = getResourceManager();
        
        if (this.config.enableResourceFallback) {
            await resourceManager.loadAllResources();
        }
        
        this.modules.set('resourceManager', resourceManager);
        this.logger.debug('AppInitializer', 'initializeResourceManager', '✅ 资源管理器初始化完成');
    }

    /**
     * 初始化核心模块
     * @private
     * @returns {Promise} 初始化完成的Promise
     */
    async initializeCoreModules() {
        this.logger.debug('AppInitializer', 'initializeCoreModules', '初始化核心模块');
        
        // 初始化通知管理器
        const notificationManager = getNotificationManager();
        this.modules.set('notificationManager', notificationManager);
        
        // 初始化DOM辅助工具
        const domHelpers = getDOMHelpers();
        this.modules.set('domHelpers', domHelpers);
        
        this.logger.debug('AppInitializer', 'initializeCoreModules', '✅ 核心模块初始化完成');
    }

    /**
     * 初始化UI组件
     * @private
     * @returns {Promise} 初始化完成的Promise
     */
    async initializeUIComponents() {
        this.logger.debug('AppInitializer', 'initializeUIComponents', '初始化UI组件');
        
        // 初始化文档编辑器
        const documentEditor = getDocumentEditor();
        this.modules.set('documentEditor', documentEditor);
        
        this.logger.debug('AppInitializer', 'initializeUIComponents', '✅ UI组件初始化完成');
    }

    /**
     * 设置全局错误处理
     * @private
     */
    setupGlobalErrorHandling() {
        this.logger.debug('AppInitializer', 'setupGlobalErrorHandling', '设置全局错误处理');
        
        // 捕获未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.logger.error('AppInitializer', 'unhandledrejection', '未处理的Promise拒绝', {
                reason: event.reason,
                promise: event.promise
            });
            
            const notificationManager = this.modules.get('notificationManager');
            if (notificationManager) {
                notificationManager.showError('系统错误', '发生了未处理的异步错误');
            }
        });
        
        // 捕获全局JavaScript错误
        window.addEventListener('error', (event) => {
            this.logger.error('AppInitializer', 'globalError', '全局JavaScript错误', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
            
            const notificationManager = this.modules.get('notificationManager');
            if (notificationManager) {
                notificationManager.showError('脚本错误', event.message);
            }
        });
        
        this.logger.debug('AppInitializer', 'setupGlobalErrorHandling', '✅ 全局错误处理设置完成');
    }

    /**
     * 启动应用
     * @private
     * @returns {Promise} 启动完成的Promise
     */
    async startApplication() {
        this.logger.debug('AppInitializer', 'startApplication', '启动应用');
        
        // 设置全局应用引用
        window.smartOfficeApp = this.createAppInterface();
        
        // 显示启动完成通知
        const notificationManager = this.modules.get('notificationManager');
        if (notificationManager) {
            notificationManager.showSuccess(
                'SmartOffice 2.0', 
                '应用启动完成，所有功能已就绪'
            );
        }
        
        this.logger.info('AppInitializer', 'startApplication', '✅ 应用启动完成');
    }

    /**
     * 创建应用接口
     * @private
     * @returns {Object} 应用接口对象
     */
    createAppInterface() {
        return {
            version: '2.0.0-refactored',
            name: 'SmartOffice',
            mode: 'modular',
            isInitialized: () => this.isInitialized,
            
            // 获取模块
            getModule: (moduleName) => {
                return this.modules.get(moduleName);
            },
            
            // 获取所有模块
            getModules: () => {
                return Array.from(this.modules.keys());
            },
            
            // 获取应用信息
            getAppInfo: () => {
                return {
                    version: '2.0.0-refactored',
                    name: 'SmartOffice',
                    mode: 'modular',
                    isInitialized: this.isInitialized,
                    modulesCount: this.modules.size,
                    uptime: performance.now() - this.startTime,
                    timestamp: new Date().toISOString(),
                    features: {
                        nlp: 'gemini',
                        export: 'full',
                        offline: false,
                        modules: 'es6',
                        refactored: true
                    }
                };
            },
            
            // 重启应用
            restart: async () => {
                this.logger.info('AppInitializer', 'restart', '重启应用');
                await this.destroy();
                await this.initialize();
            },
            
            // 销毁应用
            destroy: () => {
                return this.destroy();
            }
        };
    }

    /**
     * 获取模块
     * @param {string} moduleName - 模块名称
     * @returns {*} 模块实例
     */
    getModule(moduleName) {
        return this.modules.get(moduleName);
    }

    /**
     * 获取所有模块名称
     * @returns {Array} 模块名称列表
     */
    getModuleNames() {
        return Array.from(this.modules.keys());
    }

    /**
     * 检查应用是否已初始化
     * @returns {boolean} 是否已初始化
     */
    isReady() {
        return this.isInitialized;
    }

    /**
     * 获取应用运行时间
     * @returns {number} 运行时间（毫秒）
     */
    getUptime() {
        return performance.now() - this.startTime;
    }

    /**
     * 销毁应用
     * @returns {Promise} 销毁完成的Promise
     */
    async destroy() {
        this.logger.info('AppInitializer', 'destroy', '🔄 开始销毁应用');
        
        try {
            // 销毁所有模块
            for (const [name, module] of this.modules) {
                if (module && typeof module.destroy === 'function') {
                    try {
                        await module.destroy();
                        this.logger.debug('AppInitializer', 'destroy', `模块 ${name} 已销毁`);
                    } catch (error) {
                        this.logger.error('AppInitializer', 'destroy', `销毁模块 ${name} 失败`, error);
                    }
                }
            }
            
            // 清理模块映射
            this.modules.clear();
            
            // 清理全局引用
            if (window.smartOfficeApp) {
                delete window.smartOfficeApp;
            }
            
            this.isInitialized = false;
            
            this.logger.info('AppInitializer', 'destroy', '✅ 应用销毁完成');
            
        } catch (error) {
            this.logger.error('AppInitializer', 'destroy', '❌ 应用销毁失败', error);
            throw error;
        }
    }
}
// #endregion

// #region 全局应用初始化器实例
let globalAppInitializer = null;

/**
 * 获取全局应用初始化器实例
 * @param {Object} config - 应用配置
 * @returns {AppInitializer} 应用初始化器实例
 */
export function getAppInitializer(config = {}) {
    if (!globalAppInitializer) {
        globalAppInitializer = new AppInitializer(config);
    }
    return globalAppInitializer;
}

/**
 * 快速启动应用
 * @param {Object} config - 应用配置
 * @returns {Promise<AppInitializer>} 初始化完成的应用实例
 */
export async function quickStart(config = {}) {
    const app = getAppInitializer(config);
    
    if (!app.isReady()) {
        await app.initialize();
    }
    
    return app;
}
// #endregion
