/**
 * @file SmartOffice 2.0 统一主入口文件
 * <AUTHOR> Team
 * @description 
 * 统一架构的主入口文件，整合js/和src/双重架构
 * 提供完整的应用功能和现代化的模块接口
 * 
 * 架构特性：
 * - 统一的应用初始化器
 * - 完整的功能集成
 * - 现代化的ES6模块系统
 * - 兼容file://和HTTP双模式
 * - 性能监控和错误处理
 */

// #region 核心导入
import { 
    UnifiedAppInitializer,
    createUnifiedApp,
    getUnifiedApp,
    quickStartUnified,
    resetGlobalApp,
    getDefaultApp,
    setDefaultApp
} from './core/unified-app-initializer.js';

import { getLogger } from '../js/utils/logger.js';
// #endregion

// #region 主应用类
/**
 * @class SmartOfficeMainApp - 主应用类
 * @description 统一架构的主应用类，提供完整的应用功能
 */
export class SmartOfficeMainApp {
    /**
     * 构造函数
     * @param {Object} config - 应用配置
     * @param {HTMLElement} container - 容器元素
     */
    constructor(config = {}, container = null) {
        this.logger = getLogger();
        this.config = {
            // 默认配置
            mode: 'unified',
            version: '2.0.0-unified',
            enableAutoStart: true,
            enablePerformanceMonitoring: true,
            enableResourceFallback: true,
            enableWorkflow: true,
            
            // 合并用户配置
            ...config
        };
        
        // 创建统一应用初始化器
        this.app = createUnifiedApp(this.config, container);
        
        // 应用状态
        this.isStarted = false;
        this.startTime = null;
        
        this.logger.info('SmartOfficeMainApp', 'constructor', '🚀 SmartOffice 2.0 统一主应用创建');
        
        // 自动启动
        if (this.config.enableAutoStart) {
            this.start().catch(error => {
                this.logger.error('SmartOfficeMainApp', 'constructor', '自动启动失败', { error });
            });
        }
    }
    
    /**
     * 启动应用
     * @returns {Promise<void>}
     */
    async start() {
        if (this.isStarted) {
            this.logger.warn('SmartOfficeMainApp', 'start', '应用已启动，跳过重复启动');
            return;
        }
        
        try {
            this.startTime = performance.now();
            this.logger.info('SmartOfficeMainApp', 'start', '🔄 开始启动SmartOffice 2.0统一应用');
            
            // 初始化统一应用
            await this.app.initialize();
            
            // 标记为已启动
            this.isStarted = true;
            
            const startDuration = performance.now() - this.startTime;
            
            this.logger.info('SmartOfficeMainApp', 'start', '✅ SmartOffice 2.0统一应用启动完成', {
                duration: startDuration,
                version: this.config.version,
                mode: this.config.mode
            });
            
            // 显示启动成功通知
            this._showStartupNotification(startDuration);
            
        } catch (error) {
            this.logger.error('SmartOfficeMainApp', 'start', '❌ 应用启动失败', { error });
            this._showErrorNotification(error);
            throw error;
        }
    }
    
    /**
     * 停止应用
     * @returns {Promise<void>}
     */
    async stop() {
        if (!this.isStarted) {
            this.logger.warn('SmartOfficeMainApp', 'stop', '应用未启动，无需停止');
            return;
        }
        
        try {
            this.logger.info('SmartOfficeMainApp', 'stop', '🔄 开始停止应用');
            
            // 销毁统一应用
            await this.app.destroy();
            
            // 标记为已停止
            this.isStarted = false;
            this.startTime = null;
            
            this.logger.info('SmartOfficeMainApp', 'stop', '✅ 应用停止完成');
            
        } catch (error) {
            this.logger.error('SmartOfficeMainApp', 'stop', '❌ 应用停止失败', { error });
            throw error;
        }
    }
    
    /**
     * 重启应用
     * @returns {Promise<void>}
     */
    async restart() {
        this.logger.info('SmartOfficeMainApp', 'restart', '🔄 重启应用');
        
        await this.stop();
        await this.start();
        
        this.logger.info('SmartOfficeMainApp', 'restart', '✅ 应用重启完成');
    }
    
    /**
     * 获取应用信息
     * @returns {Object}
     */
    getAppInfo() {
        return {
            name: 'SmartOffice',
            version: this.config.version,
            mode: this.config.mode,
            isStarted: this.isStarted,
            uptime: this.startTime ? performance.now() - this.startTime : 0,
            unifiedApp: this.app.getAppInfo(),
            config: { ...this.config }
        };
    }
    
    /**
     * 获取统一应用实例
     * @returns {UnifiedAppInitializer}
     */
    getUnifiedApp() {
        return this.app;
    }
    
    /**
     * 获取模块
     * @param {string} moduleName - 模块名称
     * @returns {*}
     */
    getModule(moduleName) {
        return this.app.getModule(moduleName);
    }
    
    /**
     * 获取管理器
     * @param {string} managerName - 管理器名称
     * @returns {*}
     */
    getManager(managerName) {
        return this.app.getManager(managerName);
    }
    
    /**
     * 显示启动通知
     * @private
     * @param {number} duration - 启动耗时
     */
    _showStartupNotification(duration) {
        const notificationManager = this.app.getModule('notificationManager');
        if (notificationManager) {
            notificationManager.showSuccess(
                'SmartOffice 2.0 统一版本',
                `应用启动完成，耗时 ${Math.round(duration)}ms`
            );
        }
    }
    
    /**
     * 显示错误通知
     * @private
     * @param {Error} error - 错误对象
     */
    _showErrorNotification(error) {
        const notificationManager = this.app.getModule('notificationManager');
        if (notificationManager) {
            notificationManager.showError(
                'SmartOffice 2.0 启动失败',
                `错误信息: ${error.message}`
            );
        }
    }
}
// #endregion

// #region 全局实例管理
let globalMainApp = null;

/**
 * 创建主应用实例
 * @param {Object} config - 应用配置
 * @param {HTMLElement} container - 容器元素
 * @returns {SmartOfficeMainApp}
 */
export function createMainApp(config = {}, container = null) {
    return new SmartOfficeMainApp(config, container);
}

/**
 * 获取全局主应用实例
 * @param {Object} config - 应用配置
 * @param {HTMLElement} container - 容器元素
 * @returns {SmartOfficeMainApp}
 */
export function getMainApp(config = {}, container = null) {
    if (!globalMainApp) {
        globalMainApp = new SmartOfficeMainApp(config, container);
    }
    return globalMainApp;
}

/**
 * 快速启动主应用
 * @param {Object} config - 应用配置
 * @param {HTMLElement} container - 容器元素
 * @returns {Promise<SmartOfficeMainApp>}
 */
export async function quickStartMain(config = {}, container = null) {
    const app = getMainApp(config, container);
    
    if (!app.isStarted) {
        await app.start();
    }
    
    return app;
}

/**
 * 重置全局主应用实例
 * @returns {Promise<void>}
 */
export async function resetMainApp() {
    if (globalMainApp) {
        await globalMainApp.stop();
        globalMainApp = null;
    }
}
// #endregion

// #region 兼容性导出
// 导出统一应用初始化器相关功能
export {
    UnifiedAppInitializer,
    createUnifiedApp,
    getUnifiedApp,
    quickStartUnified,
    resetGlobalApp,
    getDefaultApp,
    setDefaultApp
};

// 兼容性别名
export { SmartOfficeMainApp as SmartOfficeApp };
export { createMainApp as createSmartOfficeApp };
export { quickStartMain as quickStart };
// #endregion

// #region 默认导出
export default SmartOfficeMainApp;
// #endregion
