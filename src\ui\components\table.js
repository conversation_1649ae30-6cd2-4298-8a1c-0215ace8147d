/**
 * @file 表格组件 - SmartOffice 数据展示表格系统
 * <AUTHOR> Team
 * @description 
 * 提供完整的表格功能，包括：
 * - Table 基础表格组件
 * - TableColumn 列定义组件
 * - TableRow 行组件
 * - 数据排序、筛选、分页
 * - 行选择、编辑、操作
 * - 完整的事件系统和生命周期管理
 */

import { BaseComponent } from './base-component.js';
import { UI_EVENTS } from '../../core/events/event-types.js';

// #region Table主组件
/**
 * @class Table - 表格主组件
 * @extends BaseComponent
 * @description 可配置的数据表格组件，支持排序、筛选、分页等功能
 */
export class Table extends BaseComponent {
    /**
     * 构造函数 - 初始化表格组件
     * @param {Object} config - 表格配置
     * @param {Array} config.columns - 列定义数组
     * @param {Array} config.data - 表格数据
     * @param {boolean} config.sortable - 是否支持排序
     * @param {boolean} config.filterable - 是否支持筛选
     * @param {boolean} config.selectable - 是否支持行选择
     * @param {Object} config.pagination - 分页配置
     */
    constructor(config = {}) {
        super(config);
        
        // 表格属性
        this.tableProps = {
            columns: config.columns || [],
            data: config.data || [],
            sortable: config.sortable !== false, // 默认支持排序
            filterable: config.filterable || false, // 默认不支持筛选
            selectable: config.selectable || false, // 默认不支持选择
            multiSelect: config.multiSelect || false, // 多选模式
            editable: config.editable || false, // 是否可编辑
            striped: config.striped !== false, // 斑马纹
            bordered: config.bordered !== false, // 边框
            hover: config.hover !== false, // 悬停效果
            size: config.size || 'medium', // 尺寸: small, medium, large
            height: config.height || 'auto', // 表格高度
            maxHeight: config.maxHeight || null, // 最大高度
            emptyText: config.emptyText || '暂无数据', // 空数据提示
            loading: config.loading || false, // 加载状态
            pagination: {
                enabled: config.pagination?.enabled || false,
                pageSize: config.pagination?.pageSize || 10,
                current: config.pagination?.current || 1,
                total: config.pagination?.total || 0,
                showSizeChanger: config.pagination?.showSizeChanger || false,
                showQuickJumper: config.pagination?.showQuickJumper || false,
                showTotal: config.pagination?.showTotal !== false
            },
            rowKey: config.rowKey || 'id', // 行键字段
            className: config.className || ''
        };
        
        // 表格状态
        this.tableState = {
            sortColumn: null, // 当前排序列
            sortDirection: 'asc', // 排序方向
            filters: new Map(), // 筛选条件
            selectedRows: new Set(), // 选中行
            editingCell: null, // 编辑中的单元格
            currentPage: this.tableProps.pagination.current,
            displayData: [], // 显示的数据
            filteredData: [], // 筛选后的数据
            loading: this.tableProps.loading
        };
        
        // DOM元素引用
        this.elements = {
            table: null,
            header: null,
            body: null,
            footer: null,
            thead: null,
            tbody: null,
            pagination: null,
            loadingOverlay: null
        };
        
        // 事件处理器
        this.handlers = {
            headerClick: this._handleHeaderClick.bind(this),
            rowClick: this._handleRowClick.bind(this),
            rowSelect: this._handleRowSelect.bind(this),
            cellEdit: this._handleCellEdit.bind(this),
            filterChange: this._handleFilterChange.bind(this),
            pageChange: this._handlePageChange.bind(this),
            pageSizeChange: this._handlePageSizeChange.bind(this)
        };
        
        // 初始化组件
        this._initializeTable();
    }

    /**
     * 初始化表格 - 创建DOM结构和设置事件
     * @private
     */
    _initializeTable() {
        this._createTableStructure();
        this._applyTableStyles();
        this._bindTableEvents();
        this._processData();
        
        // 发射初始化完成事件
        this.emit(UI_EVENTS.COMPONENT_INITIALIZED, {
            component: this,
            type: 'table'
        });
    }

    /**
     * 创建表格DOM结构 - 构建完整的表格HTML
     * @private
     */
    _createTableStructure() {
        // 创建表格容器
        this.elements.table = document.createElement('div');
        this.elements.table.className = `smartoffice-table smartoffice-table-${this.tableProps.size} ${this.tableProps.className}`;
        
        // 创建表格元素
        const tableElement = document.createElement('table');
        tableElement.className = this._getTableClasses();
        
        // 创建表头
        this._createTableHeader(tableElement);
        
        // 创建表体
        this._createTableBody(tableElement);
        
        // 创建加载遮罩
        this._createLoadingOverlay();
        
        // 组装结构
        this.elements.table.appendChild(tableElement);
        this.elements.table.appendChild(this.elements.loadingOverlay);
        
        // 创建分页
        if (this.tableProps.pagination.enabled) {
            this._createPagination();
            this.elements.table.appendChild(this.elements.pagination);
        }
        
        this.container = this.elements.table;
    }

    /**
     * 获取表格CSS类名 - 根据配置生成类名
     * @returns {string} CSS类名
     * @private
     */
    _getTableClasses() {
        const classes = ['smartoffice-table-element'];
        
        if (this.tableProps.striped) classes.push('table-striped');
        if (this.tableProps.bordered) classes.push('table-bordered');
        if (this.tableProps.hover) classes.push('table-hover');
        if (this.tableProps.selectable) classes.push('table-selectable');
        
        return classes.join(' ');
    }

    /**
     * 创建表格头部 - 列头和筛选
     * @param {HTMLElement} tableElement - 表格元素
     * @private
     */
    _createTableHeader(tableElement) {
        this.elements.thead = document.createElement('thead');
        this.elements.thead.className = 'smartoffice-table-header';
        
        // 创建标题行
        const headerRow = document.createElement('tr');
        
        // 选择列
        if (this.tableProps.selectable) {
            const selectTh = document.createElement('th');
            selectTh.className = 'table-select-column';
            selectTh.innerHTML = this.tableProps.multiSelect ? 
                '<input type="checkbox" class="select-all-checkbox">' : '';
            headerRow.appendChild(selectTh);
        }
        
        // 数据列
        this.tableProps.columns.forEach((column, index) => {
            const th = document.createElement('th');
            th.className = 'table-column-header';
            th.dataset.columnKey = column.key || index;
            
            // 列标题
            const titleSpan = document.createElement('span');
            titleSpan.className = 'column-title';
            titleSpan.textContent = column.title || column.key || `列${index + 1}`;
            th.appendChild(titleSpan);
            
            // 排序图标
            if (this.tableProps.sortable && column.sortable !== false) {
                const sortIcon = document.createElement('span');
                sortIcon.className = 'sort-icon';
                sortIcon.innerHTML = '↕️';
                th.appendChild(sortIcon);
                th.classList.add('sortable');
            }
            
            // 筛选器
            if (this.tableProps.filterable && column.filterable) {
                this._createColumnFilter(th, column);
            }
            
            // 列宽
            if (column.width) {
                th.style.width = column.width;
            }
            
            headerRow.appendChild(th);
        });
        
        this.elements.thead.appendChild(headerRow);
        tableElement.appendChild(this.elements.thead);
    }

    /**
     * 创建列筛选器 - 筛选控件
     * @param {HTMLElement} th - 表头单元格
     * @param {Object} column - 列配置
     * @private
     */
    _createColumnFilter(th, column) {
        const filterContainer = document.createElement('div');
        filterContainer.className = 'column-filter';
        
        const filterInput = document.createElement('input');
        filterInput.type = 'text';
        filterInput.className = 'filter-input';
        filterInput.placeholder = `筛选${column.title}`;
        filterInput.dataset.columnKey = column.key;
        
        filterContainer.appendChild(filterInput);
        th.appendChild(filterContainer);
    }

    /**
     * 创建表格主体 - 数据行
     * @param {HTMLElement} tableElement - 表格元素
     * @private
     */
    _createTableBody(tableElement) {
        this.elements.tbody = document.createElement('tbody');
        this.elements.tbody.className = 'smartoffice-table-body';
        
        tableElement.appendChild(this.elements.tbody);
    }

    /**
     * 创建加载遮罩 - 加载状态显示
     * @private
     */
    _createLoadingOverlay() {
        this.elements.loadingOverlay = document.createElement('div');
        this.elements.loadingOverlay.className = 'table-loading-overlay';
        this.elements.loadingOverlay.style.display = 'none';
        
        const loadingContent = document.createElement('div');
        loadingContent.className = 'loading-content';
        loadingContent.innerHTML = `
            <div class="loading-spinner"></div>
            <span>加载中...</span>
        `;
        
        this.elements.loadingOverlay.appendChild(loadingContent);
    }

    /**
     * 创建分页组件 - 分页控制
     * @private
     */
    _createPagination() {
        this.elements.pagination = document.createElement('div');
        this.elements.pagination.className = 'smartoffice-table-pagination';
        
        // 分页信息
        const paginationInfo = document.createElement('div');
        paginationInfo.className = 'pagination-info';
        this.elements.paginationInfo = paginationInfo;
        
        // 分页控制
        const paginationControls = document.createElement('div');
        paginationControls.className = 'pagination-controls';
        
        // 页面大小选择器
        if (this.tableProps.pagination.showSizeChanger) {
            const sizeSelector = this._createPageSizeSelector();
            paginationControls.appendChild(sizeSelector);
        }
        
        // 页码按钮
        const pageButtons = this._createPageButtons();
        paginationControls.appendChild(pageButtons);
        
        // 快速跳转
        if (this.tableProps.pagination.showQuickJumper) {
            const quickJumper = this._createQuickJumper();
            paginationControls.appendChild(quickJumper);
        }
        
        this.elements.pagination.appendChild(paginationInfo);
        this.elements.pagination.appendChild(paginationControls);
    }

    /**
     * 创建页面大小选择器 - 每页显示数量选择
     * @returns {HTMLElement} 选择器元素
     * @private
     */
    _createPageSizeSelector() {
        const container = document.createElement('div');
        container.className = 'page-size-selector';
        
        const label = document.createElement('span');
        label.textContent = '每页显示';
        
        const select = document.createElement('select');
        select.className = 'page-size-select';
        
        const sizes = [10, 20, 50, 100];
        sizes.forEach(size => {
            const option = document.createElement('option');
            option.value = size;
            option.textContent = size;
            if (size === this.tableProps.pagination.pageSize) {
                option.selected = true;
            }
            select.appendChild(option);
        });
        
        const suffix = document.createElement('span');
        suffix.textContent = '条';
        
        container.appendChild(label);
        container.appendChild(select);
        container.appendChild(suffix);
        
        return container;
    }

    /**
     * 创建页码按钮 - 分页导航按钮
     * @returns {HTMLElement} 按钮容器
     * @private
     */
    _createPageButtons() {
        const container = document.createElement('div');
        container.className = 'page-buttons';
        this.elements.pageButtons = container;
        
        return container;
    }

    /**
     * 创建快速跳转 - 页码输入跳转
     * @returns {HTMLElement} 跳转控件
     * @private
     */
    _createQuickJumper() {
        const container = document.createElement('div');
        container.className = 'quick-jumper';
        
        const label = document.createElement('span');
        label.textContent = '跳至';
        
        const input = document.createElement('input');
        input.type = 'number';
        input.className = 'page-input';
        input.min = '1';
        
        const suffix = document.createElement('span');
        suffix.textContent = '页';
        
        container.appendChild(label);
        container.appendChild(input);
        container.appendChild(suffix);
        
        return container;
    }

    /**
     * 应用表格样式 - 设置CSS样式
     * @private
     */
    _applyTableStyles() {
        // 容器样式
        Object.assign(this.elements.table.style, {
            position: 'relative',
            width: '100%',
            backgroundColor: 'white',
            borderRadius: '6px',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
            overflow: 'hidden'
        });
        
        // 表格高度
        if (this.tableProps.height !== 'auto') {
            this.elements.table.style.height = this.tableProps.height;
            this.elements.table.style.overflow = 'auto';
        }
        
        if (this.tableProps.maxHeight) {
            this.elements.table.style.maxHeight = this.tableProps.maxHeight;
            this.elements.table.style.overflowY = 'auto';
        }
        
        // 加载遮罩样式
        Object.assign(this.elements.loadingOverlay.style, {
            position: 'absolute',
            top: '0',
            left: '0',
            right: '0',
            bottom: '0',
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: '10'
        });
    }

    /**
     * 绑定表格事件 - 设置所有事件监听器
     * @private
     */
    _bindTableEvents() {
        // 表头点击事件（排序）
        if (this.elements.thead) {
            this.elements.thead.addEventListener('click', this.handlers.headerClick);
        }
        
        // 表体点击事件
        if (this.elements.tbody) {
            this.elements.tbody.addEventListener('click', this.handlers.rowClick);
        }
        
        // 全选checkbox事件
        const selectAllCheckbox = this.elements.table.querySelector('.select-all-checkbox');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this._toggleSelectAll(e.target.checked);
            });
        }
        
        // 筛选输入事件
        const filterInputs = this.elements.table.querySelectorAll('.filter-input');
        filterInputs.forEach(input => {
            input.addEventListener('input', this.handlers.filterChange);
        });
        
        // 分页事件
        if (this.elements.pagination) {
            this.elements.pagination.addEventListener('click', this.handlers.pageChange);
            
            const pageSizeSelect = this.elements.pagination.querySelector('.page-size-select');
            if (pageSizeSelect) {
                pageSizeSelect.addEventListener('change', this.handlers.pageSizeChange);
            }
            
            const pageInput = this.elements.pagination.querySelector('.page-input');
            if (pageInput) {
                pageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        const page = parseInt(e.target.value);
                        if (page >= 1 && page <= this._getTotalPages()) {
                            this.goToPage(page);
                        }
                    }
                });
            }
        }
    }

    /**
     * 处理数据 - 筛选、排序、分页
     * @private
     */
    _processData() {
        // 应用筛选
        this._applyFilters();
        
        // 应用排序
        this._applySorting();
        
        // 应用分页
        this._applyPagination();
        
        // 渲染表格
        this._renderTableBody();
        this._updatePagination();
    }

    /**
     * 应用筛选 - 根据筛选条件过滤数据
     * @private
     */
    _applyFilters() {
        this.tableState.filteredData = this.tableProps.data.filter(row => {
            for (const [columnKey, filterValue] of this.tableState.filters) {
                if (filterValue && filterValue.trim()) {
                    const cellValue = this._getCellValue(row, columnKey);
                    const searchValue = filterValue.toLowerCase();
                    const cellText = String(cellValue).toLowerCase();
                    
                    if (!cellText.includes(searchValue)) {
                        return false;
                    }
                }
            }
            return true;
        });
    }

    /**
     * 应用排序 - 根据排序条件排序数据
     * @private
     */
    _applySorting() {
        if (this.tableState.sortColumn) {
            this.tableState.filteredData.sort((a, b) => {
                const aValue = this._getCellValue(a, this.tableState.sortColumn);
                const bValue = this._getCellValue(b, this.tableState.sortColumn);
                
                let result = 0;
                if (aValue < bValue) result = -1;
                else if (aValue > bValue) result = 1;
                
                return this.tableState.sortDirection === 'desc' ? -result : result;
            });
        }
    }

    /**
     * 应用分页 - 计算当前页数据
     * @private
     */
    _applyPagination() {
        if (this.tableProps.pagination.enabled) {
            const start = (this.tableState.currentPage - 1) * this.tableProps.pagination.pageSize;
            const end = start + this.tableProps.pagination.pageSize;
            this.tableState.displayData = this.tableState.filteredData.slice(start, end);
        } else {
            this.tableState.displayData = this.tableState.filteredData;
        }
    }

    /**
     * 渲染表格主体 - 生成数据行
     * @private
     */
    _renderTableBody() {
        // 清空现有内容
        this.elements.tbody.innerHTML = '';
        
        // 空数据提示
        if (this.tableState.displayData.length === 0) {
            this._renderEmptyState();
            return;
        }
        
        // 渲染数据行
        this.tableState.displayData.forEach((row, index) => {
            const tr = this._createTableRow(row, index);
            this.elements.tbody.appendChild(tr);
        });
    }

    /**
     * 渲染空状态 - 无数据时的显示
     * @private
     */
    _renderEmptyState() {
        const tr = document.createElement('tr');
        tr.className = 'empty-row';
        
        const colCount = this.tableProps.columns.length + (this.tableProps.selectable ? 1 : 0);
        const td = document.createElement('td');
        td.colSpan = colCount;
        td.className = 'empty-cell';
        td.textContent = this.tableProps.emptyText;
        
        tr.appendChild(td);
        this.elements.tbody.appendChild(tr);
    }

    /**
     * 创建表格行 - 生成单行数据
     * @param {Object} row - 行数据
     * @param {number} index - 行索引
     * @returns {HTMLElement} 行元素
     * @private
     */
    _createTableRow(row, index) {
        const tr = document.createElement('tr');
        tr.className = 'table-row';
        tr.dataset.rowIndex = index;
        tr.dataset.rowKey = this._getRowKey(row);
        
        // 选择列
        if (this.tableProps.selectable) {
            const selectTd = document.createElement('td');
            selectTd.className = 'table-select-cell';
            
            const checkbox = document.createElement('input');
            checkbox.type = this.tableProps.multiSelect ? 'checkbox' : 'radio';
            checkbox.className = 'row-select-checkbox';
            checkbox.name = this.tableProps.multiSelect ? `select-${this.id}` : `select-${this.id}`;
            checkbox.dataset.rowKey = this._getRowKey(row);
            
            selectTd.appendChild(checkbox);
            tr.appendChild(selectTd);
        }
        
        // 数据列
        this.tableProps.columns.forEach((column, colIndex) => {
            const td = this._createTableCell(row, column, colIndex);
            tr.appendChild(td);
        });
        
        // 行状态
        if (this.tableState.selectedRows.has(this._getRowKey(row))) {
            tr.classList.add('selected');
        }
        
        return tr;
    }

    /**
     * 创建表格单元格 - 生成单个单元格
     * @param {Object} row - 行数据
     * @param {Object} column - 列配置
     * @param {number} colIndex - 列索引
     * @returns {HTMLElement} 单元格元素
     * @private
     */
    _createTableCell(row, column, colIndex) {
        const td = document.createElement('td');
        td.className = 'table-cell';
        td.dataset.columnKey = column.key || colIndex;
        
        // 获取单元格值
        const cellValue = this._getCellValue(row, column.key);
        
        // 应用列渲染器
        if (column.render && typeof column.render === 'function') {
            const rendered = column.render(cellValue, row, colIndex);
            if (typeof rendered === 'string') {
                td.innerHTML = rendered;
            } else if (rendered instanceof HTMLElement) {
                td.appendChild(rendered);
            } else {
                td.textContent = String(rendered);
            }
        } else {
            td.textContent = String(cellValue);
        }
        
        // 可编辑单元格
        if (this.tableProps.editable && column.editable !== false) {
            td.classList.add('editable');
            td.setAttribute('title', '双击编辑');
        }
        
        // 列对齐
        if (column.align) {
            td.style.textAlign = column.align;
        }
        
        return td;
    }

    /**
     * 获取单元格值 - 从行数据中获取列值
     * @param {Object} row - 行数据
     * @param {string} key - 列键
     * @returns {*} 单元格值
     * @private
     */
    _getCellValue(row, key) {
        if (!key) return '';
        
        // 支持嵌套属性访问，如 'user.name'
        const keys = key.split('.');
        let value = row;
        
        for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                return '';
            }
        }
        
        return value;
    }

    /**
     * 获取行键 - 获取行的唯一标识
     * @param {Object} row - 行数据
     * @returns {string} 行键
     * @private
     */
    _getRowKey(row) {
        return String(this._getCellValue(row, this.tableProps.rowKey));
    }

    // #region 公开方法
    /**
     * 设置数据 - 更新表格数据
     * @param {Array} data - 新数据
     */
    setData(data) {
        this.tableProps.data = data || [];
        this.tableState.selectedRows.clear();
        this._processData();
        
        this.emit(UI_EVENTS.COMPONENT_UPDATED, {
            component: this,
            type: 'data',
            data: this.tableProps.data
        });
    }

    /**
     * 获取数据 - 获取当前表格数据
     * @returns {Array} 表格数据
     */
    getData() {
        return this.tableProps.data;
    }

    /**
     * 获取选中行 - 获取当前选中的行数据
     * @returns {Array} 选中行数据
     */
    getSelectedRows() {
        return this.tableProps.data.filter(row => {
            const key = this._getRowKey(row);
            return this.tableState.selectedRows.has(key);
        });
    }

    /**
     * 设置选中行 - 设置选中的行
     * @param {Array} rowKeys - 行键数组
     */
    setSelectedRows(rowKeys) {
        this.tableState.selectedRows.clear();
        rowKeys.forEach(key => {
            this.tableState.selectedRows.add(String(key));
        });
        
        this._updateRowSelection();
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            type: 'selection',
            selectedRows: this.getSelectedRows()
        });
    }

    /**
     * 排序 - 按指定列排序
     * @param {string} columnKey - 列键
     * @param {string} direction - 排序方向：'asc' | 'desc'
     */
    sort(columnKey, direction = 'asc') {
        this.tableState.sortColumn = columnKey;
        this.tableState.sortDirection = direction;
        
        this._processData();
        this._updateSortIndicators();
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            type: 'sort',
            column: columnKey,
            direction: direction
        });
    }

    /**
     * 筛选 - 设置筛选条件
     * @param {string} columnKey - 列键
     * @param {string} filterValue - 筛选值
     */
    filter(columnKey, filterValue) {
        if (filterValue && filterValue.trim()) {
            this.tableState.filters.set(columnKey, filterValue);
        } else {
            this.tableState.filters.delete(columnKey);
        }
        
        this._processData();
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            type: 'filter',
            filters: Object.fromEntries(this.tableState.filters)
        });
    }

    /**
     * 跳转到指定页 - 分页跳转
     * @param {number} page - 页码
     */
    goToPage(page) {
        const totalPages = this._getTotalPages();
        const targetPage = Math.max(1, Math.min(page, totalPages));
        
        if (targetPage !== this.tableState.currentPage) {
            this.tableState.currentPage = targetPage;
            this._processData();
            
            this.emit(UI_EVENTS.COMPONENT_CHANGED, {
                component: this,
                type: 'page',
                currentPage: targetPage,
                pageSize: this.tableProps.pagination.pageSize
            });
        }
    }

    /**
     * 设置每页大小 - 改变每页显示数量
     * @param {number} pageSize - 每页大小
     */
    setPageSize(pageSize) {
        this.tableProps.pagination.pageSize = pageSize;
        this.tableState.currentPage = 1; // 重置到第一页
        this._processData();
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            type: 'pageSize',
            pageSize: pageSize
        });
    }

    /**
     * 刷新 - 重新处理和渲染数据
     */
    refresh() {
        this._processData();
        
        this.emit(UI_EVENTS.COMPONENT_UPDATED, {
            component: this,
            type: 'refresh'
        });
    }

    /**
     * 设置加载状态 - 显示/隐藏加载遮罩
     * @param {boolean} loading - 是否加载中
     */
    setLoading(loading) {
        this.tableState.loading = loading;
        this.elements.loadingOverlay.style.display = loading ? 'flex' : 'none';
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            type: 'loading',
            loading: loading
        });
    }
    // #endregion

    // #region 事件处理方法
    /**
     * 处理表头点击 - 排序功能
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleHeaderClick(e) {
        const th = e.target.closest('th.sortable');
        if (!th) return;
        
        const columnKey = th.dataset.columnKey;
        const currentDirection = this.tableState.sortColumn === columnKey ? 
            this.tableState.sortDirection : null;
        
        let newDirection = 'asc';
        if (currentDirection === 'asc') {
            newDirection = 'desc';
        } else if (currentDirection === 'desc') {
            newDirection = null;
        }
        
        if (newDirection) {
            this.sort(columnKey, newDirection);
        } else {
            // 取消排序
            this.tableState.sortColumn = null;
            this.tableState.sortDirection = 'asc';
            this._processData();
            this._updateSortIndicators();
        }
    }

    /**
     * 处理行点击 - 行选择和编辑
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleRowClick(e) {
        const tr = e.target.closest('tr.table-row');
        if (!tr) return;
        
        const td = e.target.closest('td');
        const rowKey = tr.dataset.rowKey;
        
        // 处理选择checkbox点击
        if (e.target.matches('.row-select-checkbox')) {
            this._toggleRowSelection(rowKey, e.target.checked);
            return;
        }
        
        // 处理单元格双击编辑
        if (e.detail === 2 && td && td.classList.contains('editable')) {
            this._startCellEdit(td, tr);
            return;
        }
        
        // 行点击事件
        this.emit(UI_EVENTS.COMPONENT_CLICKED, {
            component: this,
            type: 'row',
            rowKey: rowKey,
            rowData: this._getRowDataByKey(rowKey),
            event: e
        });
    }

    /**
     * 处理行选择 - 选择/取消选择行
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleRowSelect(e) {
        // 这个方法由rowClick处理
    }

    /**
     * 处理单元格编辑 - 单元格编辑功能
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleCellEdit(e) {
        // 这个方法由rowClick处理
    }

    /**
     * 处理筛选变化 - 筛选输入变化
     * @param {InputEvent} e - 输入事件
     * @private
     */
    _handleFilterChange(e) {
        const columnKey = e.target.dataset.columnKey;
        const filterValue = e.target.value;
        
        this.filter(columnKey, filterValue);
    }

    /**
     * 处理页面变化 - 分页按钮点击
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handlePageChange(e) {
        const button = e.target.closest('.page-button');
        if (!button) return;
        
        const action = button.dataset.action;
        const page = parseInt(button.dataset.page);
        
        switch (action) {
            case 'prev':
                this.goToPage(this.tableState.currentPage - 1);
                break;
            case 'next':
                this.goToPage(this.tableState.currentPage + 1);
                break;
            case 'page':
                this.goToPage(page);
                break;
        }
    }

    /**
     * 处理页面大小变化 - 每页显示数量变化
     * @param {ChangeEvent} e - 变化事件
     * @private
     */
    _handlePageSizeChange(e) {
        const pageSize = parseInt(e.target.value);
        this.setPageSize(pageSize);
    }
    // #endregion

    // #region 辅助方法
    /**
     * 切换全选 - 全选/取消全选所有行
     * @param {boolean} checked - 是否选中
     * @private
     */
    _toggleSelectAll(checked) {
        if (checked) {
            this.tableState.displayData.forEach(row => {
                const key = this._getRowKey(row);
                this.tableState.selectedRows.add(key);
            });
        } else {
            this.tableState.selectedRows.clear();
        }
        
        this._updateRowSelection();
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            type: 'selectAll',
            selected: checked,
            selectedRows: this.getSelectedRows()
        });
    }

    /**
     * 切换行选择 - 选择/取消选择单行
     * @param {string} rowKey - 行键
     * @param {boolean} selected - 是否选中
     * @private
     */
    _toggleRowSelection(rowKey, selected) {
        if (selected) {
            if (!this.tableProps.multiSelect) {
                this.tableState.selectedRows.clear();
            }
            this.tableState.selectedRows.add(rowKey);
        } else {
            this.tableState.selectedRows.delete(rowKey);
        }
        
        this._updateRowSelection();
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            type: 'rowSelect',
            rowKey: rowKey,
            selected: selected,
            selectedRows: this.getSelectedRows()
        });
    }

    /**
     * 更新行选择状态 - 更新UI中的选择状态
     * @private
     */
    _updateRowSelection() {
        // 更新行样式
        const rows = this.elements.tbody.querySelectorAll('tr.table-row');
        rows.forEach(tr => {
            const rowKey = tr.dataset.rowKey;
            const checkbox = tr.querySelector('.row-select-checkbox');
            const isSelected = this.tableState.selectedRows.has(rowKey);
            
            tr.classList.toggle('selected', isSelected);
            if (checkbox) {
                checkbox.checked = isSelected;
            }
        });
        
        // 更新全选checkbox
        const selectAllCheckbox = this.elements.table.querySelector('.select-all-checkbox');
        if (selectAllCheckbox) {
            const totalDisplayed = this.tableState.displayData.length;
            const selectedCount = this.tableState.displayData.filter(row => {
                return this.tableState.selectedRows.has(this._getRowKey(row));
            }).length;
            
            selectAllCheckbox.checked = totalDisplayed > 0 && selectedCount === totalDisplayed;
            selectAllCheckbox.indeterminate = selectedCount > 0 && selectedCount < totalDisplayed;
        }
    }

    /**
     * 更新排序指示器 - 更新列头的排序图标
     * @private
     */
    _updateSortIndicators() {
        const headers = this.elements.thead.querySelectorAll('th.sortable');
        headers.forEach(th => {
            const columnKey = th.dataset.columnKey;
            const sortIcon = th.querySelector('.sort-icon');
            
            th.classList.remove('sort-asc', 'sort-desc');
            
            if (columnKey === this.tableState.sortColumn) {
                th.classList.add(`sort-${this.tableState.sortDirection}`);
                sortIcon.innerHTML = this.tableState.sortDirection === 'asc' ? '↑' : '↓';
            } else {
                sortIcon.innerHTML = '↕️';
            }
        });
    }

    /**
     * 更新分页信息 - 更新分页组件
     * @private
     */
    _updatePagination() {
        if (!this.tableProps.pagination.enabled || !this.elements.pagination) {
            return;
        }
        
        const totalItems = this.tableState.filteredData.length;
        const totalPages = this._getTotalPages();
        const currentPage = this.tableState.currentPage;
        const pageSize = this.tableProps.pagination.pageSize;
        
        // 更新分页信息
        if (this.elements.paginationInfo) {
            const start = (currentPage - 1) * pageSize + 1;
            const end = Math.min(start + pageSize - 1, totalItems);
            
            this.elements.paginationInfo.textContent = 
                `显示 ${start}-${end} 条，共 ${totalItems} 条`;
        }
        
        // 更新页码按钮
        if (this.elements.pageButtons) {
            this._renderPageButtons(currentPage, totalPages);
        }
    }

    /**
     * 渲染页码按钮 - 生成分页按钮
     * @param {number} currentPage - 当前页
     * @param {number} totalPages - 总页数
     * @private
     */
    _renderPageButtons(currentPage, totalPages) {
        this.elements.pageButtons.innerHTML = '';
        
        // 上一页按钮
        const prevButton = document.createElement('button');
        prevButton.className = 'page-button prev-button';
        prevButton.textContent = '上一页';
        prevButton.dataset.action = 'prev';
        prevButton.disabled = currentPage <= 1;
        this.elements.pageButtons.appendChild(prevButton);
        
        // 页码按钮
        const maxVisible = 5; // 最多显示5个页码
        let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2));
        let endPage = Math.min(totalPages, startPage + maxVisible - 1);
        
        if (endPage - startPage < maxVisible - 1) {
            startPage = Math.max(1, endPage - maxVisible + 1);
        }
        
        for (let i = startPage; i <= endPage; i++) {
            const pageButton = document.createElement('button');
            pageButton.className = 'page-button number-button';
            pageButton.textContent = i;
            pageButton.dataset.action = 'page';
            pageButton.dataset.page = i;
            
            if (i === currentPage) {
                pageButton.classList.add('active');
            }
            
            this.elements.pageButtons.appendChild(pageButton);
        }
        
        // 下一页按钮
        const nextButton = document.createElement('button');
        nextButton.className = 'page-button next-button';
        nextButton.textContent = '下一页';
        nextButton.dataset.action = 'next';
        nextButton.disabled = currentPage >= totalPages;
        this.elements.pageButtons.appendChild(nextButton);
    }

    /**
     * 获取总页数 - 计算总页数
     * @returns {number} 总页数
     * @private
     */
    _getTotalPages() {
        if (!this.tableProps.pagination.enabled) {
            return 1;
        }
        
        return Math.ceil(this.tableState.filteredData.length / this.tableProps.pagination.pageSize);
    }

    /**
     * 根据键获取行数据 - 通过行键获取完整行数据
     * @param {string} rowKey - 行键
     * @returns {Object|null} 行数据
     * @private
     */
    _getRowDataByKey(rowKey) {
        return this.tableProps.data.find(row => this._getRowKey(row) === rowKey) || null;
    }

    /**
     * 开始单元格编辑 - 开启单元格编辑模式
     * @param {HTMLElement} td - 单元格元素
     * @param {HTMLElement} tr - 行元素
     * @private
     */
    _startCellEdit(td, tr) {
        if (this.tableState.editingCell) {
            this._endCellEdit();
        }
        
        const columnKey = td.dataset.columnKey;
        const rowKey = tr.dataset.rowKey;
        const currentValue = this._getCellValue(this._getRowDataByKey(rowKey), columnKey);
        
        // 创建编辑器
        const input = document.createElement('input');
        input.type = 'text';
        input.className = 'cell-editor';
        input.value = String(currentValue);
        
        // 保存原始内容
        const originalContent = td.innerHTML;
        td.innerHTML = '';
        td.appendChild(input);
        
        // 聚焦并选中
        input.focus();
        input.select();
        
        // 保存编辑状态
        this.tableState.editingCell = {
            td,
            tr,
            columnKey,
            rowKey,
            originalContent,
            input
        };
        
        // 绑定事件
        input.addEventListener('blur', () => this._endCellEdit(true));
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this._endCellEdit(true);
            } else if (e.key === 'Escape') {
                this._endCellEdit(false);
            }
        });
    }

    /**
     * 结束单元格编辑 - 完成或取消单元格编辑
     * @param {boolean} save - 是否保存更改
     * @private
     */
    _endCellEdit(save = true) {
        if (!this.tableState.editingCell) {
            return;
        }
        
        const { td, tr, columnKey, rowKey, originalContent, input } = this.tableState.editingCell;
        const newValue = input.value;
        const oldValue = this._getCellValue(this._getRowDataByKey(rowKey), columnKey);
        
        if (save && newValue !== String(oldValue)) {
            // 更新数据
            const rowData = this._getRowDataByKey(rowKey);
            if (rowData) {
                // 简单赋值，复杂情况需要深度设置
                if (columnKey.includes('.')) {
                    // 处理嵌套属性
                    const keys = columnKey.split('.');
                    let obj = rowData;
                    for (let i = 0; i < keys.length - 1; i++) {
                        if (!obj[keys[i]]) obj[keys[i]] = {};
                        obj = obj[keys[i]];
                    }
                    obj[keys[keys.length - 1]] = newValue;
                } else {
                    rowData[columnKey] = newValue;
                }
                
                // 重新渲染单元格
                const column = this.tableProps.columns.find(col => col.key === columnKey);
                if (column && column.render) {
                    const rendered = column.render(newValue, rowData, columnKey);
                    if (typeof rendered === 'string') {
                        td.innerHTML = rendered;
                    } else if (rendered instanceof HTMLElement) {
                        td.innerHTML = '';
                        td.appendChild(rendered);
                    } else {
                        td.textContent = String(rendered);
                    }
                } else {
                    td.textContent = newValue;
                }
                
                // 发射编辑事件
                this.emit(UI_EVENTS.COMPONENT_CHANGED, {
                    component: this,
                    type: 'cellEdit',
                    rowKey,
                    columnKey,
                    oldValue,
                    newValue,
                    rowData
                });
            }
        } else {
            // 恢复原始内容
            td.innerHTML = originalContent;
        }
        
        // 清理编辑状态
        this.tableState.editingCell = null;
    }
    // #endregion

    // #region 生命周期方法
    /**
     * 组件销毁 - 清理资源
     */
    destroy() {
        // 结束正在进行的编辑
        if (this.tableState.editingCell) {
            this._endCellEdit(false);
        }
        
        // 调用父类销毁方法
        super.destroy();
        
        this.emit(UI_EVENTS.COMPONENT_DESTROYED, { component: this });
    }
    // #endregion
}

/**
 * @class TableColumn - 表格列定义
 * @description 表格列的配置和行为定义
 */
export class TableColumn {
    /**
     * 构造函数 - 初始化列定义
     * @param {Object} config - 列配置
     */
    constructor(config = {}) {
        this.key = config.key || '';
        this.title = config.title || '';
        this.width = config.width || null;
        this.align = config.align || 'left'; // left, center, right
        this.sortable = config.sortable !== false;
        this.filterable = config.filterable || false;
        this.editable = config.editable || false;
        this.render = config.render || null; // 自定义渲染函数
        this.className = config.className || '';
    }
}
// #endregion

// #region 工厂函数
/**
 * 创建表格 - 便捷函数创建表格实例
 * @param {Object} config - 表格配置
 * @returns {Table} 表格实例
 */
export function createTable(config = {}) {
    return new Table(config);
}

/**
 * 创建可选择表格 - 便捷函数创建可选择表格
 * @param {Object} config - 表格配置
 * @returns {Table} 表格实例
 */
export function createSelectableTable(config = {}) {
    return new Table({
        ...config,
        selectable: true,
        multiSelect: config.multiSelect !== false
    });
}

/**
 * 创建分页表格 - 便捷函数创建分页表格
 * @param {Object} config - 表格配置
 * @returns {Table} 表格实例
 */
export function createPaginationTable(config = {}) {
    return new Table({
        ...config,
        pagination: {
            enabled: true,
            pageSize: config.pageSize || 10,
            showSizeChanger: true,
            showQuickJumper: true,
            ...config.pagination
        }
    });
}

/**
 * 创建列定义 - 便捷函数创建列定义
 * @param {string} key - 列键
 * @param {string} title - 列标题
 * @param {Object} options - 额外选项
 * @returns {TableColumn} 列定义实例
 */
export function createTableColumn(key, title, options = {}) {
    return new TableColumn({
        key,
        title,
        ...options
    });
}
// #endregion 