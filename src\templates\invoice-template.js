/**
 * @file 发票模板 - 实现发票文档的模板渲染功能
 * <AUTHOR> Team
 * @description 
 * 这个文件定义了发票文档的模板类，包括：
 * - InvoiceTemplate 类，继承自 BaseTemplate
 * - 发票专用的渲染逻辑和样式配置
 * - 发票字段验证和格式化处理
 * - 支持多种发票样式主题（经典、现代、优雅、正式）
 */

// #region 导入依赖模块
import { BaseTemplate } from './base-template.js';
import { InvoiceDocument } from '../models/invoice-document.js';
import { StringUtils } from '../core/utils/string-utils.js';
import { DateUtils } from '../core/utils/date-utils.js';
// #endregion

// #region 发票模板类定义
/**
 * @class InvoiceTemplate - 发票模板类
 * @description 专门用于渲染发票文档的模板类，继承自BaseTemplate
 */
export class InvoiceTemplate extends BaseTemplate {
    /**
     * 构造函数 - 初始化发票模板实例
     * @param {Object} config - 模板配置对象
     */
    constructor(config = {}) {
        // 设置发票模板的默认配置
        const defaultConfig = {
            name: config.name || 'invoice-template',
            type: 'invoice',
            version: config.version || '1.0.0',
            metadata: {
                title: '发票模板',
                description: '用于生成发票文档的标准模板',
                author: 'SmartOffice Team',
                created: new Date(),
                ...config.metadata
            },
            
            // 发票专用的渲染配置
            renderConfig: {
                theme: 'classic', // 默认主题：经典、现代、优雅、正式
                language: 'zh-CN', // 默认语言
                currency: 'RM', // 默认货币
                dateFormat: 'YYYY-MM-DD', // 日期格式
                showTax: true, // 是否显示税务信息
                showShipping: true, // 是否显示运费
                showDiscount: true, // 是否显示折扣
                showLogo: true, // 是否显示公司Logo
                showPaymentTerms: true, // 是否显示付款条款
                pageSize: 'A4', // 页面大小
                ...config.renderConfig
            },
            
            // 发票专用样式配置
            styles: {
                // 页面容器样式
                container: {
                    width: '210mm',
                    minHeight: '297mm',
                    margin: '0 auto',
                    padding: '20mm',
                    fontFamily: 'Arial, "Microsoft YaHei", sans-serif',
                    fontSize: '12px',
                    lineHeight: '1.5',
                    color: '#333',
                    backgroundColor: '#fff'
                },
                
                // 页眉样式
                header: {
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    marginBottom: '30px',
                    paddingBottom: '20px',
                    borderBottom: '2px solid #ddd'
                },
                
                // 公司Logo样式
                logo: {
                    maxWidth: '120px',
                    maxHeight: '80px'
                },
                
                // 公司信息样式
                companyInfo: {
                    textAlign: 'right',
                    fontSize: '11px',
                    color: '#666'
                },
                
                // 发票标题样式
                invoiceTitle: {
                    fontSize: '24px',
                    fontWeight: 'bold',
                    color: '#000',
                    textAlign: 'center',
                    marginBottom: '20px'
                },
                
                // 发票信息区域样式
                invoiceDetails: {
                    display: 'flex',
                    justifyContent: 'space-between',
                    marginBottom: '30px'
                },
                
                // 地址信息样式
                addressSection: {
                    width: '45%'
                },
                
                // 地址标题样式
                addressTitle: {
                    fontSize: '14px',
                    fontWeight: 'bold',
                    marginBottom: '10px',
                    color: '#000'
                },
                
                // 地址内容样式
                addressContent: {
                    fontSize: '11px',
                    lineHeight: '1.6'
                },
                
                // 项目表格样式
                itemsTable: {
                    width: '100%',
                    borderCollapse: 'collapse',
                    marginBottom: '20px'
                },
                
                // 表格头部样式
                tableHeader: {
                    backgroundColor: '#f5f5f5',
                    fontWeight: 'bold',
                    padding: '12px 8px',
                    borderBottom: '2px solid #ddd',
                    textAlign: 'left'
                },
                
                // 表格单元格样式
                tableCell: {
                    padding: '10px 8px',
                    borderBottom: '1px solid #eee',
                    verticalAlign: 'top'
                },
                
                // 合计区域样式
                totalsSection: {
                    marginLeft: 'auto',
                    width: '300px',
                    borderTop: '1px solid #ddd',
                    paddingTop: '15px'
                },
                
                // 合计行样式
                totalRow: {
                    display: 'flex',
                    justifyContent: 'space-between',
                    marginBottom: '8px',
                    padding: '3px 0'
                },
                
                // 最终合计样式
                grandTotalRow: {
                    fontSize: '16px',
                    fontWeight: 'bold',
                    borderTop: '2px solid #333',
                    paddingTop: '10px',
                    marginTop: '10px'
                },
                
                // 页脚样式
                footer: {
                    marginTop: '40px',
                    paddingTop: '20px',
                    borderTop: '1px solid #ddd',
                    fontSize: '10px',
                    color: '#999'
                },
                
                ...config.styles
            },
            
            // 发票字段验证规则
            validationRules: {
                invoiceNumber: {
                    type: 'string',
                    minLength: 1,
                    maxLength: 50
                },
                issueDate: {
                    validator: (value) => value instanceof Date || !isNaN(Date.parse(value))
                },
                billTo: {
                    type: 'object',
                    validator: (value) => value && value.name
                },
                items: {
                    type: 'object',
                    validator: (value) => Array.isArray(value) && value.length > 0
                },
                ...config.validationRules
            },
            
            // 必填字段列表
            requiredFields: [
                'invoiceNumber',
                'issueDate',
                'billTo',
                'items',
                ...config.requiredFields || []
            ]
        };
        
        // 调用父类构造函数
        super(defaultConfig);
        
        // 发票专用属性
        this.supportedThemes = ['classic', 'modern', 'elegant', 'formal'];
        this.supportedLanguages = ['zh-CN', 'en-US', 'ms-MY'];
        this.supportedCurrencies = ['RM', 'USD', 'CNY', 'EUR', 'SGD'];
    }

    /**
     * 执行实际渲染 - 实现基类的抽象方法
     * @param {Object} data - 预处理后的数据对象
     * @param {Object} options - 渲染选项配置
     * @returns {Promise<string>} 渲染结果字符串
     * @protected
     */
    async _doRender(data, options) {
        try {
            // 确保数据完整性
            const invoiceData = await this._ensureDataCompleteness(data);
            
            // 根据主题选择渲染方法
            switch (options.theme) {
                case 'modern':
                    return await this._renderModernTheme(invoiceData, options);
                case 'elegant':
                    return await this._renderElegantTheme(invoiceData, options);
                case 'formal':
                    return await this._renderFormalTheme(invoiceData, options);
                default:
                    return await this._renderClassicTheme(invoiceData, options);
            }
        } catch (error) {
            throw new Error(`发票模板渲染失败: ${error.message}`);
        }
    }

    /**
     * 确保数据完整性 - 补充缺失的发票数据
     * @param {Object} data - 原始数据
     * @returns {Promise<Object>} 完整的发票数据
     * @private
     */
    async _ensureDataCompleteness(data) {
        const invoiceData = { ...data };
        
        // 确保有发票号
        if (!invoiceData.invoiceNumber) {
            invoiceData.invoiceNumber = this._generateInvoiceNumber();
        }
        
        // 确保有开具日期
        if (!invoiceData.issueDate) {
            invoiceData.issueDate = new Date();
        }
        
        // 格式化日期
        invoiceData.formattedIssueDate = DateUtils.format(
            new Date(invoiceData.issueDate),
            this.renderConfig.dateFormat,
            this.renderConfig.language
        );
        
        if (invoiceData.dueDate) {
            invoiceData.formattedDueDate = DateUtils.format(
                new Date(invoiceData.dueDate),
                this.renderConfig.dateFormat,
                this.renderConfig.language
            );
        }
        
        // 确保有公司信息
        invoiceData.companyInfo = {
            name: '智能办公系统',
            address: '',
            phone: '',
            email: '',
            website: '',
            registrationNumber: '',
            taxNumber: '',
            ...invoiceData.companyInfo
        };
        
        // 确保有账单地址
        invoiceData.billTo = {
            name: '客户',
            company: '',
            address: '',
            city: '',
            state: '',
            postalCode: '',
            country: '',
            phone: '',
            email: '',
            ...invoiceData.billTo
        };
        
        // 确保有项目列表
        if (!invoiceData.items || !Array.isArray(invoiceData.items)) {
            invoiceData.items = [];
        }
        
        // 处理项目数据并计算金额
        invoiceData.items = invoiceData.items.map(item => {
            const processedItem = {
                description: item.description || '服务项目',
                quantity: item.quantity || 1,
                unitPrice: item.unitPrice || 0,
                discount: item.discount || 0,
                taxRate: item.taxRate || 0,
                ...item
            };
            
            // 计算项目小计
            const subtotal = processedItem.quantity * processedItem.unitPrice;
            const discountAmount = subtotal * (processedItem.discount / 100);
            const afterDiscount = subtotal - discountAmount;
            const taxAmount = afterDiscount * (processedItem.taxRate / 100);
            processedItem.lineTotal = afterDiscount + taxAmount;
            
            // 格式化金额
            processedItem.formattedUnitPrice = StringUtils.formatCurrency(
                processedItem.unitPrice,
                this.renderConfig.currency,
                2,
                this.renderConfig.language
            );
            
            processedItem.formattedLineTotal = StringUtils.formatCurrency(
                processedItem.lineTotal,
                this.renderConfig.currency,
                2,
                this.renderConfig.language
            );
            
            return processedItem;
        });
        
        // 计算总金额
        const subtotal = invoiceData.items.reduce((sum, item) => {
            return sum + (item.quantity * item.unitPrice);
        }, 0);
        
        const discountAmount = invoiceData.discountAmount || 0;
        const shippingAmount = invoiceData.shippingAmount || 0;
        const taxAmount = invoiceData.items.reduce((sum, item) => {
            const itemSubtotal = item.quantity * item.unitPrice;
            const itemDiscount = itemSubtotal * (item.discount / 100);
            const itemAfterDiscount = itemSubtotal - itemDiscount;
            return sum + (itemAfterDiscount * (item.taxRate / 100));
        }, 0);
        
        invoiceData.subtotal = subtotal;
        invoiceData.discountAmount = discountAmount;
        invoiceData.shippingAmount = shippingAmount;
        invoiceData.taxAmount = taxAmount;
        invoiceData.totalAmount = subtotal - discountAmount + shippingAmount + taxAmount;
        
        // 格式化金额
        invoiceData.formattedSubtotal = StringUtils.formatCurrency(
            invoiceData.subtotal,
            this.renderConfig.currency,
            2,
            this.renderConfig.language
        );
        
        invoiceData.formattedDiscountAmount = StringUtils.formatCurrency(
            invoiceData.discountAmount,
            this.renderConfig.currency,
            2,
            this.renderConfig.language
        );
        
        invoiceData.formattedShippingAmount = StringUtils.formatCurrency(
            invoiceData.shippingAmount,
            this.renderConfig.currency,
            2,
            this.renderConfig.language
        );
        
        invoiceData.formattedTaxAmount = StringUtils.formatCurrency(
            invoiceData.taxAmount,
            this.renderConfig.currency,
            2,
            this.renderConfig.language
        );
        
        invoiceData.formattedTotalAmount = StringUtils.formatCurrency(
            invoiceData.totalAmount,
            this.renderConfig.currency,
            2,
            this.renderConfig.language
        );
        
        return invoiceData;
    }

    /**
     * 渲染经典主题发票 - 标准的发票样式
     * @param {Object} data - 发票数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderClassicTheme(data, options) {
        const html = `
            <div class="invoice-container classic-theme">
                ${await this._renderHeader(data, options)}
                ${await this._renderInvoiceTitle(data, options)}
                ${await this._renderInvoiceDetails(data, options)}
                ${await this._renderAddresses(data, options)}
                ${await this._renderItemsTable(data, options)}
                ${await this._renderTotals(data, options)}
                ${await this._renderPaymentTerms(data, options)}
                ${await this._renderFooter(data, options)}
            </div>
        `;
        
        return html;
    }

    /**
     * 渲染现代主题发票 - 现代化的发票样式
     * @param {Object} data - 发票数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderModernTheme(data, options) {
        const html = `
            <div class="invoice-container modern-theme">
                <div class="modern-header-section">
                    ${await this._renderHeader(data, options)}
                    ${await this._renderInvoiceTitle(data, options)}
                </div>
                <div class="modern-content-section">
                    ${await this._renderInvoiceDetails(data, options)}
                    ${await this._renderAddresses(data, options)}
                </div>
                <div class="modern-items-section">
                    ${await this._renderItemsTable(data, options)}
                </div>
                <div class="modern-totals-section">
                    ${await this._renderTotals(data, options)}
                </div>
                <div class="modern-footer-section">
                    ${await this._renderPaymentTerms(data, options)}
                    ${await this._renderFooter(data, options)}
                </div>
            </div>
        `;
        
        return html;
    }

    /**
     * 渲染优雅主题发票 - 优雅精致的发票样式
     * @param {Object} data - 发票数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderElegantTheme(data, options) {
        const html = `
            <div class="invoice-container elegant-theme">
                <div class="elegant-border">
                    <div class="elegant-header">
                        ${await this._renderHeader(data, options)}
                    </div>
                    <div class="elegant-divider"></div>
                    <div class="elegant-title">
                        ${await this._renderInvoiceTitle(data, options)}
                    </div>
                    <div class="elegant-details">
                        ${await this._renderInvoiceDetails(data, options)}
                        ${await this._renderAddresses(data, options)}
                    </div>
                    <div class="elegant-divider"></div>
                    <div class="elegant-items">
                        ${await this._renderItemsTable(data, options)}
                    </div>
                    <div class="elegant-divider"></div>
                    <div class="elegant-totals">
                        ${await this._renderTotals(data, options)}
                    </div>
                    <div class="elegant-footer">
                        ${await this._renderPaymentTerms(data, options)}
                        ${await this._renderFooter(data, options)}
                    </div>
                </div>
            </div>
        `;
        
        return html;
    }

    /**
     * 渲染正式主题发票 - 正式商务的发票样式
     * @param {Object} data - 发票数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderFormalTheme(data, options) {
        const html = `
            <div class="invoice-container formal-theme">
                <div class="formal-letterhead">
                    ${await this._renderHeader(data, options)}
                </div>
                <div class="formal-title-section">
                    ${await this._renderInvoiceTitle(data, options)}
                    ${await this._renderInvoiceDetails(data, options)}
                </div>
                <div class="formal-addresses-section">
                    ${await this._renderAddresses(data, options)}
                </div>
                <div class="formal-items-section">
                    ${await this._renderItemsTable(data, options)}
                </div>
                <div class="formal-summary-section">
                    ${await this._renderTotals(data, options)}
                </div>
                <div class="formal-terms-section">
                    ${await this._renderPaymentTerms(data, options)}
                </div>
                <div class="formal-footer-section">
                    ${await this._renderFooter(data, options)}
                </div>
            </div>
        `;
        
        return html;
    }

    /**
     * 渲染发票页眉 - 生成发票的页眉部分
     * @param {Object} data - 发票数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderHeader(data, options) {
        const company = data.companyInfo;
        
        return `
            <div class="invoice-header">
                ${options.showLogo && company.logo ? `
                <div class="company-logo">
                    <img src="${company.logo}" alt="${company.name}" class="logo-image">
                </div>
                ` : ''}
                <div class="company-details">
                    <div class="company-name">${company.name}</div>
                    ${company.address ? `<div class="company-address">${company.address}</div>` : ''}
                    <div class="company-contact">
                        ${company.phone ? `<span>电话: ${company.phone}</span>` : ''}
                        ${company.email ? `<span>邮箱: ${company.email}</span>` : ''}
                        ${company.website ? `<span>网站: ${company.website}</span>` : ''}
                    </div>
                    ${company.registrationNumber ? `<div class="company-reg">注册号: ${company.registrationNumber}</div>` : ''}
                    ${company.taxNumber ? `<div class="company-tax">税号: ${company.taxNumber}</div>` : ''}
                </div>
                ${options.showStamp && company.stamp ? `
                <div class="company-stamp">
                    <img src="${company.stamp}" alt="公司印章" class="stamp-image">
                </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * 渲染发票标题 - 生成发票标题部分
     * @param {Object} data - 发票数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderInvoiceTitle(data, options) {
        return `
            <div class="invoice-title-section">
                <h1 class="invoice-title">发 票 / INVOICE</h1>
            </div>
        `;
    }

    /**
     * 渲染发票详情 - 生成发票号、日期等基本信息
     * @param {Object} data - 发票数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderInvoiceDetails(data, options) {
        return `
            <div class="invoice-details-section">
                <div class="invoice-info">
                    <div class="info-row">
                        <span class="info-label">发票号码:</span>
                        <span class="info-value">${data.invoiceNumber}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">开票日期:</span>
                        <span class="info-value">${data.formattedIssueDate}</span>
                    </div>
                    ${data.formattedDueDate ? `
                    <div class="info-row">
                        <span class="info-label">到期日期:</span>
                        <span class="info-value">${data.formattedDueDate}</span>
                    </div>
                    ` : ''}
                    ${data.paymentTerms ? `
                    <div class="info-row">
                        <span class="info-label">付款条款:</span>
                        <span class="info-value">${data.paymentTerms}</span>
                    </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    /**
     * 渲染地址信息 - 生成账单地址和发货地址
     * @param {Object} data - 发票数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderAddresses(data, options) {
        const billTo = data.billTo;
        const shipTo = data.shipTo;
        
        return `
            <div class="addresses-section">
                <div class="bill-to-section">
                    <div class="address-title">账单地址 / Bill To:</div>
                    <div class="address-content">
                        <div class="customer-name">${billTo.name}</div>
                        ${billTo.company ? `<div class="customer-company">${billTo.company}</div>` : ''}
                        ${billTo.address ? `<div class="customer-address">${billTo.address}</div>` : ''}
                        <div class="customer-location">
                            ${billTo.city ? billTo.city : ''}${billTo.city && billTo.state ? ', ' : ''}${billTo.state ? billTo.state : ''}
                            ${(billTo.city || billTo.state) && billTo.postalCode ? ' ' : ''}${billTo.postalCode ? billTo.postalCode : ''}
                        </div>
                        ${billTo.country ? `<div class="customer-country">${billTo.country}</div>` : ''}
                        ${billTo.phone ? `<div class="customer-phone">电话: ${billTo.phone}</div>` : ''}
                        ${billTo.email ? `<div class="customer-email">邮箱: ${billTo.email}</div>` : ''}
                        ${billTo.taxNumber ? `<div class="customer-tax">税号: ${billTo.taxNumber}</div>` : ''}
                    </div>
                </div>
                
                ${shipTo && shipTo.name && shipTo.name !== billTo.name ? `
                <div class="ship-to-section">
                    <div class="address-title">发货地址 / Ship To:</div>
                    <div class="address-content">
                        <div class="ship-name">${shipTo.name}</div>
                        ${shipTo.company ? `<div class="ship-company">${shipTo.company}</div>` : ''}
                        ${shipTo.address ? `<div class="ship-address">${shipTo.address}</div>` : ''}
                        <div class="ship-location">
                            ${shipTo.city ? shipTo.city : ''}${shipTo.city && shipTo.state ? ', ' : ''}${shipTo.state ? shipTo.state : ''}
                            ${(shipTo.city || shipTo.state) && shipTo.postalCode ? ' ' : ''}${shipTo.postalCode ? shipTo.postalCode : ''}
                        </div>
                        ${shipTo.country ? `<div class="ship-country">${shipTo.country}</div>` : ''}
                        ${shipTo.phone ? `<div class="ship-phone">电话: ${shipTo.phone}</div>` : ''}
                    </div>
                </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * 渲染项目表格 - 生成发票项目列表表格
     * @param {Object} data - 发票数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderItemsTable(data, options) {
        const itemsHtml = data.items.map((item, index) => `
            <tr class="table-row">
                <td class="table-cell item-number">${index + 1}</td>
                <td class="table-cell item-description">${item.description}</td>
                <td class="table-cell item-quantity">${item.quantity}</td>
                <td class="table-cell item-unit-price">${item.formattedUnitPrice}</td>
                ${options.showDiscount ? `<td class="table-cell item-discount">${item.discount}%</td>` : ''}
                <td class="table-cell item-total">${item.formattedLineTotal}</td>
            </tr>
        `).join('');
        
        return `
            <div class="items-table-section">
                <table class="items-table">
                    <thead>
                        <tr class="table-header-row">
                            <th class="table-header item-number-header">#</th>
                            <th class="table-header item-description-header">项目描述</th>
                            <th class="table-header item-quantity-header">数量</th>
                            <th class="table-header item-unit-price-header">单价</th>
                            ${options.showDiscount ? '<th class="table-header item-discount-header">折扣</th>' : ''}
                            <th class="table-header item-total-header">小计</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${itemsHtml}
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * 渲染合计信息 - 生成金额合计、税费、运费等信息
     * @param {Object} data - 发票数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderTotals(data, options) {
        let totalsHtml = `
            <div class="total-row">
                <span class="total-label">小计:</span>
                <span class="total-value">${data.formattedSubtotal}</span>
            </div>
        `;
        
        // 显示折扣
        if (options.showDiscount && data.discountAmount > 0) {
            totalsHtml += `
                <div class="total-row">
                    <span class="total-label">折扣:</span>
                    <span class="total-value">-${data.formattedDiscountAmount}</span>
                </div>
            `;
        }
        
        // 显示运费
        if (options.showShipping && data.shippingAmount > 0) {
            totalsHtml += `
                <div class="total-row">
                    <span class="total-label">运费:</span>
                    <span class="total-value">${data.formattedShippingAmount}</span>
                </div>
            `;
        }
        
        // 显示税费
        if (options.showTax && data.taxAmount > 0) {
            totalsHtml += `
                <div class="total-row">
                    <span class="total-label">税费:</span>
                    <span class="total-value">${data.formattedTaxAmount}</span>
                </div>
            `;
        }
        
        // 最终合计
        totalsHtml += `
            <div class="total-row grand-total">
                <span class="total-label">合计:</span>
                <span class="total-value">${data.formattedTotalAmount}</span>
            </div>
        `;
        
        return `
            <div class="totals-section">
                ${totalsHtml}
            </div>
        `;
    }

    /**
     * 渲染付款条款 - 生成付款条款和说明
     * @param {Object} data - 发票数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderPaymentTerms(data, options) {
        if (!options.showPaymentTerms && !data.paymentTerms && !data.notes) {
            return '';
        }
        
        return `
            <div class="payment-terms-section">
                ${data.paymentTerms ? `
                <div class="payment-terms">
                    <div class="terms-title">付款条款:</div>
                    <div class="terms-content">${data.paymentTerms}</div>
                </div>
                ` : ''}
                
                ${data.notes ? `
                <div class="invoice-notes">
                    <div class="notes-title">备注:</div>
                    <div class="notes-content">${data.notes}</div>
                </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * 渲染发票页脚 - 生成感谢语、联系信息等
     * @param {Object} data - 发票数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML字符串
     * @private
     */
    async _renderFooter(data, options) {
        const currentTime = DateUtils.format(new Date(), 'YYYY-MM-DD HH:mm:ss', this.renderConfig.language);
        const company = data.companyInfo;
        
        return `
            <div class="invoice-footer">
                ${options.showFooterImage && company.footer ? `
                <div class="company-footer-image">
                    <img src="${company.footer}" alt="公司页脚" class="footer-image">
                </div>
                ` : ''}
                <div class="footer-content">
                    <div class="print-time">打印时间: ${currentTime}</div>
                    <div class="footer-note">如有疑问，请联系我们。谢谢您的惠顾！</div>
                </div>
            </div>
        `;
    }

    /**
     * 生成发票号 - 创建唯一的发票编号
     * @returns {string} 发票号
     * @private
     */
    _generateInvoiceNumber() {
        const date = new Date();
        const dateStr = DateUtils.format(date, 'YYYYMMDD');
        const timeStr = DateUtils.format(date, 'HHmmss');
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `INV${dateStr}${timeStr}${random}`;
    }

    /**
     * 获取支持的主题列表 - 返回发票模板支持的所有主题
     * @returns {Array<Object>} 主题信息数组
     */
    getSupportedThemes() {
        return [
            {
                id: 'classic',
                name: '经典',
                description: '标准的发票样式，适合大多数商务场景'
            },
            {
                id: 'modern',
                name: '现代',
                description: '现代化设计，清晰的分区布局'
            },
            {
                id: 'elegant',
                name: '优雅',
                description: '优雅精致，适合高端商务场所'
            },
            {
                id: 'formal',
                name: '正式',
                description: '正式商务样式，符合官方发票要求'
            }
        ];
    }

    /**
     * 验证发票数据 - 检查发票数据的完整性和有效性
     * @param {Object} data - 要验证的发票数据
     * @returns {Object} 验证结果 {isValid: boolean, errors: Array}
     */
    validateInvoiceData(data) {
        const errors = [];
        
        // 检查必填字段
        if (!data.invoiceNumber) {
            errors.push('缺少发票号码');
        }
        
        if (!data.issueDate) {
            errors.push('缺少开票日期');
        }
        
        if (!data.billTo || !data.billTo.name) {
            errors.push('缺少账单地址信息');
        }
        
        if (!data.items || !Array.isArray(data.items) || data.items.length === 0) {
            errors.push('发票必须包含至少一个项目');
        }
        
        // 检查项目数据
        if (data.items) {
            data.items.forEach((item, index) => {
                if (!item.description) {
                    errors.push(`项目 ${index + 1} 缺少描述`);
                }
                
                if (!item.quantity || item.quantity <= 0) {
                    errors.push(`项目 ${index + 1} 数量必须大于0`);
                }
                
                if (!item.unitPrice || item.unitPrice < 0) {
                    errors.push(`项目 ${index + 1} 单价不能为负数`);
                }
            });
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 创建默认发票数据 - 生成一个包含默认值的发票数据对象
     * @param {Object} overrides - 要覆盖的默认值
     * @returns {Object} 默认发票数据
     */
    createDefaultData(overrides = {}) {
        return {
            invoiceNumber: this._generateInvoiceNumber(),
            issueDate: new Date(),
            dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后
            companyInfo: {
                name: '智能办公系统',
                address: '',
                phone: '',
                email: '',
                website: ''
            },
            billTo: {
                name: '客户',
                company: '',
                address: '',
                city: '',
                state: '',
                postalCode: '',
                country: '',
                phone: '',
                email: ''
            },
            items: [
                {
                    description: '服务项目',
                    quantity: 1,
                    unitPrice: 0,
                    discount: 0,
                    taxRate: 0
                }
            ],
            discountAmount: 0,
            shippingAmount: 0,
            paymentTerms: '收到发票后30天内付款',
            notes: '',
            ...overrides
        };
    }
}
// #endregion

// #region 工厂函数
/**
 * 创建发票模板实例 - 工厂函数，方便创建发票模板
 * @param {Object} config - 模板配置
 * @returns {InvoiceTemplate} 发票模板实例
 */
export function createInvoiceTemplate(config = {}) {
    return new InvoiceTemplate(config);
}

/**
 * 创建预定义的发票模板 - 创建具有预定义样式的发票模板
 * @param {string} theme - 主题名称
 * @param {Object} config - 额外配置
 * @returns {InvoiceTemplate} 发票模板实例
 */
export function createPresetInvoiceTemplate(theme = 'classic', config = {}) {
    const presetConfig = {
        ...config,
        renderConfig: {
            theme,
            ...config.renderConfig
        }
    };
    
    return new InvoiceTemplate(presetConfig);
}
// #endregion 