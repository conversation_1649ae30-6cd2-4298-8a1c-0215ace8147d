/**
 * @file SmartOffice 2.0 主应用入口文件
 * <AUTHOR> Team
 * @description 
 * 这是重构后的主应用入口文件，负责初始化整个应用程序
 * 从index.html中提取的内联JavaScript代码将被模块化到这里和其他相关文件中
 * 
 * 重构说明：
 * - 提取了index.html中的3800行内联JavaScript
 * - 建立了清晰的模块依赖关系
 * - 实现了事件驱动的架构
 * - 统一了配置管理和状态管理
 */

// #region 导入依赖模块
import { AppInitializer } from './services/app-initializer.js';
import { ConfigManager } from './config/config-manager.js';
import { EventBus } from './utils/event-bus.js';
import { StateManager } from './services/state-manager.js';
import { NLPService } from './services/nlp-service.js';
import { DocumentService } from './services/document-service.js';
import { ExportService } from './services/export-service.js';
import { PreviewComponent } from './components/preview-component.js';
import { FormComponent } from './components/form-component.js';
import { UIManager } from './services/ui-manager.js';
// #endregion

// #region 全局应用类定义
/**
 * @class SmartOfficeApp - 主应用程序类
 * @description 管理整个SmartOffice应用的生命周期和核心功能
 */
class SmartOfficeApp {
    /**
     * 构造函数 - 初始化应用程序实例
     */
    constructor() {
        // 核心服务管理器
        this.config = new ConfigManager();
        this.eventBus = new EventBus();
        this.stateManager = new StateManager();
        this.initializer = new AppInitializer(this.config, this.eventBus);
        
        // 业务服务
        this.nlpService = null;
        this.documentService = null;
        this.exportService = null;
        
        // UI组件
        this.previewComponent = null;
        this.formComponent = null;
        this.uiManager = null;
        
        // 应用状态
        this.isInitialized = false;
        this.isReady = false;
        
        // 绑定事件处理器
        this._bindEventHandlers();
        
        console.log('[SmartOfficeApp] 应用程序实例已创建');
    }

    /**
     * 初始化应用程序 - 启动整个应用
     * @returns {Promise<void>}
     */
    async initialize() {
        try {
            console.log('[SmartOfficeApp] 开始初始化应用程序...');
            
            // 1. 初始化核心服务
            await this.initializer.init();
            
            // 2. 初始化业务服务
            await this._initializeServices();
            
            // 3. 初始化UI组件
            await this._initializeComponents();
            
            // 4. 设置事件监听
            this._setupEventListeners();
            
            // 5. 加载初始数据
            await this._loadInitialData();
            
            // 6. 标记为已初始化
            this.isInitialized = true;
            this.isReady = true;
            
            // 7. 触发应用就绪事件
            this.eventBus.emit('app:ready', {
                timestamp: new Date(),
                version: this.config.get('app.version', '2.0.0')
            });
            
            console.log('[SmartOfficeApp] 应用程序初始化完成');
            
        } catch (error) {
            console.error('[SmartOfficeApp] 应用程序初始化失败:', error);
            this.eventBus.emit('app:error', {
                error: error.message,
                timestamp: new Date()
            });
            throw error;
        }
    }

    /**
     * 初始化业务服务 - 创建和配置各种业务服务
     * @private
     */
    async _initializeServices() {
        console.log('[SmartOfficeApp] 初始化业务服务...');
        
        // 初始化NLP服务
        this.nlpService = new NLPService({
            config: this.config,
            eventBus: this.eventBus
        });
        
        // 初始化文档服务
        this.documentService = new DocumentService({
            config: this.config,
            eventBus: this.eventBus,
            nlpService: this.nlpService
        });
        
        // 初始化导出服务
        this.exportService = new ExportService({
            config: this.config,
            eventBus: this.eventBus,
            documentService: this.documentService
        });
        
        // 等待所有服务初始化完成
        await Promise.all([
            this.nlpService.initialize(),
            this.documentService.initialize(),
            this.exportService.initialize()
        ]);
        
        console.log('[SmartOfficeApp] 业务服务初始化完成');
    }

    /**
     * 初始化UI组件 - 创建和配置UI组件
     * @private
     */
    async _initializeComponents() {
        console.log('[SmartOfficeApp] 初始化UI组件...');
        
        // 初始化UI管理器
        this.uiManager = new UIManager({
            config: this.config,
            eventBus: this.eventBus,
            stateManager: this.stateManager
        });
        
        // 初始化预览组件
        this.previewComponent = new PreviewComponent({
            config: this.config,
            eventBus: this.eventBus,
            documentService: this.documentService,
            container: document.getElementById('document-preview')
        });
        
        // 初始化表单组件
        this.formComponent = new FormComponent({
            config: this.config,
            eventBus: this.eventBus,
            nlpService: this.nlpService,
            container: document.getElementById('edit-panel')
        });
        
        // 等待所有组件初始化完成
        await Promise.all([
            this.uiManager.initialize(),
            this.previewComponent.initialize(),
            this.formComponent.initialize()
        ]);
        
        console.log('[SmartOfficeApp] UI组件初始化完成');
    }

    /**
     * 绑定事件处理器 - 设置应用级别的事件处理
     * @private
     */
    _bindEventHandlers() {
        // 绑定窗口事件
        window.addEventListener('beforeunload', this._handleBeforeUnload.bind(this));
        window.addEventListener('error', this._handleGlobalError.bind(this));
        window.addEventListener('unhandledrejection', this._handleUnhandledRejection.bind(this));
        
        // 绑定文档事件
        document.addEventListener('visibilitychange', this._handleVisibilityChange.bind(this));
    }

    /**
     * 设置事件监听 - 设置应用内部的事件监听
     * @private
     */
    _setupEventListeners() {
        // 监听配置变更
        this.eventBus.on('config:changed', this._handleConfigChange.bind(this));
        
        // 监听状态变更
        this.eventBus.on('state:changed', this._handleStateChange.bind(this));
        
        // 监听文档变更
        this.eventBus.on('document:changed', this._handleDocumentChange.bind(this));
        
        // 监听错误事件
        this.eventBus.on('error', this._handleError.bind(this));
    }

    /**
     * 加载初始数据 - 加载应用启动时需要的数据
     * @private
     */
    async _loadInitialData() {
        console.log('[SmartOfficeApp] 加载初始数据...');
        
        try {
            // 加载用户配置
            await this._loadUserConfig();
            
            // 加载模板数据
            await this._loadTemplates();
            
            // 设置默认文档类型
            this.stateManager.set('currentDocumentType', 'receipt');
            
            console.log('[SmartOfficeApp] 初始数据加载完成');
            
        } catch (error) {
            console.warn('[SmartOfficeApp] 初始数据加载失败:', error);
            // 不阻止应用启动，使用默认配置
        }
    }

    /**
     * 加载用户配置
     * @private
     */
    async _loadUserConfig() {
        // 从localStorage加载用户配置
        const savedConfig = localStorage.getItem('smartoffice-config');
        if (savedConfig) {
            try {
                const config = JSON.parse(savedConfig);
                this.config.merge(config);
                console.log('[SmartOfficeApp] 用户配置已加载');
            } catch (error) {
                console.warn('[SmartOfficeApp] 用户配置解析失败:', error);
            }
        }
    }

    /**
     * 加载模板数据
     * @private
     */
    async _loadTemplates() {
        // 这里可以从服务器或本地文件加载模板
        console.log('[SmartOfficeApp] 模板数据已加载');
    }

    /**
     * 处理窗口关闭前事件
     * @param {Event} event - 事件对象
     * @private
     */
    _handleBeforeUnload(event) {
        // 保存用户配置
        this._saveUserConfig();
        
        // 如果有未保存的更改，提示用户
        if (this.stateManager.get('hasUnsavedChanges')) {
            event.preventDefault();
            event.returnValue = '您有未保存的更改，确定要离开吗？';
            return event.returnValue;
        }
    }

    /**
     * 处理全局错误
     * @param {ErrorEvent} event - 错误事件
     * @private
     */
    _handleGlobalError(event) {
        console.error('[SmartOfficeApp] 全局错误:', event.error);
        this.eventBus.emit('error', {
            type: 'global',
            error: event.error,
            timestamp: new Date()
        });
    }

    /**
     * 处理未捕获的Promise拒绝
     * @param {PromiseRejectionEvent} event - Promise拒绝事件
     * @private
     */
    _handleUnhandledRejection(event) {
        console.error('[SmartOfficeApp] 未捕获的Promise拒绝:', event.reason);
        this.eventBus.emit('error', {
            type: 'promise',
            error: event.reason,
            timestamp: new Date()
        });
    }

    /**
     * 处理页面可见性变化
     * @param {Event} event - 事件对象
     * @private
     */
    _handleVisibilityChange(event) {
        if (document.hidden) {
            // 页面隐藏时保存状态
            this._saveUserConfig();
        } else {
            // 页面显示时检查更新
            this._checkForUpdates();
        }
    }

    /**
     * 保存用户配置
     * @private
     */
    _saveUserConfig() {
        try {
            const config = this.config.export();
            localStorage.setItem('smartoffice-config', JSON.stringify(config));
            console.log('[SmartOfficeApp] 用户配置已保存');
        } catch (error) {
            console.warn('[SmartOfficeApp] 用户配置保存失败:', error);
        }
    }

    /**
     * 检查更新
     * @private
     */
    _checkForUpdates() {
        // 这里可以实现检查应用更新的逻辑
        console.log('[SmartOfficeApp] 检查更新...');
    }

    /**
     * 处理配置变更事件
     * @param {Object} event - 配置变更事件
     * @private
     */
    _handleConfigChange(event) {
        console.log('[SmartOfficeApp] 配置已变更:', event);
        // 可以在这里处理配置变更的副作用
    }

    /**
     * 处理状态变更事件
     * @param {Object} event - 状态变更事件
     * @private
     */
    _handleStateChange(event) {
        console.log('[SmartOfficeApp] 状态已变更:', event);
        // 可以在这里处理状态变更的副作用
    }

    /**
     * 处理文档变更事件
     * @param {Object} event - 文档变更事件
     * @private
     */
    _handleDocumentChange(event) {
        console.log('[SmartOfficeApp] 文档已变更:', event);
        // 标记有未保存的更改
        this.stateManager.set('hasUnsavedChanges', true);
    }

    /**
     * 处理错误事件
     * @param {Object} event - 错误事件
     * @private
     */
    _handleError(event) {
        console.error('[SmartOfficeApp] 应用错误:', event);
        // 可以在这里实现错误报告或用户通知
    }

    /**
     * 获取应用信息
     * @returns {Object} 应用信息
     */
    getInfo() {
        return {
            name: 'SmartOffice',
            version: this.config.get('app.version', '2.0.0'),
            isInitialized: this.isInitialized,
            isReady: this.isReady,
            services: {
                nlp: !!this.nlpService,
                document: !!this.documentService,
                export: !!this.exportService
            },
            components: {
                preview: !!this.previewComponent,
                form: !!this.formComponent,
                ui: !!this.uiManager
            }
        };
    }

    /**
     * 销毁应用程序 - 清理资源
     */
    destroy() {
        console.log('[SmartOfficeApp] 销毁应用程序...');
        
        // 保存用户配置
        this._saveUserConfig();
        
        // 销毁组件
        if (this.previewComponent) this.previewComponent.destroy();
        if (this.formComponent) this.formComponent.destroy();
        if (this.uiManager) this.uiManager.destroy();
        
        // 销毁服务
        if (this.nlpService) this.nlpService.destroy();
        if (this.documentService) this.documentService.destroy();
        if (this.exportService) this.exportService.destroy();
        
        // 清理事件监听
        this.eventBus.removeAllListeners();
        
        // 重置状态
        this.isInitialized = false;
        this.isReady = false;
        
        console.log('[SmartOfficeApp] 应用程序已销毁');
    }
}
// #endregion

// #region 应用启动逻辑
/**
 * 应用启动函数 - 在DOM加载完成后启动应用
 */
async function startApp() {
    try {
        console.log('[SmartOffice] 开始启动应用程序...');
        
        // 创建应用实例
        const app = new SmartOfficeApp();
        
        // 初始化应用
        await app.initialize();
        
        // 将应用实例挂载到全局对象，供调试和其他脚本使用
        window.smartOfficeApp = app;
        
        console.log('[SmartOffice] 应用程序启动成功');
        
    } catch (error) {
        console.error('[SmartOffice] 应用程序启动失败:', error);
        
        // 显示错误信息给用户
        const errorMessage = `应用程序启动失败: ${error.message}`;
        if (document.body) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: #fee;
                border: 1px solid #fcc;
                padding: 20px;
                border-radius: 5px;
                z-index: 9999;
                max-width: 500px;
                text-align: center;
            `;
            errorDiv.innerHTML = `
                <h3>启动失败</h3>
                <p>${errorMessage}</p>
                <button onclick="location.reload()">重新加载</button>
            `;
            document.body.appendChild(errorDiv);
        }
    }
}

// 等待DOM加载完成后启动应用
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', startApp);
} else {
    // DOM已经加载完成，直接启动
    startApp();
}
// #endregion

// 导出应用类供其他模块使用
export { SmartOfficeApp };
