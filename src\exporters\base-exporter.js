/**
 * @file 导出系统基础架构 - 所有导出器的核心框架
 * <AUTHOR> Team
 * @description 
 * 这个文件定义了导出系统的核心架构，包括：
 * - BaseExporter：所有导出器的基础类
 * - ExporterRegistry：导出器注册表，管理所有已注册的导出器
 * - ExportEngine：导出引擎，提供统一的导出流程控制
 * - 事件驱动的导出进度追踪和错误处理
 * - 配置验证和导出结果管理
 */

// #region 导入依赖模块
import { EventEmitter } from '../core/events/event-emitter.js';
import { EXPORT_EVENTS } from '../core/events/event-types.js';
import { validateExportConfig, isValidFileName } from '../core/utils/validation.js';
// #endregion

// #region BaseExporter 基础导出器类
/**
 * @class BaseExporter - 所有导出器的基础类
 * @description 定义导出器的基本接口、配置管理、事件处理和生命周期
 */
export class BaseExporter extends EventEmitter {
    /**
     * 构造函数 - 初始化导出器基础配置
     * @param {Object} config - 导出器配置
     * @param {string} config.name - 导出器名称
     * @param {string} config.type - 导出器类型 (pdf, image, print)
     * @param {string} config.version - 导出器版本
     * @param {Object} config.defaultOptions - 默认导出选项
     */
    constructor(config = {}) {
        super();
        
        // 基本信息配置
        this.name = config.name || `${this.constructor.name}-${Date.now()}`;
        this.type = config.type || 'unknown';
        this.version = config.version || '1.0.0';
        this.description = config.description || '';
        
        // 导出器状态
        this.isInitialized = false;
        this.isExporting = false;
        this.lastError = null;
        
        // 配置和选项
        this.defaultOptions = {
            fileName: 'document',
            quality: 'medium',
            format: this.type,
            autoDownload: true,
            showProgress: true,
            ...config.defaultOptions
        };
        
        // 统计信息
        this.stats = {
            totalExports: 0,
            successfulExports: 0,
            failedExports: 0,
            lastExportTime: null,
            averageExportTime: 0,
            totalExportTime: 0
        };
        
        // 初始化导出器
        this._initializeExporter();
    }

    /**
     * 初始化导出器 - 子类可重写此方法进行特定初始化
     * @private
     */
    _initializeExporter() {
        try {
            // 验证导出器配置
            this._validateConfig();
            
            // 设置事件监听器
            this._setupEventListeners();
            
            // 标记为已初始化
            this.isInitialized = true;
            
            // 触发初始化完成事件
            this.emit(EXPORT_EVENTS.EXPORTER_INITIALIZED, {
                exporter: this.name,
                type: this.type
            });
            
        } catch (error) {
            this.lastError = error;
            this.emit(EXPORT_EVENTS.EXPORTER_ERROR, {
                exporter: this.name,
                error: error.message,
                phase: 'initialization'
            });
        }
    }

    /**
     * 验证导出器配置 - 确保配置的有效性
     * @private
     */
    _validateConfig() {
        if (!this.name || typeof this.name !== 'string') {
            throw new Error('导出器名称必须是有效的字符串');
        }
        
        if (!this.type || typeof this.type !== 'string') {
            throw new Error('导出器类型必须是有效的字符串');
        }
        
        // 子类可重写此方法添加特定验证
        this._validateSpecificConfig();
    }

    /**
     * 验证特定配置 - 子类重写此方法进行特定验证
     * @protected
     */
    _validateSpecificConfig() {
        // 由子类实现
    }

    /**
     * 设置事件监听器 - 建立内部事件处理
     * @private
     */
    _setupEventListeners() {
        // 监听导出开始事件
        this.on(EXPORT_EVENTS.EXPORT_STARTED, (data) => {
            this.isExporting = true;
            this.stats.totalExports++;
        });
        
        // 监听导出完成事件
        this.on(EXPORT_EVENTS.EXPORT_COMPLETED, (data) => {
            this.isExporting = false;
            this.stats.successfulExports++;
            this._updateExportStats(data.duration);
        });
        
        // 监听导出失败事件
        this.on(EXPORT_EVENTS.EXPORT_FAILED, (data) => {
            this.isExporting = false;
            this.stats.failedExports++;
            this.lastError = new Error(data.error);
        });
    }

    /**
     * 更新导出统计信息 - 记录导出性能数据
     * @param {number} duration - 导出耗时（毫秒）
     * @private
     */
    _updateExportStats(duration) {
        this.stats.lastExportTime = Date.now();
        this.stats.totalExportTime += duration;
        this.stats.averageExportTime = this.stats.totalExportTime / this.stats.successfulExports;
    }

    /**
     * 导出文档 - 主要导出方法，子类必须实现
     * @param {Object} content - 要导出的内容
     * @param {Object} options - 导出选项
     * @returns {Promise<Object>} 导出结果
     */
    async export(content, options = {}) {
        if (!this.isInitialized) {
            throw new Error(`导出器 ${this.name} 尚未初始化`);
        }
        
        if (this.isExporting) {
            throw new Error(`导出器 ${this.name} 正在导出中，请等待完成`);
        }

        // 合并导出选项
        const exportOptions = { ...this.defaultOptions, ...options };
        
        // 验证导出选项
        this._validateExportOptions(exportOptions);
        
        const startTime = Date.now();
        
        try {
            // 触发导出开始事件
            this.emit(EXPORT_EVENTS.EXPORT_STARTED, {
                exporter: this.name,
                type: this.type,
                options: exportOptions
            });
            
            // 执行实际导出
            const result = await this._performExport(content, exportOptions);
            
            const duration = Date.now() - startTime;
            
            // 触发导出完成事件
            this.emit(EXPORT_EVENTS.EXPORT_COMPLETED, {
                exporter: this.name,
                type: this.type,
                result,
                duration
            });
            
            return result;
            
        } catch (error) {
            // 触发导出失败事件
            this.emit(EXPORT_EVENTS.EXPORT_FAILED, {
                exporter: this.name,
                type: this.type,
                error: error.message,
                duration: Date.now() - startTime
            });
            
            throw error;
        }
    }

    /**
     * 验证导出选项 - 检查导出选项的有效性
     * @param {Object} options - 导出选项
     * @private
     */
    _validateExportOptions(options) {
        // 验证文件名
        if (options.fileName && !isValidFileName(options.fileName)) {
            throw new Error('无效的文件名格式');
        }
        
        // 验证质量设置
        if (options.quality && !['low', 'medium', 'high'].includes(options.quality)) {
            throw new Error('质量设置必须是 low、medium 或 high');
        }
        
        // 子类可重写此方法添加特定验证
        this._validateSpecificOptions(options);
    }

    /**
     * 验证特定导出选项 - 子类重写此方法进行特定验证
     * @param {Object} options - 导出选项
     * @protected
     */
    _validateSpecificOptions(options) {
        // 由子类实现
    }

    /**
     * 执行实际导出 - 子类必须实现此方法
     * @param {Object} content - 要导出的内容
     * @param {Object} options - 导出选项
     * @returns {Promise<Object>} 导出结果
     * @protected
     */
    async _performExport(content, options) {
        throw new Error('子类必须实现 _performExport 方法');
    }

    /**
     * 获取支持的格式 - 返回导出器支持的格式列表
     * @returns {Array<string>} 支持的格式数组
     */
    getSupportedFormats() {
        return [this.type];
    }

    /**
     * 获取导出器信息 - 返回导出器的详细信息
     * @returns {Object} 导出器信息
     */
    getInfo() {
        return {
            name: this.name,
            type: this.type,
            version: this.version,
            description: this.description,
            isInitialized: this.isInitialized,
            isExporting: this.isExporting,
            supportedFormats: this.getSupportedFormats(),
            stats: { ...this.stats }
        };
    }

    /**
     * 重置统计信息 - 清空导出统计数据
     */
    resetStats() {
        this.stats = {
            totalExports: 0,
            successfulExports: 0,
            failedExports: 0,
            lastExportTime: null,
            averageExportTime: 0,
            totalExportTime: 0
        };
    }

    /**
     * 清理资源 - 清理导出器占用的资源
     */
    cleanup() {
        this.removeAllListeners();
        this.isInitialized = false;
        this.isExporting = false;
        this.lastError = null;
    }
}
// #endregion

// #region ExporterRegistry 导出器注册表
/**
 * @class ExporterRegistry - 导出器注册表
 * @description 管理所有已注册的导出器，提供注册、查找、类型筛选等功能
 */
export class ExporterRegistry extends EventEmitter {
    /**
     * 构造函数 - 初始化导出器注册表
     */
    constructor() {
        super();
        
        // 导出器存储
        this.exporters = new Map();
        this.typeMap = new Map();
        
        // 注册表统计
        this.stats = {
            totalRegistered: 0,
            byType: {},
            lastRegistered: null
        };
    }

    /**
     * 注册导出器 - 将导出器添加到注册表
     * @param {BaseExporter} exporter - 导出器实例
     * @param {boolean} overwrite - 是否覆盖同名导出器
     */
    register(exporter, overwrite = false) {
        // 验证导出器实例
        if (!(exporter instanceof BaseExporter)) {
            throw new Error('只能注册 BaseExporter 的实例');
        }
        
        // 检查名称冲突
        if (this.exporters.has(exporter.name) && !overwrite) {
            throw new Error(`导出器 ${exporter.name} 已存在，请使用不同的名称或设置 overwrite=true`);
        }
        
        // 注册导出器
        this.exporters.set(exporter.name, exporter);
        
        // 更新类型映射
        if (!this.typeMap.has(exporter.type)) {
            this.typeMap.set(exporter.type, new Set());
        }
        this.typeMap.get(exporter.type).add(exporter.name);
        
        // 更新统计信息
        this._updateRegistryStats(exporter);
        
        // 触发注册事件
        this.emit(EXPORT_EVENTS.EXPORTER_REGISTERED, {
            exporter: exporter.name,
            type: exporter.type
        });
    }

    /**
     * 更新注册表统计信息
     * @param {BaseExporter} exporter - 新注册的导出器
     * @private
     */
    _updateRegistryStats(exporter) {
        this.stats.totalRegistered = this.exporters.size;
        this.stats.byType[exporter.type] = (this.stats.byType[exporter.type] || 0) + 1;
        this.stats.lastRegistered = {
            name: exporter.name,
            type: exporter.type,
            timestamp: Date.now()
        };
    }

    /**
     * 获取导出器 - 根据名称获取已注册的导出器
     * @param {string} name - 导出器名称
     * @returns {BaseExporter|null} 导出器实例
     */
    get(name) {
        return this.exporters.get(name) || null;
    }

    /**
     * 按类型获取导出器 - 返回指定类型的所有导出器
     * @param {string} type - 导出器类型
     * @returns {Array<BaseExporter>} 导出器实例数组
     */
    getByType(type) {
        const exporterNames = this.typeMap.get(type);
        if (!exporterNames) {
            return [];
        }
        
        return Array.from(exporterNames)
            .map(name => this.exporters.get(name))
            .filter(exporter => exporter !== undefined);
    }

    /**
     * 获取所有导出器 - 返回所有已注册的导出器
     * @returns {Array<BaseExporter>} 所有导出器实例数组
     */
    getAll() {
        return Array.from(this.exporters.values());
    }

    /**
     * 移除导出器 - 从注册表中移除指定的导出器
     * @param {string} name - 导出器名称
     * @returns {boolean} 是否成功移除
     */
    unregister(name) {
        const exporter = this.exporters.get(name);
        if (!exporter) {
            return false;
        }
        
        // 从导出器映射中移除
        this.exporters.delete(name);
        
        // 从类型映射中移除
        const typeSet = this.typeMap.get(exporter.type);
        if (typeSet) {
            typeSet.delete(name);
            if (typeSet.size === 0) {
                this.typeMap.delete(exporter.type);
            }
        }
        
        // 更新统计信息
        this.stats.totalRegistered = this.exporters.size;
        this.stats.byType[exporter.type] = Math.max(0, (this.stats.byType[exporter.type] || 1) - 1);
        
        // 清理导出器资源
        exporter.cleanup();
        
        // 触发移除事件
        this.emit(EXPORT_EVENTS.EXPORTER_UNREGISTERED, {
            exporter: name,
            type: exporter.type
        });
        
        return true;
    }

    /**
     * 检查导出器是否存在
     * @param {string} name - 导出器名称
     * @returns {boolean} 是否存在
     */
    has(name) {
        return this.exporters.has(name);
    }

    /**
     * 获取支持的类型 - 返回所有已注册的导出器类型
     * @returns {Array<string>} 导出器类型数组
     */
    getSupportedTypes() {
        return Array.from(this.typeMap.keys());
    }

    /**
     * 获取注册表统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.stats,
            supportedTypes: this.getSupportedTypes()
        };
    }

    /**
     * 清空注册表 - 移除所有导出器
     */
    clear() {
        for (const exporter of this.exporters.values()) {
            exporter.cleanup();
        }
        
        this.exporters.clear();
        this.typeMap.clear();
        
        this.stats = {
            totalRegistered: 0,
            byType: {},
            lastRegistered: null
        };
    }
}
// #endregion

// #region ExportEngine 导出引擎
/**
 * @class ExportEngine - 导出引擎
 * @description 提供统一的导出流程控制、批量导出、进度管理等高级功能
 */
export class ExportEngine extends EventEmitter {
    /**
     * 构造函数 - 初始化导出引擎
     * @param {ExporterRegistry} registry - 导出器注册表
     */
    constructor(registry = null) {
        super();
        
        // 导出器注册表
        this.registry = registry || new ExporterRegistry();
        
        // 引擎配置
        this.config = {
            defaultExporter: null,
            maxConcurrentExports: 3,
            exportTimeout: 60000, // 60秒
            enableCache: true,
            cacheMaxSize: 50
        };
        
        // 导出队列和缓存
        this.exportQueue = [];
        this.activeExports = new Map();
        this.exportCache = new Map();
        
        // 引擎统计
        this.engineStats = {
            totalExports: 0,
            queuedExports: 0,
            cachedExports: 0,
            averageWaitTime: 0
        };
    }

    /**
     * 设置默认导出器 - 指定默认使用的导出器
     * @param {string} exporterName - 导出器名称
     */
    setDefaultExporter(exporterName) {
        const exporter = this.registry.get(exporterName);
        if (!exporter) {
            throw new Error(`导出器 ${exporterName} 不存在`);
        }
        
        this.config.defaultExporter = exporterName;
    }

    /**
     * 导出文档 - 使用指定或默认导出器导出文档
     * @param {Object} content - 要导出的内容
     * @param {Object} options - 导出选项
     * @returns {Promise<Object>} 导出结果
     */
    async export(content, options = {}) {
        // 确定使用的导出器
        const exporterName = options.exporter || this.config.defaultExporter;
        if (!exporterName) {
            throw new Error('必须指定导出器或设置默认导出器');
        }
        
        const exporter = this.registry.get(exporterName);
        if (!exporter) {
            throw new Error(`导出器 ${exporterName} 不存在`);
        }
        
        // 生成导出ID
        const exportId = `export_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        // 检查缓存
        if (this.config.enableCache) {
            const cacheKey = this._generateCacheKey(content, options);
            const cachedResult = this.exportCache.get(cacheKey);
            if (cachedResult) {
                this.engineStats.cachedExports++;
                return { ...cachedResult, fromCache: true };
            }
        }
        
        try {
            // 等待导出队列
            await this._waitForExportSlot(exportId);
            
            // 执行导出
            this.activeExports.set(exportId, {
                exporter: exporterName,
                startTime: Date.now()
            });
            
            const result = await this._executeExportWithTimeout(exporter, content, options);
            
            // 缓存结果
            if (this.config.enableCache) {
                this._cacheResult(content, options, result);
            }
            
            // 更新统计
            this.engineStats.totalExports++;
            
            return result;
            
        } finally {
            // 清理活跃导出
            this.activeExports.delete(exportId);
            
            // 处理队列中的下一个导出
            this._processNextExport();
        }
    }

    /**
     * 等待导出槽位 - 实现并发控制
     * @param {string} exportId - 导出ID
     * @private
     */
    async _waitForExportSlot(exportId) {
        if (this.activeExports.size < this.config.maxConcurrentExports) {
            return; // 直接执行
        }
        
        // 加入队列等待
        return new Promise((resolve) => {
            this.exportQueue.push({ exportId, resolve });
            this.engineStats.queuedExports++;
        });
    }

    /**
     * 处理队列中的下一个导出
     * @private
     */
    _processNextExport() {
        if (this.exportQueue.length > 0 && this.activeExports.size < this.config.maxConcurrentExports) {
            const { resolve } = this.exportQueue.shift();
            this.engineStats.queuedExports--;
            resolve();
        }
    }

    /**
     * 带超时的导出执行
     * @param {BaseExporter} exporter - 导出器
     * @param {Object} content - 内容
     * @param {Object} options - 选项
     * @returns {Promise<Object>} 导出结果
     * @private
     */
    async _executeExportWithTimeout(exporter, content, options) {
        return Promise.race([
            exporter.export(content, options),
            new Promise((_, reject) => {
                setTimeout(() => {
                    reject(new Error(`导出超时（${this.config.exportTimeout}ms）`));
                }, this.config.exportTimeout);
            })
        ]);
    }

    /**
     * 生成缓存键
     * @param {Object} content - 内容
     * @param {Object} options - 选项
     * @returns {string} 缓存键
     * @private
     */
    _generateCacheKey(content, options) {
        const keyData = {
            content: typeof content === 'string' ? content : JSON.stringify(content),
            options: JSON.stringify(options)
        };
        return btoa(JSON.stringify(keyData));
    }

    /**
     * 缓存导出结果
     * @param {Object} content - 内容
     * @param {Object} options - 选项
     * @param {Object} result - 结果
     * @private
     */
    _cacheResult(content, options, result) {
        if (this.exportCache.size >= this.config.cacheMaxSize) {
            // 删除最老的缓存项
            const firstKey = this.exportCache.keys().next().value;
            this.exportCache.delete(firstKey);
        }
        
        const cacheKey = this._generateCacheKey(content, options);
        this.exportCache.set(cacheKey, { ...result, cachedAt: Date.now() });
    }

    /**
     * 批量导出 - 同时使用多个导出器导出
     * @param {Object} content - 要导出的内容
     * @param {Array<string>} exporterNames - 导出器名称数组
     * @param {Object} baseOptions - 基础导出选项
     * @returns {Promise<Object>} 批量导出结果
     */
    async batchExport(content, exporterNames, baseOptions = {}) {
        const results = {};
        const exportPromises = [];
        
        for (const exporterName of exporterNames) {
            const exportOptions = {
                ...baseOptions,
                exporter: exporterName
            };
            
            exportPromises.push(
                this.export(content, exportOptions)
                    .then(result => {
                        results[exporterName] = result;
                    })
                    .catch(error => {
                        results[exporterName] = { error: error.message };
                    })
            );
        }
        
        await Promise.all(exportPromises);
        return results;
    }

    /**
     * 获取引擎统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.engineStats,
            registryStats: this.registry.getStats(),
            activeExports: this.activeExports.size,
            queuedExports: this.exportQueue.length,
            cacheSize: this.exportCache.size,
            config: { ...this.config }
        };
    }

    /**
     * 清理引擎资源
     */
    cleanup() {
        this.exportQueue = [];
        this.activeExports.clear();
        this.exportCache.clear();
        this.removeAllListeners();
    }
}
// #endregion

// #region 默认实例和导出
// 创建默认的导出器注册表和导出引擎
export const defaultExporterRegistry = new ExporterRegistry();
export const defaultExportEngine = new ExportEngine(defaultExporterRegistry);

// 设置全局访问（如果在浏览器环境）
if (typeof window !== 'undefined') {
    window.SmartOfficeExporters = {
        registry: defaultExporterRegistry,
        engine: defaultExportEngine
    };
}
// #endregion 