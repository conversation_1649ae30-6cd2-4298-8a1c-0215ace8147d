/**
 * @file 日志管理系统
 * @description 提供统一的日志记录、调试信息和性能监控功能
 */

// #region 日志级别定义
/**
 * 日志级别枚举
 * @enum {string}
 */
export const LogLevel = {
    TRACE: 'TRACE',     // 最详细的跟踪信息
    DEBUG: 'DEBUG',     // 调试信息
    INFO: 'INFO',       // 一般信息
    WARN: 'WARN',       // 警告信息
    ERROR: 'ERROR',     // 错误信息
    FATAL: 'FATAL'      // 致命错误
};

/**
 * 日志级别优先级
 * @type {Object<string, number>}
 */
const LOG_PRIORITY = {
    [LogLevel.TRACE]: 0,
    [LogLevel.DEBUG]: 1,
    [LogLevel.INFO]: 2,
    [LogLevel.WARN]: 3,
    [LogLevel.ERROR]: 4,
    [LogLevel.FATAL]: 5
};
// #endregion

// #region Logger 主类
/**
 * @class Logger - 日志管理器
 * @description 提供统一的日志记录和调试功能
 */
export class Logger {
    /**
     * 构造函数 - 初始化日志管理器
     * @param {Object} config - 日志配置
     */
    constructor(config = {}) {
        this.config = {
            level: LogLevel.INFO,           // 当前日志级别
            enableConsole: true,            // 是否输出到控制台
            enableStorage: true,            // 是否存储到本地
            enablePerformance: true,        // 是否启用性能监控
            enableDebugPanel: false,        // 是否显示调试面板
            maxLogEntries: 1000,           // 最大日志条目数
            autoFlush: true,               // 自动刷新存储
            flushInterval: 5000,           // 刷新间隔(毫秒)
            
            // 日志格式配置
            format: {
                timestamp: true,            // 显示时间戳
                level: true,               // 显示日志级别
                module: true,              // 显示模块名
                function: true,            // 显示函数名
                emoji: true                // 使用表情符号
            },
            
            // 自定义配置覆盖
            ...config
        };
        
        // 内部状态
        this.logs = [];                    // 日志存储
        this.performanceMarks = new Map(); // 性能标记
        this.debugCounters = new Map();    // 调试计数器
        this.isInitialized = false;        // 初始化状态
        this.flushTimer = null;            // 刷新定时器
        
        // 日志级别图标映射
        this.levelIcons = {
            [LogLevel.TRACE]: '🔍',
            [LogLevel.DEBUG]: '🐛',
            [LogLevel.INFO]: 'ℹ️',
            [LogLevel.WARN]: '⚠️',
            [LogLevel.ERROR]: '❌',
            [LogLevel.FATAL]: '💥'
        };
        
        // 日志级别颜色映射
        this.levelColors = {
            [LogLevel.TRACE]: '#888888',
            [LogLevel.DEBUG]: '#00BCD4',
            [LogLevel.INFO]: '#2196F3',
            [LogLevel.WARN]: '#FF9800',
            [LogLevel.ERROR]: '#F44336',
            [LogLevel.FATAL]: '#9C27B0'
        };
        
        // 初始化日志管理器
        this._initialize();
    }
    
    /**
     * 初始化日志管理器
     * @function _initialize
     * @private
     */
    _initialize() {
        try {
            // 记录初始化开始
            console.log('📝 正在初始化日志管理器...');
            
            // 设置全局错误处理
            this._setupGlobalErrorHandling();
            
            // 启动自动刷新
            if (this.config.autoFlush) {
                this._startAutoFlush();
            }
            
            // 创建调试面板
            if (this.config.enableDebugPanel) {
                this._createDebugPanel();
            }
            
            // 标记为已初始化
            this.isInitialized = true;
            
            // 记录初始化完成
            this.info('Logger', '_initialize', '日志管理器初始化完成', {
                config: this.config,
                timestamp: new Date().toISOString()
            });
            
        } catch (error) {
            console.error('❌ 日志管理器初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 设置全局错误处理
     * @function _setupGlobalErrorHandling
     * @private
     */
    _setupGlobalErrorHandling() {
        // 捕获未处理的错误
        window.addEventListener('error', (event) => {
            this.error('Global', 'error', '全局错误捕获', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
        });
        
        // 捕获未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.error('Global', 'unhandledrejection', '未处理的Promise拒绝', {
                reason: event.reason,
                promise: event.promise
            });
        });
    }
    
    /**
     * 启动自动刷新
     * @function _startAutoFlush
     * @private
     */
    _startAutoFlush() {
        if (this.flushTimer) {
            clearInterval(this.flushTimer);
        }
        
        this.flushTimer = setInterval(() => {
            this._flushLogs();
        }, this.config.flushInterval);
    }
    
    /**
     * 刷新日志到存储
     * @function _flushLogs
     * @private
     */
    _flushLogs() {
        if (!this.config.enableStorage || this.logs.length === 0) {
            return;
        }
        
        try {
            const logData = {
                logs: this.logs.slice(-this.config.maxLogEntries),
                timestamp: new Date().toISOString(),
                config: this.config
            };
            
            localStorage.setItem('smartoffice_logs', JSON.stringify(logData));
            
            // 清理旧日志
            if (this.logs.length > this.config.maxLogEntries) {
                this.logs = this.logs.slice(-this.config.maxLogEntries);
            }
            
        } catch (error) {
            console.warn('⚠️ 日志存储失败:', error);
        }
    }
    
    /**
     * 记录日志条目
     * @function log
     * @param {string} level - 日志级别
     * @param {string} module - 模块名
     * @param {string} functionName - 函数名
     * @param {string} message - 日志消息
     * @param {*} data - 附加数据
     */
    log(level, module, functionName, message, data = null) {
        // 检查日志级别
        if (LOG_PRIORITY[level] < LOG_PRIORITY[this.config.level]) {
            return;
        }
        
        // 创建日志条目
        const logEntry = {
            timestamp: new Date().toISOString(),
            level,
            module,
            function: functionName,
            message,
            data,
            id: this._generateLogId()
        };
        
        // 存储日志
        this.logs.push(logEntry);
        
        // 输出到控制台
        if (this.config.enableConsole) {
            this._outputToConsole(logEntry);
        }
        
        // 更新调试面板
        if (this.config.enableDebugPanel) {
            this._updateDebugPanel(logEntry);
        }
    }
    
    /**
     * 输出日志到控制台
     * @function _outputToConsole
     * @param {Object} logEntry - 日志条目
     * @private
     */
    _outputToConsole(logEntry) {
        const { level, module, function: fn, message, data } = logEntry;
        const timestamp = new Date(logEntry.timestamp).toLocaleTimeString();
        
        // 构建日志消息
        let logMessage = '';
        
        if (this.config.format.emoji) {
            logMessage += `${this.levelIcons[level]} `;
        }
        
        if (this.config.format.timestamp) {
            logMessage += `[${timestamp}] `;
        }
        
        if (this.config.format.level) {
            logMessage += `${level} `;
        }
        
        if (this.config.format.module) {
            logMessage += `${module}`;
        }
        
        if (this.config.format.function && fn) {
            logMessage += `.${fn}()`;
        }
        
        logMessage += `: ${message}`;
        
        // 选择控制台方法
        const consoleMethod = this._getConsoleMethod(level);
        
        // 输出日志
        if (data) {
            consoleMethod(logMessage, data);
        } else {
            consoleMethod(logMessage);
        }
    }
    
    /**
     * 获取控制台输出方法
     * @function _getConsoleMethod
     * @param {string} level - 日志级别
     * @returns {Function} 控制台方法
     * @private
     */
    _getConsoleMethod(level) {
        switch (level) {
            case LogLevel.TRACE:
            case LogLevel.DEBUG:
                return console.debug.bind(console);
            case LogLevel.INFO:
                return console.info.bind(console);
            case LogLevel.WARN:
                return console.warn.bind(console);
            case LogLevel.ERROR:
            case LogLevel.FATAL:
                return console.error.bind(console);
            default:
                return console.log.bind(console);
        }
    }
    
    /**
     * 生成日志ID
     * @function _generateLogId
     * @returns {string} 日志ID
     * @private
     */
    _generateLogId() {
        return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    // #region 便捷日志方法
    /**
     * 记录跟踪信息
     * @function trace
     * @param {string} module - 模块名
     * @param {string} functionName - 函数名
     * @param {string} message - 日志消息
     * @param {*} data - 附加数据
     */
    trace(module, functionName, message, data = null) {
        this.log(LogLevel.TRACE, module, functionName, message, data);
    }
    
    /**
     * 记录调试信息
     * @function debug
     * @param {string} module - 模块名
     * @param {string} functionName - 函数名
     * @param {string} message - 日志消息
     * @param {*} data - 附加数据
     */
    debug(module, functionName, message, data = null) {
        this.log(LogLevel.DEBUG, module, functionName, message, data);
    }
    
    /**
     * 记录一般信息
     * @function info
     * @param {string} module - 模块名
     * @param {string} functionName - 函数名
     * @param {string} message - 日志消息
     * @param {*} data - 附加数据
     */
    info(module, functionName, message, data = null) {
        this.log(LogLevel.INFO, module, functionName, message, data);
    }
    
    /**
     * 记录警告信息
     * @function warn
     * @param {string} module - 模块名
     * @param {string} functionName - 函数名
     * @param {string} message - 日志消息
     * @param {*} data - 附加数据
     */
    warn(module, functionName, message, data = null) {
        this.log(LogLevel.WARN, module, functionName, message, data);
    }
    
    /**
     * 记录错误信息
     * @function error
     * @param {string} module - 模块名
     * @param {string} functionName - 函数名
     * @param {string} message - 日志消息
     * @param {*} data - 附加数据
     */
    error(module, functionName, message, data = null) {
        this.log(LogLevel.ERROR, module, functionName, message, data);
    }
    
    /**
     * 记录致命错误
     * @function fatal
     * @param {string} module - 模块名
     * @param {string} functionName - 函数名
     * @param {string} message - 日志消息
     * @param {*} data - 附加数据
     */
    fatal(module, functionName, message, data = null) {
        this.log(LogLevel.FATAL, module, functionName, message, data);
    }
    // #endregion
    
    // #region 性能监控方法
    /**
     * 开始性能标记
     * @function startPerformanceMark
     * @param {string} name - 标记名称
     * @param {string} module - 模块名
     * @param {string} functionName - 函数名
     */
    startPerformanceMark(name, module, functionName) {
        if (!this.config.enablePerformance) return;
        
        const markData = {
            name,
            module,
            function: functionName,
            startTime: performance.now(),
            timestamp: new Date().toISOString()
        };
        
        this.performanceMarks.set(name, markData);
        
        this.debug(module, functionName, `开始性能标记: ${name}`, {
            startTime: markData.startTime
        });
    }
    
    /**
     * 结束性能标记
     * @function endPerformanceMark
     * @param {string} name - 标记名称
     * @param {string} module - 模块名
     * @param {string} functionName - 函数名
     * @returns {number|null} 执行时间(毫秒)
     */
    endPerformanceMark(name, module, functionName) {
        if (!this.config.enablePerformance) return null;
        
        const markData = this.performanceMarks.get(name);
        if (!markData) {
            this.warn(module, functionName, `性能标记不存在: ${name}`);
            return null;
        }
        
        const endTime = performance.now();
        const duration = endTime - markData.startTime;
        
        this.performanceMarks.delete(name);
        
        this.info(module, functionName, `性能标记完成: ${name}`, {
            duration: `${duration.toFixed(2)}ms`,
            startTime: markData.startTime,
            endTime
        });
        
        return duration;
    }
    // #endregion
    
    // #region 调试计数器
    /**
     * 增加调试计数
     * @function incrementCounter
     * @param {string} name - 计数器名称
     * @param {string} module - 模块名
     * @returns {number} 当前计数
     */
    incrementCounter(name, module) {
        const currentCount = (this.debugCounters.get(name) || 0) + 1;
        this.debugCounters.set(name, currentCount);
        
        this.debug(module, 'incrementCounter', `计数器递增: ${name} = ${currentCount}`);
        
        return currentCount;
    }
    
    /**
     * 重置调试计数
     * @function resetCounter
     * @param {string} name - 计数器名称
     * @param {string} module - 模块名
     */
    resetCounter(name, module) {
        this.debugCounters.set(name, 0);
        this.debug(module, 'resetCounter', `计数器重置: ${name} = 0`);
    }
    
    /**
     * 获取计数器值
     * @function getCounter
     * @param {string} name - 计数器名称
     * @returns {number} 计数器值
     */
    getCounter(name) {
        return this.debugCounters.get(name) || 0;
    }
    // #endregion
    
    // #region 日志查询和导出
    /**
     * 获取所有日志
     * @function getLogs
     * @param {Object} filter - 过滤条件
     * @returns {Array} 日志列表
     */
    getLogs(filter = {}) {
        let logs = [...this.logs];
        
        // 按级别过滤
        if (filter.level) {
            logs = logs.filter(log => log.level === filter.level);
        }
        
        // 按模块过滤
        if (filter.module) {
            logs = logs.filter(log => log.module === filter.module);
        }
        
        // 按时间范围过滤
        if (filter.startTime || filter.endTime) {
            logs = logs.filter(log => {
                const logTime = new Date(log.timestamp);
                return (!filter.startTime || logTime >= new Date(filter.startTime)) &&
                       (!filter.endTime || logTime <= new Date(filter.endTime));
            });
        }
        
        return logs;
    }
    
    /**
     * 导出日志
     * @function exportLogs
     * @param {string} format - 导出格式 ('json'|'csv'|'text')
     * @param {Object} filter - 过滤条件
     * @returns {string} 导出的日志数据
     */
    exportLogs(format = 'json', filter = {}) {
        const logs = this.getLogs(filter);
        
        switch (format.toLowerCase()) {
            case 'json':
                return JSON.stringify(logs, null, 2);
                
            case 'csv':
                return this._exportToCsv(logs);
                
            case 'text':
                return this._exportToText(logs);
                
            default:
                throw new Error(`不支持的导出格式: ${format}`);
        }
    }
    
    /**
     * 导出为CSV格式
     * @function _exportToCsv
     * @param {Array} logs - 日志列表
     * @returns {string} CSV格式的日志
     * @private
     */
    _exportToCsv(logs) {
        const headers = ['Timestamp', 'Level', 'Module', 'Function', 'Message', 'Data'];
        const rows = [headers.join(',')];
        
        logs.forEach(log => {
            const row = [
                log.timestamp,
                log.level,
                log.module,
                log.function || '',
                `"${log.message.replace(/"/g, '""')}"`,
                log.data ? `"${JSON.stringify(log.data).replace(/"/g, '""')}"` : ''
            ];
            rows.push(row.join(','));
        });
        
        return rows.join('\n');
    }
    
    /**
     * 导出为文本格式
     * @function _exportToText
     * @param {Array} logs - 日志列表
     * @returns {string} 文本格式的日志
     * @private
     */
    _exportToText(logs) {
        return logs.map(log => {
            let line = `[${log.timestamp}] ${log.level} ${log.module}`;
            if (log.function) {
                line += `.${log.function}()`;
            }
            line += `: ${log.message}`;
            if (log.data) {
                line += `\n  Data: ${JSON.stringify(log.data, null, 2)}`;
            }
            return line;
        }).join('\n\n');
    }
    // #endregion
    
    /**
     * 清理资源
     * @function destroy
     */
    destroy() {
        // 停止自动刷新
        if (this.flushTimer) {
            clearInterval(this.flushTimer);
            this.flushTimer = null;
        }
        
        // 最后一次刷新
        this._flushLogs();
        
        // 清理数据
        this.logs = [];
        this.performanceMarks.clear();
        this.debugCounters.clear();
        
        this.info('Logger', 'destroy', '日志管理器已销毁');
    }
    
    /**
     * 创建调试面板
     * @function _createDebugPanel
     * @private
     */
    _createDebugPanel() {
        // 动态导入调试面板模块以避免循环依赖
        import('./debug-panel.js').then(({ createDebugPanel }) => {
            this.debugPanel = createDebugPanel({
                theme: 'dark',
                position: 'bottom-right',
                width: 900,
                height: 500,
                enableDebugPanel: true
            });
            
            this.debug('Logger', '_createDebugPanel', '调试面板创建完成');
        }).catch(error => {
            console.warn('⚠️ 调试面板创建失败:', error);
        });
    }
    
    /**
     * 更新调试面板
     * @function _updateDebugPanel
     * @param {Object} logEntry - 日志条目
     * @private
     */
    _updateDebugPanel(logEntry) {
        if (this.debugPanel && this.debugPanel.isVisible) {
            // 调试面板有自己的自动更新机制，这里只做通知
            this.debugPanel._updateContent();
        }
    }
}
// #endregion

// #region 全局日志实例
/**
 * 全局日志实例
 * @type {Logger}
 */
let globalLogger = null;

/**
 * 创建全局日志实例
 * @function createLogger
 * @param {Object} config - 日志配置
 * @returns {Logger} 日志管理器实例
 */
export function createLogger(config = {}) {
    globalLogger = new Logger(config);
    return globalLogger;
}

/**
 * 获取全局日志实例
 * @function getLogger
 * @returns {Logger} 日志管理器实例
 */
export function getLogger() {
    if (!globalLogger) {
        globalLogger = new Logger();
    }
    return globalLogger;
}

/**
 * 便捷的日志记录函数
 * @function logInfo
 * @param {string} module - 模块名
 * @param {string} functionName - 函数名  
 * @param {string} message - 日志消息
 * @param {*} data - 附加数据
 */
export function logInfo(module, functionName, message, data = null) {
    getLogger().info(module, functionName, message, data);
}

/**
 * 便捷的调试日志函数
 * @function logDebug
 * @param {string} module - 模块名
 * @param {string} functionName - 函数名
 * @param {string} message - 日志消息
 * @param {*} data - 附加数据
 */
export function logDebug(module, functionName, message, data = null) {
    getLogger().debug(module, functionName, message, data);
}

/**
 * 便捷的错误日志函数
 * @function logError
 * @param {string} module - 模块名
 * @param {string} functionName - 函数名
 * @param {string} message - 日志消息
 * @param {*} data - 附加数据
 */
export function logError(module, functionName, message, data = null) {
    getLogger().error(module, functionName, message, data);
}
// #endregion 