/**
 * @file 调试面板组件
 * @description 提供可视化的日志查看、性能监控和系统状态面板
 */

// #region 导入依赖
import { getLogger, LogLevel } from './logger.js';
// #endregion

// #region DebugPanel 主类
/**
 * @class DebugPanel - 调试面板管理器
 * @description 提供可视化的调试信息展示和控制功能
 */
export class DebugPanel {
    /**
     * 构造函数 - 初始化调试面板
     * @param {Object} config - 调试面板配置
     */
    constructor(config = {}) {
        this.logger = getLogger();
        this.logger.debug('DebugPanel', 'constructor', '开始初始化调试面板');
        
        this.config = {
            position: 'bottom-right',     // 面板位置：'top-left', 'top-right', 'bottom-left', 'bottom-right'
            width: 800,                   // 面板宽度
            height: 400,                  // 面板高度
            maxLogs: 100,                // 最大日志显示数量
            autoScroll: true,            // 自动滚动到底部
            showTimestamp: true,         // 显示时间戳
            showLevel: true,             // 显示日志级别
            showModule: true,            // 显示模块名
            theme: 'dark',               // 主题：'light', 'dark'
            collapsible: true,           // 是否可折叠
            draggable: true,             // 是否可拖拽
            resizable: true,             // 是否可调整大小
            
            // 过滤器配置
            filters: {
                levels: [LogLevel.DEBUG, LogLevel.INFO, LogLevel.WARN, LogLevel.ERROR],
                modules: [],
                search: ''
            },
            
            // 用户配置覆盖
            ...config
        };
        
        // 内部状态
        this.isVisible = false;
        this.isCollapsed = false;
        this.isInitialized = false;
        this.updateTimer = null;
        this.logCache = [];
        
        // DOM元素引用
        this.panel = null;
        this.logContainer = null;
        this.statsContainer = null;
        this.controlsContainer = null;
        
        this.logger.debug('DebugPanel', 'constructor', '调试面板配置完成', {
            config: this.config
        });
    }
    
    /**
     * 初始化调试面板
     * @function initialize
     */
    initialize() {
        if (this.isInitialized) {
            this.logger.warn('DebugPanel', 'initialize', '调试面板已经初始化');
            return;
        }
        
        this.logger.startPerformanceMark('debug_panel_init', 'DebugPanel', 'initialize');
        this.logger.info('DebugPanel', 'initialize', '开始初始化调试面板');
        
        try {
            // 创建面板DOM结构
            this._createPanelStructure();
            
            // 设置事件监听器
            this._setupEventListeners();
            
            // 启动自动更新
            this._startAutoUpdate();
            
            // 设置键盘快捷键
            this._setupKeyboardShortcuts();
            
            this.isInitialized = true;
            
            const initDuration = this.logger.endPerformanceMark('debug_panel_init', 'DebugPanel', 'initialize');
            this.logger.info('DebugPanel', 'initialize', '调试面板初始化完成', {
                duration: `${initDuration?.toFixed(2)}ms`
            });
            
        } catch (error) {
            this.logger.error('DebugPanel', 'initialize', '调试面板初始化失败', {
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }
    
    /**
     * 创建面板DOM结构
     * @function _createPanelStructure
     * @private
     */
    _createPanelStructure() {
        this.logger.debug('DebugPanel', '_createPanelStructure', '创建调试面板DOM结构');
        
        // 创建主面板容器
        this.panel = document.createElement('div');
        this.panel.id = 'smartoffice-debug-panel';
        this.panel.className = `debug-panel debug-panel-${this.config.theme} debug-panel-${this.config.position}`;
        
        // 设置面板样式
        this._applyPanelStyles();
        
        // 创建面板内容
        this.panel.innerHTML = this._generatePanelHTML();
        
        // 获取关键元素引用
        this.logContainer = this.panel.querySelector('.debug-logs');
        this.statsContainer = this.panel.querySelector('.debug-stats');
        this.controlsContainer = this.panel.querySelector('.debug-controls');
        
        // 添加到文档
        document.body.appendChild(this.panel);
        
        this.logger.trace('DebugPanel', '_createPanelStructure', '面板DOM结构创建完成');
    }
    
    /**
     * 应用面板样式
     * @function _applyPanelStyles
     * @private
     */
    _applyPanelStyles() {
        const styles = {
            position: 'fixed',
            width: `${this.config.width}px`,
            height: `${this.config.height}px`,
            zIndex: '9999',
            backgroundColor: this.config.theme === 'dark' ? '#1a1a1a' : '#ffffff',
            border: this.config.theme === 'dark' ? '1px solid #333' : '1px solid #ccc',
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
            fontFamily: 'Consolas, Monaco, "Courier New", monospace',
            fontSize: '12px',
            display: 'none', // 初始隐藏
            overflow: 'hidden'
        };
        
        // 设置位置
        const position = this.config.position.split('-');
        if (position[0] === 'top') {
            styles.top = '20px';
        } else {
            styles.bottom = '20px';
        }
        
        if (position[1] === 'left') {
            styles.left = '20px';
        } else {
            styles.right = '20px';
        }
        
        // 应用样式
        Object.assign(this.panel.style, styles);
    }
    
    /**
     * 生成面板HTML内容
     * @function _generatePanelHTML
     * @returns {string} HTML内容
     * @private
     */
    _generatePanelHTML() {
        const textColor = this.config.theme === 'dark' ? '#ffffff' : '#333333';
        const bgColor = this.config.theme === 'dark' ? '#2a2a2a' : '#f5f5f5';
        
        return `
            <div class="debug-panel-header" style="
                padding: 8px 12px; 
                background: ${bgColor}; 
                border-bottom: 1px solid ${this.config.theme === 'dark' ? '#444' : '#ddd'};
                display: flex;
                justify-content: space-between;
                align-items: center;
                cursor: move;
                user-select: none;
            ">
                <span style="color: ${textColor}; font-weight: bold;">📊 SmartOffice Debug Panel</span>
                <div class="debug-panel-buttons">
                    <button class="btn-minimize" style="margin-right: 8px;">➖</button>
                    <button class="btn-close">❌</button>
                </div>
            </div>
            
            <div class="debug-panel-content" style="height: calc(100% - 40px); display: flex; flex-direction: column;">
                <!-- 控制栏 -->
                <div class="debug-controls" style="
                    padding: 8px 12px; 
                    background: ${bgColor}; 
                    border-bottom: 1px solid ${this.config.theme === 'dark' ? '#444' : '#ddd'};
                ">
                    <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
                        <!-- 日志级别过滤 -->
                        <div style="display: flex; gap: 5px;">
                            <label style="color: ${textColor}; font-size: 11px;">Level:</label>
                            <select class="filter-level" style="font-size: 11px; padding: 2px;">
                                <option value="all">All</option>
                                <option value="TRACE">TRACE</option>
                                <option value="DEBUG">DEBUG</option>
                                <option value="INFO">INFO</option>
                                <option value="WARN">WARN</option>
                                <option value="ERROR">ERROR</option>
                                <option value="FATAL">FATAL</option>
                            </select>
                        </div>
                        
                        <!-- 模块过滤 -->
                        <div style="display: flex; gap: 5px;">
                            <label style="color: ${textColor}; font-size: 11px;">Module:</label>
                            <input type="text" class="filter-module" placeholder="Module name" style="font-size: 11px; padding: 2px; width: 100px;">
                        </div>
                        
                        <!-- 搜索 -->
                        <div style="display: flex; gap: 5px;">
                            <label style="color: ${textColor}; font-size: 11px;">Search:</label>
                            <input type="text" class="filter-search" placeholder="Search logs..." style="font-size: 11px; padding: 2px; width: 120px;">
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div style="display: flex; gap: 5px; margin-left: auto;">
                            <button class="btn-clear" style="font-size: 11px; padding: 2px 6px;">Clear</button>
                            <button class="btn-export" style="font-size: 11px; padding: 2px 6px;">Export</button>
                            <button class="btn-pause" style="font-size: 11px; padding: 2px 6px;">Pause</button>
                        </div>
                    </div>
                </div>
                
                <!-- 标签页 -->
                <div class="debug-tabs" style="
                    display: flex; 
                    background: ${bgColor}; 
                    border-bottom: 1px solid ${this.config.theme === 'dark' ? '#444' : '#ddd'};
                ">
                    <button class="tab-button active" data-tab="logs" style="
                        padding: 6px 12px; 
                        border: none; 
                        background: ${this.config.theme === 'dark' ? '#333' : '#e0e0e0'}; 
                        color: ${textColor}; 
                        cursor: pointer;
                        font-size: 11px;
                    ">📄 Logs</button>
                    <button class="tab-button" data-tab="performance" style="
                        padding: 6px 12px; 
                        border: none; 
                        background: transparent; 
                        color: ${textColor}; 
                        cursor: pointer;
                        font-size: 11px;
                    ">⚡ Performance</button>
                    <button class="tab-button" data-tab="stats" style="
                        padding: 6px 12px; 
                        border: none; 
                        background: transparent; 
                        color: ${textColor}; 
                        cursor: pointer;
                        font-size: 11px;
                    ">📊 Stats</button>
                </div>
                
                <!-- 内容区域 -->
                <div class="debug-content-area" style="flex: 1; overflow: hidden;">
                    <!-- 日志面板 -->
                    <div class="tab-content" data-tab="logs" style="height: 100%; display: block;">
                        <div class="debug-logs" style="
                            height: 100%; 
                            overflow-y: auto; 
                            padding: 8px; 
                            background: ${this.config.theme === 'dark' ? '#1e1e1e' : '#fafafa'};
                            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                            font-size: 11px;
                            line-height: 1.4;
                        "></div>
                    </div>
                    
                    <!-- 性能面板 -->
                    <div class="tab-content" data-tab="performance" style="height: 100%; display: none; padding: 8px;">
                        <div class="debug-performance" style="color: ${textColor};">
                            <h4 style="margin: 0 0 10px 0;">Performance Metrics</h4>
                            <div class="performance-metrics"></div>
                        </div>
                    </div>
                    
                    <!-- 统计面板 -->
                    <div class="tab-content" data-tab="stats" style="height: 100%; display: none; padding: 8px;">
                        <div class="debug-stats" style="color: ${textColor};">
                            <h4 style="margin: 0 0 10px 0;">System Statistics</h4>
                            <div class="stats-content"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * 设置事件监听器
     * @function _setupEventListeners
     * @private
     */
    _setupEventListeners() {
        this.logger.debug('DebugPanel', '_setupEventListeners', '设置调试面板事件监听器');
        
        // 关闭按钮
        this.panel.querySelector('.btn-close').addEventListener('click', () => {
            this.hide();
        });
        
        // 最小化按钮
        this.panel.querySelector('.btn-minimize').addEventListener('click', () => {
            this.toggle();
        });
        
        // 清空日志按钮
        this.panel.querySelector('.btn-clear').addEventListener('click', () => {
            this.clearLogs();
        });
        
        // 导出日志按钮
        this.panel.querySelector('.btn-export').addEventListener('click', () => {
            this.exportLogs();
        });
        
        // 暂停/恢复按钮
        this.panel.querySelector('.btn-pause').addEventListener('click', () => {
            this.toggleAutoUpdate();
        });
        
        // 过滤器事件
        this._setupFilterEvents();
        
        // 标签页切换
        this._setupTabEvents();
        
        // 拖拽功能
        if (this.config.draggable) {
            this._setupDragEvents();
        }
        
        this.logger.trace('DebugPanel', '_setupEventListeners', '事件监听器设置完成');
    }
    
    /**
     * 设置过滤器事件
     * @function _setupFilterEvents
     * @private
     */
    _setupFilterEvents() {
        const levelFilter = this.panel.querySelector('.filter-level');
        const moduleFilter = this.panel.querySelector('.filter-module');
        const searchFilter = this.panel.querySelector('.filter-search');
        
        levelFilter.addEventListener('change', () => {
            this.config.filters.levels = levelFilter.value === 'all' ? 
                [LogLevel.TRACE, LogLevel.DEBUG, LogLevel.INFO, LogLevel.WARN, LogLevel.ERROR, LogLevel.FATAL] :
                [levelFilter.value];
            this._refreshLogs();
        });
        
        moduleFilter.addEventListener('input', () => {
            this.config.filters.modules = moduleFilter.value ? [moduleFilter.value] : [];
            this._refreshLogs();
        });
        
        searchFilter.addEventListener('input', () => {
            this.config.filters.search = searchFilter.value;
            this._refreshLogs();
        });
    }
    
    /**
     * 设置标签页事件
     * @function _setupTabEvents
     * @private
     */
    _setupTabEvents() {
        const tabButtons = this.panel.querySelectorAll('.tab-button');
        const tabContents = this.panel.querySelectorAll('.tab-content');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabName = button.dataset.tab;
                
                // 更新按钮状态
                tabButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                
                // 更新内容显示
                tabContents.forEach(content => {
                    content.style.display = content.dataset.tab === tabName ? 'block' : 'none';
                });
                
                // 刷新对应标签页内容
                this._refreshTabContent(tabName);
            });
        });
    }
    
    /**
     * 设置拖拽事件
     * @function _setupDragEvents
     * @private
     */
    _setupDragEvents() {
        const header = this.panel.querySelector('.debug-panel-header');
        let isDragging = false;
        let startX, startY, startLeft, startTop;
        
        header.addEventListener('mousedown', (e) => {
            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;
            startLeft = parseInt(this.panel.style.left || 0);
            startTop = parseInt(this.panel.style.top || 0);
            
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
        });
        
        const handleMouseMove = (e) => {
            if (!isDragging) return;
            
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            
            this.panel.style.left = `${startLeft + deltaX}px`;
            this.panel.style.top = `${startTop + deltaY}px`;
            this.panel.style.right = 'auto';
            this.panel.style.bottom = 'auto';
        };
        
        const handleMouseUp = () => {
            isDragging = false;
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };
    }
    
    /**
     * 启动自动更新
     * @function _startAutoUpdate
     * @private
     */
    _startAutoUpdate() {
        this.updateTimer = setInterval(() => {
            if (this.isVisible && !this.isCollapsed) {
                this._updateContent();
            }
        }, 1000); // 每秒更新一次
    }
    
    /**
     * 更新内容
     * @function _updateContent
     * @private
     */
    _updateContent() {
        // 获取最新日志
        const logs = this.logger.getLogs();
        const newLogs = logs.slice(this.logCache.length);
        
        if (newLogs.length > 0) {
            this.logCache = logs;
            this._appendLogs(newLogs);
        }
    }
    
    /**
     * 显示调试面板
     * @function show
     */
    show() {
        if (!this.isInitialized) {
            this.initialize();
        }
        
        this.panel.style.display = 'block';
        this.isVisible = true;
        this.isCollapsed = false;
        
        // 刷新内容
        this._refreshLogs();
        
        this.logger.debug('DebugPanel', 'show', '调试面板已显示');
    }
    
    /**
     * 隐藏调试面板
     * @function hide
     */
    hide() {
        this.panel.style.display = 'none';
        this.isVisible = false;
        
        this.logger.debug('DebugPanel', 'hide', '调试面板已隐藏');
    }
    
    /**
     * 切换调试面板显示状态
     * @function toggle
     */
    toggle() {
        if (this.isVisible) {
            if (this.isCollapsed) {
                this._expand();
            } else {
                this._collapse();
            }
        } else {
            this.show();
        }
    }
    
    /**
     * 折叠面板
     * @function _collapse
     * @private
     */
    _collapse() {
        const content = this.panel.querySelector('.debug-panel-content');
        content.style.display = 'none';
        this.panel.style.height = '40px';
        this.isCollapsed = true;
        
        this.logger.debug('DebugPanel', '_collapse', '调试面板已折叠');
    }
    
    /**
     * 展开面板
     * @function _expand
     * @private
     */
    _expand() {
        const content = this.panel.querySelector('.debug-panel-content');
        content.style.display = 'flex';
        this.panel.style.height = `${this.config.height}px`;
        this.isCollapsed = false;
        
        this.logger.debug('DebugPanel', '_expand', '调试面板已展开');
    }
    
    /**
     * 清空日志
     * @function clearLogs
     */
    clearLogs() {
        this.logContainer.innerHTML = '';
        this.logCache = [];
        this.logger.debug('DebugPanel', 'clearLogs', '日志已清空');
    }
    
    /**
     * 导出日志
     * @function exportLogs
     */
    exportLogs() {
        const logs = this.logger.exportLogs('text');
        const blob = new Blob([logs], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `smartoffice-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
        a.click();
        
        URL.revokeObjectURL(url);
        
        this.logger.info('DebugPanel', 'exportLogs', '日志已导出');
    }
    
    /**
     * 切换自动更新
     * @function toggleAutoUpdate
     */
    toggleAutoUpdate() {
        const pauseBtn = this.panel.querySelector('.btn-pause');
        
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
            this.updateTimer = null;
            pauseBtn.textContent = 'Resume';
            this.logger.debug('DebugPanel', 'toggleAutoUpdate', '自动更新已暂停');
        } else {
            this._startAutoUpdate();
            pauseBtn.textContent = 'Pause';
            this.logger.debug('DebugPanel', 'toggleAutoUpdate', '自动更新已恢复');
        }
    }
    
    /**
     * 刷新日志显示
     * @function _refreshLogs
     * @private
     */
    _refreshLogs() {
        this.logContainer.innerHTML = '';
        const logs = this._getFilteredLogs();
        this._renderLogs(logs);
    }
    
    /**
     * 获取过滤后的日志
     * @function _getFilteredLogs
     * @returns {Array} 过滤后的日志列表
     * @private
     */
    _getFilteredLogs() {
        let logs = this.logger.getLogs();
        
        // 按级别过滤
        if (this.config.filters.levels.length > 0) {
            logs = logs.filter(log => this.config.filters.levels.includes(log.level));
        }
        
        // 按模块过滤
        if (this.config.filters.modules.length > 0) {
            logs = logs.filter(log => 
                this.config.filters.modules.some(module => 
                    log.module.toLowerCase().includes(module.toLowerCase())
                )
            );
        }
        
        // 按搜索词过滤
        if (this.config.filters.search) {
            const search = this.config.filters.search.toLowerCase();
            logs = logs.filter(log => 
                log.message.toLowerCase().includes(search) ||
                log.module.toLowerCase().includes(search) ||
                (log.function && log.function.toLowerCase().includes(search))
            );
        }
        
        return logs.slice(-this.config.maxLogs);
    }
    
    /**
     * 渲染日志列表
     * @function _renderLogs
     * @param {Array} logs - 日志列表
     * @private
     */
    _renderLogs(logs) {
        const fragment = document.createDocumentFragment();
        
        logs.forEach(log => {
            const logElement = this._createLogElement(log);
            fragment.appendChild(logElement);
        });
        
        this.logContainer.appendChild(fragment);
        
        // 自动滚动到底部
        if (this.config.autoScroll) {
            this.logContainer.scrollTop = this.logContainer.scrollHeight;
        }
    }
    
    /**
     * 创建日志元素
     * @function _createLogElement
     * @param {Object} log - 日志对象
     * @returns {HTMLElement} 日志元素
     * @private
     */
    _createLogElement(log) {
        const div = document.createElement('div');
        div.className = `log-entry log-${log.level.toLowerCase()}`;
        
        const colors = {
            TRACE: '#888888',
            DEBUG: '#00BCD4',
            INFO: '#2196F3',
            WARN: '#FF9800',
            ERROR: '#F44336',
            FATAL: '#9C27B0'
        };
        
        const timestamp = new Date(log.timestamp).toLocaleTimeString();
        const color = colors[log.level] || '#666666';
        
        let content = '';
        
        if (this.config.showTimestamp) {
            content += `<span style="color: #666; margin-right: 8px;">[${timestamp}]</span>`;
        }
        
        if (this.config.showLevel) {
            content += `<span style="color: ${color}; font-weight: bold; margin-right: 8px;">${log.level}</span>`;
        }
        
        if (this.config.showModule) {
            content += `<span style="color: #4CAF50; margin-right: 8px;">${log.module}`;
            if (log.function) {
                content += `.${log.function}()`;
            }
            content += `</span>`;
        }
        
        content += `<span style="color: ${this.config.theme === 'dark' ? '#fff' : '#333'};">${log.message}</span>`;
        
        if (log.data) {
            content += `<div style="margin-left: 20px; margin-top: 4px; color: #666; font-size: 10px; white-space: pre-wrap;">${JSON.stringify(log.data, null, 2)}</div>`;
        }
        
        div.innerHTML = content;
        div.style.cssText = `
            padding: 4px 8px;
            border-bottom: 1px solid ${this.config.theme === 'dark' ? '#333' : '#eee'};
            white-space: pre-wrap;
            word-break: break-word;
        `;
        
        return div;
    }
    
    /**
     * 追加新日志
     * @function _appendLogs
     * @param {Array} newLogs - 新日志列表
     * @private
     */
    _appendLogs(newLogs) {
        const filteredLogs = newLogs.filter(log => {
            // 应用当前过滤器
            return this.config.filters.levels.includes(log.level) &&
                   (this.config.filters.modules.length === 0 || 
                    this.config.filters.modules.some(module => 
                        log.module.toLowerCase().includes(module.toLowerCase())
                    )) &&
                   (!this.config.filters.search || 
                    log.message.toLowerCase().includes(this.config.filters.search.toLowerCase()));
        });
        
        if (filteredLogs.length > 0) {
            this._renderLogs(filteredLogs);
        }
    }
    
    /**
     * 刷新标签页内容
     * @function _refreshTabContent
     * @param {string} tabName - 标签页名称
     * @private
     */
    _refreshTabContent(tabName) {
        switch (tabName) {
            case 'performance':
                this._updatePerformanceTab();
                break;
            case 'stats':
                this._updateStatsTab();
                break;
        }
    }
    
    /**
     * 更新性能标签页
     * @function _updatePerformanceTab
     * @private
     */
    _updatePerformanceTab() {
        const container = this.panel.querySelector('.performance-metrics');
        const marks = this.logger.performanceMarks;
        
        let html = '<div style="font-family: monospace; font-size: 11px;">';
        
        if (marks.size === 0) {
            html += '<p>No performance marks recorded.</p>';
        } else {
            marks.forEach((mark, name) => {
                html += `
                    <div style="margin-bottom: 8px; padding: 4px; background: rgba(0,188,212,0.1); border-radius: 4px;">
                        <strong>${name}</strong><br>
                        Module: ${mark.module}<br>
                        Function: ${mark.function}<br>
                        Start Time: ${mark.startTime.toFixed(2)}ms<br>
                        Duration: Running...
                    </div>
                `;
            });
        }
        
        html += '</div>';
        container.innerHTML = html;
    }
    
    /**
     * 更新统计标签页
     * @function _updateStatsTab
     * @private
     */
    _updateStatsTab() {
        const container = this.panel.querySelector('.stats-content');
        const counters = this.logger.debugCounters;
        
        let html = '<div style="font-family: monospace; font-size: 11px;">';
        
        // 日志统计
        const logStats = this._getLogStats();
        html += '<h5 style="margin: 0 0 8px 0;">Log Statistics:</h5>';
        Object.entries(logStats).forEach(([level, count]) => {
            html += `<div>${level}: ${count}</div>`;
        });
        
        html += '<h5 style="margin: 16px 0 8px 0;">Debug Counters:</h5>';
        if (counters.size === 0) {
            html += '<p>No counters recorded.</p>';
        } else {
            counters.forEach((count, name) => {
                html += `<div>${name}: ${count}</div>`;
            });
        }
        
        html += '</div>';
        container.innerHTML = html;
    }
    
    /**
     * 获取日志统计
     * @function _getLogStats
     * @returns {Object} 日志统计对象
     * @private
     */
    _getLogStats() {
        const logs = this.logger.getLogs();
        const stats = {};
        
        Object.values(LogLevel).forEach(level => {
            stats[level] = logs.filter(log => log.level === level).length;
        });
        
        return stats;
    }
    
    /**
     * 设置键盘快捷键
     * @function _setupKeyboardShortcuts
     * @private
     */
    _setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Shift + D: 切换调试面板
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'D') {
                e.preventDefault();
                if (this.isVisible) {
                    this.hide();
                } else {
                    this.show();
                }
            }
        });
    }
    
    /**
     * 销毁调试面板
     * @function destroy
     */
    destroy() {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
        }
        
        if (this.panel && this.panel.parentElement) {
            this.panel.remove();
        }
        
        this.isInitialized = false;
        this.logger.info('DebugPanel', 'destroy', '调试面板已销毁');
    }
}
// #endregion

// #region 全局调试面板实例
/**
 * 全局调试面板实例
 * @type {DebugPanel}
 */
let globalDebugPanel = null;

/**
 * 创建调试面板
 * @function createDebugPanel
 * @param {Object} config - 调试面板配置
 * @returns {DebugPanel} 调试面板实例
 */
export function createDebugPanel(config = {}) {
    globalDebugPanel = new DebugPanel(config);
    return globalDebugPanel;
}

/**
 * 获取全局调试面板实例
 * @function getDebugPanel
 * @returns {DebugPanel} 调试面板实例
 */
export function getDebugPanel() {
    if (!globalDebugPanel) {
        globalDebugPanel = new DebugPanel();
    }
    return globalDebugPanel;
}

/**
 * 显示调试面板
 * @function showDebugPanel
 */
export function showDebugPanel() {
    getDebugPanel().show();
}

/**
 * 隐藏调试面板
 * @function hideDebugPanel
 */
export function hideDebugPanel() {
    getDebugPanel().hide();
}

/**
 * 切换调试面板显示状态
 * @function toggleDebugPanel
 */
export function toggleDebugPanel() {
    getDebugPanel().toggle();
}
// #endregion 