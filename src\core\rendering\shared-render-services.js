/**
 * SmartOffice 2.0 共享渲染服务
 * 提取渲染器间的通用功能，减少代码重复
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-12-19
 */

import { DocumentModel } from './document-model.js';
import { StyleManager } from './style-manager.js';
import { PositionManager } from './position-manager.js';

/**
 * 共享样式处理服务
 * 统一处理各种格式的样式编译和优化
 */
export class SharedStyleService {
    constructor(styleManager) {
        this.styleManager = styleManager;
    }

    /**
     * 通用样式处理方法
     * @param {DocumentModel} document - 文档模型
     * @param {Object} options - 处理选项
     * @param {string} format - 目标格式 (html, pdf, print)
     * @returns {Promise<Object>} 处理后的样式
     */
    async processStyles(document, options = {}, format = 'html') {
        if (!this.styleManager) {
            console.warn(`[SharedStyleService] 样式管理器未设置，使用${format}默认样式`);
            return this._getDefaultStyles(format);
        }

        try {
            // 获取文档组件
            const components = document.getComponents ? document.getComponents() : [];
            
            // 编译基础样式
            const baseStyles = await this.styleManager.compileStyles(
                options.theme || 'default',
                components,
                format
            );

            // 根据格式添加特定样式
            const formatSpecificStyles = this._getFormatSpecificStyles(format, options);

            return {
                ...baseStyles,
                [format]: formatSpecificStyles,
                format: format,
                optimized: true
            };
        } catch (error) {
            console.error(`[SharedStyleService] ${format}样式处理失败:`, error);
            return this._getDefaultStyles(format);
        }
    }

    /**
     * 获取格式特定样式
     * @param {string} format - 格式类型
     * @param {Object} options - 选项
     * @returns {Object} 格式特定样式
     * @private
     */
    _getFormatSpecificStyles(format, options) {
        switch (format) {
            case 'pdf':
                return {
                    pageSize: options.format || 'A4',
                    orientation: options.orientation || 'portrait',
                    printBackground: options.printBackground !== false,
                    compression: options.compression !== false
                };
            case 'print':
                return {
                    printOptimized: true,
                    removeBackground: options.removeBackground || false,
                    enablePageBreaks: options.enablePageBreaks !== false,
                    scaleToFit: options.scaleToFit !== false
                };
            case 'html':
            default:
                return {
                    responsive: options.compatibility?.mobile || false,
                    printOptimized: options.compatibility?.print || false,
                    minifyCSS: options.minifyCSS !== false
                };
        }
    }

    /**
     * 获取默认样式
     * @param {string} format - 格式类型
     * @returns {Object} 默认样式
     * @private
     */
    _getDefaultStyles(format) {
        const baseStyles = {
            css: 'body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }',
            format: format
        };

        return {
            ...baseStyles,
            [format]: this._getFormatSpecificStyles(format, {})
        };
    }
}

/**
 * 共享布局处理服务
 * 统一处理各种格式的布局计算和优化
 */
export class SharedLayoutService {
    constructor() {
        this.layoutCache = new Map();
    }

    /**
     * 通用布局处理方法
     * @param {DocumentModel} document - 文档模型
     * @param {Object} options - 处理选项
     * @param {string} format - 目标格式
     * @returns {Promise<Object>} 处理后的布局
     */
    async processLayout(document, options = {}, format = 'html') {
        const cacheKey = this._generateCacheKey(document, options, format);
        
        if (this.layoutCache.has(cacheKey)) {
            return this.layoutCache.get(cacheKey);
        }

        try {
            // 计算基础布局
            const baseLayout = await document.calculateLayout();
            
            // 添加格式特定布局
            const formatLayout = this._getFormatSpecificLayout(format, options);
            
            const result = {
                ...baseLayout,
                ...formatLayout,
                format: format,
                timestamp: Date.now()
            };

            // 缓存结果
            this.layoutCache.set(cacheKey, result);
            
            return result;
        } catch (error) {
            console.error(`[SharedLayoutService] ${format}布局处理失败:`, error);
            throw error;
        }
    }

    /**
     * 获取格式特定布局
     * @param {string} format - 格式类型
     * @param {Object} options - 选项
     * @returns {Object} 格式特定布局
     * @private
     */
    _getFormatSpecificLayout(format, options) {
        switch (format) {
            case 'pdf':
                return {
                    pageSize: options.format || 'A4',
                    orientation: options.orientation || 'portrait',
                    margins: options.margin || { top: '20mm', right: '20mm', bottom: '20mm', left: '20mm' },
                    dpi: options.dpi || 300,
                    dimensions: this._calculatePageDimensions(options)
                };
            case 'print':
                return {
                    pageSize: options.pageSize || 'A4',
                    orientation: options.orientation || 'portrait',
                    margins: options.margins || '20mm',
                    printOptimized: true,
                    enablePageBreaks: options.enablePageBreaks !== false
                };
            case 'html':
            default:
                return {
                    type: 'html',
                    responsive: options.compatibility?.mobile || false,
                    printOptimized: options.compatibility?.print || false
                };
        }
    }

    /**
     * 计算页面尺寸
     * @param {Object} options - 选项
     * @returns {Object} 页面尺寸
     * @private
     */
    _calculatePageDimensions(options) {
        const format = options.format || options.pageSize || 'A4';
        const orientation = options.orientation || 'portrait';
        
        // 标准页面尺寸 (mm)
        const pageSizes = {
            'A4': { width: 210, height: 297 },
            'A3': { width: 297, height: 420 },
            'Letter': { width: 216, height: 279 },
            'Legal': { width: 216, height: 356 }
        };
        
        let dimensions = pageSizes[format] || pageSizes['A4'];
        
        if (orientation === 'landscape') {
            dimensions = { width: dimensions.height, height: dimensions.width };
        }
        
        return dimensions;
    }

    /**
     * 生成缓存键
     * @param {DocumentModel} document - 文档模型
     * @param {Object} options - 选项
     * @param {string} format - 格式
     * @returns {string} 缓存键
     * @private
     */
    _generateCacheKey(document, options, format) {
        const docId = document.id || 'unknown';
        const optionsHash = JSON.stringify(options);
        return `${docId}-${format}-${btoa(optionsHash).slice(0, 8)}`;
    }

    /**
     * 清除布局缓存
     */
    clearCache() {
        this.layoutCache.clear();
    }
}

/**
 * 共享位置处理服务
 * 统一处理各种格式的位置计算和管理
 */
export class SharedPositionService {
    constructor(positionManager) {
        this.positionManager = positionManager;
    }

    /**
     * 通用位置处理方法
     * @param {DocumentModel} document - 文档模型
     * @param {Object} layout - 布局信息
     * @param {Object} options - 处理选项
     * @param {string} format - 目标格式
     * @returns {Promise<Object>} 处理后的位置
     */
    async processPositions(document, layout, options = {}, format = 'html') {
        if (!this.positionManager) {
            console.warn(`[SharedPositionService] 位置管理器未设置，使用${format}默认位置`);
            return this._getDefaultPositions(format);
        }

        try {
            // 计算基础位置
            const basePositions = await document.calculatePositions(layout);
            
            // 注册元素到位置管理器
            if (basePositions.elements) {
                for (const element of basePositions.elements) {
                    this.positionManager.registerElement(element.id, {
                        ...element,
                        format: format
                    });
                }
            }

            // 添加格式特定位置处理
            const formatPositions = this._getFormatSpecificPositions(format, options, document, layout);

            return {
                ...basePositions,
                ...formatPositions,
                format: format
            };
        } catch (error) {
            console.error(`[SharedPositionService] ${format}位置处理失败:`, error);
            return this._getDefaultPositions(format);
        }
    }

    /**
     * 获取格式特定位置处理
     * @param {string} format - 格式类型
     * @param {Object} options - 选项
     * @param {DocumentModel} document - 文档模型
     * @param {Object} layout - 布局信息
     * @returns {Object} 格式特定位置
     * @private
     */
    _getFormatSpecificPositions(format, options, document, layout) {
        switch (format) {
            case 'pdf':
                return {
                    pdfOptimized: true,
                    pageBreaks: this._calculatePDFPageBreaks(document, layout)
                };
            case 'print':
                return {
                    printOptimized: true,
                    pageBreaks: this._calculatePrintPageBreaks(document, options)
                };
            case 'html':
            default:
                return {
                    responsive: options.compatibility?.mobile || false
                };
        }
    }

    /**
     * 计算PDF分页
     * @param {DocumentModel} document - 文档模型
     * @param {Object} layout - 布局信息
     * @returns {Array} 分页信息
     * @private
     */
    _calculatePDFPageBreaks(document, layout) {
        // PDF分页逻辑
        return [];
    }

    /**
     * 计算打印分页
     * @param {DocumentModel} document - 文档模型
     * @param {Object} options - 选项
     * @returns {Array} 分页信息
     * @private
     */
    _calculatePrintPageBreaks(document, options) {
        // 打印分页逻辑
        return [];
    }

    /**
     * 获取默认位置
     * @param {string} format - 格式类型
     * @returns {Object} 默认位置
     * @private
     */
    _getDefaultPositions(format) {
        return {
            elements: [],
            format: format
        };
    }
}

/**
 * 渲染服务工厂
 * 统一创建和管理共享渲染服务
 */
export class RenderServiceFactory {
    constructor() {
        this.services = new Map();
    }

    /**
     * 获取样式服务
     * @param {StyleManager} styleManager - 样式管理器
     * @returns {SharedStyleService} 样式服务
     */
    getStyleService(styleManager) {
        if (!this.services.has('style')) {
            this.services.set('style', new SharedStyleService(styleManager));
        }
        return this.services.get('style');
    }

    /**
     * 获取布局服务
     * @returns {SharedLayoutService} 布局服务
     */
    getLayoutService() {
        if (!this.services.has('layout')) {
            this.services.set('layout', new SharedLayoutService());
        }
        return this.services.get('layout');
    }

    /**
     * 获取位置服务
     * @param {PositionManager} positionManager - 位置管理器
     * @returns {SharedPositionService} 位置服务
     */
    getPositionService(positionManager) {
        if (!this.services.has('position')) {
            this.services.set('position', new SharedPositionService(positionManager));
        }
        return this.services.get('position');
    }

    /**
     * 清除所有服务缓存
     */
    clearAllCaches() {
        const layoutService = this.services.get('layout');
        if (layoutService) {
            layoutService.clearCache();
        }
    }
}

// 导出单例工厂
export const renderServiceFactory = new RenderServiceFactory();