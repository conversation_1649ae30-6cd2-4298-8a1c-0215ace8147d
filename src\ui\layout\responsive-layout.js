/**
 * @file 响应式布局管理器 - SmartOffice 响应式布局系统
 * <AUTHOR> Team
 * @description 
 * 这个文件实现了响应式布局管理系统，提供：
 * - 断点管理和媒体查询
 * - 自适应布局调整
 * - 组件响应式行为
 * - 设备检测和适配
 * - 布局状态管理
 */

// #region 导入依赖模块
import { EventEmitter } from '../../core/events/event-emitter.js';
import { UIEvents } from '../../core/events/event-types.js';
import { getLogger } from '../../core/utils/logger.js';
// #endregion

// #region 常量定义
/**
 * 默认断点配置
 */
const DEFAULT_BREAKPOINTS = {
    xs: { min: 0, max: 575 },      // 超小屏幕
    sm: { min: 576, max: 767 },    // 小屏幕
    md: { min: 768, max: 991 },    // 中等屏幕
    lg: { min: 992, max: 1199 },   // 大屏幕
    xl: { min: 1200, max: 1399 },  // 超大屏幕
    xxl: { min: 1400, max: Infinity } // 极大屏幕
};

/**
 * 设备类型
 */
const DEVICE_TYPES = {
    MOBILE: 'mobile',
    TABLET: 'tablet',
    DESKTOP: 'desktop',
    TV: 'tv'
};

/**
 * 方向类型
 */
const ORIENTATIONS = {
    PORTRAIT: 'portrait',
    LANDSCAPE: 'landscape'
};
// #endregion

// #region ResponsiveLayout 响应式布局管理器类
/**
 * @class ResponsiveLayout - 响应式布局管理器
 * @description 管理应用程序的响应式布局系统
 */
export class ResponsiveLayout extends EventEmitter {
    /**
     * 构造函数 - 初始化响应式布局管理器
     * @param {Object} config - 布局配置
     */
    constructor(config = {}) {
        super();
        
        this.logger = getLogger();
        this.logger.debug('ResponsiveLayout', 'constructor', '初始化响应式布局管理器');
        
        // 配置
        this.config = {
            breakpoints: DEFAULT_BREAKPOINTS,
            enableTouch: true,
            enableOrientation: true,
            enableResize: true,
            debounceDelay: 150,
            containerSelector: '.responsive-container',
            gridColumns: 12,
            gridGutter: 16,
            ...config
        };
        
        // 当前状态
        this.currentBreakpoint = null;
        this.currentDeviceType = null;
        this.currentOrientation = null;
        this.currentViewport = {
            width: 0,
            height: 0
        };
        
        // 媒体查询
        this.mediaQueries = new Map();
        this.mediaQueryListeners = new Map();
        
        // 响应式组件
        this.responsiveComponents = new Map();
        
        // 布局容器
        this.containers = new Set();
        
        // 事件处理器
        this.resizeHandler = null;
        this.orientationHandler = null;
        
        // 初始化
        this._initializeMediaQueries();
        this._initializeEventListeners();
        this._detectInitialState();
        
        this.logger.info('ResponsiveLayout', 'constructor', '响应式布局管理器初始化完成', {
            config: this.config,
            currentBreakpoint: this.currentBreakpoint,
            currentDeviceType: this.currentDeviceType
        });
    }

    /**
     * 初始化媒体查询
     * @private
     */
    _initializeMediaQueries() {
        for (const [name, breakpoint] of Object.entries(this.config.breakpoints)) {
            let query;
            
            if (breakpoint.max === Infinity) {
                query = `(min-width: ${breakpoint.min}px)`;
            } else {
                query = `(min-width: ${breakpoint.min}px) and (max-width: ${breakpoint.max}px)`;
            }
            
            const mediaQuery = window.matchMedia(query);
            this.mediaQueries.set(name, mediaQuery);
            
            // 添加监听器
            const listener = (e) => {
                if (e.matches) {
                    this._handleBreakpointChange(name);
                }
            };
            
            mediaQuery.addEventListener('change', listener);
            this.mediaQueryListeners.set(name, listener);
            
            this.logger.debug('ResponsiveLayout', '_initializeMediaQueries', 
                `创建媒体查询: ${name} - ${query}`);
        }
    }

    /**
     * 初始化事件监听器
     * @private
     */
    _initializeEventListeners() {
        // 窗口大小变化
        if (this.config.enableResize) {
            this.resizeHandler = this._debounce(() => {
                this._handleResize();
            }, this.config.debounceDelay);
            
            window.addEventListener('resize', this.resizeHandler);
        }
        
        // 设备方向变化
        if (this.config.enableOrientation) {
            this.orientationHandler = () => {
                this._handleOrientationChange();
            };
            
            window.addEventListener('orientationchange', this.orientationHandler);
            screen.orientation?.addEventListener('change', this.orientationHandler);
        }
    }

    /**
     * 检测初始状态
     * @private
     */
    _detectInitialState() {
        // 检测当前断点
        this._detectCurrentBreakpoint();
        
        // 检测设备类型
        this._detectDeviceType();
        
        // 检测方向
        this._detectOrientation();
        
        // 更新视口信息
        this._updateViewport();
    }

    /**
     * 检测当前断点
     * @private
     */
    _detectCurrentBreakpoint() {
        for (const [name, mediaQuery] of this.mediaQueries) {
            if (mediaQuery.matches) {
                this.currentBreakpoint = name;
                break;
            }
        }
    }

    /**
     * 检测设备类型
     * @private
     */
    _detectDeviceType() {
        const width = window.innerWidth;
        const userAgent = navigator.userAgent.toLowerCase();
        
        // 基于用户代理检测
        if (/mobile|android|iphone|ipod|blackberry|iemobile|opera mini/i.test(userAgent)) {
            this.currentDeviceType = DEVICE_TYPES.MOBILE;
        } else if (/tablet|ipad/i.test(userAgent)) {
            this.currentDeviceType = DEVICE_TYPES.TABLET;
        } else if (width >= 1920) {
            this.currentDeviceType = DEVICE_TYPES.TV;
        } else {
            this.currentDeviceType = DEVICE_TYPES.DESKTOP;
        }
        
        // 基于屏幕尺寸进一步判断
        if (width <= 767) {
            this.currentDeviceType = DEVICE_TYPES.MOBILE;
        } else if (width <= 1024 && this.currentDeviceType !== DEVICE_TYPES.MOBILE) {
            this.currentDeviceType = DEVICE_TYPES.TABLET;
        }
    }

    /**
     * 检测设备方向
     * @private
     */
    _detectOrientation() {
        if (window.innerWidth > window.innerHeight) {
            this.currentOrientation = ORIENTATIONS.LANDSCAPE;
        } else {
            this.currentOrientation = ORIENTATIONS.PORTRAIT;
        }
    }

    /**
     * 更新视口信息
     * @private
     */
    _updateViewport() {
        this.currentViewport = {
            width: window.innerWidth,
            height: window.innerHeight
        };
    }

    /**
     * 处理断点变化
     * @param {string} breakpoint - 新断点
     * @private
     */
    _handleBreakpointChange(breakpoint) {
        const oldBreakpoint = this.currentBreakpoint;
        this.currentBreakpoint = breakpoint;
        
        this.logger.debug('ResponsiveLayout', '_handleBreakpointChange', 
            `断点变化: ${oldBreakpoint} -> ${breakpoint}`);
        
        // 触发断点变化事件
        this.emit(UIEvents.BREAKPOINT_CHANGED, {
            oldBreakpoint,
            newBreakpoint: breakpoint,
            viewport: this.currentViewport,
            timestamp: Date.now()
        });
        
        // 更新响应式组件
        this._updateResponsiveComponents();
        
        // 更新布局容器
        this._updateLayoutContainers();
    }

    /**
     * 处理窗口大小变化
     * @private
     */
    _handleResize() {
        const oldViewport = { ...this.currentViewport };
        this._updateViewport();
        
        // 重新检测设备类型
        const oldDeviceType = this.currentDeviceType;
        this._detectDeviceType();
        
        // 触发窗口大小变化事件
        this.emit(UIEvents.VIEWPORT_RESIZED, {
            oldViewport,
            newViewport: this.currentViewport,
            deviceType: this.currentDeviceType,
            timestamp: Date.now()
        });
        
        // 如果设备类型变化，触发相应事件
        if (oldDeviceType !== this.currentDeviceType) {
            this.emit(UIEvents.DEVICE_TYPE_CHANGED, {
                oldDeviceType,
                newDeviceType: this.currentDeviceType,
                timestamp: Date.now()
            });
        }
        
        this.logger.debug('ResponsiveLayout', '_handleResize', '窗口大小变化', {
            oldViewport,
            newViewport: this.currentViewport,
            deviceType: this.currentDeviceType
        });
    }

    /**
     * 处理方向变化
     * @private
     */
    _handleOrientationChange() {
        const oldOrientation = this.currentOrientation;
        
        // 延迟检测，等待方向变化完成
        setTimeout(() => {
            this._detectOrientation();
            this._updateViewport();
            
            if (oldOrientation !== this.currentOrientation) {
                this.emit(UIEvents.ORIENTATION_CHANGED, {
                    oldOrientation,
                    newOrientation: this.currentOrientation,
                    viewport: this.currentViewport,
                    timestamp: Date.now()
                });
                
                this.logger.debug('ResponsiveLayout', '_handleOrientationChange', 
                    `方向变化: ${oldOrientation} -> ${this.currentOrientation}`);
            }
        }, 100);
    }

    /**
     * 注册响应式组件
     * @param {string} id - 组件ID
     * @param {Object} component - 组件配置
     */
    registerResponsiveComponent(id, component) {
        const {
            element,
            breakpoints = {},
            onBreakpointChange = null,
            onResize = null,
            onOrientationChange = null
        } = component;
        
        if (!element) {
            this.logger.error('ResponsiveLayout', 'registerResponsiveComponent', 
                '组件元素不能为空', { id });
            return false;
        }
        
        const responsiveComponent = {
            id,
            element,
            breakpoints,
            onBreakpointChange,
            onResize,
            onOrientationChange,
            currentConfig: null
        };
        
        this.responsiveComponents.set(id, responsiveComponent);
        
        // 立即应用当前断点配置
        this._applyComponentConfig(responsiveComponent);
        
        this.logger.debug('ResponsiveLayout', 'registerResponsiveComponent', 
            '注册响应式组件', { id });
        
        return true;
    }

    /**
     * 注销响应式组件
     * @param {string} id - 组件ID
     */
    unregisterResponsiveComponent(id) {
        if (this.responsiveComponents.has(id)) {
            this.responsiveComponents.delete(id);
            this.logger.debug('ResponsiveLayout', 'unregisterResponsiveComponent', 
                '注销响应式组件', { id });
            return true;
        }
        return false;
    }

    /**
     * 更新响应式组件
     * @private
     */
    _updateResponsiveComponents() {
        for (const component of this.responsiveComponents.values()) {
            this._applyComponentConfig(component);
        }
    }

    /**
     * 应用组件配置
     * @param {Object} component - 响应式组件
     * @private
     */
    _applyComponentConfig(component) {
        const { breakpoints, element, onBreakpointChange } = component;
        
        // 获取当前断点配置
        const config = breakpoints[this.currentBreakpoint] || {};
        
        // 应用样式
        if (config.styles) {
            Object.assign(element.style, config.styles);
        }
        
        // 应用类名
        if (config.classes) {
            // 移除旧类名
            if (component.currentConfig && component.currentConfig.classes) {
                element.classList.remove(...component.currentConfig.classes);
            }
            
            // 添加新类名
            element.classList.add(...config.classes);
        }
        
        // 应用属性
        if (config.attributes) {
            for (const [attr, value] of Object.entries(config.attributes)) {
                element.setAttribute(attr, value);
            }
        }
        
        // 调用回调函数
        if (onBreakpointChange) {
            onBreakpointChange(this.currentBreakpoint, config, element);
        }
        
        // 保存当前配置
        component.currentConfig = config;
    }

    /**
     * 注册布局容器
     * @param {HTMLElement} container - 容器元素
     */
    registerContainer(container) {
        if (!container || !(container instanceof HTMLElement)) {
            this.logger.error('ResponsiveLayout', 'registerContainer', '无效的容器元素');
            return false;
        }
        
        this.containers.add(container);
        this._applyContainerLayout(container);
        
        this.logger.debug('ResponsiveLayout', 'registerContainer', '注册布局容器');
        
        return true;
    }

    /**
     * 注销布局容器
     * @param {HTMLElement} container - 容器元素
     */
    unregisterContainer(container) {
        if (this.containers.has(container)) {
            this.containers.delete(container);
            this.logger.debug('ResponsiveLayout', 'unregisterContainer', '注销布局容器');
            return true;
        }
        return false;
    }

    /**
     * 更新布局容器
     * @private
     */
    _updateLayoutContainers() {
        for (const container of this.containers) {
            this._applyContainerLayout(container);
        }
    }

    /**
     * 应用容器布局
     * @param {HTMLElement} container - 容器元素
     * @private
     */
    _applyContainerLayout(container) {
        // 添加响应式类名
        container.classList.add('responsive-container');
        container.classList.add(`breakpoint-${this.currentBreakpoint}`);
        container.classList.add(`device-${this.currentDeviceType}`);
        container.classList.add(`orientation-${this.currentOrientation}`);
        
        // 移除旧的断点类名
        for (const breakpoint of Object.keys(this.config.breakpoints)) {
            if (breakpoint !== this.currentBreakpoint) {
                container.classList.remove(`breakpoint-${breakpoint}`);
            }
        }
        
        // 设置CSS变量
        container.style.setProperty('--viewport-width', `${this.currentViewport.width}px`);
        container.style.setProperty('--viewport-height', `${this.currentViewport.height}px`);
        container.style.setProperty('--grid-columns', this.config.gridColumns);
        container.style.setProperty('--grid-gutter', `${this.config.gridGutter}px`);
    }

    /**
     * 创建响应式网格
     * @param {HTMLElement} container - 容器元素
     * @param {Object} config - 网格配置
     */
    createResponsiveGrid(container, config = {}) {
        const {
            columns = this.config.gridColumns,
            gutter = this.config.gridGutter,
            responsive = true
        } = config;
        
        // 添加网格类名
        container.classList.add('responsive-grid');
        
        // 设置网格样式
        container.style.display = 'grid';
        container.style.gridTemplateColumns = `repeat(${columns}, 1fr)`;
        container.style.gap = `${gutter}px`;
        
        if (responsive) {
            // 注册为响应式组件
            this.registerResponsiveComponent(`grid-${Date.now()}`, {
                element: container,
                breakpoints: {
                    xs: { styles: { gridTemplateColumns: 'repeat(1, 1fr)' } },
                    sm: { styles: { gridTemplateColumns: 'repeat(2, 1fr)' } },
                    md: { styles: { gridTemplateColumns: 'repeat(3, 1fr)' } },
                    lg: { styles: { gridTemplateColumns: `repeat(${Math.min(columns, 4)}, 1fr)` } },
                    xl: { styles: { gridTemplateColumns: `repeat(${columns}, 1fr)` } },
                    xxl: { styles: { gridTemplateColumns: `repeat(${columns}, 1fr)` } }
                }
            });
        }
        
        this.logger.debug('ResponsiveLayout', 'createResponsiveGrid', '创建响应式网格', {
            columns,
            gutter,
            responsive
        });
    }

    /**
     * 获取当前断点
     * @returns {string} 当前断点
     */
    getCurrentBreakpoint() {
        return this.currentBreakpoint;
    }

    /**
     * 获取当前设备类型
     * @returns {string} 当前设备类型
     */
    getCurrentDeviceType() {
        return this.currentDeviceType;
    }

    /**
     * 获取当前方向
     * @returns {string} 当前方向
     */
    getCurrentOrientation() {
        return this.currentOrientation;
    }

    /**
     * 获取当前视口信息
     * @returns {Object} 视口信息
     */
    getCurrentViewport() {
        return { ...this.currentViewport };
    }

    /**
     * 检查是否匹配断点
     * @param {string} breakpoint - 断点名称
     * @returns {boolean} 是否匹配
     */
    matchesBreakpoint(breakpoint) {
        const mediaQuery = this.mediaQueries.get(breakpoint);
        return mediaQuery ? mediaQuery.matches : false;
    }

    /**
     * 检查是否为移动设备
     * @returns {boolean} 是否为移动设备
     */
    isMobile() {
        return this.currentDeviceType === DEVICE_TYPES.MOBILE;
    }

    /**
     * 检查是否为平板设备
     * @returns {boolean} 是否为平板设备
     */
    isTablet() {
        return this.currentDeviceType === DEVICE_TYPES.TABLET;
    }

    /**
     * 检查是否为桌面设备
     * @returns {boolean} 是否为桌面设备
     */
    isDesktop() {
        return this.currentDeviceType === DEVICE_TYPES.DESKTOP;
    }

    /**
     * 检查是否为横屏
     * @returns {boolean} 是否为横屏
     */
    isLandscape() {
        return this.currentOrientation === ORIENTATIONS.LANDSCAPE;
    }

    /**
     * 检查是否为竖屏
     * @returns {boolean} 是否为竖屏
     */
    isPortrait() {
        return this.currentOrientation === ORIENTATIONS.PORTRAIT;
    }

    /**
     * 获取断点配置
     * @returns {Object} 断点配置
     */
    getBreakpoints() {
        return { ...this.config.breakpoints };
    }

    /**
     * 添加自定义断点
     * @param {string} name - 断点名称
     * @param {Object} config - 断点配置
     */
    addBreakpoint(name, config) {
        const { min, max } = config;
        
        if (typeof min !== 'number' || typeof max !== 'number') {
            this.logger.error('ResponsiveLayout', 'addBreakpoint', '无效的断点配置', {
                name,
                config
            });
            return false;
        }
        
        // 添加到配置
        this.config.breakpoints[name] = { min, max };
        
        // 创建媒体查询
        let query;
        if (max === Infinity) {
            query = `(min-width: ${min}px)`;
        } else {
            query = `(min-width: ${min}px) and (max-width: ${max}px)`;
        }
        
        const mediaQuery = window.matchMedia(query);
        this.mediaQueries.set(name, mediaQuery);
        
        // 添加监听器
        const listener = (e) => {
            if (e.matches) {
                this._handleBreakpointChange(name);
            }
        };
        
        mediaQuery.addEventListener('change', listener);
        this.mediaQueryListeners.set(name, listener);
        
        this.logger.debug('ResponsiveLayout', 'addBreakpoint', '添加自定义断点', {
            name,
            config,
            query
        });
        
        return true;
    }

    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} delay - 延迟时间
     * @returns {Function} 防抖后的函数
     * @private
     */
    _debounce(func, delay) {
        let timeoutId;
        return function (...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    }

    /**
     * 获取布局状态
     * @returns {Object} 布局状态
     */
    getLayoutState() {
        return {
            breakpoint: this.currentBreakpoint,
            deviceType: this.currentDeviceType,
            orientation: this.currentOrientation,
            viewport: this.currentViewport,
            isMobile: this.isMobile(),
            isTablet: this.isTablet(),
            isDesktop: this.isDesktop(),
            isLandscape: this.isLandscape(),
            isPortrait: this.isPortrait(),
            componentsCount: this.responsiveComponents.size,
            containersCount: this.containers.size
        };
    }

    /**
     * 销毁响应式布局管理器
     */
    destroy() {
        // 移除事件监听器
        if (this.resizeHandler) {
            window.removeEventListener('resize', this.resizeHandler);
        }
        
        if (this.orientationHandler) {
            window.removeEventListener('orientationchange', this.orientationHandler);
            screen.orientation?.removeEventListener('change', this.orientationHandler);
        }
        
        // 移除媒体查询监听器
        for (const [name, listener] of this.mediaQueryListeners) {
            const mediaQuery = this.mediaQueries.get(name);
            if (mediaQuery) {
                mediaQuery.removeEventListener('change', listener);
            }
        }
        
        // 清理数据
        this.mediaQueries.clear();
        this.mediaQueryListeners.clear();
        this.responsiveComponents.clear();
        this.containers.clear();
        
        // 移除事件监听器
        this.removeAllListeners();
        
        this.logger.info('ResponsiveLayout', 'destroy', '响应式布局管理器已销毁');
    }
}
// #endregion

// #region 导出常量
export { DEFAULT_BREAKPOINTS, DEVICE_TYPES, ORIENTATIONS };
// #endregion

// #region 导出
export default ResponsiveLayout;
// #endregion