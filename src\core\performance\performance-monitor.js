/**
 * @file 性能监控器 - SmartOffice 性能监控系统
 * <AUTHOR> Team
 * @description 
 * 这个文件实现了性能监控系统，提供：
 * - 性能指标收集和分析
 * - 内存使用监控
 * - 渲染性能跟踪
 * - 性能报告生成
 * - 性能预警机制
 */

// #region 导入依赖模块
import { EventEmitter } from '../events/event-emitter.js';
import { AppEvents, RenderEvents, CacheEvents } from '../events/event-types.js';
import { getLogger } from '../utils/logger.js';
// #endregion

// #region PerformanceMonitor 性能监控器类
/**
 * @class PerformanceMonitor - 性能监控器
 * @description 监控应用程序性能指标，提供性能分析和优化建议
 */
export class PerformanceMonitor extends EventEmitter {
    /**
     * 构造函数 - 初始化性能监控器
     * @param {Object} config - 监控配置
     */
    constructor(config = {}) {
        super();
        
        this.logger = getLogger();
        this.logger.debug('PerformanceMonitor', 'constructor', '初始化性能监控器');
        
        // 配置
        this.config = {
            enableMemoryMonitoring: true,
            enableRenderMonitoring: true,
            enableCacheMonitoring: true,
            memoryWarningThreshold: 100 * 1024 * 1024, // 100MB
            renderTimeWarningThreshold: 1000, // 1秒
            monitoringInterval: 5000, // 5秒
            maxMetricsHistory: 1000,
            ...config
        };
        
        // 性能指标存储
        this.metrics = {
            memory: [],
            render: [],
            cache: [],
            network: [],
            general: []
        };
        
        // 监控状态
        this.isMonitoring = false;
        this.monitoringTimer = null;
        this.startTime = Date.now();
        
        // 性能统计
        this.stats = {
            totalRenders: 0,
            totalRenderTime: 0,
            averageRenderTime: 0,
            slowRenders: 0,
            memoryPeakUsage: 0,
            cacheHitRate: 0,
            totalCacheHits: 0,
            totalCacheMisses: 0
        };
        
        // 性能观察器
        this.observers = new Map();
        
        this._initializeObservers();
        
        this.logger.info('PerformanceMonitor', 'constructor', '性能监控器初始化完成', {
            config: this.config
        });
    }

    /**
     * 初始化性能观察器
     * @private
     */
    _initializeObservers() {
        // 初始化 Performance Observer（如果支持）
        if (typeof PerformanceObserver !== 'undefined') {
            try {
                // 监控导航性能
                const navigationObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        this._recordNavigationMetric(entry);
                    }
                });
                navigationObserver.observe({ entryTypes: ['navigation'] });
                this.observers.set('navigation', navigationObserver);
                
                // 监控资源加载性能
                const resourceObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        this._recordResourceMetric(entry);
                    }
                });
                resourceObserver.observe({ entryTypes: ['resource'] });
                this.observers.set('resource', resourceObserver);
                
                // 监控用户交互性能
                const measureObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        this._recordMeasureMetric(entry);
                    }
                });
                measureObserver.observe({ entryTypes: ['measure'] });
                this.observers.set('measure', measureObserver);
                
            } catch (error) {
                this.logger.warn('PerformanceMonitor', '_initializeObservers', '性能观察器初始化失败', {
                    error: error.message
                });
            }
        }
    }

    /**
     * 开始性能监控
     */
    startMonitoring() {
        if (this.isMonitoring) {
            this.logger.warn('PerformanceMonitor', 'startMonitoring', '性能监控已在运行中');
            return;
        }
        
        this.isMonitoring = true;
        this.startTime = Date.now();
        
        // 开始定期监控
        this.monitoringTimer = setInterval(() => {
            this._collectMetrics();
        }, this.config.monitoringInterval);
        
        // 触发监控开始事件
        this.emit(AppEvents.PERFORMANCE_MEASURED, {
            type: 'monitoring_started',
            timestamp: Date.now()
        });
        
        this.logger.info('PerformanceMonitor', 'startMonitoring', '性能监控已开始');
    }

    /**
     * 停止性能监控
     */
    stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }
        
        this.isMonitoring = false;
        
        if (this.monitoringTimer) {
            clearInterval(this.monitoringTimer);
            this.monitoringTimer = null;
        }
        
        // 触发监控停止事件
        this.emit(AppEvents.PERFORMANCE_MEASURED, {
            type: 'monitoring_stopped',
            timestamp: Date.now(),
            duration: Date.now() - this.startTime
        });
        
        this.logger.info('PerformanceMonitor', 'stopMonitoring', '性能监控已停止');
    }

    /**
     * 收集性能指标
     * @private
     */
    _collectMetrics() {
        const timestamp = Date.now();
        
        // 收集内存指标
        if (this.config.enableMemoryMonitoring) {
            this._collectMemoryMetrics(timestamp);
        }
        
        // 收集通用性能指标
        this._collectGeneralMetrics(timestamp);
        
        // 检查性能警告
        this._checkPerformanceWarnings();
    }

    /**
     * 收集内存指标
     * @param {number} timestamp - 时间戳
     * @private
     */
    _collectMemoryMetrics(timestamp) {
        if (typeof performance !== 'undefined' && performance.memory) {
            const memoryInfo = {
                timestamp,
                usedJSHeapSize: performance.memory.usedJSHeapSize,
                totalJSHeapSize: performance.memory.totalJSHeapSize,
                jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
            };
            
            this._addMetric('memory', memoryInfo);
            
            // 更新峰值使用量
            if (memoryInfo.usedJSHeapSize > this.stats.memoryPeakUsage) {
                this.stats.memoryPeakUsage = memoryInfo.usedJSHeapSize;
            }
            
            // 检查内存警告
            if (memoryInfo.usedJSHeapSize > this.config.memoryWarningThreshold) {
                this.emit(AppEvents.MEMORY_USAGE_HIGH, {
                    usedMemory: memoryInfo.usedJSHeapSize,
                    threshold: this.config.memoryWarningThreshold,
                    timestamp
                });
            }
        }
    }

    /**
     * 收集通用性能指标
     * @param {number} timestamp - 时间戳
     * @private
     */
    _collectGeneralMetrics(timestamp) {
        const generalInfo = {
            timestamp,
            uptime: timestamp - this.startTime,
            totalRenders: this.stats.totalRenders,
            averageRenderTime: this.stats.averageRenderTime,
            cacheHitRate: this._calculateCacheHitRate()
        };
        
        this._addMetric('general', generalInfo);
    }

    /**
     * 记录渲染性能
     * @param {string} renderType - 渲染类型
     * @param {number} duration - 渲染时长
     * @param {Object} details - 详细信息
     */
    recordRenderPerformance(renderType, duration, details = {}) {
        const timestamp = Date.now();
        
        const renderMetric = {
            timestamp,
            type: renderType,
            duration,
            ...details
        };
        
        this._addMetric('render', renderMetric);
        
        // 更新渲染统计
        this.stats.totalRenders++;
        this.stats.totalRenderTime += duration;
        this.stats.averageRenderTime = this.stats.totalRenderTime / this.stats.totalRenders;
        
        // 检查慢渲染
        if (duration > this.config.renderTimeWarningThreshold) {
            this.stats.slowRenders++;
            
            this.emit(AppEvents.PERFORMANCE_WARNING, {
                type: 'slow_render',
                renderType,
                duration,
                threshold: this.config.renderTimeWarningThreshold,
                timestamp
            });
        }
        
        // 触发渲染性能事件
        this.emit(RenderEvents.RENDER_COMPLETED, {
            type: renderType,
            duration,
            timestamp
        });
        
        this.logger.debug('PerformanceMonitor', 'recordRenderPerformance', '记录渲染性能', {
            renderType,
            duration: `${duration}ms`
        });
    }

    /**
     * 记录缓存性能
     * @param {string} operation - 操作类型 (hit/miss/set/clear)
     * @param {string} cacheKey - 缓存键
     * @param {Object} details - 详细信息
     */
    recordCachePerformance(operation, cacheKey, details = {}) {
        const timestamp = Date.now();
        
        const cacheMetric = {
            timestamp,
            operation,
            cacheKey,
            ...details
        };
        
        this._addMetric('cache', cacheMetric);
        
        // 更新缓存统计
        if (operation === 'hit') {
            this.stats.totalCacheHits++;
        } else if (operation === 'miss') {
            this.stats.totalCacheMisses++;
        }
        
        // 触发缓存事件
        this.emit(CacheEvents[`CACHE_${operation.toUpperCase()}`], {
            cacheKey,
            timestamp,
            ...details
        });
    }

    /**
     * 添加性能指标
     * @param {string} category - 指标类别
     * @param {Object} metric - 指标数据
     * @private
     */
    _addMetric(category, metric) {
        if (!this.metrics[category]) {
            this.metrics[category] = [];
        }
        
        this.metrics[category].push(metric);
        
        // 限制历史记录数量
        if (this.metrics[category].length > this.config.maxMetricsHistory) {
            this.metrics[category].shift();
        }
    }

    /**
     * 计算缓存命中率
     * @returns {number} 缓存命中率（0-1）
     * @private
     */
    _calculateCacheHitRate() {
        const total = this.stats.totalCacheHits + this.stats.totalCacheMisses;
        return total > 0 ? this.stats.totalCacheHits / total : 0;
    }

    /**
     * 检查性能警告
     * @private
     */
    _checkPerformanceWarnings() {
        // 检查缓存命中率
        const hitRate = this._calculateCacheHitRate();
        if (hitRate < 0.7 && (this.stats.totalCacheHits + this.stats.totalCacheMisses) > 100) {
            this.emit(AppEvents.PERFORMANCE_WARNING, {
                type: 'low_cache_hit_rate',
                hitRate,
                timestamp: Date.now()
            });
        }
        
        // 检查平均渲染时间
        if (this.stats.averageRenderTime > this.config.renderTimeWarningThreshold * 0.8) {
            this.emit(AppEvents.PERFORMANCE_WARNING, {
                type: 'high_average_render_time',
                averageTime: this.stats.averageRenderTime,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 记录导航性能指标
     * @param {PerformanceNavigationTiming} entry - 导航性能条目
     * @private
     */
    _recordNavigationMetric(entry) {
        const navigationMetric = {
            timestamp: Date.now(),
            type: 'navigation',
            domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
            loadComplete: entry.loadEventEnd - entry.loadEventStart,
            totalTime: entry.loadEventEnd - entry.navigationStart
        };
        
        this._addMetric('general', navigationMetric);
    }

    /**
     * 记录资源性能指标
     * @param {PerformanceResourceTiming} entry - 资源性能条目
     * @private
     */
    _recordResourceMetric(entry) {
        const resourceMetric = {
            timestamp: Date.now(),
            type: 'resource',
            name: entry.name,
            duration: entry.duration,
            size: entry.transferSize || 0
        };
        
        this._addMetric('network', resourceMetric);
    }

    /**
     * 记录测量性能指标
     * @param {PerformanceMeasure} entry - 测量性能条目
     * @private
     */
    _recordMeasureMetric(entry) {
        const measureMetric = {
            timestamp: Date.now(),
            type: 'measure',
            name: entry.name,
            duration: entry.duration
        };
        
        this._addMetric('general', measureMetric);
    }

    /**
     * 获取性能报告
     * @param {Object} options - 报告选项
     * @returns {Object} 性能报告
     */
    getPerformanceReport(options = {}) {
        const {
            timeRange = 3600000, // 默认1小时
            includeDetails = false
        } = options;
        
        const now = Date.now();
        const startTime = now - timeRange;
        
        const report = {
            timestamp: now,
            timeRange,
            summary: {
                ...this.stats,
                uptime: now - this.startTime,
                cacheHitRate: this._calculateCacheHitRate()
            },
            metrics: {}
        };
        
        // 过滤时间范围内的指标
        for (const [category, metrics] of Object.entries(this.metrics)) {
            const filteredMetrics = metrics.filter(m => m.timestamp >= startTime);
            
            if (includeDetails) {
                report.metrics[category] = filteredMetrics;
            } else {
                report.metrics[category] = {
                    count: filteredMetrics.length,
                    latest: filteredMetrics[filteredMetrics.length - 1] || null
                };
            }
        }
        
        return report;
    }

    /**
     * 清除性能数据
     * @param {string} category - 要清除的类别（可选）
     */
    clearMetrics(category = null) {
        if (category) {
            if (this.metrics[category]) {
                this.metrics[category] = [];
                this.logger.info('PerformanceMonitor', 'clearMetrics', `已清除 ${category} 类别的性能数据`);
            }
        } else {
            for (const key of Object.keys(this.metrics)) {
                this.metrics[key] = [];
            }
            
            // 重置统计
            this.stats = {
                totalRenders: 0,
                totalRenderTime: 0,
                averageRenderTime: 0,
                slowRenders: 0,
                memoryPeakUsage: 0,
                cacheHitRate: 0,
                totalCacheHits: 0,
                totalCacheMisses: 0
            };
            
            this.logger.info('PerformanceMonitor', 'clearMetrics', '已清除所有性能数据');
        }
    }

    /**
     * 销毁性能监控器
     */
    destroy() {
        this.stopMonitoring();
        
        // 断开所有观察器
        for (const [name, observer] of this.observers) {
            try {
                observer.disconnect();
            } catch (error) {
                this.logger.warn('PerformanceMonitor', 'destroy', `断开观察器失败: ${name}`, {
                    error: error.message
                });
            }
        }
        this.observers.clear();
        
        // 清除数据
        this.clearMetrics();
        
        this.logger.info('PerformanceMonitor', 'destroy', '性能监控器已销毁');
    }
}
// #endregion

// #region 导出
export default PerformanceMonitor;
// #endregion