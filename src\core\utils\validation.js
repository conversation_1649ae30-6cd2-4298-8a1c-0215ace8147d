/**
 * @file 验证工具
 * @description 提供数据验证功能，支持各种数据类型和格式的验证
 */

/**
 * @class ValidationError
 * @description 验证错误类
 */
export class ValidationError extends Error {
    /**
     * 创建验证错误实例
     * @param {string} message - 错误消息
     * @param {string} field - 字段名
     * @param {*} value - 字段值
     */
    constructor(message, field = null, value = null) {
        super(message);
        this.name = 'ValidationError';
        this.field = field;
        this.value = value;
    }
}

/**
 * @class Validator
 * @description 验证器类，提供各种数据验证方法
 */
export class Validator {
    /**
     * 验证是否为空
     * @param {*} value - 要验证的值
     * @returns {boolean} 是否为空
     */
    static isEmpty(value) {
        if (value === null || value === undefined) {
            return true;
        }
        
        if (typeof value === 'string') {
            return value.trim().length === 0;
        }
        
        if (Array.isArray(value)) {
            return value.length === 0;
        }
        
        if (typeof value === 'object') {
            return Object.keys(value).length === 0;
        }
        
        return false;
    }

    /**
     * 验证必填字段
     * @param {*} value - 要验证的值
     * @param {string} fieldName - 字段名
     * @throws {ValidationError} 验证失败时抛出错误
     */
    static required(value, fieldName = '字段') {
        if (this.isEmpty(value)) {
            throw new ValidationError(`${fieldName}不能为空`, fieldName, value);
        }
    }

    /**
     * 验证字符串长度
     * @param {string} value - 要验证的字符串
     * @param {number} min - 最小长度
     * @param {number} max - 最大长度
     * @param {string} fieldName - 字段名
     * @throws {ValidationError} 验证失败时抛出错误
     */
    static stringLength(value, min = 0, max = Infinity, fieldName = '字段') {
        if (typeof value !== 'string') {
            throw new ValidationError(`${fieldName}必须是字符串`, fieldName, value);
        }
        
        const length = value.trim().length;
        
        if (length < min) {
            throw new ValidationError(`${fieldName}长度不能少于${min}个字符`, fieldName, value);
        }
        
        if (length > max) {
            throw new ValidationError(`${fieldName}长度不能超过${max}个字符`, fieldName, value);
        }
    }

    /**
     * 验证数字范围
     * @param {number} value - 要验证的数字
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @param {string} fieldName - 字段名
     * @throws {ValidationError} 验证失败时抛出错误
     */
    static numberRange(value, min = -Infinity, max = Infinity, fieldName = '字段') {
        if (typeof value !== 'number' || isNaN(value)) {
            throw new ValidationError(`${fieldName}必须是有效数字`, fieldName, value);
        }
        
        if (value < min) {
            throw new ValidationError(`${fieldName}不能小于${min}`, fieldName, value);
        }
        
        if (value > max) {
            throw new ValidationError(`${fieldName}不能大于${max}`, fieldName, value);
        }
    }

    /**
     * 验证邮箱格式
     * @param {string} email - 邮箱地址
     * @param {string} fieldName - 字段名
     * @throws {ValidationError} 验证失败时抛出错误
     */
    static email(email, fieldName = '邮箱') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (!emailRegex.test(email)) {
            throw new ValidationError(`${fieldName}格式不正确`, fieldName, email);
        }
    }

    /**
     * 验证电话号码格式
     * @param {string} phone - 电话号码
     * @param {string} fieldName - 字段名
     * @throws {ValidationError} 验证失败时抛出错误
     */
    static phone(phone, fieldName = '电话号码') {
        // 支持中国大陆、香港、马来西亚等地区的电话号码格式
        const phoneRegex = /^(\+?6?01[0-46-9]-*[0-9]{7,8}|(\+?86)?1[3-9][0-9]{9}|\+?852[2-9][0-9]{7})$/;
        
        if (!phoneRegex.test(phone.replace(/\s|-/g, ''))) {
            throw new ValidationError(`${fieldName}格式不正确`, fieldName, phone);
        }
    }

    /**
     * 验证身份证号格式
     * @param {string} idNumber - 身份证号
     * @param {string} fieldName - 字段名
     * @throws {ValidationError} 验证失败时抛出错误
     */
    static idNumber(idNumber, fieldName = '身份证号') {
        // 支持中国大陆18位身份证号和马来西亚身份证号
        const cnIdRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
        const myIdRegex = /^\d{6}-\d{2}-\d{4}$/;
        
        if (!cnIdRegex.test(idNumber) && !myIdRegex.test(idNumber)) {
            throw new ValidationError(`${fieldName}格式不正确`, fieldName, idNumber);
        }
    }

    /**
     * 验证日期格式
     * @param {string} date - 日期字符串
     * @param {string} fieldName - 字段名
     * @throws {ValidationError} 验证失败时抛出错误
     */
    static date(date, fieldName = '日期') {
        const dateObj = new Date(date);
        
        if (isNaN(dateObj.getTime())) {
            throw new ValidationError(`${fieldName}格式不正确`, fieldName, date);
        }
    }

    /**
     * 验证货币金额
     * @param {number} amount - 金额
     * @param {string} fieldName - 字段名
     * @throws {ValidationError} 验证失败时抛出错误
     */
    static currency(amount, fieldName = '金额') {
        if (typeof amount !== 'number' || isNaN(amount)) {
            throw new ValidationError(`${fieldName}必须是有效数字`, fieldName, amount);
        }
        
        if (amount < 0) {
            throw new ValidationError(`${fieldName}不能为负数`, fieldName, amount);
        }
        
        // 检查小数位数不超过2位
        const decimalPlaces = (amount.toString().split('.')[1] || '').length;
        if (decimalPlaces > 2) {
            throw new ValidationError(`${fieldName}小数位数不能超过2位`, fieldName, amount);
        }
    }

    /**
     * 验证车牌号格式
     * @param {string} plateNumber - 车牌号
     * @param {string} fieldName - 字段名
     * @throws {ValidationError} 验证失败时抛出错误
     */
    static plateNumber(plateNumber, fieldName = '车牌号') {
        // 支持中国大陆和马来西亚车牌号格式
        const cnPlateRegex = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4}[A-Z0-9挂学警港澳]$/;
        const myPlateRegex = /^[A-Z]{1,3}\s?\d{1,4}[A-Z]?$/;
        
        if (!cnPlateRegex.test(plateNumber) && !myPlateRegex.test(plateNumber)) {
            throw new ValidationError(`${fieldName}格式不正确`, fieldName, plateNumber);
        }
    }

    /**
     * 验证驾照号格式
     * @param {string} licenseNumber - 驾照号
     * @param {string} fieldName - 字段名
     * @throws {ValidationError} 验证失败时抛出错误
     */
    static licenseNumber(licenseNumber, fieldName = '驾照号') {
        // 基本格式验证，支持字母数字组合
        const licenseRegex = /^[A-Z0-9]{6,20}$/;
        
        if (!licenseRegex.test(licenseNumber)) {
            throw new ValidationError(`${fieldName}格式不正确`, fieldName, licenseNumber);
        }
    }

    /**
     * 验证对象结构
     * @param {Object} obj - 要验证的对象
     * @param {Object} schema - 验证规则
     * @param {string} objectName - 对象名称
     * @throws {ValidationError} 验证失败时抛出错误
     */
    static object(obj, schema, objectName = '对象') {
        if (typeof obj !== 'object' || obj === null) {
            throw new ValidationError(`${objectName}必须是对象`, objectName, obj);
        }

        for (const [field, rules] of Object.entries(schema)) {
            const value = obj[field];
            const fieldName = `${objectName}.${field}`;

            try {
                // 执行验证规则
                if (Array.isArray(rules)) {
                    for (const rule of rules) {
                        if (typeof rule === 'function') {
                            rule(value, fieldName);
                        }
                    }
                } else if (typeof rules === 'function') {
                    rules(value, fieldName);
                }
            } catch (error) {
                if (error instanceof ValidationError) {
                    throw error;
                } else {
                    throw new ValidationError(`${fieldName}验证失败: ${error.message}`, field, value);
                }
            }
        }
    }

    /**
     * 验证数组
     * @param {Array} arr - 要验证的数组
     * @param {Function} itemValidator - 数组项验证函数
     * @param {string} fieldName - 字段名
     * @throws {ValidationError} 验证失败时抛出错误
     */
    static array(arr, itemValidator, fieldName = '数组') {
        if (!Array.isArray(arr)) {
            throw new ValidationError(`${fieldName}必须是数组`, fieldName, arr);
        }

        if (typeof itemValidator === 'function') {
            arr.forEach((item, index) => {
                try {
                    itemValidator(item, `${fieldName}[${index}]`);
                } catch (error) {
                    if (error instanceof ValidationError) {
                        throw error;
                    } else {
                        throw new ValidationError(`${fieldName}[${index}]验证失败: ${error.message}`, `${fieldName}[${index}]`, item);
                    }
                }
            });
        }
    }

    /**
     * 批量验证
     * @param {Object} data - 要验证的数据
     * @param {Object} rules - 验证规则
     * @returns {Array} 验证错误数组
     */
    static validateAll(data, rules) {
        const errors = [];

        for (const [field, validators] of Object.entries(rules)) {
            const value = data[field];

            try {
                if (Array.isArray(validators)) {
                    for (const validator of validators) {
                        if (typeof validator === 'function') {
                            validator(value, field);
                        }
                    }
                } else if (typeof validators === 'function') {
                    validators(value, field);
                }
            } catch (error) {
                if (error instanceof ValidationError) {
                    errors.push(error);
                } else {
                    errors.push(new ValidationError(`${field}验证失败: ${error.message}`, field, value));
                }
            }
        }

        return errors;
    }
}

/**
 * 文档数据验证规则
 */
export const DocumentValidationRules = {
    /**
     * 收据验证规则
     */
    receipt: {
        receiptNumber: [
            (value, field) => Validator.required(value, field),
            (value, field) => Validator.stringLength(value, 1, 50, field)
        ],
        receiptDate: [
            (value, field) => Validator.required(value, field),
            (value, field) => Validator.date(value, field)
        ],
        customer: [
            (value, field) => Validator.required(value, field),
            (value, field) => Validator.stringLength(value, 1, 100, field)
        ],
        totalAmount: [
            (value, field) => Validator.required(value, field),
            (value, field) => Validator.currency(value, field)
        ],
        items: [
            (value, field) => Validator.required(value, field),
            (value, field) => Validator.array(value, (item, itemField) => {
                Validator.required(item.description, `${itemField}.description`);
                Validator.stringLength(item.description, 1, 200, `${itemField}.description`);
                Validator.required(item.amount, `${itemField}.amount`);
                Validator.currency(item.amount, `${itemField}.amount`);
            }, field)
        ]
    },

    /**
     * 发票验证规则
     */
    invoice: {
        receiptNumber: [
            (value, field) => Validator.required(value, field),
            (value, field) => Validator.stringLength(value, 1, 50, field)
        ],
        receiptDate: [
            (value, field) => Validator.required(value, field),
            (value, field) => Validator.date(value, field)
        ],
        customer: [
            (value, field) => Validator.required(value, field),
            (value, field) => Validator.stringLength(value, 1, 100, field)
        ],
        totalAmount: [
            (value, field) => Validator.required(value, field),
            (value, field) => Validator.currency(value, field)
        ],
        taxRate: [
            (value, field) => Validator.required(value, field),
            (value, field) => Validator.numberRange(value, 0, 1, field)
        ],
        buyer: [
            (value, field) => Validator.object(value, {
                company: [(v, f) => Validator.stringLength(v, 0, 100, f)],
                taxid: [(v, f) => Validator.stringLength(v, 0, 50, f)],
                tel: [(v, f) => {
                    if (!Validator.isEmpty(v)) {
                        Validator.phone(v, f);
                    }
                }]
            }, field)
        ]
    },

    /**
     * 司机协议验证规则
     */
    driverAgreement: {
        agreementNumber: [
            (value, field) => Validator.required(value, field),
            (value, field) => Validator.stringLength(value, 1, 50, field)
        ],
        signDate: [
            (value, field) => Validator.required(value, field),
            (value, field) => Validator.date(value, field)
        ],
        driverInfo: [
            (value, field) => Validator.object(value, {
                name: [
                    (v, f) => Validator.required(v, f),
                    (v, f) => Validator.stringLength(v, 1, 50, f)
                ],
                id: [
                    (v, f) => Validator.required(v, f),
                    (v, f) => Validator.idNumber(v, f)
                ],
                phone: [
                    (v, f) => Validator.required(v, f),
                    (v, f) => Validator.phone(v, f)
                ],
                licenseNumber: [
                    (v, f) => Validator.required(v, f),
                    (v, f) => Validator.licenseNumber(v, f)
                ],
                plateNumber: [
                    (v, f) => Validator.required(v, f),
                    (v, f) => Validator.plateNumber(v, f)
                ],
                baseSalary: [
                    (v, f) => Validator.required(v, f),
                    (v, f) => Validator.currency(v, f)
                ]
            }, field)
        ]
    }
};

/**
 * 导出配置验证规则
 */
export const ExportValidationRules = {
    /**
     * PDF导出验证规则
     */
    pdf: {
        fileName: [
            (value, field) => Validator.stringLength(value, 1, 100, field),
            (value, field) => isValidFileName(value, field)
        ],
        quality: [
            (value, field) => {
                if (value && !['low', 'medium', 'high'].includes(value)) {
                    throw new ValidationError(`${field}必须是 low、medium 或 high 之一`, field, value);
                }
            }
        ],
        format: [
            (value, field) => {
                if (value && !['A4', 'A3', 'A5', 'Letter', 'Legal'].includes(value)) {
                    throw new ValidationError(`${field}必须是支持的页面格式`, field, value);
                }
            }
        ],
        engine: [
            (value, field) => {
                if (value && !['html2pdf', 'jspdf', 'puppeteer'].includes(value)) {
                    throw new ValidationError(`${field}必须是支持的PDF引擎`, field, value);
                }
            }
        ]
    },

    /**
     * 图片导出验证规则
     */
    image: {
        fileName: [
            (value, field) => Validator.stringLength(value, 1, 100, field),
            (value, field) => isValidFileName(value, field)
        ],
        format: [
            (value, field) => {
                if (value && !['png', 'jpeg', 'jpg', 'svg', 'webp'].includes(value.toLowerCase())) {
                    throw new ValidationError(`${field}必须是支持的图片格式`, field, value);
                }
            }
        ],
        quality: [
            (value, field) => {
                if (value && (typeof value !== 'number' || value < 0 || value > 1)) {
                    throw new ValidationError(`${field}必须是0-1之间的数字`, field, value);
                }
            }
        ],
        width: [
            (value, field) => {
                if (value && (typeof value !== 'number' || value <= 0 || value > 10000)) {
                    throw new ValidationError(`${field}必须是1-10000之间的正整数`, field, value);
                }
            }
        ],
        height: [
            (value, field) => {
                if (value && (typeof value !== 'number' || value <= 0 || value > 10000)) {
                    throw new ValidationError(`${field}必须是1-10000之间的正整数`, field, value);
                }
            }
        ]
    },

    /**
     * 打印导出验证规则
     */
    print: {
        orientation: [
            (value, field) => {
                if (value && !['portrait', 'landscape'].includes(value)) {
                    throw new ValidationError(`${field}必须是 portrait 或 landscape`, field, value);
                }
            }
        ],
        paperSize: [
            (value, field) => {
                if (value && !['A4', 'A3', 'A5', 'Letter', 'Legal'].includes(value)) {
                    throw new ValidationError(`${field}必须是支持的纸张尺寸`, field, value);
                }
            }
        ],
        margins: [
            (value, field) => {
                if (value && typeof value === 'object') {
                    ['top', 'right', 'bottom', 'left'].forEach(margin => {
                        if (value[margin] !== undefined) {
                            if (typeof value[margin] !== 'number' || value[margin] < 0 || value[margin] > 100) {
                                throw new ValidationError(`${field}.${margin}必须是0-100之间的数字`, `${field}.${margin}`, value[margin]);
                            }
                        }
                    });
                }
            }
        ]
    }
};

/**
 * 验证导出配置 - 验证导出配置对象的有效性
 * @param {Object} config - 导出配置对象
 * @param {string} exportType - 导出类型 ('pdf', 'image', 'print')
 * @throws {ValidationError} 验证失败时抛出错误
 */
export function validateExportConfig(config, exportType = 'pdf') {
    if (!config || typeof config !== 'object') {
        throw new ValidationError('导出配置必须是有效的对象', 'config', config);
    }

    // 获取对应类型的验证规则
    const rules = ExportValidationRules[exportType];
    if (!rules) {
        throw new ValidationError(`不支持的导出类型: ${exportType}`, 'exportType', exportType);
    }

    // 执行验证
    const errors = Validator.validateAll(config, rules);
    if (errors.length > 0) {
        throw errors[0]; // 抛出第一个错误
    }
}

/**
 * 验证文件名格式 - 检查文件名是否符合系统要求
 * @param {string} fileName - 文件名
 * @param {string} fieldName - 字段名（用于错误消息）
 * @throws {ValidationError} 验证失败时抛出错误
 * @returns {boolean} 文件名是否有效
 */
export function isValidFileName(fileName, fieldName = '文件名') {
    if (typeof fileName !== 'string') {
        throw new ValidationError(`${fieldName}必须是字符串`, fieldName, fileName);
    }

    // 去除首尾空白
    const trimmed = fileName.trim();
    
    if (trimmed.length === 0) {
        throw new ValidationError(`${fieldName}不能为空`, fieldName, fileName);
    }

    // 检查文件名长度
    if (trimmed.length > 255) {
        throw new ValidationError(`${fieldName}长度不能超过255个字符`, fieldName, fileName);
    }

    // 检查非法字符（Windows和Linux通用的非法字符）
    const illegalChars = /[<>:"/\\|?*\x00-\x1f]/;
    if (illegalChars.test(trimmed)) {
        throw new ValidationError(`${fieldName}包含非法字符`, fieldName, fileName);
    }

    // 检查保留名称（Windows系统保留）
    const reservedNames = /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])(\.|$)/i;
    if (reservedNames.test(trimmed)) {
        throw new ValidationError(`${fieldName}不能使用系统保留名称`, fieldName, fileName);
    }

    // 检查文件名是否以点开头或结尾
    if (trimmed.startsWith('.') || trimmed.endsWith('.')) {
        throw new ValidationError(`${fieldName}不能以点号开头或结尾`, fieldName, fileName);
    }

    return true;
}

/**
 * 验证文件扩展名 - 检查文件扩展名是否与导出类型匹配
 * @param {string} fileName - 文件名
 * @param {string} expectedType - 期望的文件类型
 * @param {string} fieldName - 字段名
 * @throws {ValidationError} 验证失败时抛出错误
 * @returns {boolean} 扩展名是否匹配
 */
export function validateFileExtension(fileName, expectedType, fieldName = '文件名') {
    if (typeof fileName !== 'string') {
        throw new ValidationError(`${fieldName}必须是字符串`, fieldName, fileName);
    }

    const extension = fileName.toLowerCase().split('.').pop();
    
    const validExtensions = {
        'pdf': ['pdf'],
        'image': ['png', 'jpg', 'jpeg', 'svg', 'webp'],
        'print': [] // 打印不需要文件扩展名
    };

    const expected = validExtensions[expectedType];
    if (!expected) {
        throw new ValidationError(`不支持的文件类型: ${expectedType}`, 'expectedType', expectedType);
    }

    if (expectedType === 'print') {
        return true; // 打印类型不需要验证扩展名
    }

    if (!expected.includes(extension)) {
        throw new ValidationError(
            `${fieldName}的扩展名必须是: ${expected.join(', ')}`,
            fieldName,
            fileName
        );
    }

    return true;
}

/**
 * 验证导出选项 - 综合验证导出选项的完整性和正确性
 * @param {Object} options - 导出选项
 * @param {string} exporterType - 导出器类型
 * @throws {ValidationError} 验证失败时抛出错误
 */
export function validateExportOptions(options, exporterType) {
    try {
        // 基础配置验证
        validateExportConfig(options, exporterType);
        
        // 文件名验证
        if (options.fileName) {
            isValidFileName(options.fileName);
            
            // 如果指定了格式，验证文件扩展名
            if (options.format || exporterType !== 'print') {
                const fileType = options.format ? 
                    (options.format.toLowerCase() === 'pdf' ? 'pdf' : 'image') : 
                    exporterType;
                validateFileExtension(options.fileName, fileType);
            }
        }
        
        // 特定类型的额外验证
        if (exporterType === 'image' && options.width && options.height) {
            // 验证图片尺寸比例合理性
            const aspectRatio = options.width / options.height;
            if (aspectRatio > 10 || aspectRatio < 0.1) {
                throw new ValidationError('图片宽高比例不合理', 'aspectRatio', aspectRatio);
            }
        }
        
    } catch (error) {
        if (error instanceof ValidationError) {
            throw error;
        } else {
            throw new ValidationError(`导出选项验证失败: ${error.message}`, 'options', options);
        }
    }
}
