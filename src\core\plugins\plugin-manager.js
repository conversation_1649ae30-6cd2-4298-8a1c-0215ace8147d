/**
 * @file 插件管理器 - SmartOffice 插件系统
 * <AUTHOR> Team
 * @description 
 * 这个文件实现了插件管理系统，提供：
 * - 插件加载和卸载
 * - 插件生命周期管理
 * - 插件依赖解析
 * - 插件安全验证
 * - 插件API管理
 */

// #region 导入依赖模块
import { EventEmitter } from '../events/event-emitter.js';
import { AppEvents } from '../events/event-types.js';
import { getLogger } from '../utils/logger.js';
// #endregion

// #region PluginManager 插件管理器类
/**
 * @class PluginManager - 插件管理器
 * @description 管理应用程序的插件系统
 */
export class PluginManager extends EventEmitter {
    /**
     * 构造函数 - 初始化插件管理器
     * @param {Object} config - 插件配置
     */
    constructor(config = {}) {
        super();
        
        this.logger = getLogger();
        this.logger.debug('PluginManager', 'constructor', '初始化插件管理器');
        
        // 配置
        this.config = {
            pluginDirectory: './plugins',
            enableSandbox: true,
            enableSecurity: true,
            maxPlugins: 50,
            loadTimeout: 30000, // 30秒
            apiVersion: '1.0.0',
            allowedPermissions: [
                'ui.create',
                'ui.modify',
                'data.read',
                'data.write',
                'export.pdf',
                'export.html',
                'render.custom'
            ],
            ...config
        };
        
        // 插件存储
        this.plugins = new Map();
        this.pluginInstances = new Map();
        this.pluginDependencies = new Map();
        
        // 插件状态
        this.pluginStates = new Map();
        
        // API注册表
        this.apiRegistry = new Map();
        
        // 插件加载队列
        this.loadQueue = [];
        this.isLoading = false;
        
        // 安全上下文
        this.securityContext = {
            trustedPlugins: new Set(),
            permissions: new Map(),
            sandboxes: new Map()
        };
        
        this._initializeAPI();
        
        this.logger.info('PluginManager', 'constructor', '插件管理器初始化完成', {
            config: this.config
        });
    }

    /**
     * 初始化插件API
     * @private
     */
    _initializeAPI() {
        // 注册核心API
        this.registerAPI('core', {
            version: this.config.apiVersion,
            
            // 日志API
            log: {
                debug: (message, data) => this.logger.debug('Plugin', 'api', message, data),
                info: (message, data) => this.logger.info('Plugin', 'api', message, data),
                warn: (message, data) => this.logger.warn('Plugin', 'api', message, data),
                error: (message, data) => this.logger.error('Plugin', 'api', message, data)
            },
            
            // 事件API
            events: {
                emit: (event, data) => this.emit(event, data),
                on: (event, handler) => this.on(event, handler),
                off: (event, handler) => this.off(event, handler)
            },
            
            // 存储API
            storage: {
                get: (key) => this._getPluginStorage(key),
                set: (key, value) => this._setPluginStorage(key, value),
                delete: (key) => this._deletePluginStorage(key)
            }
        });
    }

    /**
     * 注册插件API
     * @param {string} namespace - API命名空间
     * @param {Object} api - API对象
     */
    registerAPI(namespace, api) {
        this.apiRegistry.set(namespace, api);
        
        this.logger.debug('PluginManager', 'registerAPI', '注册插件API', {
            namespace,
            methods: Object.keys(api)
        });
    }

    /**
     * 获取插件API
     * @param {string} namespace - API命名空间
     * @returns {Object} API对象
     */
    getAPI(namespace) {
        return this.apiRegistry.get(namespace);
    }

    /**
     * 加载插件
     * @param {string|Object} plugin - 插件路径或插件对象
     * @param {Object} options - 加载选项
     * @returns {Promise<boolean>} 是否加载成功
     */
    async loadPlugin(plugin, options = {}) {
        const {
            force = false,
            dependencies = true
        } = options;
        
        try {
            let pluginInfo;
            
            // 解析插件信息
            if (typeof plugin === 'string') {
                pluginInfo = await this._loadPluginFromPath(plugin);
            } else {
                pluginInfo = plugin;
            }
            
            const { id, name, version } = pluginInfo;
            
            // 检查是否已加载
            if (this.plugins.has(id) && !force) {
                this.logger.warn('PluginManager', 'loadPlugin', '插件已加载', { id, name });
                return false;
            }
            
            // 验证插件
            if (!this._validatePlugin(pluginInfo)) {
                throw new Error(`插件验证失败: ${id}`);
            }
            
            // 检查依赖
            if (dependencies && pluginInfo.dependencies) {
                await this._loadDependencies(pluginInfo.dependencies);
            }
            
            // 创建安全上下文
            const sandbox = this._createSandbox(pluginInfo);
            
            // 实例化插件
            const instance = await this._instantiatePlugin(pluginInfo, sandbox);
            
            // 存储插件信息
            this.plugins.set(id, pluginInfo);
            this.pluginInstances.set(id, instance);
            this.pluginStates.set(id, 'loaded');
            
            if (sandbox) {
                this.securityContext.sandboxes.set(id, sandbox);
            }
            
            // 触发加载事件
            this.emit(AppEvents.PLUGIN_LOADED, {
                id,
                name,
                version,
                timestamp: Date.now()
            });
            
            this.logger.info('PluginManager', 'loadPlugin', '插件加载成功', {
                id,
                name,
                version
            });
            
            return true;
            
        } catch (error) {
            this.logger.error('PluginManager', 'loadPlugin', '插件加载失败', {
                plugin: typeof plugin === 'string' ? plugin : plugin.id,
                error: error.message
            });
            
            // 触发错误事件
            this.emit(AppEvents.PLUGIN_ERROR, {
                plugin: typeof plugin === 'string' ? plugin : plugin.id,
                error: error.message,
                timestamp: Date.now()
            });
            
            return false;
        }
    }

    /**
     * 从路径加载插件
     * @param {string} pluginPath - 插件路径
     * @returns {Promise<Object>} 插件信息
     * @private
     */
    async _loadPluginFromPath(pluginPath) {
        // 这里应该实现从文件系统或URL加载插件的逻辑
        // 为了演示，我们返回一个模拟的插件对象
        
        const response = await fetch(pluginPath);
        if (!response.ok) {
            throw new Error(`无法加载插件: ${pluginPath}`);
        }
        
        const pluginCode = await response.text();
        
        // 解析插件代码（这里简化处理）
        const pluginInfo = this._parsePluginCode(pluginCode);
        
        return pluginInfo;
    }

    /**
     * 解析插件代码
     * @param {string} code - 插件代码
     * @returns {Object} 插件信息
     * @private
     */
    _parsePluginCode(code) {
        // 这里应该实现安全的代码解析逻辑
        // 为了演示，我们返回一个基本的插件结构
        
        try {
            // 使用Function构造器创建插件（在实际应用中需要更安全的方法）
            const pluginFactory = new Function('api', code + '; return plugin;');
            
            // 创建临时API对象用于获取插件信息
            const tempAPI = this._createTempAPI();
            const plugin = pluginFactory(tempAPI);
            
            return {
                id: plugin.id || 'unknown',
                name: plugin.name || 'Unknown Plugin',
                version: plugin.version || '1.0.0',
                description: plugin.description || '',
                author: plugin.author || 'Unknown',
                dependencies: plugin.dependencies || [],
                permissions: plugin.permissions || [],
                factory: pluginFactory,
                code
            };
        } catch (error) {
            throw new Error(`插件代码解析失败: ${error.message}`);
        }
    }

    /**
     * 创建临时API对象
     * @returns {Object} 临时API
     * @private
     */
    _createTempAPI() {
        return {
            version: this.config.apiVersion,
            log: {
                debug: () => {},
                info: () => {},
                warn: () => {},
                error: () => {}
            },
            events: {
                emit: () => {},
                on: () => {},
                off: () => {}
            },
            storage: {
                get: () => null,
                set: () => {},
                delete: () => {}
            }
        };
    }

    /**
     * 验证插件
     * @param {Object} pluginInfo - 插件信息
     * @returns {boolean} 是否有效
     * @private
     */
    _validatePlugin(pluginInfo) {
        // 检查必需字段
        if (!pluginInfo.id || !pluginInfo.name || !pluginInfo.version) {
            return false;
        }
        
        // 检查权限
        if (this.config.enableSecurity && pluginInfo.permissions) {
            for (const permission of pluginInfo.permissions) {
                if (!this.config.allowedPermissions.includes(permission)) {
                    this.logger.warn('PluginManager', '_validatePlugin', '插件请求未授权权限', {
                        plugin: pluginInfo.id,
                        permission
                    });
                    return false;
                }
            }
        }
        
        // 检查插件数量限制
        if (this.plugins.size >= this.config.maxPlugins) {
            this.logger.warn('PluginManager', '_validatePlugin', '已达到插件数量限制');
            return false;
        }
        
        return true;
    }

    /**
     * 加载依赖
     * @param {Array} dependencies - 依赖列表
     * @returns {Promise<void>}
     * @private
     */
    async _loadDependencies(dependencies) {
        for (const dependency of dependencies) {
            if (!this.plugins.has(dependency)) {
                // 尝试加载依赖插件
                const dependencyPath = `${this.config.pluginDirectory}/${dependency}.js`;
                await this.loadPlugin(dependencyPath, { dependencies: false });
            }
        }
    }

    /**
     * 创建沙箱环境
     * @param {Object} pluginInfo - 插件信息
     * @returns {Object} 沙箱对象
     * @private
     */
    _createSandbox(pluginInfo) {
        if (!this.config.enableSandbox) {
            return null;
        }
        
        // 创建受限的全局对象
        const sandbox = {
            console: {
                log: (...args) => this.logger.info('Plugin', pluginInfo.id, ...args),
                warn: (...args) => this.logger.warn('Plugin', pluginInfo.id, ...args),
                error: (...args) => this.logger.error('Plugin', pluginInfo.id, ...args)
            },
            
            // 提供安全的API访问
            api: this._createPluginAPI(pluginInfo),
            
            // 插件信息
            plugin: {
                id: pluginInfo.id,
                name: pluginInfo.name,
                version: pluginInfo.version
            }
        };
        
        return sandbox;
    }

    /**
     * 创建插件API
     * @param {Object} pluginInfo - 插件信息
     * @returns {Object} 插件API
     * @private
     */
    _createPluginAPI(pluginInfo) {
        const api = {};
        
        // 复制核心API
        for (const [namespace, namespaceAPI] of this.apiRegistry) {
            api[namespace] = { ...namespaceAPI };
        }
        
        // 添加插件特定的权限检查
        if (this.config.enableSecurity) {
            api.core = this._wrapAPIWithPermissions(api.core, pluginInfo);
        }
        
        return api;
    }

    /**
     * 为API添加权限检查
     * @param {Object} api - API对象
     * @param {Object} pluginInfo - 插件信息
     * @returns {Object} 包装后的API
     * @private
     */
    _wrapAPIWithPermissions(api, pluginInfo) {
        const wrappedAPI = { ...api };
        const permissions = pluginInfo.permissions || [];
        
        // 这里可以添加更细粒度的权限控制
        // 例如，检查插件是否有特定操作的权限
        
        return wrappedAPI;
    }

    /**
     * 实例化插件
     * @param {Object} pluginInfo - 插件信息
     * @param {Object} sandbox - 沙箱环境
     * @returns {Promise<Object>} 插件实例
     * @private
     */
    async _instantiatePlugin(pluginInfo, sandbox) {
        const api = sandbox ? sandbox.api : this._createPluginAPI(pluginInfo);
        
        // 使用插件工厂创建实例
        const instance = pluginInfo.factory(api);
        
        // 调用插件的初始化方法
        if (instance && typeof instance.initialize === 'function') {
            await instance.initialize();
        }
        
        return instance;
    }

    /**
     * 卸载插件
     * @param {string} pluginId - 插件ID
     * @returns {Promise<boolean>} 是否卸载成功
     */
    async unloadPlugin(pluginId) {
        try {
            if (!this.plugins.has(pluginId)) {
                this.logger.warn('PluginManager', 'unloadPlugin', '插件不存在', { pluginId });
                return false;
            }
            
            const pluginInfo = this.plugins.get(pluginId);
            const instance = this.pluginInstances.get(pluginId);
            
            // 调用插件的清理方法
            if (instance && typeof instance.destroy === 'function') {
                await instance.destroy();
            }
            
            // 清理沙箱
            if (this.securityContext.sandboxes.has(pluginId)) {
                this.securityContext.sandboxes.delete(pluginId);
            }
            
            // 移除插件
            this.plugins.delete(pluginId);
            this.pluginInstances.delete(pluginId);
            this.pluginStates.delete(pluginId);
            this.securityContext.permissions.delete(pluginId);
            
            // 触发卸载事件
            this.emit(AppEvents.PLUGIN_UNLOADED, {
                id: pluginId,
                name: pluginInfo.name,
                timestamp: Date.now()
            });
            
            this.logger.info('PluginManager', 'unloadPlugin', '插件卸载成功', {
                id: pluginId,
                name: pluginInfo.name
            });
            
            return true;
            
        } catch (error) {
            this.logger.error('PluginManager', 'unloadPlugin', '插件卸载失败', {
                pluginId,
                error: error.message
            });
            
            // 触发错误事件
            this.emit(AppEvents.PLUGIN_ERROR, {
                plugin: pluginId,
                error: error.message,
                timestamp: Date.now()
            });
            
            return false;
        }
    }

    /**
     * 获取插件实例
     * @param {string} pluginId - 插件ID
     * @returns {Object} 插件实例
     */
    getPlugin(pluginId) {
        return this.pluginInstances.get(pluginId);
    }

    /**
     * 获取插件信息
     * @param {string} pluginId - 插件ID
     * @returns {Object} 插件信息
     */
    getPluginInfo(pluginId) {
        return this.plugins.get(pluginId);
    }

    /**
     * 获取所有插件
     * @returns {Array} 插件列表
     */
    getAllPlugins() {
        const plugins = [];
        
        for (const [id, info] of this.plugins) {
            plugins.push({
                id,
                ...info,
                state: this.pluginStates.get(id),
                instance: this.pluginInstances.has(id)
            });
        }
        
        return plugins;
    }

    /**
     * 检查插件是否已加载
     * @param {string} pluginId - 插件ID
     * @returns {boolean} 是否已加载
     */
    isPluginLoaded(pluginId) {
        return this.plugins.has(pluginId) && this.pluginStates.get(pluginId) === 'loaded';
    }

    /**
     * 启用插件
     * @param {string} pluginId - 插件ID
     * @returns {Promise<boolean>} 是否启用成功
     */
    async enablePlugin(pluginId) {
        if (!this.plugins.has(pluginId)) {
            return false;
        }
        
        const instance = this.pluginInstances.get(pluginId);
        
        if (instance && typeof instance.enable === 'function') {
            await instance.enable();
        }
        
        this.pluginStates.set(pluginId, 'enabled');
        
        this.logger.info('PluginManager', 'enablePlugin', '插件已启用', { pluginId });
        
        return true;
    }

    /**
     * 禁用插件
     * @param {string} pluginId - 插件ID
     * @returns {Promise<boolean>} 是否禁用成功
     */
    async disablePlugin(pluginId) {
        if (!this.plugins.has(pluginId)) {
            return false;
        }
        
        const instance = this.pluginInstances.get(pluginId);
        
        if (instance && typeof instance.disable === 'function') {
            await instance.disable();
        }
        
        this.pluginStates.set(pluginId, 'disabled');
        
        this.logger.info('PluginManager', 'disablePlugin', '插件已禁用', { pluginId });
        
        return true;
    }

    /**
     * 获取插件存储
     * @param {string} key - 存储键
     * @returns {*} 存储值
     * @private
     */
    _getPluginStorage(key) {
        try {
            const data = localStorage.getItem(`plugin_storage_${key}`);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            this.logger.error('PluginManager', '_getPluginStorage', '获取插件存储失败', {
                key,
                error: error.message
            });
            return null;
        }
    }

    /**
     * 设置插件存储
     * @param {string} key - 存储键
     * @param {*} value - 存储值
     * @private
     */
    _setPluginStorage(key, value) {
        try {
            localStorage.setItem(`plugin_storage_${key}`, JSON.stringify(value));
        } catch (error) {
            this.logger.error('PluginManager', '_setPluginStorage', '设置插件存储失败', {
                key,
                error: error.message
            });
        }
    }

    /**
     * 删除插件存储
     * @param {string} key - 存储键
     * @private
     */
    _deletePluginStorage(key) {
        try {
            localStorage.removeItem(`plugin_storage_${key}`);
        } catch (error) {
            this.logger.error('PluginManager', '_deletePluginStorage', '删除插件存储失败', {
                key,
                error: error.message
            });
        }
    }

    /**
     * 获取插件统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        const stats = {
            total: this.plugins.size,
            loaded: 0,
            enabled: 0,
            disabled: 0,
            errors: 0
        };
        
        for (const state of this.pluginStates.values()) {
            switch (state) {
                case 'loaded':
                    stats.loaded++;
                    break;
                case 'enabled':
                    stats.enabled++;
                    break;
                case 'disabled':
                    stats.disabled++;
                    break;
                case 'error':
                    stats.errors++;
                    break;
            }
        }
        
        return stats;
    }

    /**
     * 销毁插件管理器
     */
    async destroy() {
        // 卸载所有插件
        const pluginIds = Array.from(this.plugins.keys());
        
        for (const pluginId of pluginIds) {
            await this.unloadPlugin(pluginId);
        }
        
        // 清理API注册表
        this.apiRegistry.clear();
        
        // 清理安全上下文
        this.securityContext.trustedPlugins.clear();
        this.securityContext.permissions.clear();
        this.securityContext.sandboxes.clear();
        
        // 移除事件监听器
        this.removeAllListeners();
        
        this.logger.info('PluginManager', 'destroy', '插件管理器已销毁');
    }
}
// #endregion

// #region 插件基类
/**
 * @class BasePlugin - 插件基类
 * @description 所有插件应该继承的基类
 */
export class BasePlugin {
    /**
     * 构造函数
     * @param {Object} api - 插件API
     */
    constructor(api) {
        this.api = api;
        this.id = 'base-plugin';
        this.name = 'Base Plugin';
        this.version = '1.0.0';
        this.description = 'Base plugin class';
        this.author = 'SmartOffice Team';
        this.dependencies = [];
        this.permissions = [];
    }

    /**
     * 初始化插件
     * @returns {Promise<void>}
     */
    async initialize() {
        this.api.core.log.info(`插件 ${this.name} 初始化`);
    }

    /**
     * 启用插件
     * @returns {Promise<void>}
     */
    async enable() {
        this.api.core.log.info(`插件 ${this.name} 已启用`);
    }

    /**
     * 禁用插件
     * @returns {Promise<void>}
     */
    async disable() {
        this.api.core.log.info(`插件 ${this.name} 已禁用`);
    }

    /**
     * 销毁插件
     * @returns {Promise<void>}
     */
    async destroy() {
        this.api.core.log.info(`插件 ${this.name} 已销毁`);
    }
}
// #endregion

// #region 导出
export default PluginManager;
// #endregion