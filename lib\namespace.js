/**
 * @file SmartOffice 2.0 全局命名空间初始化
 * <AUTHOR> Team
 * @description 
 * 初始化SmartOffice全局命名空间，为传统script标签架构提供基础
 * 替代ES6模块系统，确保file://协议完全兼容
 * 
 * 架构特性：
 * - 全局命名空间管理
 * - 模块依赖检查
 * - 版本信息管理
 * - 环境检测功能
 */

(function(window) {
    'use strict';
    
    // 防止重复初始化
    if (window.SmartOffice) {
        console.warn('SmartOffice命名空间已存在，跳过重复初始化');
        return;
    }
    
    /**
     * SmartOffice 2.0 全局命名空间
     * @namespace SmartOffice
     */
    window.SmartOffice = {
        // 版本信息
        version: '2.0.0-traditional',
        buildDate: '2024-12-19',
        mode: 'traditional',
        
        // 核心功能命名空间
        Core: {
            // 日志管理器
            Logger: null,
            getLogger: null,
            
            // 配置管理器
            Config: null,
            ConfigManager: null,
            
            // 事件总线
            Events: null,
            EventBus: null,
            
            // 应用初始化器
            App: null,
            AppInitializer: null
        },
        
        // 业务服务命名空间
        Services: {
            // NLP服务
            NLP: null,
            NLPService: null,
            
            // 文档服务
            Document: null,
            DocumentService: null,
            
            // 导出服务
            Export: null,
            ExportService: null
        },
        
        // UI组件命名空间
        UI: {
            // 文档编辑器
            Editor: null,
            DocumentEditor: null,
            
            // 预览组件
            Preview: null,
            PreviewComponent: null,
            
            // 表单组件
            Forms: null,
            FormComponent: null
        },
        
        // 工具函数命名空间
        Utils: {
            // DOM辅助工具
            DOM: null,
            DOMHelpers: null,
            
            // 通知管理器
            Notification: null,
            NotificationManager: null,
            
            // 资源管理器
            Resource: null,
            ResourceManager: null
        },
        
        // 渲染器系统命名空间
        Renderers: {
            // 统一渲染器
            Unified: null,
            UnifiedRenderer: null,
            
            // HTML渲染器
            HTML: null,
            HTMLRenderer: null,
            
            // PDF渲染器
            PDF: null,
            PDFRenderer: null,
            
            // 打印渲染器
            Print: null,
            PrintRenderer: null
        },
        
        // 导出器系统命名空间
        Exporters: {
            // 基础导出器
            Base: null,
            BaseExporter: null,
            
            // 图片导出器
            Image: null,
            ImageExporter: null,
            
            // PDF导出器
            PDF: null,
            PDFExporter: null,
            
            // 打印导出器
            Print: null,
            PrintExporter: null
        },
        
        // 状态管理命名空间
        State: {
            // 全局状态管理器
            Global: null,
            GlobalStateManager: null
        },
        
        // 模板系统命名空间
        Templates: {
            // 基础模板
            Base: null,
            BaseTemplate: null,
            
            // 收据模板
            Receipt: null,
            ReceiptTemplate: null,
            
            // 发票模板
            Invoice: null,
            InvoiceTemplate: null,
            
            // 司机协议模板
            DriverAgreement: null,
            DriverAgreementTemplate: null
        },
        
        // 工作流引擎命名空间
        Workflow: {
            // 工作流引擎
            Engine: null,
            WorkflowEngine: null
        },
        
        // 应用实例和状态
        App: {
            // 应用实例
            instance: null,
            
            // 应用状态
            isInitialized: false,
            isReady: false,
            
            // 启动时间
            startTime: null,
            
            // 配置信息
            config: {},
            
            // 统计信息
            stats: {
                modulesLoaded: 0,
                initializationTime: 0,
                errors: 0,
                warnings: 0
            }
        },
        
        // 模块管理功能
        Modules: {
            // 已加载模块列表
            loaded: new Set(),
            
            // 模块依赖关系
            dependencies: new Map(),
            
            // 模块加载状态
            loadingStatus: new Map(),
            
            /**
             * 注册模块
             * @param {string} moduleName - 模块名称
             * @param {Array} dependencies - 依赖模块列表
             */
            register: function(moduleName, dependencies = []) {
                this.dependencies.set(moduleName, dependencies);
                this.loadingStatus.set(moduleName, 'registered');
                console.debug(`📦 模块注册: ${moduleName}`, dependencies);
            },
            
            /**
             * 标记模块为已加载
             * @param {string} moduleName - 模块名称
             */
            markLoaded: function(moduleName) {
                this.loaded.add(moduleName);
                this.loadingStatus.set(moduleName, 'loaded');
                window.SmartOffice.App.stats.modulesLoaded++;
                console.debug(`✅ 模块加载完成: ${moduleName}`);
            },
            
            /**
             * 检查模块依赖
             * @param {string} moduleName - 模块名称
             * @returns {boolean} 依赖是否满足
             */
            checkDependencies: function(moduleName) {
                const dependencies = this.dependencies.get(moduleName) || [];
                const missing = dependencies.filter(dep => !this.loaded.has(dep));
                
                if (missing.length > 0) {
                    console.error(`❌ 模块 ${moduleName} 缺少依赖:`, missing);
                    return false;
                }
                
                return true;
            },
            
            /**
             * 获取模块加载状态
             * @returns {Object} 加载状态统计
             */
            getLoadStatus: function() {
                return {
                    total: this.dependencies.size,
                    loaded: this.loaded.size,
                    pending: this.dependencies.size - this.loaded.size,
                    loadedModules: Array.from(this.loaded),
                    pendingModules: Array.from(this.dependencies.keys()).filter(name => !this.loaded.has(name))
                };
            }
        },
        
        // 环境检测功能
        Environment: {
            // 浏览器信息
            browser: {
                name: null,
                version: null,
                engine: null
            },
            
            // 协议信息
            protocol: window.location.protocol,
            isFileProtocol: window.location.protocol === 'file:',
            isHttpProtocol: window.location.protocol.startsWith('http'),
            
            // 功能支持检测
            features: {
                es6: false,
                modules: false,
                webWorkers: false,
                localStorage: false,
                sessionStorage: false
            },
            
            /**
             * 检测浏览器环境
             */
            detect: function() {
                // 检测浏览器
                const userAgent = navigator.userAgent;
                if (userAgent.includes('Chrome')) {
                    this.browser.name = 'Chrome';
                    this.browser.engine = 'Blink';
                } else if (userAgent.includes('Firefox')) {
                    this.browser.name = 'Firefox';
                    this.browser.engine = 'Gecko';
                } else if (userAgent.includes('Safari')) {
                    this.browser.name = 'Safari';
                    this.browser.engine = 'WebKit';
                } else if (userAgent.includes('Edge')) {
                    this.browser.name = 'Edge';
                    this.browser.engine = 'Blink';
                }
                
                // 检测功能支持
                this.features.es6 = typeof Symbol !== 'undefined';
                this.features.modules = 'noModule' in document.createElement('script');
                this.features.webWorkers = typeof Worker !== 'undefined';
                this.features.localStorage = typeof localStorage !== 'undefined';
                this.features.sessionStorage = typeof sessionStorage !== 'undefined';
                
                console.info('🌐 环境检测完成:', {
                    browser: this.browser,
                    protocol: this.protocol,
                    features: this.features
                });
            }
        },
        
        // 工具函数
        Utils: {
            /**
             * 深度合并对象
             * @param {Object} target - 目标对象
             * @param {...Object} sources - 源对象
             * @returns {Object} 合并后的对象
             */
            deepMerge: function(target, ...sources) {
                if (!sources.length) return target;
                const source = sources.shift();
                
                if (this.isObject(target) && this.isObject(source)) {
                    for (const key in source) {
                        if (this.isObject(source[key])) {
                            if (!target[key]) Object.assign(target, { [key]: {} });
                            this.deepMerge(target[key], source[key]);
                        } else {
                            Object.assign(target, { [key]: source[key] });
                        }
                    }
                }
                
                return this.deepMerge(target, ...sources);
            },
            
            /**
             * 检查是否为对象
             * @param {*} item - 要检查的项
             * @returns {boolean} 是否为对象
             */
            isObject: function(item) {
                return item && typeof item === 'object' && !Array.isArray(item);
            },
            
            /**
             * 生成唯一ID
             * @returns {string} 唯一ID
             */
            generateId: function() {
                return 'smartoffice_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            }
        }
    };
    
    // 执行环境检测
    window.SmartOffice.Environment.detect();
    
    // 设置应用启动时间
    window.SmartOffice.App.startTime = performance.now();
    
    // 输出初始化信息
    console.info('🚀 SmartOffice 2.0 命名空间初始化完成', {
        version: window.SmartOffice.version,
        mode: window.SmartOffice.mode,
        protocol: window.SmartOffice.Environment.protocol,
        timestamp: new Date().toISOString()
    });
    
})(window);
