/**
 * @file 字符串工具
 * @description 提供字符串处理和格式化功能
 */

/**
 * @class StringUtils
 * @description 字符串工具类，提供各种字符串操作和格式化方法
 */
export class StringUtils {
    /**
     * 格式化货币金额
     * @param {number} amount - 金额
     * @param {string} currency - 货币符号，默认为 'RM'
     * @param {number} decimals - 小数位数，默认为 2
     * @param {string} locale - 语言环境，默认为 'en-US'
     * @returns {string} 格式化后的货币字符串
     */
    static formatCurrency(amount, currency = 'RM', decimals = 2, locale = 'en-US') {
        if (typeof amount !== 'number' || isNaN(amount)) {
            return `${currency} 0.00`;
        }

        const formatter = new Intl.NumberFormat(locale, {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });

        return `${currency} ${formatter.format(amount)}`;
    }

    /**
     * 格式化数字，添加千位分隔符
     * @param {number} number - 数字
     * @param {string} locale - 语言环境，默认为 'en-US'
     * @returns {string} 格式化后的数字字符串
     */
    static formatNumber(number, locale = 'en-US') {
        if (typeof number !== 'number' || isNaN(number)) {
            return '0';
        }

        return new Intl.NumberFormat(locale).format(number);
    }

    /**
     * 首字母大写
     * @param {string} str - 输入字符串
     * @returns {string} 首字母大写的字符串
     */
    static capitalize(str) {
        if (typeof str !== 'string' || str.length === 0) {
            return '';
        }

        return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
    }

    /**
     * 转换为标题格式（每个单词首字母大写）
     * @param {string} str - 输入字符串
     * @returns {string} 标题格式的字符串
     */
    static toTitleCase(str) {
        if (typeof str !== 'string') {
            return '';
        }

        return str.replace(/\w\S*/g, (txt) => 
            txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
        );
    }

    /**
     * 转换为驼峰命名法
     * @param {string} str - 输入字符串
     * @returns {string} 驼峰命名法字符串
     */
    static toCamelCase(str) {
        if (typeof str !== 'string') {
            return '';
        }

        return str
            .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => 
                index === 0 ? word.toLowerCase() : word.toUpperCase()
            )
            .replace(/\s+/g, '');
    }

    /**
     * 转换为短横线命名法（kebab-case）
     * @param {string} str - 输入字符串
     * @returns {string} 短横线命名法字符串
     */
    static toKebabCase(str) {
        if (typeof str !== 'string') {
            return '';
        }

        return str
            .replace(/([a-z])([A-Z])/g, '$1-$2')
            .replace(/[\s_]+/g, '-')
            .toLowerCase();
    }

    /**
     * 转换为下划线命名法（snake_case）
     * @param {string} str - 输入字符串
     * @returns {string} 下划线命名法字符串
     */
    static toSnakeCase(str) {
        if (typeof str !== 'string') {
            return '';
        }

        return str
            .replace(/([a-z])([A-Z])/g, '$1_$2')
            .replace(/[\s-]+/g, '_')
            .toLowerCase();
    }

    /**
     * 截断字符串并添加省略号
     * @param {string} str - 输入字符串
     * @param {number} maxLength - 最大长度
     * @param {string} suffix - 后缀，默认为 '...'
     * @returns {string} 截断后的字符串
     */
    static truncate(str, maxLength, suffix = '...') {
        if (typeof str !== 'string') {
            return '';
        }

        if (str.length <= maxLength) {
            return str;
        }

        return str.substring(0, maxLength - suffix.length) + suffix;
    }

    /**
     * 移除字符串中的HTML标签
     * @param {string} str - 包含HTML的字符串
     * @returns {string} 纯文本字符串
     */
    static stripHtml(str) {
        if (typeof str !== 'string') {
            return '';
        }

        return str.replace(/<[^>]*>/g, '');
    }

    /**
     * 转义HTML特殊字符
     * @param {string} str - 输入字符串
     * @returns {string} 转义后的字符串
     */
    static escapeHtml(str) {
        if (typeof str !== 'string') {
            return '';
        }

        const htmlEscapes = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#x27;',
            '/': '&#x2F;'
        };

        return str.replace(/[&<>"'\/]/g, (match) => htmlEscapes[match]);
    }

    /**
     * 反转义HTML特殊字符
     * @param {string} str - 转义后的字符串
     * @returns {string} 原始字符串
     */
    static unescapeHtml(str) {
        if (typeof str !== 'string') {
            return '';
        }

        const htmlUnescapes = {
            '&amp;': '&',
            '&lt;': '<',
            '&gt;': '>',
            '&quot;': '"',
            '&#x27;': "'",
            '&#x2F;': '/'
        };

        return str.replace(/&(?:amp|lt|gt|quot|#x27|#x2F);/g, (match) => htmlUnescapes[match]);
    }

    /**
     * 生成随机字符串
     * @param {number} length - 字符串长度
     * @param {string} charset - 字符集，默认为字母数字
     * @returns {string} 随机字符串
     */
    static random(length = 8, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
        let result = '';
        
        for (let i = 0; i < length; i++) {
            result += charset.charAt(Math.floor(Math.random() * charset.length));
        }
        
        return result;
    }

    /**
     * 生成UUID
     * @returns {string} UUID字符串
     */
    static generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    /**
     * 检查字符串是否为空或只包含空白字符
     * @param {string} str - 输入字符串
     * @returns {boolean} 是否为空白
     */
    static isBlank(str) {
        return typeof str !== 'string' || str.trim().length === 0;
    }

    /**
     * 检查字符串是否包含中文字符
     * @param {string} str - 输入字符串
     * @returns {boolean} 是否包含中文
     */
    static containsChinese(str) {
        if (typeof str !== 'string') {
            return false;
        }

        return /[\u4e00-\u9fa5]/.test(str);
    }

    /**
     * 计算字符串的字节长度（中文字符按2字节计算）
     * @param {string} str - 输入字符串
     * @returns {number} 字节长度
     */
    static getByteLength(str) {
        if (typeof str !== 'string') {
            return 0;
        }

        let length = 0;
        
        for (let i = 0; i < str.length; i++) {
            const charCode = str.charCodeAt(i);
            
            // 中文字符范围
            if (charCode >= 0x4e00 && charCode <= 0x9fa5) {
                length += 2;
            } else {
                length += 1;
            }
        }
        
        return length;
    }

    /**
     * 按字节长度截断字符串
     * @param {string} str - 输入字符串
     * @param {number} maxBytes - 最大字节数
     * @param {string} suffix - 后缀
     * @returns {string} 截断后的字符串
     */
    static truncateByBytes(str, maxBytes, suffix = '...') {
        if (typeof str !== 'string') {
            return '';
        }

        let length = 0;
        let result = '';
        
        for (let i = 0; i < str.length; i++) {
            const char = str.charAt(i);
            const charCode = str.charCodeAt(i);
            const charBytes = (charCode >= 0x4e00 && charCode <= 0x9fa5) ? 2 : 1;
            
            if (length + charBytes > maxBytes) {
                break;
            }
            
            result += char;
            length += charBytes;
        }
        
        if (result.length < str.length) {
            result += suffix;
        }
        
        return result;
    }

    /**
     * 格式化模板字符串
     * @param {string} template - 模板字符串，使用 {key} 作为占位符
     * @param {Object} data - 替换数据
     * @returns {string} 格式化后的字符串
     */
    static template(template, data = {}) {
        if (typeof template !== 'string') {
            return '';
        }

        return template.replace(/\{([^}]+)\}/g, (match, key) => {
            const value = data[key.trim()];
            return value !== undefined ? String(value) : match;
        });
    }

    /**
     * 移除字符串开头和结尾的指定字符
     * @param {string} str - 输入字符串
     * @param {string} chars - 要移除的字符，默认为空白字符
     * @returns {string} 处理后的字符串
     */
    static trim(str, chars = null) {
        if (typeof str !== 'string') {
            return '';
        }

        if (chars === null) {
            return str.trim();
        }

        const pattern = new RegExp(`^[${chars}]+|[${chars}]+$`, 'g');
        return str.replace(pattern, '');
    }

    /**
     * 重复字符串指定次数
     * @param {string} str - 输入字符串
     * @param {number} count - 重复次数
     * @returns {string} 重复后的字符串
     */
    static repeat(str, count) {
        if (typeof str !== 'string' || count < 0) {
            return '';
        }

        return str.repeat(count);
    }

    /**
     * 左侧填充字符串到指定长度
     * @param {string} str - 输入字符串
     * @param {number} targetLength - 目标长度
     * @param {string} padString - 填充字符串，默认为空格
     * @returns {string} 填充后的字符串
     */
    static padStart(str, targetLength, padString = ' ') {
        if (typeof str !== 'string') {
            str = String(str);
        }

        return str.padStart(targetLength, padString);
    }

    /**
     * 右侧填充字符串到指定长度
     * @param {string} str - 输入字符串
     * @param {number} targetLength - 目标长度
     * @param {string} padString - 填充字符串，默认为空格
     * @returns {string} 填充后的字符串
     */
    static padEnd(str, targetLength, padString = ' ') {
        if (typeof str !== 'string') {
            str = String(str);
        }

        return str.padEnd(targetLength, padString);
    }

    /**
     * 计算字符串的哈希值（简单哈希算法）
     * @param {string} str - 输入字符串
     * @returns {number} 哈希值
     */
    static hash(str) {
        if (typeof str !== 'string') {
            return 0;
        }

        let hash = 0;
        
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        
        return hash;
    }

    /**
     * 比较两个字符串的相似度（Levenshtein距离）
     * @param {string} str1 - 第一个字符串
     * @param {string} str2 - 第二个字符串
     * @returns {number} 相似度（0-1之间，1表示完全相同）
     */
    static similarity(str1, str2) {
        if (typeof str1 !== 'string' || typeof str2 !== 'string') {
            return 0;
        }

        if (str1 === str2) {
            return 1;
        }

        const maxLength = Math.max(str1.length, str2.length);
        
        if (maxLength === 0) {
            return 1;
        }

        const distance = this.levenshteinDistance(str1, str2);
        return (maxLength - distance) / maxLength;
    }

    /**
     * 计算Levenshtein距离
     * @param {string} str1 - 第一个字符串
     * @param {string} str2 - 第二个字符串
     * @returns {number} Levenshtein距离
     */
    static levenshteinDistance(str1, str2) {
        const matrix = [];

        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }

        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }

        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1, // 替换
                        matrix[i][j - 1] + 1,     // 插入
                        matrix[i - 1][j] + 1      // 删除
                    );
                }
            }
        }

        return matrix[str2.length][str1.length];
    }
}
