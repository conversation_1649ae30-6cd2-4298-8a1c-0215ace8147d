/**
 * @file 图片导出器 - 专门用于图片格式的文档导出
 * <AUTHOR> Team
 * @description 
 * 这个文件实现了图片导出器，继承自BaseExporter，提供：
 * - 图片格式的文档导出功能
 * - html2canvas集成进行截图生成
 * - 多种图片格式支持（PNG、JPEG、SVG、WebP）
 * - 分辨率和质量控制
 * - Canvas处理和尺寸调整
 */

// #region 导入依赖模块
import { BaseExporter } from './base-exporter.js';
import { validateExportOptions } from '../core/utils/validation.js';
import { EXPORT_EVENTS } from '../core/events/event-types.js';
// #endregion

// #region ImageExporter 类定义
/**
 * @class ImageExporter - 图片导出器类
 * @description 专门用于图片格式的文档导出，提供完整的图片生成和下载功能
 */
export class ImageExporter extends BaseExporter {
    /**
     * 构造函数 - 初始化图片导出器
     * @param {Object} config - 图片导出器配置
     * @param {string} config.format - 图片格式 ('png', 'jpeg', 'svg', 'webp')
     * @param {number} config.quality - 图片质量 (0-1)
     * @param {number} config.width - 图片宽度
     * @param {number} config.height - 图片高度
     * @param {number} config.scale - 缩放比例
     */
    constructor(config = {}) {
        // 调用父类构造函数
        super({
            name: config.name || 'image-exporter',
            type: 'image',
            version: config.version || '1.0.0',
            description: config.description || '图片格式文档导出器',
            ...config
        });
        
        // 图片特有配置
        this.imageConfig = {
            format: config.format || 'png',
            quality: config.quality || 0.92,
            width: config.width || null,
            height: config.height || null,
            scale: config.scale || 1,
            backgroundColor: config.backgroundColor || '#ffffff',
            useCORS: config.useCORS !== false,
            allowTaint: config.allowTaint || false,
            removeContainer: config.removeContainer !== false,
            ...config.imageConfig
        };
        
        // html2canvas库状态
        this.html2canvasLoaded = false;
        this.html2canvasPromise = null;
        
        // 下载管理
        this.downloadQueue = [];
        this.activeDownloads = new Map();
        
        // 图片生成统计
        this.imageStats = {
            totalImagesGenerated: 0,
            totalFileSizeMB: 0,
            averageGenerationTime: 0,
            byFormat: {
                png: 0,
                jpeg: 0,
                svg: 0,
                webp: 0
            },
            byQuality: {}
        };
    }

    /**
     * 验证特定配置 - 验证图片导出器特有的配置
     * @protected
     */
    _validateSpecificConfig() {
        // 验证图片格式
        const validFormats = ['png', 'jpeg', 'jpg', 'svg', 'webp'];
        if (!validFormats.includes(this.imageConfig.format.toLowerCase())) {
            throw new Error(`不支持的图片格式: ${this.imageConfig.format}`);
        }
        
        // 验证质量设置
        if (this.imageConfig.quality < 0 || this.imageConfig.quality > 1) {
            throw new Error(`图片质量必须在0-1之间: ${this.imageConfig.quality}`);
        }
        
        // 验证尺寸设置
        if (this.imageConfig.width !== null && (this.imageConfig.width <= 0 || this.imageConfig.width > 10000)) {
            throw new Error(`图片宽度必须在1-10000之间: ${this.imageConfig.width}`);
        }
        
        if (this.imageConfig.height !== null && (this.imageConfig.height <= 0 || this.imageConfig.height > 10000)) {
            throw new Error(`图片高度必须在1-10000之间: ${this.imageConfig.height}`);
        }
        
        // 验证缩放比例
        if (this.imageConfig.scale <= 0 || this.imageConfig.scale > 10) {
            throw new Error(`缩放比例必须在0-10之间: ${this.imageConfig.scale}`);
        }
    }

    /**
     * 验证特定导出选项 - 验证图片导出特有的选项
     * @param {Object} options - 导出选项
     * @protected
     */
    _validateSpecificOptions(options) {
        try {
            validateExportOptions(options, 'image');
        } catch (error) {
            throw new Error(`图片导出选项验证失败: ${error.message}`);
        }
    }

    /**
     * 执行实际导出 - 生成图片文档并处理下载
     * @param {Object} content - 要导出的内容
     * @param {Object} options - 导出选项
     * @returns {Promise<Object>} 导出结果
     * @protected
     */
    async _performExport(content, options) {
        // 确保html2canvas库已加载
        await this._ensureHtml2canvasLoaded();
        
        // 准备图片生成选项
        const imageOptions = this._prepareImageOptions(options);
        
        // 触发图片生成开始事件
        this.emit(EXPORT_EVENTS.EXPORT_STARTED, {
            exporter: this.name,
            type: 'image',
            options: imageOptions
        });
        
        try {
            // 准备HTML内容用于截图
            const htmlElement = await this._prepareHtmlContent(content, options);
            
            // 生成Canvas
            const canvas = await this._generateCanvas(htmlElement, imageOptions);
            
            // 处理图片结果
            const exportResult = await this._processImageResult(canvas, imageOptions);
            
            // 清理临时HTML元素
            this._cleanupHtmlContent(htmlElement);
            
            // 如果启用自动下载，触发下载
            if (options.autoDownload !== false) {
                await this._downloadImage(exportResult, options);
            }
            
            return exportResult;
            
        } catch (error) {
            throw new Error(`图片生成失败: ${error.message}`);
        }
    }

    /**
     * 确保html2canvas库已加载 - 动态加载html2canvas库
     * @private
     */
    async _ensureHtml2canvasLoaded() {
        if (this.html2canvasLoaded) {
            return;
        }
        
        if (this.html2canvasPromise) {
            return this.html2canvasPromise;
        }
        
        this.html2canvasPromise = new Promise((resolve, reject) => {
            // 检查是否已经全局加载
            if (typeof window !== 'undefined' && window.html2canvas) {
                this.html2canvasLoaded = true;
                resolve();
                return;
            }
            
            // 动态加载html2canvas
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js';
            script.async = true;
            
            script.onload = () => {
                this.html2canvasLoaded = true;
                resolve();
            };
            
            script.onerror = () => {
                reject(new Error('无法加载html2canvas库'));
            };
            
            document.head.appendChild(script);
        });
        
        return this.html2canvasPromise;
    }

    /**
     * 准备图片生成选项 - 合并和转换导出选项为图片选项
     * @param {Object} options - 导出选项
     * @returns {Object} 图片生成选项
     * @private
     */
    _prepareImageOptions(options) {
        return {
            // 基础配置
            format: options.format || this.imageConfig.format,
            quality: options.quality !== undefined ? options.quality : this.imageConfig.quality,
            
            // 尺寸配置
            width: options.width || this.imageConfig.width,
            height: options.height || this.imageConfig.height,
            scale: options.scale || this.imageConfig.scale,
            
            // Canvas配置
            backgroundColor: options.backgroundColor || this.imageConfig.backgroundColor,
            useCORS: options.useCORS !== undefined ? options.useCORS : this.imageConfig.useCORS,
            allowTaint: options.allowTaint !== undefined ? options.allowTaint : this.imageConfig.allowTaint,
            removeContainer: options.removeContainer !== undefined ? options.removeContainer : this.imageConfig.removeContainer,
            
            // 文件信息
            fileName: options.fileName || `document.${options.format || this.imageConfig.format}`,
            
            // 其他选项
            ...options.imageOptions
        };
    }

    /**
     * 准备HTML内容 - 将内容转换为可截图的HTML元素
     * @param {Object} content - 要导出的内容
     * @param {Object} options - 导出选项
     * @returns {Promise<HTMLElement>} HTML元素
     * @private
     */
    async _prepareHtmlContent(content, options) {
        let htmlElement;
        
        if (typeof content === 'string') {
            // 如果是HTML字符串，创建临时容器
            htmlElement = document.createElement('div');
            htmlElement.innerHTML = content;
            htmlElement.style.position = 'absolute';
            htmlElement.style.left = '-9999px';
            htmlElement.style.top = '-9999px';
            htmlElement.style.visibility = 'hidden';
            document.body.appendChild(htmlElement);
            
        } else if (content instanceof HTMLElement) {
            // 如果是HTML元素，直接使用
            htmlElement = content;
            
        } else if (content.html || content.content) {
            // 如果是对象，提取HTML内容
            const htmlContent = content.html || content.content;
            htmlElement = document.createElement('div');
            htmlElement.innerHTML = htmlContent;
            htmlElement.style.position = 'absolute';
            htmlElement.style.left = '-9999px';
            htmlElement.style.top = '-9999px';
            htmlElement.style.visibility = 'hidden';
            document.body.appendChild(htmlElement);
            
        } else {
            throw new Error('无效的内容格式，必须是HTML字符串、HTML元素或包含html/content属性的对象');
        }
        
        // 等待样式和图片加载
        await this._waitForContentLoad(htmlElement);
        
        return htmlElement;
    }

    /**
     * 等待内容加载 - 确保样式和图片完全加载
     * @param {HTMLElement} element - HTML元素
     * @returns {Promise<void>}
     * @private
     */
    async _waitForContentLoad(element) {
        // 等待图片加载
        const images = element.querySelectorAll('img');
        const imagePromises = Array.from(images).map(img => {
            if (img.complete) {
                return Promise.resolve();
            }
            
            return new Promise((resolve, reject) => {
                img.onload = resolve;
                img.onerror = resolve; // 即使图片加载失败也继续
                
                // 超时处理
                setTimeout(resolve, 3000);
            });
        });
        
        await Promise.all(imagePromises);
        
        // 等待字体和样式加载
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    /**
     * 生成Canvas - 使用html2canvas生成Canvas
     * @param {HTMLElement} element - HTML元素
     * @param {Object} options - 图片选项
     * @returns {Promise<HTMLCanvasElement>} Canvas元素
     * @private
     */
    async _generateCanvas(element, options) {
        const html2canvasOptions = {
            backgroundColor: options.backgroundColor,
            useCORS: options.useCORS,
            allowTaint: options.allowTaint,
            removeContainer: options.removeContainer,
            scale: options.scale,
            logging: false, // 禁用日志输出
            imageTimeout: 5000, // 图片加载超时
            onclone: (clonedDoc) => {
                // 在克隆文档中应用样式调整
                const clonedElement = clonedDoc.body.querySelector('div');
                if (clonedElement) {
                    clonedElement.style.position = 'static';
                    clonedElement.style.left = 'auto';
                    clonedElement.style.top = 'auto';
                    clonedElement.style.visibility = 'visible';
                }
            }
        };
        
        // 如果指定了尺寸，设置canvas尺寸
        if (options.width) {
            html2canvasOptions.width = options.width;
        }
        if (options.height) {
            html2canvasOptions.height = options.height;
        }
        
        return await html2canvas(element, html2canvasOptions);
    }

    /**
     * 处理图片结果 - 将Canvas转换为导出结果
     * @param {HTMLCanvasElement} canvas - Canvas元素
     * @param {Object} options - 图片选项
     * @returns {Promise<Object>} 处理后的导出结果
     * @private
     */
    async _processImageResult(canvas, options) {
        const format = options.format.toLowerCase();
        const fileName = this._ensureFileExtension(options.fileName, format);
        
        // 调整Canvas尺寸（如果需要）
        const finalCanvas = await this._resizeCanvas(canvas, options);
        
        // 生成图片数据
        const mimeType = this._getMimeType(format);
        const quality = this._getQuality(format, options.quality);
        
        // 生成数据URL
        const dataUrl = finalCanvas.toDataURL(mimeType, quality);
        
        // 生成Blob
        const blob = await this._dataUrlToBlob(dataUrl);
        
        // 创建下载URL
        const url = URL.createObjectURL(blob);
        
        // 计算文件大小
        const fileSize = blob.size;
        
        return {
            type: 'image',
            format: format,
            fileName: fileName,
            content: dataUrl,
            blob: blob,
            url: url,
            canvas: finalCanvas,
            metadata: {
                width: finalCanvas.width,
                height: finalCanvas.height,
                format: format,
                quality: quality,
                fileSize: fileSize,
                generatedAt: new Date().toISOString(),
                scale: options.scale,
                backgroundColor: options.backgroundColor
            }
        };
    }

    /**
     * 确保文件扩展名 - 确保文件名有正确的扩展名
     * @param {string} fileName - 原文件名
     * @param {string} format - 图片格式
     * @returns {string} 带扩展名的文件名
     * @private
     */
    _ensureFileExtension(fileName, format) {
        const validExtensions = {
            'png': '.png',
            'jpeg': '.jpg',
            'jpg': '.jpg',
            'svg': '.svg',
            'webp': '.webp'
        };
        
        const extension = validExtensions[format] || '.png';
        
        if (!fileName.toLowerCase().endsWith(extension)) {
            // 移除现有扩展名（如果有）
            const baseName = fileName.replace(/\.[^/.]+$/, '');
            return baseName + extension;
        }
        
        return fileName;
    }

    /**
     * 调整Canvas尺寸 - 根据指定尺寸调整Canvas
     * @param {HTMLCanvasElement} canvas - 原Canvas
     * @param {Object} options - 图片选项
     * @returns {Promise<HTMLCanvasElement>} 调整后的Canvas
     * @private
     */
    async _resizeCanvas(canvas, options) {
        const targetWidth = options.width;
        const targetHeight = options.height;
        
        // 如果没有指定尺寸，直接返回原Canvas
        if (!targetWidth && !targetHeight) {
            return canvas;
        }
        
        // 计算目标尺寸
        let newWidth = targetWidth || canvas.width;
        let newHeight = targetHeight || canvas.height;
        
        // 如果只指定了一个维度，按比例计算另一个维度
        if (targetWidth && !targetHeight) {
            newHeight = (canvas.height * targetWidth) / canvas.width;
        } else if (targetHeight && !targetWidth) {
            newWidth = (canvas.width * targetHeight) / canvas.height;
        }
        
        // 创建新Canvas
        const newCanvas = document.createElement('canvas');
        newCanvas.width = newWidth;
        newCanvas.height = newHeight;
        
        const ctx = newCanvas.getContext('2d');
        
        // 设置高质量缩放
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        
        // 绘制调整后的图片
        ctx.drawImage(canvas, 0, 0, newWidth, newHeight);
        
        return newCanvas;
    }

    /**
     * 获取MIME类型 - 根据格式获取MIME类型
     * @param {string} format - 图片格式
     * @returns {string} MIME类型
     * @private
     */
    _getMimeType(format) {
        const mimeTypes = {
            'png': 'image/png',
            'jpeg': 'image/jpeg',
            'jpg': 'image/jpeg',
            'svg': 'image/svg+xml',
            'webp': 'image/webp'
        };
        
        return mimeTypes[format] || 'image/png';
    }

    /**
     * 获取质量参数 - 根据格式和质量获取质量参数
     * @param {string} format - 图片格式
     * @param {number} quality - 质量值
     * @returns {number} 质量参数
     * @private
     */
    _getQuality(format, quality) {
        // 只有JPEG和WebP支持质量参数
        if (format === 'jpeg' || format === 'jpg' || format === 'webp') {
            return quality;
        }
        
        return undefined; // PNG和SVG不需要质量参数
    }

    /**
     * DataURL转Blob - 将DataURL转换为Blob对象
     * @param {string} dataUrl - DataURL字符串
     * @returns {Promise<Blob>} Blob对象
     * @private
     */
    async _dataUrlToBlob(dataUrl) {
        return new Promise((resolve) => {
            const parts = dataUrl.split(',');
            const mime = parts[0].match(/:(.*?);/)[1];
            const binaryString = atob(parts[1]);
            const arrayBuffer = new ArrayBuffer(binaryString.length);
            const uint8Array = new Uint8Array(arrayBuffer);
            
            for (let i = 0; i < binaryString.length; i++) {
                uint8Array[i] = binaryString.charCodeAt(i);
            }
            
            resolve(new Blob([arrayBuffer], { type: mime }));
        });
    }

    /**
     * 清理HTML内容 - 清理临时创建的HTML元素
     * @param {HTMLElement} element - HTML元素
     * @private
     */
    _cleanupHtmlContent(element) {
        // 只清理我们创建的临时元素
        if (element && element.parentNode === document.body && 
            element.style.position === 'absolute' && 
            element.style.left === '-9999px') {
            document.body.removeChild(element);
        }
    }

    /**
     * 下载图片文件 - 处理图片文件的下载
     * @param {Object} exportResult - 导出结果
     * @param {Object} options - 导出选项
     * @returns {Promise<void>}
     * @private
     */
    async _downloadImage(exportResult, options) {
        const downloadId = `download_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        try {
            // 记录下载任务
            this.activeDownloads.set(downloadId, {
                fileName: exportResult.fileName,
                url: exportResult.url,
                startTime: Date.now()
            });
            
            // 执行下载
            await this._triggerDownload(exportResult.url, exportResult.fileName);
            
            // 触发下载完成事件
            this.emit(EXPORT_EVENTS.EXPORT_COMPLETED, {
                exporter: this.name,
                type: 'image',
                result: exportResult,
                downloadId
            });
            
        } catch (error) {
            this.emit(EXPORT_EVENTS.EXPORT_FAILED, {
                exporter: this.name,
                error: error.message,
                phase: 'download',
                downloadId
            });
            throw error;
            
        } finally {
            // 清理下载记录
            this.activeDownloads.delete(downloadId);
            
            // 延迟清理URL
            setTimeout(() => {
                if (exportResult.url && exportResult.url.startsWith('blob:')) {
                    URL.revokeObjectURL(exportResult.url);
                }
            }, 1000);
        }
    }

    /**
     * 触发浏览器下载 - 使用浏览器API触发文件下载
     * @param {string} url - 下载URL
     * @param {string} fileName - 文件名
     * @returns {Promise<void>}
     * @private
     */
    async _triggerDownload(url, fileName) {
        return new Promise((resolve, reject) => {
            try {
                // 创建下载链接
                const link = document.createElement('a');
                link.href = url;
                link.download = fileName;
                link.style.display = 'none';
                
                // 添加到页面并触发点击
                document.body.appendChild(link);
                link.click();
                
                // 清理
                document.body.removeChild(link);
                
                // 延迟一点时间确保下载开始
                setTimeout(resolve, 100);
                
            } catch (error) {
                reject(new Error(`下载触发失败: ${error.message}`));
            }
        });
    }

    /**
     * 更新图片统计信息 - 记录图片生成的统计数据
     * @param {Object} data - 生成完成数据
     * @private
     */
    _updateImageStats(data) {
        this.imageStats.totalImagesGenerated++;
        
        if (data.result && data.result.metadata) {
            const metadata = data.result.metadata;
            
            // 更新文件大小统计
            if (metadata.fileSize) {
                this.imageStats.totalFileSizeMB += metadata.fileSize / (1024 * 1024);
            }
            
            // 更新格式统计
            if (metadata.format && this.imageStats.byFormat[metadata.format] !== undefined) {
                this.imageStats.byFormat[metadata.format]++;
            }
            
            // 更新质量统计
            if (metadata.quality !== undefined) {
                const qualityKey = Math.round(metadata.quality * 100);
                this.imageStats.byQuality[qualityKey] = (this.imageStats.byQuality[qualityKey] || 0) + 1;
            }
        }
        
        // 更新平均生成时间
        if (data.duration) {
            const totalTime = this.imageStats.averageGenerationTime * (this.imageStats.totalImagesGenerated - 1) + data.duration;
            this.imageStats.averageGenerationTime = totalTime / this.imageStats.totalImagesGenerated;
        }
    }

    /**
     * 批量图片导出 - 同时导出多个图片文档
     * @param {Array<Object>} contents - 要导出的内容数组
     * @param {Object} baseOptions - 基础导出选项
     * @returns {Promise<Array<Object>>} 导出结果数组
     */
    async batchExport(contents, baseOptions = {}) {
        const results = [];
        const exportPromises = [];
        
        for (let i = 0; i < contents.length; i++) {
            const content = contents[i];
            const format = baseOptions.format || this.imageConfig.format;
            const options = {
                ...baseOptions,
                fileName: baseOptions.fileName ? 
                    this._ensureFileExtension(`${baseOptions.fileName.replace(/\.[^/.]+$/, '')}_${i + 1}`, format) : 
                    `document_${i + 1}.${format}`,
                autoDownload: false // 批量导出时先不自动下载
            };
            
            exportPromises.push(
                this.export(content, options)
                    .then(result => {
                        results[i] = result;
                    })
                    .catch(error => {
                        results[i] = { error: error.message };
                    })
            );
        }
        
        await Promise.all(exportPromises);
        
        // 如果启用批量下载，逐个下载
        if (baseOptions.batchDownload !== false) {
            await this._downloadBatch(results.filter(r => !r.error), baseOptions);
        }
        
        return results;
    }

    /**
     * 批量下载 - 下载多个图片
     * @param {Array<Object>} results - 导出结果数组
     * @param {Object} options - 下载选项
     * @returns {Promise<void>}
     * @private
     */
    async _downloadBatch(results, options) {
        for (const result of results) {
            if (result && result.url) {
                await this._downloadImage(result, { autoDownload: true });
                // 添加延迟避免浏览器阻止多个下载
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }
    }

    /**
     * 预览图片 - 在新窗口中预览图片
     * @param {Object} content - 要预览的内容
     * @param {Object} options - 预览选项
     * @returns {Promise<Object>} 预览结果
     */
    async preview(content, options = {}) {
        const previewOptions = {
            ...options,
            autoDownload: false
        };
        
        const result = await this.export(content, previewOptions);
        
        // 在新窗口打开图片
        if (result.url) {
            window.open(result.url, '_blank');
        }
        
        return result;
    }

    /**
     * 获取支持的格式 - 返回图片导出器支持的格式
     * @returns {Array<string>} 支持的格式数组
     */
    getSupportedFormats() {
        return ['png', 'jpeg', 'jpg', 'svg', 'webp'];
    }

    /**
     * 获取图片统计信息 - 返回图片导出的详细统计
     * @returns {Object} 图片统计信息
     */
    getImageStats() {
        return {
            ...this.imageStats,
            averageFileSizeMB: this.imageStats.totalImagesGenerated > 0 ? 
                this.imageStats.totalFileSizeMB / this.imageStats.totalImagesGenerated : 0,
            activeDownloads: this.activeDownloads.size
        };
    }

    /**
     * 清理资源 - 清理图片导出器占用的资源
     */
    cleanup() {
        // 清理下载队列
        this.downloadQueue = [];
        
        // 清理活跃下载并释放URL
        for (const [downloadId, download] of this.activeDownloads.entries()) {
            if (download.url && download.url.startsWith('blob:')) {
                URL.revokeObjectURL(download.url);
            }
        }
        this.activeDownloads.clear();
        
        // 调用父类清理
        super.cleanup();
    }
}
// #endregion

// #region 工厂函数和预设
/**
 * 创建图片导出器 - 工厂函数，创建配置好的图片导出器实例
 * @param {Object} config - 导出器配置
 * @returns {ImageExporter} 图片导出器实例
 */
export function createImageExporter(config = {}) {
    return new ImageExporter(config);
}

/**
 * 创建预设图片导出器 - 使用预设配置创建图片导出器
 * @param {string} preset - 预设名称 ('default', 'high-quality', 'web', 'thumbnail', 'print')
 * @param {Object} customConfig - 自定义配置
 * @returns {ImageExporter} 配置好的图片导出器实例
 */
export function createPresetImageExporter(preset = 'default', customConfig = {}) {
    const presets = {
        'default': {
            format: 'png',
            quality: 0.92,
            scale: 1,
            backgroundColor: '#ffffff'
        },
        'high-quality': {
            format: 'png',
            quality: 1.0,
            scale: 2,
            backgroundColor: '#ffffff'
        },
        'web': {
            format: 'jpeg',
            quality: 0.85,
            scale: 1,
            backgroundColor: '#ffffff',
            width: 1200
        },
        'thumbnail': {
            format: 'jpeg',
            quality: 0.8,
            scale: 0.5,
            backgroundColor: '#ffffff',
            width: 400
        },
        'print': {
            format: 'png',
            quality: 1.0,
            scale: 3,
            backgroundColor: '#ffffff'
        }
    };
    
    const presetConfig = presets[preset];
    if (!presetConfig) {
        throw new Error(`不支持的图片导出器预设: ${preset}`);
    }
    
    return createImageExporter({
        name: `image-exporter-${preset}`,
        description: `${preset}预设图片导出器`,
        ...presetConfig,
        ...customConfig
    });
}

/**
 * 便捷图片导出函数 - 快速导出图片文档
 * @param {Object} content - 要导出的内容
 * @param {Object} options - 导出选项
 * @returns {Promise<Object>} 导出结果
 */
export async function exportToImage(content, options = {}) {
    const exporter = createImageExporter(options.exporterConfig);
    
    try {
        return await exporter.export(content, options);
    } finally {
        exporter.cleanup();
    }
}
// #endregion 