/**
 * @file 输入框组件 - 可复用的输入框UI组件
 * <AUTHOR> Team
 * @description 
 * 这个文件实现了输入框组件，提供：
 * - Input 基础输入框组件
 * - TextArea 文本域组件
 * - Select 选择框组件
 * - 实时验证和错误提示
 * - 多种输入类型和格式支持
 */

// #region 导入依赖模块
import { BaseComponent } from './base-component.js';
import { UI_EVENTS } from '../../core/events/event-types.js';
import { validateNonEmpty, validateEmail, validatePhone } from '../../core/utils/validation.js';
// #endregion

// #region Input 输入框组件
/**
 * @class Input - 输入框组件
 * @description 可复用的输入框组件，支持多种输入类型、验证和格式化
 */
export class Input extends BaseComponent {
    /**
     * 构造函数 - 初始化输入框组件
     * @param {Object} config - 输入框配置
     * @param {string} config.type - 输入类型 ('text', 'email', 'password', 'number', 'tel', 'url', 'search')
     * @param {string} config.value - 输入值
     * @param {string} config.placeholder - 占位符文本
     * @param {string} config.label - 标签文本
     * @param {boolean} config.required - 是否必填
     * @param {boolean} config.readonly - 是否只读
     * @param {number} config.maxLength - 最大长度
     * @param {string} config.pattern - 验证模式
     * @param {Function} config.validator - 自定义验证函数
     * @param {Function} config.onChange - 值改变事件处理函数
     */
    constructor(config = {}) {
        super({
            type: 'input',
            name: config.name || 'input',
            ...config
        });
        
        // 输入框特有属性
        this.inputProps = {
            type: config.type || 'text',
            value: config.value || '',
            placeholder: config.placeholder || '',
            label: config.label || '',
            required: config.required || false,
            readonly: config.readonly || false,
            maxLength: config.maxLength || null,
            pattern: config.pattern || '',
            autocomplete: config.autocomplete || 'off',
            size: config.size || 'md',
            ...config.inputProps
        };
        
        // 验证配置
        this.validation = {
            validator: config.validator || null,
            validateOnChange: config.validateOnChange !== false,
            validateOnBlur: config.validateOnBlur !== false,
            showValidationMessage: config.showValidationMessage !== false,
            ...config.validation
        };
        
        // 事件处理
        this.changeHandler = config.onChange || null;
        this.inputHandler = config.onInput || null;
        this.focusHandler = config.onFocus || null;
        this.blurHandler = config.onBlur || null;
        
        // 输入框状态
        this.inputState = {
            focused: false,
            valid: true,
            validationMessage: '',
            touched: false,
            dirty: false,
            ...config.inputState
        };
        
        // 合并到组件状态
        this.state = { ...this.state, ...this.inputState };
        
        // 验证配置
        this._validateInputConfig();
    }

    /**
     * 验证输入框配置 - 验证输入框特有的配置
     * @private
     */
    _validateInputConfig() {
        // 验证输入类型
        const validTypes = ['text', 'email', 'password', 'number', 'tel', 'url', 'search', 'date', 'time'];
        if (!validTypes.includes(this.inputProps.type)) {
            console.warn(`无效的输入类型: ${this.inputProps.type}，将使用默认值 'text'`);
            this.inputProps.type = 'text';
        }
        
        // 验证大小
        const validSizes = ['xs', 'sm', 'md', 'lg', 'xl'];
        if (!validSizes.includes(this.inputProps.size)) {
            console.warn(`无效的输入框大小: ${this.inputProps.size}，将使用默认值 'md'`);
            this.inputProps.size = 'md';
        }
    }

    /**
     * 渲染输入框 - 创建输入框DOM元素
     * @returns {Promise<HTMLElement>} 输入框容器元素
     */
    async render() {
        // 创建输入框容器
        const container = document.createElement('div');
        container.id = this.id;
        
        // 创建标签
        if (this.inputProps.label) {
            const label = this._createLabel();
            container.appendChild(label);
        }
        
        // 创建输入框包装器
        const wrapper = this._createInputWrapper();
        container.appendChild(wrapper);
        
        // 创建输入框
        const input = this._createInput();
        wrapper.appendChild(input);
        
        // 创建验证消息容器
        if (this.validation.showValidationMessage) {
            const validationMessage = this._createValidationMessage();
            container.appendChild(validationMessage);
        }
        
        return container;
    }

    /**
     * 创建标签元素 - 创建输入框标签
     * @returns {HTMLElement} 标签元素
     * @private
     */
    _createLabel() {
        const label = document.createElement('label');
        label.className = 'input-label';
        label.textContent = this.inputProps.label;
        label.setAttribute('for', `${this.id}-input`);
        
        // 添加必填标记
        if (this.inputProps.required) {
            const required = document.createElement('span');
            required.className = 'required-mark';
            required.textContent = ' *';
            label.appendChild(required);
        }
        
        return label;
    }

    /**
     * 创建输入框包装器 - 创建输入框的包装容器
     * @returns {HTMLElement} 包装器元素
     * @private
     */
    _createInputWrapper() {
        const wrapper = document.createElement('div');
        wrapper.className = 'input-wrapper';
        return wrapper;
    }

    /**
     * 创建输入框元素 - 创建实际的输入框
     * @returns {HTMLElement} 输入框元素
     * @private
     */
    _createInput() {
        const input = document.createElement('input');
        input.id = `${this.id}-input`;
        input.type = this.inputProps.type;
        input.value = this.inputProps.value;
        input.placeholder = this.inputProps.placeholder;
        input.readOnly = this.inputProps.readonly;
        input.autocomplete = this.inputProps.autocomplete;
        input.className = 'input-field';
        
        // 设置属性
        if (this.inputProps.required) {
            input.required = true;
        }
        
        if (this.inputProps.maxLength) {
            input.maxLength = this.inputProps.maxLength;
        }
        
        if (this.inputProps.pattern) {
            input.pattern = this.inputProps.pattern;
        }
        
        // 设置ARIA属性
        input.setAttribute('aria-label', this.inputProps.label || this.inputProps.placeholder);
        
        if (!this.state.valid) {
            input.setAttribute('aria-invalid', 'true');
            input.setAttribute('aria-describedby', `${this.id}-validation`);
        }
        
        return input;
    }

    /**
     * 创建验证消息容器 - 创建显示验证消息的容器
     * @returns {HTMLElement} 验证消息容器
     * @private
     */
    _createValidationMessage() {
        const message = document.createElement('div');
        message.id = `${this.id}-validation`;
        message.className = 'validation-message';
        message.style.display = 'none';
        return message;
    }

    /**
     * 应用样式 - 应用输入框特有的样式类
     * @private
     */
    _applyStyles() {
        if (!this.element) {
            return;
        }
        
        // 构建样式类列表
        const styleClasses = [
            'smartoffice-input',
            'form-group',
            `input-${this.inputProps.size}`,
            this.state.focused ? 'focused' : '',
            this.state.valid ? 'valid' : 'invalid',
            this.state.enabled ? '' : 'disabled',
            this.state.visible ? '' : 'hidden',
            this.inputProps.required ? 'required' : '',
            this.props.className
        ].filter(Boolean);
        
        // 应用样式类
        this.element.className = styleClasses.join(' ');
        
        // 更新输入框样式
        const input = this.element.querySelector('.input-field');
        if (input) {
            const inputClasses = [
                'input-field',
                'form-control',
                `form-control-${this.inputProps.size}`,
                this.state.valid ? '' : 'is-invalid',
                this.inputProps.readonly ? 'readonly' : ''
            ].filter(Boolean);
            
            input.className = inputClasses.join(' ');
        }
        
        // 设置自定义样式
        if (this.props.style) {
            Object.assign(this.element.style, this.props.style);
        }
    }

    /**
     * 绑定组件特定事件 - 绑定输入框特有的事件
     * @protected
     */
    _bindComponentEvents() {
        const input = this.element?.querySelector('.input-field');
        if (!input) {
            return;
        }
        
        // 绑定输入事件
        this._addEventListener(input, 'input', (event) => {
            this._handleInput(event);
        });
        
        // 绑定改变事件
        this._addEventListener(input, 'change', (event) => {
            this._handleChange(event);
        });
        
        // 绑定焦点事件
        this._addEventListener(input, 'focus', (event) => {
            this._handleFocus(event);
        });
        
        this._addEventListener(input, 'blur', (event) => {
            this._handleBlur(event);
        });
        
        // 绑定键盘事件
        this._addEventListener(input, 'keydown', (event) => {
            this._handleKeyDown(event);
        });
    }

    /**
     * 处理输入事件 - 处理实时输入
     * @param {Event} event - 输入事件
     * @private
     */
    _handleInput(event) {
        const value = event.target.value;
        
        // 更新值
        this.inputProps.value = value;
        this.state.dirty = true;
        
        // 实时验证
        if (this.validation.validateOnChange) {
            this._validateValue(value);
        }
        
        // 触发输入事件
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            event,
            value,
            inputType: 'input'
        });
        
        // 执行输入处理函数
        if (this.inputHandler) {
            try {
                this.inputHandler(event, value, this);
            } catch (error) {
                this.emit(UI_EVENTS.COMPONENT_ERROR, {
                    component: this,
                    error: error.message,
                    phase: 'input'
                });
            }
        }
    }

    /**
     * 处理改变事件 - 处理值改变
     * @param {Event} event - 改变事件
     * @private
     */
    _handleChange(event) {
        const value = event.target.value;
        
        // 更新值
        this.inputProps.value = value;
        
        // 触发改变事件
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            event,
            value,
            inputType: 'change'
        });
        
        // 执行改变处理函数
        if (this.changeHandler) {
            try {
                this.changeHandler(event, value, this);
            } catch (error) {
                this.emit(UI_EVENTS.COMPONENT_ERROR, {
                    component: this,
                    error: error.message,
                    phase: 'change'
                });
            }
        }
    }

    /**
     * 处理焦点事件 - 处理获得焦点
     * @param {Event} event - 焦点事件
     * @private
     */
    _handleFocus(event) {
        this.update({}, { focused: true, touched: true });
        
        // 触发焦点事件
        this.emit(UI_EVENTS.COMPONENT_FOCUSED, {
            component: this,
            event
        });
        
        // 执行焦点处理函数
        if (this.focusHandler) {
            try {
                this.focusHandler(event, this);
            } catch (error) {
                this.emit(UI_EVENTS.COMPONENT_ERROR, {
                    component: this,
                    error: error.message,
                    phase: 'focus'
                });
            }
        }
    }

    /**
     * 处理失焦事件 - 处理失去焦点
     * @param {Event} event - 失焦事件
     * @private
     */
    _handleBlur(event) {
        this.update({}, { focused: false });
        
        // 失焦验证
        if (this.validation.validateOnBlur) {
            this._validateValue(event.target.value);
        }
        
        // 触发失焦事件
        this.emit(UI_EVENTS.COMPONENT_BLURRED, {
            component: this,
            event
        });
        
        // 执行失焦处理函数
        if (this.blurHandler) {
            try {
                this.blurHandler(event, this);
            } catch (error) {
                this.emit(UI_EVENTS.COMPONENT_ERROR, {
                    component: this,
                    error: error.message,
                    phase: 'blur'
                });
            }
        }
    }

    /**
     * 处理键盘事件 - 处理键盘交互
     * @param {KeyboardEvent} event - 键盘事件
     * @private
     */
    _handleKeyDown(event) {
        // 回车键触发提交
        if (event.key === 'Enter' && this.inputProps.type !== 'textarea') {
            this.emit(UI_EVENTS.FORM_SUBMIT, {
                component: this,
                event,
                value: this.inputProps.value
            });
        }
        
        // Escape键清空输入
        if (event.key === 'Escape') {
            this.setValue('');
            this.blur();
        }
    }

    /**
     * 验证输入值 - 验证当前输入值
     * @param {string} value - 要验证的值
     * @returns {boolean} 是否有效
     * @private
     */
    _validateValue(value) {
        let isValid = true;
        let message = '';
        
        try {
            // 必填验证
            if (this.inputProps.required && !value.trim()) {
                isValid = false;
                message = '此字段为必填项';
            }
            
            // 类型验证
            if (isValid && value) {
                switch (this.inputProps.type) {
                    case 'email':
                        if (!validateEmail(value)) {
                            isValid = false;
                            message = '请输入有效的邮箱地址';
                        }
                        break;
                    case 'tel':
                        if (!validatePhone(value)) {
                            isValid = false;
                            message = '请输入有效的电话号码';
                        }
                        break;
                    case 'url':
                        if (!this._validateUrl(value)) {
                            isValid = false;
                            message = '请输入有效的URL地址';
                        }
                        break;
                    case 'number':
                        if (isNaN(value) || isNaN(parseFloat(value))) {
                            isValid = false;
                            message = '请输入有效的数字';
                        }
                        break;
                }
            }
            
            // 模式验证
            if (isValid && value && this.inputProps.pattern) {
                const regex = new RegExp(this.inputProps.pattern);
                if (!regex.test(value)) {
                    isValid = false;
                    message = '输入格式不正确';
                }
            }
            
            // 长度验证
            if (isValid && this.inputProps.maxLength && value.length > this.inputProps.maxLength) {
                isValid = false;
                message = `输入内容不能超过 ${this.inputProps.maxLength} 个字符`;
            }
            
            // 自定义验证
            if (isValid && this.validation.validator) {
                const result = this.validation.validator(value, this);
                if (result !== true) {
                    isValid = false;
                    message = typeof result === 'string' ? result : '输入值无效';
                }
            }
            
        } catch (error) {
            isValid = false;
            message = '验证过程中出现错误';
            console.error('输入验证错误:', error);
        }
        
        // 更新验证状态
        this.update({}, {
            valid: isValid,
            validationMessage: message
        });
        
        // 更新UI显示
        this._updateValidationDisplay(isValid, message);
        
        // 触发验证事件
        if (isValid) {
            this.emit(UI_EVENTS.FORM_VALIDATION_PASSED, {
                component: this,
                value
            });
        } else {
            this.emit(UI_EVENTS.FORM_VALIDATION_FAILED, {
                component: this,
                value,
                message
            });
        }
        
        return isValid;
    }

    /**
     * 验证URL - 验证URL格式
     * @param {string} url - URL字符串
     * @returns {boolean} 是否有效
     * @private
     */
    _validateUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * 更新验证显示 - 更新验证消息的显示
     * @param {boolean} isValid - 是否有效
     * @param {string} message - 验证消息
     * @private
     */
    _updateValidationDisplay(isValid, message) {
        const input = this.element?.querySelector('.input-field');
        const messageElement = this.element?.querySelector('.validation-message');
        
        if (input) {
            if (isValid) {
                input.classList.remove('is-invalid');
                input.removeAttribute('aria-invalid');
                input.removeAttribute('aria-describedby');
            } else {
                input.classList.add('is-invalid');
                input.setAttribute('aria-invalid', 'true');
                input.setAttribute('aria-describedby', `${this.id}-validation`);
            }
        }
        
        if (messageElement) {
            if (message && !isValid) {
                messageElement.textContent = message;
                messageElement.style.display = 'block';
            } else {
                messageElement.textContent = '';
                messageElement.style.display = 'none';
            }
        }
    }

    /**
     * 设置输入值 - 设置输入框的值
     * @param {string} value - 新的值
     */
    setValue(value) {
        this.inputProps.value = String(value);
        
        const input = this.element?.querySelector('.input-field');
        if (input) {
            input.value = this.inputProps.value;
        }
        
        // 触发验证
        if (this.validation.validateOnChange) {
            this._validateValue(this.inputProps.value);
        }
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            value: this.inputProps.value,
            inputType: 'programmatic'
        });
    }

    /**
     * 获取输入值 - 获取当前输入值
     * @returns {string} 当前值
     */
    getValue() {
        return this.inputProps.value;
    }

    /**
     * 清空输入 - 清空输入框内容
     */
    clear() {
        this.setValue('');
    }

    /**
     * 聚焦输入框 - 让输入框获得焦点
     */
    focus() {
        const input = this.element?.querySelector('.input-field');
        if (input) {
            input.focus();
        }
    }

    /**
     * 失焦输入框 - 让输入框失去焦点
     */
    blur() {
        const input = this.element?.querySelector('.input-field');
        if (input) {
            input.blur();
        }
    }

    /**
     * 验证输入 - 手动触发验证
     * @returns {boolean} 是否有效
     */
    validate() {
        return this._validateValue(this.inputProps.value);
    }

    /**
     * 重置输入 - 重置输入框到初始状态
     */
    reset() {
        this.setValue('');
        this.update({}, {
            valid: true,
            validationMessage: '',
            touched: false,
            dirty: false,
            focused: false
        });
        
        this._updateValidationDisplay(true, '');
    }

    /**
     * 获取输入框信息 - 获取输入框的详细信息
     * @returns {Object} 输入框信息
     */
    getInputInfo() {
        return {
            ...this._getComponentInfo(),
            inputProps: { ...this.inputProps },
            inputState: {
                focused: this.state.focused,
                valid: this.state.valid,
                validationMessage: this.state.validationMessage,
                touched: this.state.touched,
                dirty: this.state.dirty
            },
            validation: { ...this.validation }
        };
    }
}
// #endregion

// #region TextArea 文本域组件
/**
 * @class TextArea - 文本域组件
 * @description 多行文本输入组件
 */
export class TextArea extends Input {
    /**
     * 构造函数 - 初始化文本域组件
     * @param {Object} config - 文本域配置
     * @param {number} config.rows - 行数
     * @param {number} config.cols - 列数
     * @param {boolean} config.autoResize - 是否自动调整大小
     */
    constructor(config = {}) {
        super({
            type: 'textarea',
            ...config
        });
        
        // 文本域特有属性
        this.textareaProps = {
            rows: config.rows || 3,
            cols: config.cols || null,
            autoResize: config.autoResize || false,
            ...config.textareaProps
        };
    }

    /**
     * 创建输入框元素 - 创建文本域元素
     * @returns {HTMLElement} 文本域元素
     * @private
     */
    _createInput() {
        const textarea = document.createElement('textarea');
        textarea.id = `${this.id}-input`;
        textarea.value = this.inputProps.value;
        textarea.placeholder = this.inputProps.placeholder;
        textarea.readOnly = this.inputProps.readonly;
        textarea.rows = this.textareaProps.rows;
        textarea.className = 'input-field';
        
        if (this.textareaProps.cols) {
            textarea.cols = this.textareaProps.cols;
        }
        
        // 设置属性
        if (this.inputProps.required) {
            textarea.required = true;
        }
        
        if (this.inputProps.maxLength) {
            textarea.maxLength = this.inputProps.maxLength;
        }
        
        // 设置ARIA属性
        textarea.setAttribute('aria-label', this.inputProps.label || this.inputProps.placeholder);
        
        return textarea;
    }

    /**
     * 绑定组件特定事件 - 绑定文本域特有的事件
     * @protected
     */
    _bindComponentEvents() {
        super._bindComponentEvents();
        
        const textarea = this.element?.querySelector('.input-field');
        if (textarea && this.textareaProps.autoResize) {
            // 绑定自动调整大小事件
            this._addEventListener(textarea, 'input', () => {
                this._autoResize(textarea);
            });
        }
    }

    /**
     * 自动调整大小 - 根据内容自动调整文本域高度
     * @param {HTMLElement} textarea - 文本域元素
     * @private
     */
    _autoResize(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 'px';
    }
}
// #endregion

// #region Select 选择框组件
/**
 * @class Select - 选择框组件
 * @description 下拉选择框组件
 */
export class Select extends Input {
    /**
     * 构造函数 - 初始化选择框组件
     * @param {Object} config - 选择框配置
     * @param {Array<Object>} config.options - 选项列表
     * @param {boolean} config.multiple - 是否多选
     * @param {string} config.emptyOption - 空选项文本
     */
    constructor(config = {}) {
        super({
            type: 'select',
            ...config
        });
        
        // 选择框特有属性
        this.selectProps = {
            options: config.options || [],
            multiple: config.multiple || false,
            emptyOption: config.emptyOption || '请选择...',
            ...config.selectProps
        };
    }

    /**
     * 创建输入框元素 - 创建选择框元素
     * @returns {HTMLElement} 选择框元素
     * @private
     */
    _createInput() {
        const select = document.createElement('select');
        select.id = `${this.id}-input`;
        select.value = this.inputProps.value;
        select.className = 'input-field';
        select.multiple = this.selectProps.multiple;
        
        // 添加空选项
        if (!this.selectProps.multiple && this.selectProps.emptyOption) {
            const emptyOption = document.createElement('option');
            emptyOption.value = '';
            emptyOption.textContent = this.selectProps.emptyOption;
            select.appendChild(emptyOption);
        }
        
        // 添加选项
        this.selectProps.options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value || option;
            optionElement.textContent = option.label || option.text || option;
            
            if (option.disabled) {
                optionElement.disabled = true;
            }
            
            if (this.selectProps.multiple) {
                if (Array.isArray(this.inputProps.value) && this.inputProps.value.includes(optionElement.value)) {
                    optionElement.selected = true;
                }
            } else {
                if (this.inputProps.value === optionElement.value) {
                    optionElement.selected = true;
                }
            }
            
            select.appendChild(optionElement);
        });
        
        // 设置属性
        if (this.inputProps.required) {
            select.required = true;
        }
        
        // 设置ARIA属性
        select.setAttribute('aria-label', this.inputProps.label || '选择选项');
        
        return select;
    }

    /**
     * 处理改变事件 - 处理选择框值改变
     * @param {Event} event - 改变事件
     * @private
     */
    _handleChange(event) {
        let value;
        
        if (this.selectProps.multiple) {
            value = Array.from(event.target.selectedOptions).map(option => option.value);
        } else {
            value = event.target.value;
        }
        
        // 更新值
        this.inputProps.value = value;
        
        // 触发改变事件
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            event,
            value,
            inputType: 'change'
        });
        
        // 执行改变处理函数
        if (this.changeHandler) {
            try {
                this.changeHandler(event, value, this);
            } catch (error) {
                this.emit(UI_EVENTS.COMPONENT_ERROR, {
                    component: this,
                    error: error.message,
                    phase: 'change'
                });
            }
        }
    }

    /**
     * 设置选项 - 更新选择框选项
     * @param {Array<Object>} options - 新的选项列表
     */
    setOptions(options) {
        this.selectProps.options = options;
        
        const select = this.element?.querySelector('.input-field');
        if (select) {
            // 清空现有选项
            select.innerHTML = '';
            
            // 重新创建选项
            const newSelect = this._createInput();
            select.parentNode.replaceChild(newSelect, select);
            
            // 重新绑定事件
            this._bindComponentEvents();
        }
    }

    /**
     * 获取选中的选项 - 获取当前选中的选项
     * @returns {Array<Object>} 选中的选项对象
     */
    getSelectedOptions() {
        const value = this.inputProps.value;
        
        if (this.selectProps.multiple) {
            return this.selectProps.options.filter(option => 
                value.includes(option.value || option)
            );
        } else {
            return this.selectProps.options.filter(option => 
                (option.value || option) === value
            );
        }
    }
}
// #endregion

// #region 工厂函数
/**
 * 创建输入框 - 工厂函数，创建输入框实例
 * @param {Object} config - 输入框配置
 * @returns {Input} 输入框实例
 */
export function createInput(config = {}) {
    if (config.type === 'textarea') {
        return new TextArea(config);
    } else if (config.type === 'select') {
        return new Select(config);
    } else {
        return new Input(config);
    }
}

/**
 * 创建文本域 - 工厂函数，创建文本域实例
 * @param {Object} config - 文本域配置
 * @returns {TextArea} 文本域实例
 */
export function createTextArea(config = {}) {
    return new TextArea(config);
}

/**
 * 创建选择框 - 工厂函数，创建选择框实例
 * @param {Object} config - 选择框配置
 * @returns {Select} 选择框实例
 */
export function createSelect(config = {}) {
    return new Select(config);
}

/**
 * 创建预设输入框 - 创建预设类型的输入框
 * @param {string} preset - 预设名称
 * @param {Object} config - 额外配置
 * @returns {Input} 输入框实例
 */
export function createPresetInput(preset, config = {}) {
    const presetConfigs = {
        'email': { type: 'email', placeholder: '请输入邮箱地址' },
        'password': { type: 'password', placeholder: '请输入密码' },
        'phone': { type: 'tel', placeholder: '请输入电话号码' },
        'url': { type: 'url', placeholder: '请输入网址' },
        'number': { type: 'number', placeholder: '请输入数字' },
        'search': { type: 'search', placeholder: '搜索...' },
        'date': { type: 'date' },
        'time': { type: 'time' }
    };
    
    const presetConfig = presetConfigs[preset];
    if (!presetConfig) {
        throw new Error(`不支持的输入框预设: ${preset}`);
    }
    
    return createInput({
        ...presetConfig,
        ...config
    });
}
// #endregion 