/**
 * @file 本地自然语言处理器
 * @description 替代Gemini API的本地自然语言处理功能
 * - 使用正则表达式和关键词匹配
 * - 支持中英文双语解析
 * - 完全离线工作，无需API调用
 * <AUTHOR> Team
 */

import { getLogger } from './logger.js';

/**
 * @class LocalNLPProcessor
 * @description 本地自然语言处理器类
 * 提供文本解析、信息提取、文档类型识别等功能
 */
export class LocalNLPProcessor {
    constructor() {
        this.logger = getLogger();
        this.logger.info('LocalNLPProcessor', 'constructor', '🧠 初始化本地自然语言处理器');
        
        // 初始化模式匹配规则
        this._initializePatterns();
        
        // 初始化关键词映射
        this._initializeKeywordMaps();
        
        // 性能统计
        this.stats = {
            processedTexts: 0,
            extractedFields: 0,
            processingTime: 0
        };
        
        this.logger.info('LocalNLPProcessor', 'constructor', '✅ 本地NLP处理器初始化完成');
    }

    /**
     * @function _initializePatterns
     * @description 初始化正则表达式模式 - 增强版本，支持更精确的模板字段识别
     */
    _initializePatterns() {
        this.patterns = {
            // 金额模式 - 支持多种货币格式和表达方式
            amount: [
                // 标准货币格式
                /(?:RM|令吉|马币)\s*(\d+(?:\.\d{1,2})?)/gi,
                /(?:RMB|人民币|元)\s*(\d+(?:\.\d{1,2})?)/gi,
                /(\d+(?:\.\d{1,2})?)\s*(?:RM|令吉|马币)/gi,
                /(\d+(?:\.\d{1,2})?)\s*(?:RMB|人民币|元)/gi,
                // 金额标识词
                /(?:总计|合计|总额|金额|价格|费用|应付|实付|支付)[:：\s]*(\d+(?:\.\d{1,2})?)/gi,
                // 订单金额
                /(?:订单金额|实际支付|支付金额|总价)[:：\s]*(\d+(?:\.\d{1,2})?)/gi,
                // 纯数字金额（在特定上下文中）
                /(?:^|\s)(\d{2,6}(?:\.\d{1,2})?)(?=\s*(?:元|RM|$))/gm
            ],
            
            // 数量模式 - 增强对不同服务类型的识别
            quantity: [
                /(\d+)\s*(?:晚|夜|天|日|次|个|人|位|座|辆|台)/gi,
                /(?:数量|晚数|天数|人数|座位数)[:：\s]*(\d+)/gi,
                // 特定服务数量
                /(\d+)\s*(?:座|人座|座位)/gi,
                /(?:共|总共)\s*(\d+)\s*(?:天|晚|次|人)/gi
            ],
            
            // 日期模式 - 支持更多日期格式
            date: [
                // 标准格式
                /(\d{4}[-\/]\d{1,2}[-\/]\d{1,2})/g,
                /(\d{1,2}[-\/]\d{1,2}[-\/]\d{4})/g,
                /(\d{4}年\d{1,2}月\d{1,2}日)/g,
                // 支付时间等特定格式
                /(?:支付时间|下单时间|预订时间)[:：\s]*(\d{4}[-\/]\d{1,2}[-\/]\d{1,2})/gi,
                // 月日格式
                /(\d{1,2}月\d{1,2}日)/g,
                // ISO格式
                /(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})/g
            ],
            
            // 姓名模式 - 增强对不同平台用户名的识别
            name: [
                // 标准姓名标识
                /(?:客户|姓名|乘客|司机|联系人|买家|用户)[:：\s]*([^\s,，。.]{2,15})/gi,
                // 敬语后缀
                /([^\s,，。.]{2,10})(?:先生|女士|小姐|师傅)/gi,
                // 平台用户名
                /(?:买家|用户)[:：\s]*([a-zA-Z0-9_\u4e00-\u9fa5]{2,20})/gi,
                // 淘宝等平台格式
                /tb\d+|[a-zA-Z0-9_]{6,20}/g,
                // 中文姓名（2-4个字符）
                /[\u4e00-\u9fa5]{2,4}(?=\s|$|[，。,.])/g
            ],
            
            // 电话模式 - 增强国际号码识别
            phone: [
                /(?:电话|手机|联系方式|联系电话)[:：\s]*([\d\-\+\s\(\)]{8,20})/gi,
                // 马来西亚手机号
                /((?:\+?60|0)1[0-9][\d\-\s]{7,12})/g,
                // 中国手机号
                /((?:\+?86|0)?1[3-9]\d{9})/g,
                // 通用格式
                /(\+?[\d\-\s\(\)]{8,20})/g
            ],
            
            // 订单/编号模式 - 新增
            orderNumber: [
                /(?:订单编号|订单号|单号)[:：\s]*([A-Z0-9]{10,30})/gi,
                /(?:编号|NO|Number)[:：\s]*([A-Z0-9]{6,20})/gi,
                // 数字编号
                /\b(\d{10,20})\b/g
            ],
            
            // 身份证/护照模式
            idNumber: [
                /(?:身份证|护照|ID)[:：\s]*([A-Z0-9\-]{6,20})/gi
            ],
            
            // 车牌号模式 - 增强识别
            plateNumber: [
                /(?:车牌|车号|车辆号码)[:：\s]*([A-Z0-9\-]{3,10})/gi,
                // 马来西亚车牌格式
                /([A-Z]{1,3}\s*\d{1,4}\s*[A-Z]?)/g,
                // 中国车牌格式
                /([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4,5}[A-Z0-9挂学警港澳])/g
            ],
            
            // 地址模式 - 增强位置识别
            address: [
                /(?:地址|位置|酒店|目的地|出发地|到达地)[:：\s]*([^,，。.]{5,50})/gi,
                // 机场相关
                /([^,，。.]*(?:机场|Airport)[^,，。.]*)/gi,
                // 酒店相关
                /([^,，。.]*(?:酒店|Hotel|Resort)[^,，。.]*)/gi,
                // 大学等地标
                /([^,，。.]*(?:大学|University|学院)[^,，。.]*)/gi
            ],
            
            // 服务项目模式 - 大幅增强
            service: [
                // 交通服务
                /(?:接机|送机|包车|租车|专车|出租车|的士)/gi,
                // 住宿服务
                /(?:酒店|住宿|民宿|旅馆|Resort|Hotel)/gi,
                // 旅游服务
                /(?:导游|门票|景点|旅游|观光|一日游)/gi,
                // 餐饮服务
                /(?:餐饮|用餐|早餐|午餐|晚餐|自助餐)/gi,
                // 座位类型
                /(?:舒适|豪华|经济|商务)\s*(?:\d+座|座位)/gi,
                // 具体服务描述
                /【[^】]*】/g, // 方括号内的服务描述
                /\[[^\]]*\]/g  // 方括号内的服务描述
            ],
            
            // 平台/渠道识别模式 - 新增
            platform: [
                /(?:飞猪|fliggy|天猫|淘宝|taobao)/gi,
                /(?:携程|ctrip|去哪儿|qunar)/gi,
                /(?:美团|meituan|大众点评)/gi,
                /(?:booking|agoda|expedia)/gi
            ]
        };
    }

    /**
     * @function _initializeKeywordMaps
     * @description 初始化关键词映射表 - 增强版本，支持更精确的模板字段识别
     */
    _initializeKeywordMaps() {
        // 文档类型关键词 - 增强识别
        this.documentTypeKeywords = {
            receipt: ['收据', 'receipt', '收款', '付款凭证', '支付凭证', '交易记录'],
            invoice: ['发票', 'invoice', '税务', '开票', '发票申请', '税务发票'],
            quotation: ['报价', 'quotation', '预估', '报价单', '估价', '预算'],
            driver_agreement: ['司机', 'driver', '协议', 'agreement', '合同', '司机合同', '驾驶员协议']
        };
        
        // 货币类型关键词 - 增强识别
        this.currencyKeywords = {
            'RM': ['RM', '令吉', '马币', '马来西亚', 'MYR', 'Malaysian Ringgit'],
            'RMB': ['RMB', '人民币', '元', '中国', 'CNY', 'Chinese Yuan'],
            'USD': ['USD', '美元', 'Dollar', '$'],
            'SGD': ['SGD', '新币', 'Singapore Dollar']
        };
        
        // 支付方式关键词 - 增强识别
        this.paymentMethodKeywords = {
            'online': ['线上', 'online', '在线', '网上', '支付宝', '微信', '网银', '电子支付'],
            'bank': ['银行', 'bank', '转账', 'transfer', '银行转账', '汇款'],
            'platform': ['平台', 'platform', '代理', 'agent', '第三方', '中介'],
            'cash': ['现金', 'cash', '现付', '当面付'],
            'card': ['刷卡', 'card', '信用卡', '银行卡']
        };
        
        // 渠道关键词 - 大幅增强
        this.channelKeywords = {
            '飞猪国际': ['飞猪', 'fliggy', '阿里飞猪', 'alitrip'],
            '天空号国际': ['天空号', 'skyline', 'sky'],
            'Whatsapp': ['whatsapp', 'wa', 'whatapp', 'wechat', '微信'],
            'GoMyHire Webpage': ['gomyhire', '官网', 'webpage', 'website', 'gmh'],
            'BNI Member': ['bni', 'member', 'BNI会员'],
            'Wilson': ['wilson', 'Wilson代理'],
            'Wiracle': ['wiracle', 'Wiracle平台'],
            'SMW Agent': ['smw', 'agent', 'SMW代理'],
            'Travel Agency': ['travel', 'agency', '旅行社', '旅游公司', '旅游代理'],
            '携程': ['携程', 'ctrip', 'trip'],
            '去哪儿': ['去哪儿', 'qunar'],
            '美团': ['美团', 'meituan'],
            'Booking': ['booking', 'booking.com'],
            'Agoda': ['agoda', 'agoda.com'],
            '淘宝': ['淘宝', 'taobao', 'tb'],
            '天猫': ['天猫', 'tmall']
        };
        
        // 服务类型关键词 - 新增
        this.serviceTypeKeywords = {
            'transport': ['接机', '送机', '包车', '租车', '专车', '出租车', '的士', '交通'],
            'accommodation': ['酒店', '住宿', '民宿', '旅馆', 'hotel', 'resort'],
            'tour': ['导游', '旅游', '观光', '一日游', '景点', '门票'],
            'dining': ['餐饮', '用餐', '早餐', '午餐', '晚餐', '自助餐'],
            'other': ['服务', '其他', '杂费', '小费']
        };
        
        // 模板特定字段映射 - 新增
        this.templateFieldMaps = {
            receipt: {
                required: ['customerName', 'totalAmount', 'date'],
                optional: ['receiptNumber', 'paymentMethod', 'channel', 'services']
            },
            invoice: {
                required: ['buyerName', 'totalAmount', 'issueDate'],
                optional: ['invoiceNumber', 'taxRate', 'buyerCompany', 'buyerTaxId']
            },
            quotation: {
                required: ['clientName', 'estimatedAmount', 'quotationDate'],
                optional: ['quotationNumber', 'validUntil', 'preparedBy', 'estimatedDelivery']
            },
            driver_agreement: {
                required: ['driverName', 'driverPhone', 'signingDate'],
                optional: ['agreementNumber', 'vehiclePlate', 'baseSalary', 'effectiveDate']
            }
        };
        
        // 字段优先级权重 - 新增
        this.fieldPriorityWeights = {
            // 高优先级字段
            customerName: 1.0,
            totalAmount: 1.0,
            date: 0.9,
            // 中优先级字段
            services: 0.8,
            paymentMethod: 0.7,
            channel: 0.7,
            // 低优先级字段
            orderNumber: 0.6,
            phone: 0.5,
            address: 0.4
        };
    }

    /**
     * @function processText
     * @description 处理自然语言文本，提取结构化信息
     * @param {string} text - 输入文本
     * @param {string} documentType - 文档类型
     * @returns {Object} 提取的结构化数据
     */
    async processText(text, documentType = 'receipt') {
        const startTime = performance.now();
        this.logger.startPerformanceMark('nlp_process_text', 'LocalNLPProcessor', 'processText');
        
        try {
            this.logger.info('LocalNLPProcessor', 'processText', '🔍 开始处理文本', {
                textLength: text.length,
                documentType
            });
            
            // 预处理文本
            const cleanText = this._preprocessText(text);
            
            // 提取基础信息
            const extractedData = {
                // 基础字段
                amounts: this._extractAmounts(cleanText),
                quantities: this._extractQuantities(cleanText),
                dates: this._extractDates(cleanText),
                names: this._extractNames(cleanText),
                phones: this._extractPhones(cleanText),
                addresses: this._extractAddresses(cleanText),
                orderNumber: this._extractOrderNumbers(cleanText),
                
                // 文档特定字段
                documentType: this._detectDocumentType(cleanText) || documentType,
                currency: this._detectCurrency(cleanText),
                paymentMethod: this._detectPaymentMethod(cleanText),
                channel: this._detectChannel(cleanText),
                services: this._extractServices(cleanText),
                
                // 元数据
                confidence: 0,
                processedAt: new Date().toISOString(),
                originalText: text
            };
            
            // 计算置信度
            extractedData.confidence = this._calculateConfidence(extractedData);
            
            // 根据文档类型优化数据
            const optimizedData = this._optimizeForDocumentType(extractedData, documentType);
            
            // 更新统计
            this.stats.processedTexts++;
            this.stats.extractedFields += Object.keys(optimizedData).length;
            
            const duration = this.logger.endPerformanceMark('nlp_process_text', 'LocalNLPProcessor', 'processText');
            this.stats.processingTime += duration || 0;
            
            this.logger.info('LocalNLPProcessor', 'processText', '✅ 文本处理完成', {
                extractedFields: Object.keys(optimizedData).length,
                confidence: optimizedData.confidence,
                duration: `${duration?.toFixed(2)}ms`
            });
            
            return optimizedData;
            
        } catch (error) {
            this.logger.error('LocalNLPProcessor', 'processText', '文本处理失败', { error });
            throw error;
        }
    }

    /**
     * @function _preprocessText
     * @description 预处理文本
     * @param {string} text - 原始文本
     * @returns {string} 清理后的文本
     */
    _preprocessText(text) {
        return text
            .replace(/[，。；：！？]/g, ',') // 统一标点符号
            .replace(/\s+/g, ' ') // 合并多个空格
            .trim();
    }

    /**
     * @function _extractAmounts
     * @description 提取金额信息
     * @param {string} text - 文本
     * @returns {Array} 金额数组
     */
    _extractAmounts(text) {
        const amounts = [];
        
        this.patterns.amount.forEach(pattern => {
            const matches = [...text.matchAll(pattern)];
            matches.forEach(match => {
                const amount = parseFloat(match[1]);
                if (!isNaN(amount) && amount > 0) {
                    amounts.push(amount);
                }
            });
        });
        
        return [...new Set(amounts)]; // 去重
    }

    /**
     * @function _extractQuantities
     * @description 提取数量信息
     * @param {string} text - 文本
     * @returns {Array} 数量数组
     */
    _extractQuantities(text) {
        const quantities = [];
        
        this.patterns.quantity.forEach(pattern => {
            const matches = [...text.matchAll(pattern)];
            matches.forEach(match => {
                const quantity = parseInt(match[1]);
                if (!isNaN(quantity) && quantity > 0) {
                    quantities.push(quantity);
                }
            });
        });
        
        return [...new Set(quantities)];
    }

    /**
     * @function _extractDates
     * @description 提取日期信息
     * @param {string} text - 文本
     * @returns {Array} 日期数组
     */
    _extractDates(text) {
        const dates = [];
        
        this.patterns.date.forEach(pattern => {
            const matches = [...text.matchAll(pattern)];
            matches.forEach(match => {
                dates.push(match[1]);
            });
        });
        
        return [...new Set(dates)];
    }

    /**
     * @function _extractNames
     * @description 提取姓名信息
     * @param {string} text - 文本
     * @returns {Array} 姓名数组
     */
    _extractNames(text) {
        const names = [];
        
        this.patterns.name.forEach(pattern => {
            const matches = [...text.matchAll(pattern)];
            matches.forEach(match => {
                const name = match[1].trim();
                if (name.length >= 2 && name.length <= 10) {
                    names.push(name);
                }
            });
        });
        
        return [...new Set(names)];
    }

    /**
     * @function _extractPhones
     * @description 提取电话信息
     * @param {string} text - 文本
     * @returns {Array} 电话数组
     */
    _extractPhones(text) {
        const phones = [];
        
        this.patterns.phone.forEach(pattern => {
            const matches = [...text.matchAll(pattern)];
            matches.forEach(match => {
                const phone = match[1].replace(/[\s\-\(\)]/g, '');
                if (phone.length >= 8) {
                    phones.push(phone);
                }
            });
        });
        
        return [...new Set(phones)];
    }

    /**
     * @function _extractAddresses
     * @description 提取地址信息
     * @param {string} text - 文本
     * @returns {Array} 地址数组
     */
    _extractAddresses(text) {
        const addresses = [];
        
        this.patterns.address.forEach(pattern => {
            const matches = [...text.matchAll(pattern)];
            matches.forEach(match => {
                const address = match[1].trim();
                if (address.length >= 5) {
                    addresses.push(address);
                }
            });
        });
        
        return [...new Set(addresses)];
    }

    /**
     * @function _extractServices
     * @description 提取服务项目
     * @param {string} text - 文本
     * @returns {Array} 服务数组
     */
    _extractServices(text) {
        const services = [];
        
        this.patterns.service.forEach(pattern => {
            const matches = [...text.matchAll(pattern)];
            matches.forEach(match => {
                services.push(match[0]);
            });
        });
        
        return [...new Set(services)];
    }

    /**
     * @function _extractOrderNumbers
     * @description 提取订单编号信息
     * @param {string} text - 文本
     * @returns {Array} 订单编号数组
     */
    _extractOrderNumbers(text) {
        const orderNumbers = [];
        
        this.patterns.orderNumber.forEach(pattern => {
            const matches = [...text.matchAll(pattern)];
            matches.forEach(match => {
                const orderNumber = match[1] || match[0];
                if (orderNumber && orderNumber.length >= 6) {
                    orderNumbers.push(orderNumber);
                }
            });
        });
        
        return [...new Set(orderNumbers)];
    }

    /**
     * @function _detectDocumentType
     * @description 检测文档类型
     * @param {string} text - 文本
     * @returns {string|null} 文档类型
     */
    _detectDocumentType(text) {
        const lowerText = text.toLowerCase();
        
        for (const [type, keywords] of Object.entries(this.documentTypeKeywords)) {
            for (const keyword of keywords) {
                if (lowerText.includes(keyword.toLowerCase())) {
                    return type;
                }
            }
        }
        
        return null;
    }

    /**
     * @function _detectCurrency
     * @description 检测货币类型
     * @param {string} text - 文本
     * @returns {string} 货币类型
     */
    _detectCurrency(text) {
        const lowerText = text.toLowerCase();
        
        for (const [currency, keywords] of Object.entries(this.currencyKeywords)) {
            for (const keyword of keywords) {
                if (lowerText.includes(keyword.toLowerCase())) {
                    return currency;
                }
            }
        }
        
        return 'RM'; // 默认货币
    }

    /**
     * @function _detectPaymentMethod
     * @description 检测支付方式
     * @param {string} text - 文本
     * @returns {string} 支付方式
     */
    _detectPaymentMethod(text) {
        const lowerText = text.toLowerCase();
        
        for (const [method, keywords] of Object.entries(this.paymentMethodKeywords)) {
            for (const keyword of keywords) {
                if (lowerText.includes(keyword.toLowerCase())) {
                    return method;
                }
            }
        }
        
        return 'online'; // 默认支付方式
    }

    /**
     * @function _detectChannel
     * @description 检测渠道
     * @param {string} text - 文本
     * @returns {string|null} 渠道
     */
    _detectChannel(text) {
        const lowerText = text.toLowerCase();
        
        for (const [channel, keywords] of Object.entries(this.channelKeywords)) {
            for (const keyword of keywords) {
                if (lowerText.includes(keyword.toLowerCase())) {
                    return channel;
                }
            }
        }
        
        return null;
    }

    /**
     * @function _calculateConfidence
     * @description 计算置信度
     * @param {Object} data - 提取的数据
     * @returns {number} 置信度 (0-1)
     */
    _calculateConfidence(data) {
        let score = 0;
        let maxScore = 0;
        
        // 基础字段权重
        const weights = {
            amounts: 0.3,
            names: 0.2,
            dates: 0.15,
            phones: 0.1,
            addresses: 0.1,
            services: 0.15
        };
        
        for (const [field, weight] of Object.entries(weights)) {
            maxScore += weight;
            if (data[field] && data[field].length > 0) {
                score += weight;
            }
        }
        
        return Math.min(score / maxScore, 1);
    }

    /**
     * @function _optimizeForDocumentType
     * @description 根据文档类型优化数据 - 增强版本，支持更精确的模板字段映射
     * @param {Object} data - 原始数据
     * @param {string} documentType - 文档类型
     * @returns {Object} 优化后的数据
     */
    _optimizeForDocumentType(data, documentType) {
        const optimized = { ...data };
        
        // 获取模板字段映射
        const templateFields = this.templateFieldMaps[documentType] || this.templateFieldMaps.receipt;
        
        // 基础字段映射
        this._mapBasicFields(optimized, data);
        
        // 根据文档类型进行特定优化
        switch (documentType) {
            case 'receipt':
                this._optimizeReceiptFields(optimized, data);
                break;
                
            case 'invoice':
                this._optimizeInvoiceFields(optimized, data);
                break;
                
            case 'quotation':
                this._optimizeQuotationFields(optimized, data);
                break;
                
            case 'driver_agreement':
                this._optimizeDriverAgreementFields(optimized, data);
                break;
        }
        
        // 应用字段优先级权重
        this._applyFieldPriorities(optimized, templateFields);
        
        // 验证必需字段
        this._validateRequiredFields(optimized, templateFields);
        
        return optimized;
    }
    
    /**
     * @function _mapBasicFields
     * @description 映射基础字段
     * @param {Object} optimized - 优化后的数据对象
     * @param {Object} data - 原始数据
     */
    _mapBasicFields(optimized, data) {
        // 映射姓名字段
        if (data.names && data.names.length > 0) {
            optimized.customerName = data.names[0];
            optimized.buyerName = data.names[0];
            optimized.clientName = data.names[0];
            optimized.driverName = data.names[0];
        }
        
        // 映射金额字段
        if (data.amounts && data.amounts.length > 0) {
            optimized.totalAmount = data.amounts[0];
            optimized.estimatedAmount = data.amounts[0];
            optimized.amount = data.amounts[0];
        }
        
        // 映射日期字段
        if (data.dates && data.dates.length > 0) {
            const dateStr = data.dates[0];
            optimized.date = dateStr;
            optimized.issueDate = dateStr;
            optimized.quotationDate = dateStr;
            optimized.signingDate = dateStr;
        }
        
        // 映射电话字段
        if (data.phones && data.phones.length > 0) {
            optimized.phone = data.phones[0];
            optimized.driverPhone = data.phones[0];
        }
        
        // 映射服务项目
        if (data.services && data.services.length > 0) {
            optimized.services = data.services;
            optimized.description = data.services.join(', ');
        }
        
        // 映射订单编号
        if (data.orderNumber && data.orderNumber.length > 0) {
            optimized.orderNumber = data.orderNumber[0];
        }
    }
    
    /**
     * @function _optimizeReceiptFields
     * @description 优化收据字段
     * @param {Object} optimized - 优化后的数据对象
     * @param {Object} data - 原始数据
     */
    _optimizeReceiptFields(optimized, data) {
        // 生成收据编号
        optimized.receiptNumber = this._generateReceiptNumber();
        
        // 确保客户名称存在
        if (!optimized.customerName || optimized.customerName === 'N/A') {
            optimized.customerName = this._extractBestName(data) || 'N/A';
        }
        
        // 设置支付方式
        if (!optimized.paymentMethod) {
            optimized.paymentMethod = data.paymentMethod || 'online';
        }
        
        // 设置渠道
        if (!optimized.channel) {
            optimized.channel = data.channel || 'N/A';
        }
    }
    
    /**
     * @function _optimizeInvoiceFields
     * @description 优化发票字段
     * @param {Object} optimized - 优化后的数据对象
     * @param {Object} data - 原始数据
     */
    _optimizeInvoiceFields(optimized, data) {
        // 生成发票编号
        optimized.invoiceNumber = this._generateInvoiceNumber();
        
        // 确保买方名称存在
        if (!optimized.buyerName || optimized.buyerName === 'N/A') {
            optimized.buyerName = this._extractBestName(data) || 'N/A';
        }
        
        // 计算税额（默认6%）
        if (optimized.totalAmount > 0) {
            optimized.taxRate = 6; // 6%
            optimized.taxAmount = optimized.totalAmount * 0.06;
            optimized.subtotal = optimized.totalAmount - optimized.taxAmount;
        }
        
        // 设置发票日期
        if (!optimized.issueDate) {
            optimized.issueDate = new Date().toISOString().split('T')[0];
        }
    }
    
    /**
     * @function _optimizeQuotationFields
     * @description 优化报价单字段
     * @param {Object} optimized - 优化后的数据对象
     * @param {Object} data - 原始数据
     */
    _optimizeQuotationFields(optimized, data) {
        // 生成报价单编号
        optimized.quotationNumber = this._generateQuotationNumber();
        
        // 确保客户名称存在
        if (!optimized.clientName || optimized.clientName === 'N/A') {
            optimized.clientName = this._extractBestName(data) || 'N/A';
        }
        
        // 设置有效期（默认30天）
        if (!optimized.validUntil) {
            optimized.validUntil = this._getValidUntilDate();
        }
        
        // 设置报价人
        if (!optimized.preparedBy) {
            optimized.preparedBy = 'Sales Team';
        }
        
        // 设置预计交付时间
        if (!optimized.estimatedDelivery) {
            const deliveryDate = new Date();
            deliveryDate.setDate(deliveryDate.getDate() + 7); // 7天后
            optimized.estimatedDelivery = deliveryDate.toISOString().split('T')[0];
        }
    }
    
    /**
     * @function _optimizeDriverAgreementFields
     * @description 优化司机协议字段
     * @param {Object} optimized - 优化后的数据对象
     * @param {Object} data - 原始数据
     */
    _optimizeDriverAgreementFields(optimized, data) {
        // 生成协议编号
        optimized.agreementNumber = this._generateAgreementNumber();
        
        // 确保司机名称存在
        if (!optimized.driverName || optimized.driverName === 'N/A') {
            optimized.driverName = this._extractBestName(data) || 'N/A';
        }
        
        // 确保司机电话存在
        if (!optimized.driverPhone || optimized.driverPhone === 'N/A') {
            optimized.driverPhone = data.phones?.[0] || 'N/A';
        }
        
        // 设置车牌号
        if (!optimized.vehiclePlate) {
            optimized.vehiclePlate = data.plateNumber?.[0] || 'N/A';
        }
        
        // 设置基础工资（如果有金额信息）
        if (optimized.totalAmount > 0) {
            optimized.baseSalary = optimized.totalAmount;
        }
        
        // 设置生效日期
        if (!optimized.effectiveDate) {
            optimized.effectiveDate = new Date().toISOString().split('T')[0];
        }
    }
    
    /**
     * @function _extractBestName
     * @description 提取最佳姓名
     * @param {Object} data - 原始数据
     * @returns {string} 最佳姓名
     */
    _extractBestName(data) {
        if (!data.names || data.names.length === 0) {
            return null;
        }
        
        // 优先选择中文姓名
        const chineseName = data.names.find(name => /[\u4e00-\u9fa5]/.test(name));
        if (chineseName) {
            return chineseName;
        }
        
        // 其次选择最短的名称（通常是真实姓名）
        return data.names.reduce((shortest, current) => 
            current.length < shortest.length ? current : shortest
        );
    }
    
    /**
     * @function _applyFieldPriorities
     * @description 应用字段优先级权重
     * @param {Object} optimized - 优化后的数据对象
     * @param {Object} templateFields - 模板字段配置
     */
    _applyFieldPriorities(optimized, templateFields) {
        // 计算字段置信度
        let totalWeight = 0;
        let achievedWeight = 0;
        
        [...templateFields.required, ...templateFields.optional].forEach(field => {
            const weight = this.fieldPriorityWeights[field] || 0.5;
            totalWeight += weight;
            
            if (optimized[field] && optimized[field] !== 'N/A' && optimized[field] !== 0) {
                achievedWeight += weight;
            }
        });
        
        // 更新置信度
        if (totalWeight > 0) {
            optimized.confidence = Math.max(optimized.confidence || 0, achievedWeight / totalWeight);
        }
    }
    
    /**
     * @function _validateRequiredFields
     * @description 验证必需字段
     * @param {Object} optimized - 优化后的数据对象
     * @param {Object} templateFields - 模板字段配置
     */
    _validateRequiredFields(optimized, templateFields) {
        const missingFields = [];
        
        templateFields.required.forEach(field => {
            if (!optimized[field] || optimized[field] === 'N/A' || optimized[field] === 0) {
                missingFields.push(field);
            }
        });
        
        if (missingFields.length > 0) {
            optimized.missingFields = missingFields;
            optimized.confidence = Math.max(0, (optimized.confidence || 0) - 0.2); // 降低置信度
        }
    }

    /**
     * @function _generateReceiptNumber
     * @description 生成收据编号
     * @returns {string} 收据编号
     */
    _generateReceiptNumber() {
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `R${year}${month}${day}${random}`;
    }

    /**
     * @function _generateInvoiceNumber
     * @description 生成发票编号
     * @returns {string} 发票编号
     */
    _generateInvoiceNumber() {
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `INV${year}${month}${day}${random}`;
    }

    /**
     * @function _generateQuotationNumber
     * @description 生成报价单编号
     * @returns {string} 报价单编号
     */
    _generateQuotationNumber() {
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `Q${year}${month}${day}${random}`;
    }

    /**
     * @function _generateAgreementNumber
     * @description 生成协议编号
     * @returns {string} 协议编号
     */
    _generateAgreementNumber() {
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `DA${year}${month}${day}${random}`;
    }

    /**
     * @function _getValidUntilDate
     * @description 获取报价单有效期
     * @returns {string} 有效期日期
     */
    _getValidUntilDate() {
        const date = new Date();
        date.setDate(date.getDate() + 30); // 30天有效期
        return date.toISOString().split('T')[0];
    }

    /**
     * @function getStats
     * @description 获取处理统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.stats,
            averageProcessingTime: this.stats.processedTexts > 0 
                ? this.stats.processingTime / this.stats.processedTexts 
                : 0
        };
    }

    /**
     * @function reset
     * @description 重置处理器状态
     */
    reset() {
        this.stats = {
            processedTexts: 0,
            extractedFields: 0,
            processingTime: 0
        };
        
        this.logger.info('LocalNLPProcessor', 'reset', '🔄 处理器状态已重置');
    }
}

// 单例实例
let nlpProcessorInstance = null;

/**
 * @function createLocalNLPProcessor
 * @description 创建本地NLP处理器实例
 * @returns {LocalNLPProcessor} 处理器实例
 */
export function createLocalNLPProcessor() {
    if (!nlpProcessorInstance) {
        nlpProcessorInstance = new LocalNLPProcessor();
    }
    return nlpProcessorInstance;
}

/**
 * @function getLocalNLPProcessor
 * @description 获取本地NLP处理器实例
 * @returns {LocalNLPProcessor} 处理器实例
 */
export function getLocalNLPProcessor() {
    return createLocalNLPProcessor();
}

// 默认导出
export default LocalNLPProcessor;