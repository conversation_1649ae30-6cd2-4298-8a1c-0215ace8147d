/**
 * @file 资源管理器模块
 * <AUTHOR> Team
 * @description 
 * 从index.html中提取的资源管理功能
 * 提供CSS、JavaScript、字体等资源的加载和管理
 */

import { getLogger } from './logger.js';

// #region 资源管理器类
/**
 * @class ResourceManager - 资源管理器
 * @description 提供统一的资源加载和管理功能
 */
export class ResourceManager {
    /**
     * 构造函数
     * @param {Object} config - 资源管理器配置
     */
    constructor(config = {}) {
        this.logger = getLogger();
        this.config = config;
        this.loadedResources = new Set();
        this.isInitialized = false;
        
        this._initialize();
    }

    /**
     * 初始化资源管理器
     * @private
     */
    _initialize() {
        if (this.isInitialized) return;
        
        this.logger.info('ResourceManager', '_initialize', '🔄 初始化资源管理器');
        this.isInitialized = true;
    }

    /**
     * 加载所有必需资源
     * @returns {Promise} 加载完成的Promise
     */
    async loadAllResources() {
        this.logger.info('ResourceManager', 'loadAllResources', '🔄 开始加载所有资源');
        
        try {
            // 加载Tailwind CSS fallback
            this.loadTailwindFallback();
            
            // 加载Font Awesome fallback
            this.loadFontAwesomeFallback();
            
            // 加载html2canvas fallback
            this.loadHtml2CanvasFallback();
            
            // 加载jsPDF fallback
            this.loadJsPDFFallback();
            
            this.logger.info('ResourceManager', 'loadAllResources', '✅ 所有资源加载完成');
        } catch (error) {
            this.logger.error('ResourceManager', 'loadAllResources', '资源加载失败', error);
            throw error;
        }
    }

    /**
     * 加载Tailwind CSS fallback
     */
    loadTailwindFallback() {
        if (this.loadedResources.has('tailwind')) return;
        
        const css = `
            /* Tailwind CSS 核心样式 - 简化版本 */
            .container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
            .flex { display: flex; }
            .flex-col { flex-direction: column; }
            .flex-row { flex-direction: row; }
            .items-center { align-items: center; }
            .justify-between { justify-content: space-between; }
            .justify-center { justify-content: center; }
            .space-x-2 > * + * { margin-left: 0.5rem; }
            .space-x-4 > * + * { margin-left: 1rem; }
            .space-y-2 > * + * { margin-top: 0.5rem; }
            .space-y-3 > * + * { margin-top: 0.75rem; }
            .space-y-4 > * + * { margin-top: 1rem; }
            .w-full { width: 100%; }
            .w-1\\/2 { width: 50%; }
            .w-1\\/3 { width: 33.333333%; }
            .w-1\\/4 { width: 25%; }
            .h-full { height: 100%; }
            .p-2 { padding: 0.5rem; }
            .p-4 { padding: 1rem; }
            .p-6 { padding: 1.5rem; }
            .px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
            .px-4 { padding-left: 1rem; padding-right: 1rem; }
            .py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
            .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
            .py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
            .m-2 { margin: 0.5rem; }
            .m-4 { margin: 1rem; }
            .mb-2 { margin-bottom: 0.5rem; }
            .mb-4 { margin-bottom: 1rem; }
            .mt-2 { margin-top: 0.5rem; }
            .mt-4 { margin-top: 1rem; }
            .ml-2 { margin-left: 0.5rem; }
            .mr-2 { margin-right: 0.5rem; }
            .text-sm { font-size: 0.875rem; }
            .text-lg { font-size: 1.125rem; }
            .text-xl { font-size: 1.25rem; }
            .text-2xl { font-size: 1.5rem; }
            .font-medium { font-weight: 500; }
            .font-semibold { font-weight: 600; }
            .font-bold { font-weight: 700; }
            .text-white { color: #ffffff; }
            .text-gray-500 { color: #6b7280; }
            .text-gray-600 { color: #4b5563; }
            .text-gray-700 { color: #374151; }
            .text-gray-800 { color: #1f2937; }
            .text-blue-600 { color: #2563eb; }
            .text-red-500 { color: #ef4444; }
            .text-green-600 { color: #16a34a; }
            .bg-white { background-color: #ffffff; }
            .bg-gray-50 { background-color: #f9fafb; }
            .bg-gray-100 { background-color: #f3f4f6; }
            .bg-blue-600 { background-color: #2563eb; }
            .bg-blue-700 { background-color: #1d4ed8; }
            .bg-green-500 { background-color: #22c55e; }
            .bg-red-500 { background-color: #ef4444; }
            .border { border-width: 1px; }
            .border-gray-200 { border-color: #e5e7eb; }
            .border-gray-300 { border-color: #d1d5db; }
            .rounded { border-radius: 0.25rem; }
            .rounded-lg { border-radius: 0.5rem; }
            .shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); }
            .shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); }
            .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }
            .hidden { display: none; }
            .block { display: block; }
            .fixed { position: fixed; }
            .relative { position: relative; }
            .absolute { position: absolute; }
            .top-4 { top: 1rem; }
            .right-4 { right: 1rem; }
            .z-50 { z-index: 50; }
            .cursor-pointer { cursor: pointer; }
            .hover\\:bg-blue-700:hover { background-color: #1d4ed8; }
            .hover\\:text-blue-800:hover { color: #1e40af; }
            .focus\\:ring-blue-500:focus { --tw-ring-color: #3b82f6; }
            .focus\\:border-blue-500:focus { border-color: #3b82f6; }
            .grid { display: grid; }
            .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
            .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
            .gap-4 { gap: 1rem; }
            .gap-6 { gap: 1.5rem; }
            .overflow-hidden { overflow: hidden; }
            .overflow-x-auto { overflow-x: auto; }
            .antialiased { -webkit-font-smoothing: antialiased; }
            @media (min-width: 1024px) {
                .lg\\:w-1\\/4 { width: 25%; }
                .lg\\:w-1\\/3 { width: 33.333333%; }
                .lg\\:w-1\\/2 { width: 50%; }
                .lg\\:flex-row { flex-direction: row; }
                .lg\\:hidden { display: none; }
            }
            @media (min-width: 768px) {
                .md\\:w-1\\/2 { width: 50%; }
                .md\\:flex-row { flex-direction: row; }
                .md\\:hidden { display: none; }
            }
        `;
        
        this.injectCSS(css, 'tailwind-fallback');
        this.loadedResources.add('tailwind');
        this.logger.debug('ResourceManager', 'loadTailwindFallback', '✅ Tailwind CSS fallback已加载');
    }

    /**
     * 加载Font Awesome fallback
     */
    loadFontAwesomeFallback() {
        if (this.loadedResources.has('fontawesome')) return;
        
        const css = `
            /* Font Awesome 核心图标 - 简化版本 */
            .fas, .fa { 
                font-family: system-ui, -apple-system, sans-serif;
                font-style: normal;
                font-variant: normal;
                text-rendering: auto;
                line-height: 1;
            }
            /* 简单的图标替代 */
            .fa-magic:before { content: "✨"; }
            .fa-sync-alt:before { content: "🔄"; }
            .fa-undo:before { content: "↶"; }
            .fa-search-minus:before { content: "🔍-"; }
            .fa-search-plus:before { content: "🔍+"; }
            .fa-redo-alt:before { content: "↷"; }
            .fa-expand-alt:before { content: "⛶"; }
            .fa-file-export:before { content: "📤"; }
            .fa-save:before { content: "💾"; }
            .fa-image:before { content: "🖼️"; }
            .fa-file-pdf:before { content: "📄"; }
            .fa-chevron-down:before { content: "▼"; }
            .fa-plus:before { content: "+"; }
            .fa-times:before { content: "×"; }
            .fa-eye:before { content: "👁️"; }
            .fa-eye-slash:before { content: "🙈"; }
            .fa-flask:before { content: "🧪"; }
            .fa-file-image:before { content: "🖼️"; }
            .fa-spinner:before { content: "⟳"; }
            .fa-exclamation-triangle:before { content: "⚠️"; }
            .fa-check-circle:before { content: "✅"; }
            .fa-info-circle:before { content: "ℹ️"; }
            .fa-spin { animation: fa-spin 2s infinite linear; }
            @keyframes fa-spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        
        this.injectCSS(css, 'fontawesome-fallback');
        this.loadedResources.add('fontawesome');
        this.logger.debug('ResourceManager', 'loadFontAwesomeFallback', '✅ Font Awesome fallback已加载');
    }

    /**
     * 加载html2canvas fallback
     */
    loadHtml2CanvasFallback() {
        if (this.loadedResources.has('html2canvas')) return;
        
        // 创建简化的html2canvas替代
        window.html2canvas = function(element, options = {}) {
            return new Promise((resolve, reject) => {
                const logger = getLogger();
                logger.warn('ResourceManager', 'html2canvas', '⚠️ 使用html2canvas fallback，功能受限');
                
                // 创建一个简单的canvas
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                // 设置canvas尺寸
                const rect = element.getBoundingClientRect();
                canvas.width = rect.width || 800;
                canvas.height = rect.height || 600;
                
                // 填充白色背景
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 添加简单的文本提示
                ctx.fillStyle = '#333333';
                ctx.font = '16px Arial, sans-serif';
                ctx.textAlign = 'center';
                ctx.fillText('图片导出功能需要完整的html2canvas库', canvas.width / 2, canvas.height / 2);
                ctx.fillText('请在HTTP服务器环境下使用完整功能', canvas.width / 2, canvas.height / 2 + 30);
                
                resolve(canvas);
            });
        };
        
        this.loadedResources.add('html2canvas');
        this.logger.debug('ResourceManager', 'loadHtml2CanvasFallback', '✅ html2canvas fallback已加载');
    }

    /**
     * 加载jsPDF fallback
     */
    loadJsPDFFallback() {
        if (this.loadedResources.has('jspdf')) return;
        
        // 创建简化的jsPDF替代
        window.jspdf = {
            jsPDF: function() {
                const logger = getLogger();
                return {
                    addImage: function() {
                        logger.warn('ResourceManager', 'jsPDF', '⚠️ 使用jsPDF fallback，功能受限');
                    },
                    save: function(filename) {
                        logger.warn('ResourceManager', 'jsPDF', '⚠️ PDF导出功能需要完整的jsPDF库');
                        alert('PDF导出功能需要在HTTP服务器环境下使用。\n当前为简化模式，请使用浏览器的打印功能代替。');
                    }
                };
            }
        };
        
        this.loadedResources.add('jspdf');
        this.logger.debug('ResourceManager', 'loadJsPDFFallback', '✅ jsPDF fallback已加载');
    }

    /**
     * 注入CSS样式
     * @param {string} css - CSS内容
     * @param {string} id - 样式表ID
     */
    injectCSS(css, id) {
        // 检查是否已存在
        if (document.getElementById(id)) return;
        
        const style = document.createElement('style');
        style.id = id;
        style.textContent = css;
        document.head.appendChild(style);
        
        this.logger.trace('ResourceManager', 'injectCSS', `CSS样式已注入: ${id}`);
    }

    /**
     * 加载外部CSS文件
     * @param {string} url - CSS文件URL
     * @param {string} id - 链接ID
     * @returns {Promise} 加载完成的Promise
     */
    loadCSS(url, id) {
        return new Promise((resolve, reject) => {
            // 检查是否已存在
            if (document.getElementById(id)) {
                resolve();
                return;
            }
            
            const link = document.createElement('link');
            link.id = id;
            link.rel = 'stylesheet';
            link.href = url;
            
            link.onload = () => {
                this.logger.debug('ResourceManager', 'loadCSS', `CSS文件加载成功: ${url}`);
                resolve();
            };
            
            link.onerror = () => {
                this.logger.error('ResourceManager', 'loadCSS', `CSS文件加载失败: ${url}`);
                reject(new Error(`CSS文件加载失败: ${url}`));
            };
            
            document.head.appendChild(link);
        });
    }

    /**
     * 加载外部JavaScript文件
     * @param {string} url - JavaScript文件URL
     * @param {string} id - 脚本ID
     * @returns {Promise} 加载完成的Promise
     */
    loadScript(url, id) {
        return new Promise((resolve, reject) => {
            // 检查是否已存在
            if (document.getElementById(id)) {
                resolve();
                return;
            }
            
            const script = document.createElement('script');
            script.id = id;
            script.src = url;
            
            script.onload = () => {
                this.logger.debug('ResourceManager', 'loadScript', `JavaScript文件加载成功: ${url}`);
                resolve();
            };
            
            script.onerror = () => {
                this.logger.error('ResourceManager', 'loadScript', `JavaScript文件加载失败: ${url}`);
                reject(new Error(`JavaScript文件加载失败: ${url}`));
            };
            
            document.head.appendChild(script);
        });
    }

    /**
     * 检查资源是否已加载
     * @param {string} resourceName - 资源名称
     * @returns {boolean} 是否已加载
     */
    isResourceLoaded(resourceName) {
        return this.loadedResources.has(resourceName);
    }

    /**
     * 获取已加载的资源列表
     * @returns {Array} 已加载的资源列表
     */
    getLoadedResources() {
        return Array.from(this.loadedResources);
    }

    /**
     * 销毁资源管理器
     */
    destroy() {
        this.logger.info('ResourceManager', 'destroy', '销毁资源管理器');
        
        this.loadedResources.clear();
        this.isInitialized = false;
    }
}
// #endregion

// #region 全局资源管理器实例
let globalResourceManager = null;

/**
 * 获取全局资源管理器实例
 * @returns {ResourceManager} 资源管理器实例
 */
export function getResourceManager() {
    if (!globalResourceManager) {
        globalResourceManager = new ResourceManager();
    }
    return globalResourceManager;
}
// #endregion
