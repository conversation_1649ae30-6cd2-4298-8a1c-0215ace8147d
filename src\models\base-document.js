/**
 * @file 基础文档模型
 * @description 定义所有文档类型的基础模型和接口
 */

import { Validator, ValidationError } from '../core/utils/validation.js';
import { DateUtils } from '../core/utils/date-utils.js';
import { StringUtils } from '../core/utils/string-utils.js';

/**
 * @class BaseDocument
 * @description 基础文档类，所有文档类型的父类
 */
export class BaseDocument {
    /**
     * 创建基础文档实例
     * @param {Object} data - 文档数据
     */
    constructor(data = {}) {
        this.id = data.id || StringUtils.generateUUID();
        this.type = data.type || 'unknown';
        this.createdAt = data.createdAt || new Date();
        this.updatedAt = data.updatedAt || new Date();
        this.version = data.version || '1.0.0';
        this.status = data.status || 'draft'; // draft, final, archived
        this.metadata = data.metadata || {};
        
        // 初始化数据
        this._initializeData(data);
    }

    /**
     * 初始化文档数据
     * @param {Object} data - 原始数据
     * @protected
     */
    _initializeData(data) {
        // 子类需要重写此方法来初始化特定的数据结构
    }

    /**
     * 获取文档类型
     * @returns {string} 文档类型
     */
    getType() {
        return this.type;
    }

    /**
     * 获取文档ID
     * @returns {string} 文档ID
     */
    getId() {
        return this.id;
    }

    /**
     * 获取创建时间
     * @returns {Date} 创建时间
     */
    getCreatedAt() {
        return this.createdAt;
    }

    /**
     * 获取更新时间
     * @returns {Date} 更新时间
     */
    getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * 更新文档数据
     * @param {Object} data - 要更新的数据
     * @returns {BaseDocument} 当前实例
     */
    update(data) {
        this.updatedAt = new Date();
        this._updateData(data);
        return this;
    }

    /**
     * 更新具体数据
     * @param {Object} data - 要更新的数据
     * @protected
     */
    _updateData(data) {
        // 子类需要重写此方法来处理特定的数据更新
    }

    /**
     * 验证文档数据
     * @returns {Array} 验证错误数组
     */
    validate() {
        const errors = [];
        
        try {
            // 基础验证
            this._validateBase();
            
            // 子类特定验证
            this._validateSpecific();
            
        } catch (error) {
            if (error instanceof ValidationError) {
                errors.push(error);
            } else {
                errors.push(new ValidationError(`验证失败: ${error.message}`));
            }
        }
        
        return errors;
    }

    /**
     * 基础验证
     * @protected
     */
    _validateBase() {
        Validator.required(this.id, 'ID');
        Validator.required(this.type, '文档类型');
        Validator.required(this.createdAt, '创建时间');
        Validator.required(this.updatedAt, '更新时间');
    }

    /**
     * 特定验证（子类重写）
     * @protected
     */
    _validateSpecific() {
        // 子类需要重写此方法来实现特定的验证逻辑
    }

    /**
     * 转换为JSON对象
     * @returns {Object} JSON对象
     */
    toJSON() {
        return {
            id: this.id,
            type: this.type,
            createdAt: this.createdAt.toISOString(),
            updatedAt: this.updatedAt.toISOString(),
            version: this.version,
            status: this.status,
            metadata: this.metadata,
            ...this._getSpecificData()
        };
    }

    /**
     * 获取特定数据（子类重写）
     * @returns {Object} 特定数据对象
     * @protected
     */
    _getSpecificData() {
        return {};
    }

    /**
     * 从JSON对象创建文档实例
     * @param {Object} json - JSON对象
     * @returns {BaseDocument} 文档实例
     * @static
     */
    static fromJSON(json) {
        const data = {
            ...json,
            createdAt: new Date(json.createdAt),
            updatedAt: new Date(json.updatedAt)
        };
        
        return new this(data);
    }

    /**
     * 克隆文档
     * @returns {BaseDocument} 克隆的文档实例
     */
    clone() {
        const json = this.toJSON();
        json.id = StringUtils.generateUUID(); // 生成新ID
        json.createdAt = new Date().toISOString();
        json.updatedAt = new Date().toISOString();
        
        return this.constructor.fromJSON(json);
    }

    /**
     * 获取文档摘要信息
     * @returns {Object} 摘要信息
     */
    getSummary() {
        return {
            id: this.id,
            type: this.type,
            createdAt: DateUtils.format(this.createdAt, 'YYYY-MM-DD HH:mm'),
            updatedAt: DateUtils.format(this.updatedAt, 'YYYY-MM-DD HH:mm'),
            status: this.status
        };
    }

    /**
     * 设置文档状态
     * @param {string} status - 新状态
     * @returns {BaseDocument} 当前实例
     */
    setStatus(status) {
        const validStatuses = ['draft', 'final', 'archived'];
        
        if (!validStatuses.includes(status)) {
            throw new ValidationError(`无效的状态值: ${status}`);
        }
        
        this.status = status;
        this.updatedAt = new Date();
        
        return this;
    }

    /**
     * 添加元数据
     * @param {string} key - 键
     * @param {*} value - 值
     * @returns {BaseDocument} 当前实例
     */
    setMetadata(key, value) {
        this.metadata[key] = value;
        this.updatedAt = new Date();
        return this;
    }

    /**
     * 获取元数据
     * @param {string} key - 键
     * @param {*} defaultValue - 默认值
     * @returns {*} 元数据值
     */
    getMetadata(key, defaultValue = null) {
        return this.metadata[key] || defaultValue;
    }

    /**
     * 检查文档是否为草稿状态
     * @returns {boolean} 是否为草稿
     */
    isDraft() {
        return this.status === 'draft';
    }

    /**
     * 检查文档是否为最终状态
     * @returns {boolean} 是否为最终状态
     */
    isFinal() {
        return this.status === 'final';
    }

    /**
     * 检查文档是否已归档
     * @returns {boolean} 是否已归档
     */
    isArchived() {
        return this.status === 'archived';
    }

    /**
     * 获取文档年龄（天数）
     * @returns {number} 文档年龄
     */
    getAge() {
        return DateUtils.daysBetween(this.createdAt, new Date());
    }

    /**
     * 检查文档是否在指定时间内创建
     * @param {number} days - 天数
     * @returns {boolean} 是否在指定时间内
     */
    isCreatedWithin(days) {
        const targetDate = DateUtils.addDays(new Date(), -days);
        return this.createdAt >= targetDate;
    }

    /**
     * 检查文档是否在指定时间内更新
     * @param {number} days - 天数
     * @returns {boolean} 是否在指定时间内
     */
    isUpdatedWithin(days) {
        const targetDate = DateUtils.addDays(new Date(), -days);
        return this.updatedAt >= targetDate;
    }

    /**
     * 获取文档大小（估算字符数）
     * @returns {number} 字符数
     */
    getSize() {
        const json = JSON.stringify(this.toJSON());
        return json.length;
    }

    /**
     * 获取格式化的文档大小
     * @returns {string} 格式化的大小
     */
    getFormattedSize() {
        const size = this.getSize();
        
        if (size < 1024) {
            return `${size} 字符`;
        } else if (size < 1024 * 1024) {
            return `${(size / 1024).toFixed(1)} KB`;
        } else {
            return `${(size / (1024 * 1024)).toFixed(1)} MB`;
        }
    }

    /**
     * 比较两个文档
     * @param {BaseDocument} other - 另一个文档
     * @returns {Object} 比较结果
     */
    compare(other) {
        if (!(other instanceof BaseDocument)) {
            throw new Error('比较对象必须是BaseDocument实例');
        }

        return {
            sameId: this.id === other.id,
            sameType: this.type === other.type,
            sameStatus: this.status === other.status,
            createdBefore: this.createdAt < other.createdAt,
            updatedBefore: this.updatedAt < other.updatedAt,
            ageDifference: Math.abs(this.getAge() - other.getAge())
        };
    }

    /**
     * 转换为字符串表示
     * @returns {string} 字符串表示
     */
    toString() {
        return `${this.constructor.name}(id=${this.id}, type=${this.type}, status=${this.status})`;
    }
}
