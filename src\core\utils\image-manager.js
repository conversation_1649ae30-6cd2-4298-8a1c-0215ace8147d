/**
 * @file 图片管理器 - 统一管理图片资源的获取和处理
 * <AUTHOR> Team
 * @description 
 * 这个文件定义了图片管理器类，包括：
 * - ImageManager 类，统一管理图片资源
 * - 与 ImageBase64 对象的集成
 * - 图片数据的缓存和优化
 * - 支持多种图片类型和公司
 */

import { getLogger } from './logger.js';

// #region 图片管理器类定义
/**
 * @class ImageManager - 图片管理器类
 * @description 统一管理所有图片资源的获取、缓存和处理
 */
export class ImageManager {
    /**
     * 构造函数 - 初始化图片管理器
     * @param {Object} config - 配置对象
     */
    constructor(config = {}) {
        this.logger = getLogger();
        this.logger.info('ImageManager', 'constructor', '初始化图片管理器');
        
        // 配置选项
        this.config = {
            enableCache: config.enableCache !== false, // 默认启用缓存
            cacheTimeout: config.cacheTimeout || 300000, // 缓存超时时间（5分钟）
            defaultCompany: config.defaultCompany || 'gomyhire', // 默认公司
            fallbackImages: config.fallbackImages || {}, // 备用图片
            ...config
        };
        
        // 图片缓存
        this.imageCache = new Map();
        this.cacheTimestamps = new Map();
        
        // 支持的图片类型
        this.supportedTypes = ['logo', 'stamp', 'footer'];
        
        // 支持的公司代码
        this.supportedCompanies = ['sky-mirror', 'gomyhire'];
        
        this.logger.debug('ImageManager', 'constructor', '图片管理器初始化完成', {
            config: this.config,
            supportedTypes: this.supportedTypes,
            supportedCompanies: this.supportedCompanies
        });
    }

    /**
     * 获取公司的所有图片资源
     * @function getCompanyImages
     * @param {string} companyCode - 公司代码
     * @returns {Object} 包含所有图片类型的对象
     */
    getCompanyImages(companyCode = this.config.defaultCompany) {
        this.logger.debug('ImageManager', 'getCompanyImages', '获取公司图片资源', { companyCode });
        
        try {
            // 验证公司代码
            if (!this.isValidCompany(companyCode)) {
                this.logger.warn('ImageManager', 'getCompanyImages', '无效的公司代码，使用默认公司', { 
                    companyCode, 
                    defaultCompany: this.config.defaultCompany 
                });
                companyCode = this.config.defaultCompany;
            }
            
            const images = {
                logo: this.getImage('logo', companyCode),
                stamp: this.getImage('stamp', companyCode),
                footer: this.getImage('footer', companyCode)
            };
            
            this.logger.info('ImageManager', 'getCompanyImages', '成功获取公司图片资源', {
                companyCode,
                hasLogo: !!images.logo,
                hasStamp: !!images.stamp,
                hasFooter: !!images.footer
            });
            
            return images;
        } catch (error) {
            this.logger.error('ImageManager', 'getCompanyImages', '获取公司图片资源失败', { companyCode, error });
            return this._getFallbackImages();
        }
    }

    /**
     * 获取指定类型的图片
     * @function getImage
     * @param {string} type - 图片类型 (logo, stamp, footer)
     * @param {string} companyCode - 公司代码
     * @returns {string} 图片的Base64数据或URL
     */
    getImage(type, companyCode = this.config.defaultCompany) {
        this.logger.debug('ImageManager', 'getImage', '获取指定图片', { type, companyCode });
        
        try {
            // 验证参数
            if (!this.isValidType(type)) {
                this.logger.warn('ImageManager', 'getImage', '无效的图片类型', { type });
                return this._getFallbackImage(type, companyCode);
            }
            
            if (!this.isValidCompany(companyCode)) {
                this.logger.warn('ImageManager', 'getImage', '无效的公司代码', { companyCode });
                companyCode = this.config.defaultCompany;
            }
            
            // 检查缓存
            const cacheKey = `${type}_${companyCode}`;
            if (this.config.enableCache && this._isCacheValid(cacheKey)) {
                const cachedImage = this.imageCache.get(cacheKey);
                this.logger.debug('ImageManager', 'getImage', '从缓存获取图片', { cacheKey });
                return cachedImage;
            }
            
            // 从 ImageBase64 获取图片
            let imageData = '';
            if (typeof window !== 'undefined' && window.ImageBase64) {
                switch (type) {
                    case 'logo':
                        imageData = window.ImageBase64.getLogo(companyCode);
                        break;
                    case 'stamp':
                        imageData = window.ImageBase64.getStamp(companyCode);
                        break;
                    case 'footer':
                        imageData = window.ImageBase64.getFooter(companyCode);
                        break;
                }
            }
            
            // 如果没有获取到图片，使用备用图片
            if (!imageData) {
                this.logger.warn('ImageManager', 'getImage', '未获取到图片数据，使用备用图片', { type, companyCode });
                imageData = this._getFallbackImage(type, companyCode);
            }
            
            // 缓存图片数据
            if (this.config.enableCache && imageData) {
                this._cacheImage(cacheKey, imageData);
            }
            
            this.logger.debug('ImageManager', 'getImage', '成功获取图片', { 
                type, 
                companyCode, 
                hasData: !!imageData,
                dataLength: imageData ? imageData.length : 0
            });
            
            return imageData;
        } catch (error) {
            this.logger.error('ImageManager', 'getImage', '获取图片失败', { type, companyCode, error });
            return this._getFallbackImage(type, companyCode);
        }
    }

    /**
     * 获取公司Logo
     * @function getLogo
     * @param {string} companyCode - 公司代码
     * @returns {string} Logo图片数据
     */
    getLogo(companyCode = this.config.defaultCompany) {
        return this.getImage('logo', companyCode);
    }

    /**
     * 获取公司印章
     * @function getStamp
     * @param {string} companyCode - 公司代码
     * @returns {string} 印章图片数据
     */
    getStamp(companyCode = this.config.defaultCompany) {
        return this.getImage('stamp', companyCode);
    }

    /**
     * 获取公司页脚图片
     * @function getFooter
     * @param {string} companyCode - 公司代码
     * @returns {string} 页脚图片数据
     */
    getFooter(companyCode = this.config.defaultCompany) {
        return this.getImage('footer', companyCode);
    }

    /**
     * 验证图片类型是否有效
     * @function isValidType
     * @param {string} type - 图片类型
     * @returns {boolean} 是否有效
     */
    isValidType(type) {
        return this.supportedTypes.includes(type);
    }

    /**
     * 验证公司代码是否有效
     * @function isValidCompany
     * @param {string} companyCode - 公司代码
     * @returns {boolean} 是否有效
     */
    isValidCompany(companyCode) {
        return this.supportedCompanies.includes(companyCode);
    }

    /**
     * 清除图片缓存
     * @function clearCache
     * @param {string} [cacheKey] - 特定的缓存键，不提供则清除所有缓存
     */
    clearCache(cacheKey = null) {
        this.logger.info('ImageManager', 'clearCache', '清除图片缓存', { cacheKey });
        
        if (cacheKey) {
            this.imageCache.delete(cacheKey);
            this.cacheTimestamps.delete(cacheKey);
        } else {
            this.imageCache.clear();
            this.cacheTimestamps.clear();
        }
    }

    /**
     * 获取缓存统计信息
     * @function getCacheStats
     * @returns {Object} 缓存统计信息
     */
    getCacheStats() {
        return {
            cacheSize: this.imageCache.size,
            cacheKeys: Array.from(this.imageCache.keys()),
            enableCache: this.config.enableCache,
            cacheTimeout: this.config.cacheTimeout
        };
    }

    /**
     * 检查缓存是否有效
     * @function _isCacheValid
     * @param {string} cacheKey - 缓存键
     * @returns {boolean} 缓存是否有效
     * @private
     */
    _isCacheValid(cacheKey) {
        if (!this.imageCache.has(cacheKey) || !this.cacheTimestamps.has(cacheKey)) {
            return false;
        }
        
        const timestamp = this.cacheTimestamps.get(cacheKey);
        const now = Date.now();
        return (now - timestamp) < this.config.cacheTimeout;
    }

    /**
     * 缓存图片数据
     * @function _cacheImage
     * @param {string} cacheKey - 缓存键
     * @param {string} imageData - 图片数据
     * @private
     */
    _cacheImage(cacheKey, imageData) {
        this.imageCache.set(cacheKey, imageData);
        this.cacheTimestamps.set(cacheKey, Date.now());
        
        this.logger.debug('ImageManager', '_cacheImage', '图片已缓存', { 
            cacheKey, 
            dataLength: imageData.length,
            cacheSize: this.imageCache.size
        });
    }

    /**
     * 获取备用图片
     * @function _getFallbackImage
     * @param {string} type - 图片类型
     * @param {string} companyCode - 公司代码
     * @returns {string} 备用图片数据
     * @private
     */
    _getFallbackImage(type, companyCode) {
        const fallbackKey = `${type}_${companyCode}`;
        return this.config.fallbackImages[fallbackKey] || 
               this.config.fallbackImages[type] || 
               '';
    }

    /**
     * 获取所有备用图片
     * @function _getFallbackImages
     * @returns {Object} 备用图片对象
     * @private
     */
    _getFallbackImages() {
        return {
            logo: this._getFallbackImage('logo', this.config.defaultCompany),
            stamp: this._getFallbackImage('stamp', this.config.defaultCompany),
            footer: this._getFallbackImage('footer', this.config.defaultCompany)
        };
    }
}

// #region 单例模式和工厂函数
let defaultImageManager = null;

/**
 * 创建图片管理器实例
 * @function createImageManager
 * @param {Object} config - 配置对象
 * @returns {ImageManager} 图片管理器实例
 */
export function createImageManager(config = {}) {
    return new ImageManager(config);
}

/**
 * 获取默认图片管理器实例
 * @function getImageManager
 * @returns {ImageManager} 默认图片管理器实例
 */
export function getImageManager() {
    if (!defaultImageManager) {
        defaultImageManager = new ImageManager();
    }
    return defaultImageManager;
}

/**
 * 设置默认图片管理器实例
 * @function setImageManager
 * @param {ImageManager} manager - 图片管理器实例
 */
export function setImageManager(manager) {
    defaultImageManager = manager;
}

// #region 便捷函数
/**
 * 获取公司的所有图片资源（便捷函数）
 * @function getCompanyImages
 * @param {string} companyCode - 公司代码
 * @returns {Object} 包含所有图片类型的对象
 */
export function getCompanyImages(companyCode) {
    return getImageManager().getCompanyImages(companyCode);
}

/**
 * 获取公司Logo（便捷函数）
 * @function getCompanyLogo
 * @param {string} companyCode - 公司代码
 * @returns {string} Logo图片数据
 */
export function getCompanyLogo(companyCode) {
    return getImageManager().getLogo(companyCode);
}

/**
 * 获取公司印章（便捷函数）
 * @function getCompanyStamp
 * @param {string} companyCode - 公司代码
 * @returns {string} 印章图片数据
 */
export function getCompanyStamp(companyCode) {
    return getImageManager().getStamp(companyCode);
}

/**
 * 获取公司页脚图片（便捷函数）
 * @function getCompanyFooter
 * @param {string} companyCode - 公司代码
 * @returns {string} 页脚图片数据
 */
export function getCompanyFooter(companyCode) {
    return getImageManager().getFooter(companyCode);
}
// #endregion 