/**
 * @file 统一渲染器基类 - 重构后的统一渲染器架构
 * <AUTHOR> Team
 * @description
 * 这个文件定义了重构后的统一渲染器基类，整合了原BaseRenderer和UnifiedRenderer的功能
 * 消除了重复代码，提供了清晰的继承体系和标准化的渲染接口
 *
 * 重构说明：
 * - 合并了BaseRenderer和UnifiedRenderer的重复功能
 * - 统一了事件处理和性能统计
 * - 简化了配置管理和缓存机制
 * - 提供了更清晰的抽象方法定义
 */

// #region 导入依赖模块
import { EventEmitter } from '../core/events/event-emitter.js';
import { EventTypes } from '../core/events/event-types.js';
import { Validator } from '../core/utils/validation.js';
import { DateUtils } from '../core/utils/date-utils.js';
// #endregion

// #region 统一渲染器基类定义
/**
 * @class UnifiedRenderer - 统一渲染器基类
 * @description 重构后的统一渲染器基类，整合了BaseRenderer和原UnifiedRenderer的功能
 * @extends EventEmitter
 */
export class UnifiedRenderer extends EventEmitter {
    /**
     * 构造函数 - 初始化渲染器实例
     * @param {Object} config - 渲染器配置对象
     */
    constructor(config = {}) {
        super();

        // 基础配置 - 整合了原BaseRenderer的配置结构
        this.name = config.name || 'unified-renderer';
        this.type = config.type || 'base';
        this.version = config.version || '2.0.0';
        this.description = config.description || '统一渲染器基类';

        // 元数据信息
        this.metadata = {
            created: new Date(),
            lastModified: new Date(),
            author: 'SmartOffice Team',
            ...config.metadata
        };

        // 渲染器配置 - 整合了两个类的配置选项
        this.renderConfig = {
            // 输出格式配置
            outputFormat: config.format || 'html', // html, pdf, image, print等
            outputQuality: config.quality || 'high', // low, medium, high
            outputCompression: false, // 是否压缩输出
            theme: config.theme || 'default',

            // 性能配置
            enableCache: config.enableCache !== undefined ? config.enableCache : true,
            maxCacheSize: 100, // 最大缓存项数
            cacheTimeout: 300000, // 缓存超时时间（毫秒）
            enableParallel: config.enableParallel || false,
            enableOptimization: config.enableOptimization !== undefined ? config.enableOptimization : true,

            // 调试配置
            enableDebug: config.enableDebug || false,
            enableProfiling: config.enableProfiling || false,
            logLevel: 'info', // 日志级别

            // 错误处理
            errorHandling: config.errorHandling || 'throw', // 'throw', 'log', 'ignore'

            // 渲染选项
            preserveWhitespace: false,
            minifyOutput: true,
            includeMetadata: true,
            validateOutput: true,

            ...config.renderConfig
        };

        // 渲染器状态 - 整合了BaseRenderer的状态管理
        this.state = {
            isInitialized: false,
            isRendering: false,
            lastRenderTime: null,
            renderCount: 0,
            errorCount: 0
        };

        // 缓存管理 - 整合了BaseRenderer的缓存机制
        this.cache = new Map();
        this.cacheStats = {
            hits: 0,
            misses: 0,
            total: 0
        };

        // 性能统计 - 整合了两个类的性能统计
        this.performanceStats = {
            totalRenderTime: 0,
            averageRenderTime: 0,
            minRenderTime: Infinity,
            maxRenderTime: 0,
            renderHistory: [],
            totalRenders: 0,
            successfulRenders: 0,
            failedRenders: 0
        };

        // 管理器实例
        this.styleManager = null;
        this.positionManager = null;

        // 共享渲染服务
        this.sharedStyleService = null;
        this.sharedLayoutService = null;
        this.sharedPositionService = null;

        // 错误收集
        this.errors = [];
        this.warnings = [];

        // 渲染钩子
        this.hooks = {
            beforeRender: [],
            afterRender: [],
            onError: [],
            onWarning: []
        };

        // 初始化渲染器
        this._initialize();
    }

    /**
     * 初始化渲染器 - 整合了BaseRenderer的初始化逻辑
     * @private
     */
    _initialize() {
        try {
            // 验证配置
            this._validateConfig();

            // 初始化共享服务
            this._initializeSharedServices();

            // 设置缓存清理定时器
            if (this.renderConfig.enableCache) {
                this._setupCacheCleanup();
            }

            // 标记为已初始化
            this.state.isInitialized = true;

            // 触发初始化完成事件
            this.emit(EventTypes.RENDERER.INITIALIZED, {
                renderer: this.name,
                type: this.type,
                timestamp: new Date()
            });

            console.log(`[${this.name}] 统一渲染器初始化完成`);

        } catch (error) {
            this.emit(EventTypes.RENDERER.ERROR, {
                renderer: this.name,
                error: error.message,
                timestamp: new Date()
            });
            throw new Error(`渲染器初始化失败: ${error.message}`);
        }
    }
    
    /**
     * 初始化共享渲染服务
     * @private
     */
    _initializeSharedServices() {
        this.sharedLayoutService = renderServiceFactory.getLayoutService();
        console.log(`[${this.config.name}] 共享渲染服务初始化完成`);
    }
    
    /**
     * 设置样式管理器
     * @param {StyleManager} styleManager - 样式管理器实例
     */
    setStyleManager(styleManager) {
        if (!(styleManager instanceof StyleManager)) {
            throw new Error('样式管理器必须是StyleManager实例');
        }
        
        this.styleManager = styleManager;
        this.sharedStyleService = renderServiceFactory.getStyleService(styleManager);
        console.log(`[${this.config.name}] 样式管理器已设置`);
    }
    
    /**
     * 设置位置管理器
     * @param {PositionManager} positionManager - 位置管理器实例
     */
    setPositionManager(positionManager) {
        if (!(positionManager instanceof PositionManager)) {
            throw new Error('位置管理器必须是PositionManager实例');
        }
        
        this.positionManager = positionManager;
        this.sharedPositionService = renderServiceFactory.getPositionService(positionManager);
        console.log(`[${this.config.name}] 位置管理器已设置`);
    }
    
    /**
     * 通用样式处理方法
     * 子类可以直接调用或覆盖此方法
     * @param {DocumentModel} document - 文档模型
     * @param {Object} options - 处理选项
     * @returns {Promise<Object>} 处理后的样式
     * @protected
     */
    async _processStyles(document, options = {}) {
        if (!this.sharedStyleService) {
            console.warn(`[${this.config.name}] 共享样式服务未初始化，使用默认处理`);
            return this._getDefaultStyles();
        }
        
        return await this.sharedStyleService.processStyles(
            document, 
            options, 
            this.config.format
        );
    }
    
    /**
     * 通用布局处理方法
     * 子类可以直接调用或覆盖此方法
     * @param {DocumentModel} document - 文档模型
     * @param {Object} options - 处理选项
     * @returns {Promise<Object>} 处理后的布局
     * @protected
     */
    async _processLayout(document, options = {}) {
        if (!this.sharedLayoutService) {
            console.warn(`[${this.config.name}] 共享布局服务未初始化，使用默认处理`);
            return await document.calculateLayout();
        }
        
        return await this.sharedLayoutService.processLayout(
            document, 
            options, 
            this.config.format
        );
    }
    
    /**
     * 通用位置处理方法
     * 子类可以直接调用或覆盖此方法
     * @param {DocumentModel} document - 文档模型
     * @param {Object} layout - 布局信息
     * @param {Object} options - 处理选项
     * @returns {Promise<Object>} 处理后的位置
     * @protected
     */
    async _processPositions(document, layout, options = {}) {
        if (!this.sharedPositionService) {
            console.warn(`[${this.config.name}] 共享位置服务未初始化，使用默认处理`);
            return { elements: [] };
        }
        
        return await this.sharedPositionService.processPositions(
            document, 
            layout, 
            options, 
            this.config.format
        );
    }
    
    /**
     * 获取默认样式
     * @returns {Object} 默认样式
     * @protected
     */
    _getDefaultStyles() {
        return {
            css: 'body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }',
            format: this.config.format
        };
    }
    
    /**
     * 渲染文档
     * @param {DocumentModel|Object} document - 文档模型或文档数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<Object>} 渲染结果
     */
    async render(document, options = {}) {
        if (this.isRendering) {
            throw new Error('渲染器正在执行中，请等待当前渲染完成');
        }
        
        const startTime = Date.now();
        this.isRendering = true;
        this.performanceStats.totalRenders++;
        
        try {
            console.log(`[${this.config.name}] 开始渲染`);
            
            // 准备渲染
            const preparedData = await this._prepareRender(document, options);
            
            // 执行渲染
            const result = await this._executeRender(preparedData);
            
            // 后处理
            const finalResult = await this._postProcessRender(result, preparedData);
            
            // 更新统计
            const renderTime = Date.now() - startTime;
            this._updateStats(renderTime, true);
            
            console.log(`[${this.config.name}] 渲染完成，耗时: ${renderTime}ms`);
            
            return {
                success: true,
                result: finalResult,
                renderTime,
                metadata: {
                    renderer: this.config.name,
                    version: this.config.version,
                    format: this.config.format,
                    timestamp: new Date().toISOString()
                }
            };
            
        } catch (error) {
            const renderTime = Date.now() - startTime;
            this._updateStats(renderTime, false);
            this._handleError(error);
            
            console.error(`[${this.config.name}] 渲染失败:`, error);
            
            return {
                success: false,
                error: error.message,
                renderTime,
                metadata: {
                    renderer: this.config.name,
                    version: this.config.version,
                    format: this.config.format,
                    timestamp: new Date().toISOString()
                }
            };
            
        } finally {
            this.isRendering = false;
            this.renderingDocument = null;
            this.renderingOptions = null;
        }
    }
    
    /**
     * 批量渲染
     * @param {Array} documents - 文档列表
     * @param {Object} options - 渲染选项
     * @returns {Promise<Array>} 渲染结果列表
     */
    async batchRender(documents, options = {}) {
        const results = [];
        
        if (this.config.enableParallel) {
            // 并行渲染
            const promises = documents.map(doc => this.render(doc, options));
            const batchResults = await Promise.allSettled(promises);
            
            for (const result of batchResults) {
                if (result.status === 'fulfilled') {
                    results.push(result.value);
                } else {
                    results.push({
                        success: false,
                        error: result.reason.message
                    });
                }
            }
        } else {
            // 串行渲染
            for (const document of documents) {
                const result = await this.render(document, options);
                results.push(result);
            }
        }
        
        return results;
    }
    
    /**
     * 预览渲染
     * @param {DocumentModel|Object} document - 文档模型或文档数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<Object>} 预览结果
     */
    async preview(document, options = {}) {
        const previewOptions = {
            ...options,
            quality: 'medium', // 预览使用中等质量
            enableOptimization: false, // 预览不优化
            includeMetadata: false // 预览不包含元数据
        };
        
        return await this.render(document, previewOptions);
    }
    
    /**
     * 添加渲染钩子
     * @param {string} hookName - 钩子名称
     * @param {Function} callback - 回调函数
     */
    addHook(hookName, callback) {
        if (!this.hooks[hookName]) {
            throw new Error(`不支持的钩子: ${hookName}`);
        }
        
        if (typeof callback !== 'function') {
            throw new Error('钩子回调必须是函数');
        }
        
        this.hooks[hookName].push(callback);
        console.log(`[${this.config.name}] 钩子已添加: ${hookName}`);
    }
    
    /**
     * 移除渲染钩子
     * @param {string} hookName - 钩子名称
     * @param {Function} callback - 回调函数
     */
    removeHook(hookName, callback) {
        if (!this.hooks[hookName]) {
            return;
        }
        
        const index = this.hooks[hookName].indexOf(callback);
        if (index > -1) {
            this.hooks[hookName].splice(index, 1);
            console.log(`[${this.config.name}] 钩子已移除: ${hookName}`);
        }
    }
    
    /**
     * 添加事件监听器
     * @param {string} event - 事件名称
     * @param {Function} listener - 监听器函数
     */
    on(event, listener) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        
        this.eventListeners.get(event).push(listener);
        console.log(`[${this.config.name}] 事件监听器已添加: ${event}`);
    }
    
    /**
     * 移除事件监听器
     * @param {string} event - 事件名称
     * @param {Function} listener - 监听器函数
     */
    off(event, listener) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(listener);
            if (index > -1) {
                listeners.splice(index, 1);
                console.log(`[${this.config.name}] 事件监听器已移除: ${event}`);
            }
        }
    }
    
    /**
     * 获取性能统计
     * @returns {Object} 性能统计信息
     */
    getPerformanceStats() {
        return {
            ...this.performanceStats,
            cacheSize: this.renderCache.size,
            isRendering: this.isRendering,
            renderer: this.config.name
        };
    }
    
    /**
     * 获取渲染器信息
     * @returns {Object} 渲染器信息
     */
    getInfo() {
        return {
            name: this.config.name,
            version: this.config.version,
            type: this.config.type,
            format: this.config.format,
            supportedFormats: this.getSupportedFormats(),
            features: this.getFeatures(),
            status: this.isRendering ? 'rendering' : 'idle'
        };
    }
    
    /**
     * 获取支持的格式
     * @returns {Array} 支持的格式列表
     */
    getSupportedFormats() {
        // 子类应该重写此方法
        return [this.config.format];
    }
    
    /**
     * 获取渲染器特性
     * @returns {Array} 特性列表
     */
    getFeatures() {
        // 子类应该重写此方法
        return ['basic-rendering'];
    }
    
    /**
     * 验证文档
     * @param {DocumentModel|Object} document - 文档
     * @returns {Object} 验证结果
     */
    validateDocument(document) {
        const errors = [];
        const warnings = [];
        
        // 基础验证
        if (!document) {
            errors.push('文档不能为空');
        }
        
        // 如果是DocumentModel实例，使用其验证方法
        if (document instanceof DocumentModel) {
            const validation = document.validate();
            errors.push(...validation.errors);
            warnings.push(...validation.warnings);
        }
        
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
    
    /**
     * 清除缓存
     */
    clearCache() {
        this.renderCache.clear();
        console.log(`[${this.config.name}] 渲染缓存已清除`);
    }
    
    /**
     * 重置统计信息
     */
    resetStats() {
        this.performanceStats = {
            totalRenders: 0,
            successfulRenders: 0,
            failedRenders: 0,
            averageRenderTime: 0,
            lastRenderTime: 0,
            cacheHits: 0,
            cacheMisses: 0
        };
        
        this.errors = [];
        this.warnings = [];
        
        console.log(`[${this.config.name}] 统计信息已重置`);
    }
    
    /**
     * 更新配置
     * @param {Object} newConfig - 新配置
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        console.log(`[${this.config.name}] 配置已更新`);
    }
    
    /**
     * 销毁渲染器
     */
    destroy() {
        this.renderCache.clear();
        this.eventListeners.clear();
        this.errors = [];
        this.warnings = [];
        
        // 清除钩子
        for (const hookName in this.hooks) {
            this.hooks[hookName] = [];
        }
        
        console.log(`[${this.config.name}] 渲染器已销毁`);
    }
    
    // ==================== 受保护的方法（子类可重写） ====================
    
    /**
     * 准备渲染
     * @param {DocumentModel|Object} document - 文档
     * @param {Object} options - 选项
     * @returns {Promise<Object>} 准备好的数据
     * @protected
     */
    async _prepareRender(document, options) {
        // 执行前置钩子
        await this._executeHooks('beforeRender', { document, options });
        
        // 确保文档是DocumentModel实例
        const documentModel = document instanceof DocumentModel 
            ? document 
            : new DocumentModel(document);
        
        // 验证文档
        const validation = this.validateDocument(documentModel);
        if (!validation.isValid) {
            throw new Error(`文档验证失败: ${validation.errors.join(', ')}`);
        }
        
        // 合并选项
        const mergedOptions = {
            ...this.config.options,
            ...options,
            format: options.format || this.config.format,
            quality: options.quality || this.config.quality,
            theme: options.theme || this.config.theme
        };
        
        // 检查缓存
        const cacheKey = this._generateCacheKey(documentModel, mergedOptions);
        if (this.config.enableCache && this.renderCache.has(cacheKey)) {
            this.performanceStats.cacheHits++;
            console.log(`[${this.config.name}] 缓存命中: ${cacheKey}`);
            return {
                fromCache: true,
                result: this.renderCache.get(cacheKey)
            };
        }
        
        this.performanceStats.cacheMisses++;
        
        // 保存渲染状态
        this.renderingDocument = documentModel;
        this.renderingOptions = mergedOptions;
        
        return {
            document: documentModel,
            options: mergedOptions,
            cacheKey,
            fromCache: false
        };
    }
    
    /**
     * 执行渲染 - 子类必须实现的抽象方法
     * @param {Object} preparedData - 准备好的数据
     * @returns {Promise<Object>} 渲染结果
     * @protected
     * @abstract
     */
    async _executeRender(preparedData) {
        throw new Error('_executeRender 方法必须在子类中实现');
    }

    /**
     * 后处理渲染结果 - 整合了BaseRenderer的后处理逻辑
     * @param {Object} result - 原始渲染结果
     * @param {Object} preparedData - 准备好的数据
     * @returns {Promise<Object>} 处理后的结果
     * @protected
     */
    async _postProcessRender(result, preparedData) {
        // 如果是从缓存获取的结果，直接返回
        if (preparedData.fromCache) {
            return preparedData.result;
        }

        // 执行后置钩子
        await this._executeHooks('afterRender', { result, preparedData });

        // 添加元数据
        const finalResult = {
            content: result,
            metadata: {
                renderer: this.name,
                type: this.type,
                outputFormat: preparedData.options.format,
                timestamp: new Date(),
                version: this.version,
                renderTime: Date.now() - this.state.lastRenderTime
            }
        };

        // 缓存结果
        if (this.renderConfig.enableCache && preparedData.cacheKey) {
            this._cacheResult(preparedData.cacheKey, finalResult);
        }

        return finalResult;
    }

    /**
     * 验证渲染器配置 - 整合了BaseRenderer的验证逻辑
     * @private
     */
    _validateConfig() {
        const requiredFields = ['name', 'type'];

        for (const field of requiredFields) {
            if (!this[field]) {
                throw new Error(`渲染器配置缺少必填字段: ${field}`);
            }
        }

        // 验证输出格式
        const validOutputFormats = ['html', 'pdf', 'image', 'print'];
        if (!validOutputFormats.includes(this.renderConfig.outputFormat)) {
            throw new Error(`不支持的输出格式: ${this.renderConfig.outputFormat}`);
        }
    }

    /**
     * 设置缓存清理 - 整合了BaseRenderer的缓存管理
     * @private
     */
    _setupCacheCleanup() {
        setInterval(() => {
            this._cleanupCache();
        }, this.renderConfig.cacheTimeout);
    }

    /**
     * 清理过期缓存 - 整合了BaseRenderer的缓存清理逻辑
     * @private
     */
    _cleanupCache() {
        const now = Date.now();
        let cleanedCount = 0;

        for (const [key, entry] of this.cache.entries()) {
            if (now - entry.timestamp > this.renderConfig.cacheTimeout) {
                this.cache.delete(key);
                cleanedCount++;
            }
        }

        if (cleanedCount > 0 && this.renderConfig.enableDebug) {
            console.log(`渲染器 ${this.name} 清理了 ${cleanedCount} 个过期缓存项`);
        }
    }

    /**
     * 生成缓存键 - 整合了BaseRenderer的缓存键生成逻辑
     * @param {Object} document - 文档对象
     * @param {Object} options - 渲染选项
     * @returns {string} 缓存键
     * @private
     */
    _generateCacheKey(document, options) {
        const keyComponents = [
            this.name,
            document.name || 'unknown',
            document.version || '1.0.0',
            JSON.stringify(document.data || {}),
            JSON.stringify(options)
        ];

        // 使用简单的哈希算法生成键
        const keyString = keyComponents.join('|');
        let hash = 0;
        for (let i = 0; i < keyString.length; i++) {
            const char = keyString.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }

        return `${this.name}_${Math.abs(hash)}_${Date.now()}`;
    }

    /**
     * 缓存结果 - 整合了BaseRenderer的缓存存储逻辑
     * @param {string} key - 缓存键
     * @param {Object} result - 渲染结果
     * @private
     */
    _cacheResult(key, result) {
        // 检查缓存大小限制
        if (this.cache.size >= this.renderConfig.maxCacheSize) {
            // 删除最老的缓存项
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }

        this.cache.set(key, {
            result: result,
            timestamp: Date.now()
        });
    }

    /**
     * 更新统计信息 - 整合了BaseRenderer的性能统计逻辑
     * @param {number} renderTime - 渲染时间
     * @param {boolean} success - 是否成功
     * @private
     */
    _updateStats(renderTime, success) {
        this.state.renderCount++;
        this.performanceStats.totalRenderTime += renderTime;
        this.performanceStats.averageRenderTime =
            this.performanceStats.totalRenderTime / this.state.renderCount;
        this.performanceStats.minRenderTime =
            Math.min(this.performanceStats.minRenderTime, renderTime);
        this.performanceStats.maxRenderTime =
            Math.max(this.performanceStats.maxRenderTime, renderTime);

        if (success) {
            this.performanceStats.successfulRenders++;
        } else {
            this.performanceStats.failedRenders++;
            this.state.errorCount++;
        }

        // 保留最近的渲染历史
        this.performanceStats.renderHistory.push({
            timestamp: new Date(),
            renderTime: renderTime,
            success: success
        });

        // 限制历史记录数量
        if (this.performanceStats.renderHistory.length > 100) {
            this.performanceStats.renderHistory.shift();
        }

        this.state.lastRenderTime = new Date();
    }

    /**
     * 执行钩子函数
     * @param {string} hookName - 钩子名称
     * @param {Object} data - 传递给钩子的数据
     * @private
     */
    async _executeHooks(hookName, data) {
        const hooks = this.hooks[hookName] || [];

        for (const hook of hooks) {
            try {
                await hook(data);
            } catch (error) {
                console.error(`[${this.name}] 钩子执行失败: ${hookName}`, error);
                await this._executeHooks('onError', { error, hookName, data });
            }
        }
    }

    /**
     * 处理错误
     * @param {Error} error - 错误对象
     * @private
     */
    _handleError(error) {
        this.errors.push({
            error: error.message,
            timestamp: new Date(),
            stack: error.stack
        });

        // 限制错误记录数量
        if (this.errors.length > 50) {
            this.errors.shift();
        }

        // 触发错误事件
        this.emit(EventTypes.RENDERER.ERROR, {
            renderer: this.name,
            error: error.message,
            timestamp: new Date()
        });

        // 执行错误钩子
        this._executeHooks('onError', { error });
    }

    /**
     * 验证输出结果
     * @param {Object} result - 渲染结果
     * @protected
     */
    _validateOutput(result) {
        if (!result) {
            throw new Error('渲染结果不能为空');
        }

        // 子类可以重写此方法添加特定的验证逻辑
        console.log(`[${this.name}] 输出验证通过`);
    }

    /**
     * 对象哈希算法 - 用于生成缓存键
     * @param {Object} obj - 要哈希的对象
     * @returns {string} 哈希值
     * @private
     */
    _hashObject(obj) {
        const str = JSON.stringify(obj, Object.keys(obj).sort());
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return hash.toString(36);
    }

    /**
     * 触发内部事件
     * @param {string} event - 事件名称
     * @param {Object} data - 事件数据
     * @private
     */
    _emitEvent(event, data) {
        // 使用继承的EventEmitter的emit方法
        this.emit(event, {
            ...data,
            renderer: this.name,
            timestamp: new Date()
        });
    }
}

// #endregion

// 导出统一渲染器基类
export { UnifiedRenderer };