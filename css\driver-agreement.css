/* @file 司机协议专用样式文件 */
/* @project SmartOffice */
/* 此文件包含与司机协议模板相关的样式规则 */

/* #region 司机协议基本样式 */
#specific-driver-agreement-render-area {
    position: relative;
    width: 100%;
    max-width: 595px; /* A4 宽度 */
    min-height: 842px; /* A4 高度 */
    margin: 0 auto;
    padding: 0;
    background-color: white;
    box-sizing: border-box;
    font-family: 'Times New Roman', Times, serif; /* 统一字体为 Times New Roman */
    color: #000; /* 文本颜色为黑色 */
    border: 1px solid #e0e0e0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
}

#driver-agreement-content {
    flex: 1;
    padding: 10px 20px 30px 20px; /* 调整内边距，特别是底部 */
    position: relative;
    width: 100%;
    box-sizing: border-box;
    background-color: white;
}

/* 公司信息 - .company-info-container rules commented out as class is not used in driver-agreement-template.js */
/*
.company-info-container {
    display: flex;
    justify-content: space-between;
    font-size: 6pt; 
    line-height: 1.2; 
    color: #555; 
    padding: 5px 0;
    border-bottom: 1px solid #000; 
    margin-bottom: 10px; 
}

.company-info {
    flex-basis: 30%; 
    padding: 0 5px; 
}

.company-info p {
    margin: 0;
    font-size: inherit; 
    text-align: left;
}
*/

/* 协议主标题 */
#driver-agreement-content .document-title h1 {
    font-size: 14pt; /* 调整字体大小 */
    text-align: center;
    margin-top: 5px;
    margin-bottom: 5px;
    font-weight: bold;
    color: #000;
}

/* 协议类型标题 (DRIVER AGREEMENT) */
#driver-agreement-content h2.agreement-type-title {
    font-size: 16pt; /* 显著增大字体 */
    text-align: center;
    margin-top: 0;
    margin-bottom: 5px;
    font-weight: bold;
    color: #000;
}

/* 协议编号样式 */
.agreement-meta-info {
    text-align: right;
    font-size: 8pt; /* 减小字体 */
    margin-bottom: 10px; /* 增加底部间距 */
    color: #000;
}

/* .agreement-number-section span rule removed as HTML uses <p> tags directly */


/* 协议方信息样式 */
.parties-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px; /* 增加底部间距 */
    font-size: 8pt; /* 减小字体 */
    color: #000;
}

.party-info {
    width: 48%;
    border: 1px solid #000; /* 黑色边框 */
    padding: 5px;
    box-sizing: border-box;
}

.party-info h3 {
    font-size: 8pt; /* 与内容字体一致 */
    font-weight: bold;
    margin: 0 0 5px 0;
    color: #000;
}

.party-info p {
    margin: 0 0 3px 0;
    font-size: 8pt;
    text-align: left;
    color: #000;
}

/* 协议内容区域 - HTML模板直接使用 .agreement-content 和 .agreement-left-column/.agreement-right-column */
.agreement-content {
    display: flex;
    justify-content: space-between;
    gap: 20px; /* 栏间距调整 */
    margin-bottom: 15px;
    font-size: 8.5pt; /* 整体字号略微增大 */
    line-height: 1.4; /* 增加行高，提升可读性 */
    color: #333; /* 深灰色文本，比纯黑柔和 */
}

.agreement-left-column,
.agreement-right-column {
    width: 48%;
}

.agreement-section h2 {
    font-size: 10pt; /* 章节标题字号 */
    font-weight: bold;
    color: #111; /* 更深的标题颜色 */
    margin-top: 12px;
    margin-bottom: 6px;
    padding-bottom: 2px;
    border-bottom: 1px solid #ccc; /* 为章节标题添加下划线 */
}

.agreement-section p,
.agreement-section li {
    font-size: 8.5pt;
    text-align: justify;
    margin-bottom: 5px; /* 段落和列表项间距 */
}

.agreement-section ol {
    padding-left: 18px; /* 列表缩进调整 */
    margin-top: 3px;
    margin-bottom: 8px;
}

.agreement-section ol li {
    margin-bottom: 4px; /* 列表项之间更紧凑一些 */
}

/* 旧的 .agreement-columns 替换为 .agreement-content 的直接子元素布局 */
.agreement-columns {
    display: flex;
    justify-content: space-between;
    gap: 15px;
    margin-bottom: 10px;
    font-size: 8pt; /* 减小字体 */
    line-height: 1.3; /* 调整行高 */
    color: #000;
}

/* 司机协议条款标题 */
.driver-agreement-container h3 {
    font-size: 9pt; /* 条款标题略大 */
    font-weight: bold;
    margin-top: 8px;
    margin-bottom: 4px;
    color: #000;
}

/* 司机协议段落 */
.driver-agreement-container p {
    font-size: 8pt;
    text-align: justify;
    margin-bottom: 3px;
    color: #000;
}

/* 司机协议列表项 */
.driver-agreement-container li {
    padding-left: 15px;
    margin: 0;
}

/* 页眉样式 */
.document-header-image-container {
    position: relative;
    width: 100%;
    height: auto;
    padding: 5px 10px; /* 增加左右内边距 */
    display: flex;
    align-items: center;
    justify-content: space-between; /* 两端对齐 */
    background-color: white;
    border-bottom: 2px solid purple; /* 紫色粗下边框 */
    box-sizing: border-box;
    z-index: 10;
}

.document-header-image-container .company-logo-img {
    max-height: 40px; /* 限制logo高度 */
    width: auto;
    object-fit: contain;
    display: block;
}

.document-header-image-container .company-name-img {
    max-height: 30px; /* 限制公司名称图片高度 */
    width: auto;
    object-fit: contain;
    display: block;
}

.document-header-image-container .contact-info-img {
    max-height: 40px; /* 限制联系方式图片高度 */
    width: auto;
    object-fit: contain;
    display: block;
}

/* 电子生成提示样式 - 移到公司信息下方 */
.electronic-generated-notice {
    width: 100%;
    margin: 5px 0 10px 0; /* 调整外边距 */
    padding: 0; /* 移除上下内边距 */
    font-size: 7pt; /* 显著减小字体 */
    color: #333; /* 调整颜色 */
    text-align: center;
    border-top: none; /* 移除顶部边框 */
    box-sizing: border-box;
}

.electronic-generated-notice p {
    text-align: center; /* 确保p标签内的文本也居中 */
    margin: 0; /* 继承或重置外边距 */
    padding: 0; /* 继承或重置内边距 */
}

/* 页脚样式 */
.company-footer-image-container {
   display: block;
   margin-top: 20px;
   text-align: center;
}

.company-footer-image {
   max-width: 100%;
}

/* 旧的电子生成提示样式已被合并和移除 */

/* 旧的印章样式已被移除，相关样式已迁移到 stamp-position.css */

/* 打印优化 */
@media print {
    #specific-driver-agreement-render-area {
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
        border: none;
        box-shadow: none;
    }

    #driver-agreement-content {
        padding: 10px 15px 80px 15px; /* 调整打印内边距 */
    }

    .document-header-image-container {
        position: fixed; /* 确保页眉打印时固定 */
        top: 0;
        left: 0;
        right: 0;
        border-bottom: 2px solid purple !important; /* 确保打印时显示边框 */
        -webkit-print-color-adjust: exact; /* 强制打印颜色和背景 */
        print-color-adjust: exact;
    }
    
    /* 确保公司信息打印时显示正确 */
    .company-info-container {
        border-bottom: 1px solid #000 !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
    .parties-info .party-info {
        border: 1px solid #000 !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
     /* 确保电子生成提示正确打印 */
    .electronic-generated-notice {
        color: #333 !important; /* 确保颜色正确 */
        font-size: 7pt !important; /* 确保字体大小正确 */
    }
    /* 确保内容不被截断 */
    .agreement-column {
        page-break-inside: avoid;
    }
}

/* 隐藏字段 */
.hidden-field {
    display: none !important;
}

/* 从core-styles.css和template-image.css中迁移并调整的页眉图片容器通用样式 */
#specific-driver-agreement-render-area .document-header-image-container {
    height: auto; /* 移除固定高度 */
    max-height: 50px; /* 减小最大高度 */
    padding: 5px 10px; /* 调整内边距 */
    background-color: white; /* 确保背景色 */
    border-bottom: 2px solid purple; /* 紫色粗下边框 */
    box-shadow: none; /* 移除阴影 */
    display: flex;
    justify-content: space-between; /* logo 左，公司名和联系方式右 */
    align-items: center;
}

#specific-driver-agreement-render-area .document-header-image-container .header-left {
    /* 左侧（logo） */
    flex-shrink: 0; /* 防止logo被压缩 */
}

#specific-driver-agreement-render-area .document-header-image-container .header-right {
    /* 右侧（公司名和联系方式） */
    display: flex;
    align-items: center;
    gap: 10px; /* 图片之间的间隔 */
}

#specific-driver-agreement-render-area .document-header-image-container img {
    object-fit: contain; /* 保持图片比例 */
    display: block;
}

#specific-driver-agreement-render-area .company-logo-img { /* 左侧logo图片 */
    max-height: 30px; /* 调整高度 */
    width: auto;
}

#specific-driver-agreement-render-area .company-name-img { /* 右侧公司名图片 */
    max-height: 20px; /* 调整高度 */
    width: auto;
}

#specific-driver-agreement-render-area .contact-info-img { /* 右侧联系方式图片 */
    max-height: 30px; /* 调整高度 */
    width: auto;
}

/* 确保内容区域在页眉下方开始 */
#specific-driver-agreement-render-area #driver-agreement-content {
    padding-top: 60px; /* 留出页眉空间，根据页眉实际高度调整 */
}

/* 协议双方信息样式优化 */
.parties-info {
    display: flex;
    justify-content: space-between;
    margin: 20px 0;
    padding: 15px;
    background-color: #f9f9f9;
    border: 1px solid #eaeaea;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.party-info {
    width: 48%;
    padding: 10px;
    box-sizing: border-box;
}

.party-info h3 {
    font-size: 10pt !important;
    color: #333;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #ddd;
}

.party-info p {
    font-size: 9pt !important;
    margin: 5px 0 !important;
    line-height: 1.4 !important;
}

/* 协议编号和签署日期样式 */
.agreement-meta-info {
    display: flex;
    justify-content: space-between;
    margin: 10px 0 15px;
    padding: 8px 10px;
    background-color: #f5f5f5;
    border-radius: 4px;
    font-size: 9pt;
}

.agreement-meta-info p {
    margin: 0 !important;
    font-weight: 500;
}

/* 打印模式下的样式调整 */
@media print {
    .parties-info {
        background-color: #fff;
        border: 1px solid #ddd;
        box-shadow: none;
        break-inside: avoid;
    }
    
    .agreement-meta-info {
        background-color: #fff;
        border-bottom: 1px solid #eee;
    }
}

/* 移除旧的、可能冲突的h1, h2, p, li, ol样式 */
/* #driver-agreement-content h1 (旧) */
/* #driver-agreement-content h2 (旧) */
/* #driver-agreement-content p, #driver-agreement-content li (旧) */
/* #driver-agreement-content ol (旧) */

/* 移除旧的协议双栏布局 */
/* .agreement-columns (旧) */
/* .agreement-column (旧) */

/* 移除旧的协议方信息样式 */
/* .parties-info (旧，由新的.parties-info替代) */
/* .party-info (旧，由新的.party-info替代) */
/* .party-info h3 (旧，由新的.party-info h3替代) */

/* 移除旧的页脚、页码、印章样式，因为截图上不需要 */
/* .company-footer (旧，现已隐藏) */
/* .company-stamp, .company-stamp img, img.stamp-image, img.company-stamp-gmh, img.company-stamp-smw (旧，现已隐藏) */

/* 确保打印时页眉固定 */
@media print {
    #specific-driver-agreement-render-area .document-header-image-container {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background-color: white !important; /* 强制打印背景色 */
        border-bottom: 2px solid purple !important; /* 强制打印边框 */
        -webkit-print-color-adjust: exact; /* 确保颜色和背景打印 */
        print-color-adjust: exact;
    }
     #specific-driver-agreement-render-area #driver-agreement-content {
        padding-top: 60px; /* 确保打印时内容也在页眉下方 */
    }
}/* Migrated from driver-agreement-template.js */
.driver-agreement-text {
    font-size: 60%;
    padding-left: 5px;
    padding-right: 5px;
    line-height: 1.2;
    max-width: 100%;
    margin: 0;
}
.driver-agreement-text h1 {
    font-size: 1rem;
    margin-top: 5px;
    margin-bottom: 5px;
    line-height: 1.1;
    text-align: center;
    font-weight: bold;
    padding: 0;
}
.driver-agreement-text h2 {
    font-size: 0.8rem;
    margin-top: 4px;
    margin-bottom: 2px;
    line-height: 1;
    font-weight: bold;
    padding: 0;
}
.driver-agreement-text .section {
    margin-bottom: 3px;
    margin-top: 3px;
    padding: 0;
}
.driver-agreement-text p {
    margin-bottom: 2px;
    margin-top: 0;
    line-height: 1.2;
    font-size: 0.7rem;
    padding: 0;
}
.driver-agreement-text ol {
    margin-top: 2px;
    margin-bottom: 2px;
    padding-left: 15px;
    padding-top: 0;
    padding-bottom: 0;
}
.driver-agreement-text li {
    margin-bottom: 1px;
    margin-top: 0;
    line-height: 1.2;
    font-size: 0.7rem;
    padding: 0;
}
.company-header-image {
    width: 100%;
    max-height: 30px;
    margin-bottom: 2px;
}
.document-header-image-container {
    margin-bottom: 0; /* 移除底部margin，避免与统一页眉设计冲突 */
    text-align: center;
    min-height: 50px;
}
.document-title {
    text-align: center;
    margin-top: 10px;
    margin-bottom: 15px;
    font-size: 1.2em;
    font-weight: bold;
}
.company-header-image-container {
    margin: 0 0 2px 0;
    padding: 0;
    height: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}
.driver-agreement-single-page {
    padding: 5px;
    background-color: white;
}
.company-footer-image-container {
    text-align: center;
    margin-top: 2px;
    position: relative;
    z-index: 1000;
}
.company-footer-image {
    max-width: 100%;
    position: relative;
    z-index: 1001;
}
.agreement-content {
    width: 100%;
    display: flex;
    justify-content: space-between;
    gap: 4px;
    position: relative;
    margin: 0;
    padding: 0;
}
.agreement-left-column {
    flex: 0 0 49.5%;
    margin: 0;
    padding: 0;
}
.agreement-right-column {
    flex: 0 0 49.5%;
    margin: 0;
    padding: 0;
}
.parties-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1px;
    margin-top: 0;
    padding: 0;
}
.party-info {
    flex: 1;
    padding: 1px;
    border: 1px solid #ddd;
    border-radius: 2px;
    margin: 0 1px;
    font-size: 0.5rem;
}
.party-info h3 {
    font-size: 0.55rem;
    margin-bottom: 0;
    margin-top: 0;
    font-weight: bold;
    padding: 0;
}
.party-info p {
    margin: 0;
    padding: 0;
    line-height: 0.8;
}
/* .electronic-doc 样式已被移除，请使用 .electronic-generated-notice */
@media print {
    .driver-agreement-container {
        page-break-inside: avoid !important;
        page-break-before: auto !important;
        page-break-after: auto !important;
        break-inside: avoid !important;
        width: 210mm !important;
        height: 297mm !important;
        margin: 0 !important;
        padding: 0 !important;
        font-size: 40% !important;
    }
    .agreement-content {
        break-inside: avoid !important;
        width: 100% !important;
    }
    .agreement-left-column, .agreement-right-column {
        break-inside: avoid !important;
    }
    .party-info {
        break-inside: avoid !important;
    }
    .agreement-section { /* Added this based on template structure */
        break-inside: avoid !important;
    }
    body, html { /* This was in template's print styles */
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        overflow: hidden !important;
    }
    .document-header-image-container, .company-footer-image-container { /* From template's print styles */
        display: block !important;
        visibility: visible !important;
    }
}
