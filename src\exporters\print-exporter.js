/**
 * @file 打印导出器 - 专门用于打印功能的文档导出
 * <AUTHOR> Team
 * @description 
 * 这个文件实现了打印导出器，继承自BaseExporter，提供：
 * - 打印功能的文档导出
 * - 集成现有的打印渲染器系统
 * - 打印预览和设置功能
 * - 页面分割和格式优化
 * - 浏览器原生打印API集成
 */

// #region 导入依赖模块
import { BaseExporter } from './base-exporter.js';
import { UnifiedRenderEngine } from '../core/rendering/unified-render-engine.js';
import { DocumentModel } from '../core/rendering/document-model.js';
import { StyleManager } from '../core/rendering/style-manager.js';
import { PositionManager } from '../core/rendering/position-manager.js';
import { PrintRenderer } from '../renderers/print-renderer.js';
import { createPrintRenderer, createPresetPrintRenderer } from '../renderers/print-renderer.js';
import { validateExportOptions } from '../core/utils/validation.js';
import { EXPORT_EVENTS } from '../core/events/event-types.js';
// #endregion

// #region PrintExporter 类定义
/**
 * @class PrintExporter - 打印导出器类
 * @description 专门用于打印功能的文档导出，提供完整的打印预览和控制功能
 */
export class PrintExporter extends BaseExporter {
    /**
     * 构造函数 - 初始化打印导出器
     * @param {Object} config - 打印导出器配置
     * @param {string} config.orientation - 页面方向 ('portrait', 'landscape')
     * @param {string} config.paperSize - 纸张尺寸 ('A4', 'A3', 'A5', 'Letter', 'Legal')
     * @param {Object} config.margins - 页面边距
     * @param {boolean} config.showPreview - 是否显示打印预览
     */
    constructor(config = {}) {
        // 调用父类构造函数
        super({
            name: config.name || 'print-exporter',
            type: 'print',
            version: config.version || '1.0.0',
            description: config.description || '打印功能文档导出器',
            ...config
        });
        
        // 打印特有配置
        this.printConfig = {
            orientation: config.orientation || 'portrait',
            paperSize: config.paperSize || 'A4',
            margins: config.margins || {
                top: 20,
                right: 20,
                bottom: 20,
                left: 20
            },
            showPreview: config.showPreview !== false,
            enablePageBreaks: config.enablePageBreaks !== false,
            printBackground: config.printBackground !== false,
            printHeaders: config.printHeaders !== false,
            scale: config.scale || 1,
            ...config.printConfig
        };
        
        // 统一渲染架构组件
        this.renderEngine = new UnifiedRenderEngine();
        this.styleManager = new StyleManager();
        this.positionManager = new PositionManager();
        this.printRenderer = new PrintRenderer({
            styleManager: this.styleManager,
            positionManager: this.positionManager,
            renderConfig: this.printConfig
        });
        
        // 打印管理
        this.printQueue = [];
        this.activePrints = new Map();
        this.printWindows = new Map();
        
        // 打印统计
        this.printStats = {
            totalPrintJobs: 0,
            successfulPrints: 0,
            cancelledPrints: 0,
            averagePrintTime: 0,
            byPaperSize: {},
            byOrientation: {
                portrait: 0,
                landscape: 0
            }
        };
    }

    /**
     * 验证特定配置 - 验证打印导出器特有的配置
     * @protected
     */
    _validateSpecificConfig() {
        // 验证页面方向
        const validOrientations = ['portrait', 'landscape'];
        if (!validOrientations.includes(this.printConfig.orientation)) {
            throw new Error(`不支持的页面方向: ${this.printConfig.orientation}`);
        }
        
        // 验证纸张尺寸
        const validPaperSizes = ['A4', 'A3', 'A5', 'Letter', 'Legal'];
        if (!validPaperSizes.includes(this.printConfig.paperSize)) {
            throw new Error(`不支持的纸张尺寸: ${this.printConfig.paperSize}`);
        }
        
        // 验证边距设置
        if (this.printConfig.margins) {
            ['top', 'right', 'bottom', 'left'].forEach(margin => {
                const value = this.printConfig.margins[margin];
                if (value !== undefined && (typeof value !== 'number' || value < 0 || value > 100)) {
                    throw new Error(`边距${margin}必须在0-100之间: ${value}`);
                }
            });
        }
        
        // 验证缩放比例
        if (this.printConfig.scale <= 0 || this.printConfig.scale > 5) {
            throw new Error(`缩放比例必须在0-5之间: ${this.printConfig.scale}`);
        }
    }

    /**
     * 初始化打印导出器 - 创建打印渲染器实例
     * @private
     */
    async _initializePrintExporter() {
        try {
            // 创建打印渲染器
            this.printRenderer = createPrintRenderer({
                name: `${this.name}-renderer`,
                renderConfig: this.printConfig
            });
            
            // 监听渲染器事件
            this.printRenderer.on('render:completed', (data) => {
                this._updatePrintStats(data);
            });
            
            this.printRenderer.on('render:failed', (data) => {
                this.emit(EXPORT_EVENTS.EXPORT_FAILED, {
                    exporter: this.name,
                    error: data.error,
                    phase: 'print-rendering'
                });
            });
            
        } catch (error) {
            throw new Error(`打印渲染器初始化失败: ${error.message}`);
        }
    }

    /**
     * 验证特定导出选项 - 验证打印导出特有的选项
     * @param {Object} options - 导出选项
     * @protected
     */
    _validateSpecificOptions(options) {
        try {
            validateExportOptions(options, 'print');
        } catch (error) {
            throw new Error(`打印导出选项验证失败: ${error.message}`);
        }
    }

    /**
     * 执行实际导出 - 生成打印文档并处理打印
     * @param {Object} content - 要导出的内容
     * @param {Object} options - 导出选项
     * @returns {Promise<Object>} 导出结果
     * @protected
     */
    async _performExport(content, options) {
        // 确保打印渲染器已初始化
        if (!this.printRenderer) {
            await this._initializePrintExporter();
        }
        
        // 准备打印选项
        const printOptions = this._preparePrintOptions(options);
        
        // 触发打印开始事件
        this.emit(EXPORT_EVENTS.EXPORT_STARTED, {
            exporter: this.name,
            type: 'print',
            options: printOptions
        });
        
        try {
            // 使用打印渲染器生成打印文档
            const renderResult = await this.printRenderer.render(content, printOptions);
            
            // 处理打印结果
            const exportResult = await this._processPrintResult(renderResult, options);
            
            // 如果启用自动打印，触发打印
            if (options.autoPrint !== false) {
                await this._triggerPrint(exportResult, options);
            }
            
            return exportResult;
            
        } catch (error) {
            throw new Error(`打印生成失败: ${error.message}`);
        }
    }

    /**
     * 准备打印选项 - 合并和转换导出选项为打印选项
     * @param {Object} options - 导出选项
     * @returns {Object} 打印选项
     * @private
     */
    _preparePrintOptions(options) {
        return {
            // 基础配置
            orientation: options.orientation || this.printConfig.orientation,
            paperSize: options.paperSize || this.printConfig.paperSize,
            scale: options.scale || this.printConfig.scale,
            
            // 边距配置
            margins: options.margins ? 
                { ...this.printConfig.margins, ...options.margins } : 
                this.printConfig.margins,
            
            // 打印特有选项
            showPreview: options.showPreview !== undefined ? 
                options.showPreview : this.printConfig.showPreview,
            enablePageBreaks: options.enablePageBreaks !== undefined ? 
                options.enablePageBreaks : this.printConfig.enablePageBreaks,
            printBackground: options.printBackground !== undefined ? 
                options.printBackground : this.printConfig.printBackground,
            printHeaders: options.printHeaders !== undefined ? 
                options.printHeaders : this.printConfig.printHeaders,
            
            // 其他选项
            ...options.printOptions
        };
    }

    /**
     * 处理打印渲染结果 - 将渲染结果转换为导出结果
     * @param {Object} renderResult - 打印渲染结果
     * @param {Object} options - 导出选项
     * @returns {Promise<Object>} 处理后的导出结果
     * @private
     */
    async _processPrintResult(renderResult, options) {
        return {
            type: 'print',
            content: renderResult.content,
            html: renderResult.html,
            styles: renderResult.styles,
            printWindow: null, // 将在打印时设置
            metadata: {
                pageCount: renderResult.pageCount || 1,
                orientation: renderResult.orientation,
                paperSize: renderResult.paperSize,
                margins: renderResult.margins,
                scale: renderResult.scale,
                generatedAt: new Date().toISOString(),
                ...renderResult.metadata
            }
        };
    }

    /**
     * 触发打印 - 处理文档的打印
     * @param {Object} exportResult - 导出结果
     * @param {Object} options - 导出选项
     * @returns {Promise<void>}
     * @private
     */
    async _triggerPrint(exportResult, options) {
        const printId = `print_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        try {
            // 记录打印任务
            this.activePrints.set(printId, {
                startTime: Date.now(),
                options: options
            });
            
            // 根据配置选择打印方式
            if (options.showPreview !== false) {
                // 显示打印预览
                await this._showPrintPreview(exportResult, options, printId);
            } else {
                // 直接打印
                await this._directPrint(exportResult, options, printId);
            }
            
            // 触发打印完成事件
            this.emit(EXPORT_EVENTS.EXPORT_COMPLETED, {
                exporter: this.name,
                type: 'print',
                result: exportResult,
                printId
            });
            
        } catch (error) {
            this.emit(EXPORT_EVENTS.EXPORT_FAILED, {
                exporter: this.name,
                error: error.message,
                phase: 'print',
                printId
            });
            throw error;
            
        } finally {
            // 清理打印记录
            this.activePrints.delete(printId);
        }
    }

    /**
     * 显示打印预览 - 在新窗口中显示打印预览
     * @param {Object} exportResult - 导出结果
     * @param {Object} options - 打印选项
     * @param {string} printId - 打印ID
     * @returns {Promise<void>}
     * @private
     */
    async _showPrintPreview(exportResult, options, printId) {
        return new Promise((resolve, reject) => {
            try {
                // 创建打印预览窗口
                const printWindow = window.open('', '_blank', this._getPrintWindowFeatures());
                
                if (!printWindow) {
                    throw new Error('无法打开打印预览窗口，请检查弹窗拦截设置');
                }
                
                // 记录打印窗口
                this.printWindows.set(printId, printWindow);
                exportResult.printWindow = printWindow;
                
                // 写入打印内容
                this._writePrintContent(printWindow, exportResult);
                
                // 设置打印窗口事件
                this._setupPrintWindowEvents(printWindow, resolve, reject);
                
                // 自动触发打印对话框
                setTimeout(() => {
                    try {
                        printWindow.print();
                    } catch (error) {
                        console.warn('自动打印失败:', error.message);
                    }
                }, 500);
                
            } catch (error) {
                reject(new Error(`打印预览失败: ${error.message}`));
            }
        });
    }

    /**
     * 直接打印 - 不显示预览直接打印
     * @param {Object} exportResult - 导出结果
     * @param {Object} options - 打印选项
     * @param {string} printId - 打印ID
     * @returns {Promise<void>}
     * @private
     */
    async _directPrint(exportResult, options, printId) {
        return new Promise((resolve, reject) => {
            try {
                // 创建隐藏的打印窗口
                const printWindow = window.open('', '_blank', 'width=1,height=1,left=-999,top=-999');
                
                if (!printWindow) {
                    throw new Error('无法创建打印窗口');
                }
                
                // 记录打印窗口
                this.printWindows.set(printId, printWindow);
                exportResult.printWindow = printWindow;
                
                // 写入打印内容
                this._writePrintContent(printWindow, exportResult);
                
                // 等待内容加载后打印
                printWindow.onload = () => {
                    try {
                        printWindow.print();
                        
                        // 延迟关闭窗口
                        setTimeout(() => {
                            printWindow.close();
                            resolve();
                        }, 1000);
                        
                    } catch (error) {
                        reject(new Error(`打印失败: ${error.message}`));
                    }
                };
                
            } catch (error) {
                reject(new Error(`直接打印失败: ${error.message}`));
            }
        });
    }

    /**
     * 获取打印窗口特性 - 返回打印窗口的配置参数
     * @returns {string} 窗口特性字符串
     * @private
     */
    _getPrintWindowFeatures() {
        return [
            'width=800',
            'height=600',
            'scrollbars=yes',
            'resizable=yes',
            'status=no',
            'location=no',
            'toolbar=no',
            'menubar=no'
        ].join(',');
    }

    /**
     * 写入打印内容 - 向打印窗口写入HTML内容
     * @param {Window} printWindow - 打印窗口
     * @param {Object} exportResult - 导出结果
     * @private
     */
    _writePrintContent(printWindow, exportResult) {
        const doc = printWindow.document;
        
        // 构建完整的HTML文档
        const htmlContent = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打印文档</title>
    ${exportResult.styles || ''}
    <style>
        @media print {
            body { margin: 0; padding: 0; }
            .no-print { display: none !important; }
        }
        @page {
            size: ${exportResult.metadata.paperSize} ${exportResult.metadata.orientation};
            margin: ${this._formatMargins(exportResult.metadata.margins)};
        }
    </style>
</head>
<body>
    ${exportResult.html || exportResult.content}
    <script>
        // 打印完成后的处理
        window.onafterprint = function() {
            if (window.opener) {
                window.close();
            }
        };
        
        // 键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
            if (e.key === 'Escape') {
                window.close();
            }
        });
    </script>
</body>
</html>
        `;
        
        doc.open();
        doc.write(htmlContent);
        doc.close();
    }

    /**
     * 格式化边距 - 将边距对象转换为CSS格式
     * @param {Object} margins - 边距对象
     * @returns {string} CSS边距字符串
     * @private
     */
    _formatMargins(margins) {
        if (!margins) {
            return '20mm';
        }
        
        const { top = 20, right = 20, bottom = 20, left = 20 } = margins;
        return `${top}mm ${right}mm ${bottom}mm ${left}mm`;
    }

    /**
     * 设置打印窗口事件 - 绑定打印窗口的事件处理
     * @param {Window} printWindow - 打印窗口
     * @param {Function} resolve - 成功回调
     * @param {Function} reject - 失败回调
     * @private
     */
    _setupPrintWindowEvents(printWindow, resolve, reject) {
        // 窗口关闭事件
        printWindow.onbeforeunload = () => {
            resolve();
        };
        
        // 打印完成事件
        printWindow.onafterprint = () => {
            setTimeout(() => {
                printWindow.close();
                resolve();
            }, 100);
        };
        
        // 错误处理
        printWindow.onerror = (error) => {
            reject(new Error(`打印窗口错误: ${error}`));
        };
        
        // 超时处理
        setTimeout(() => {
            if (!printWindow.closed) {
                resolve(); // 即使超时也算成功，用户可能在查看预览
            }
        }, 30000); // 30秒超时
    }

    /**
     * 更新打印统计信息 - 记录打印的统计数据
     * @param {Object} data - 打印完成数据
     * @private
     */
    _updatePrintStats(data) {
        this.printStats.totalPrintJobs++;
        
        if (data.result && data.result.metadata) {
            const metadata = data.result.metadata;
            
            // 更新纸张尺寸统计
            if (metadata.paperSize) {
                this.printStats.byPaperSize[metadata.paperSize] = 
                    (this.printStats.byPaperSize[metadata.paperSize] || 0) + 1;
            }
            
            // 更新方向统计
            if (metadata.orientation && this.printStats.byOrientation[metadata.orientation] !== undefined) {
                this.printStats.byOrientation[metadata.orientation]++;
            }
        }
        
        // 更新平均打印时间
        if (data.duration) {
            const totalTime = this.printStats.averagePrintTime * (this.printStats.totalPrintJobs - 1) + data.duration;
            this.printStats.averagePrintTime = totalTime / this.printStats.totalPrintJobs;
        }
        
        this.printStats.successfulPrints++;
    }

    /**
     * 批量打印 - 同时打印多个文档
     * @param {Array<Object>} contents - 要打印的内容数组
     * @param {Object} baseOptions - 基础打印选项
     * @returns {Promise<Array<Object>>} 打印结果数组
     */
    async batchPrint(contents, baseOptions = {}) {
        const results = [];
        
        // 批量打印时，建议显示预览让用户确认
        const printOptions = {
            ...baseOptions,
            showPreview: baseOptions.showPreview !== false,
            autoPrint: false // 批量打印时先不自动打印
        };
        
        for (let i = 0; i < contents.length; i++) {
            const content = contents[i];
            
            try {
                const result = await this.export(content, printOptions);
                results[i] = result;
                
                // 添加延迟避免同时打开太多窗口
                if (i < contents.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                
            } catch (error) {
                results[i] = { error: error.message };
            }
        }
        
        return results;
    }

    /**
     * 预览打印 - 只显示打印预览不自动打印
     * @param {Object} content - 要预览的内容
     * @param {Object} options - 预览选项
     * @returns {Promise<Object>} 预览结果
     */
    async preview(content, options = {}) {
        const previewOptions = {
            ...options,
            showPreview: true,
            autoPrint: false
        };
        
        return await this.export(content, previewOptions);
    }

    /**
     * 获取支持的格式 - 返回打印导出器支持的格式
     * @returns {Array<string>} 支持的格式数组
     */
    getSupportedFormats() {
        return ['print'];
    }

    /**
     * 获取支持的纸张尺寸 - 返回支持的纸张尺寸
     * @returns {Array<string>} 支持的纸张尺寸数组
     */
    getSupportedPaperSizes() {
        return ['A4', 'A3', 'A5', 'Letter', 'Legal'];
    }

    /**
     * 获取打印统计信息 - 返回打印的详细统计
     * @returns {Object} 打印统计信息
     */
    getPrintStats() {
        return {
            ...this.printStats,
            activePrints: this.activePrints.size,
            openPrintWindows: this.printWindows.size
        };
    }

    /**
     * 关闭所有打印窗口 - 清理所有打开的打印窗口
     */
    closeAllPrintWindows() {
        for (const [printId, printWindow] of this.printWindows.entries()) {
            try {
                if (!printWindow.closed) {
                    printWindow.close();
                }
            } catch (error) {
                console.warn(`关闭打印窗口失败 ${printId}:`, error.message);
            }
        }
        this.printWindows.clear();
    }

    /**
     * 清理资源 - 清理打印导出器占用的资源
     */
    cleanup() {
        // 清理打印渲染器
        if (this.printRenderer) {
            this.printRenderer.cleanup();
            this.printRenderer = null;
        }
        
        // 清理打印队列
        this.printQueue = [];
        
        // 关闭所有打印窗口
        this.closeAllPrintWindows();
        
        // 清理活跃打印
        this.activePrints.clear();
        
        // 调用父类清理
        super.cleanup();
    }
}
// #endregion

// #region 工厂函数和预设
/**
 * 创建打印导出器 - 工厂函数，创建配置好的打印导出器实例
 * @param {Object} config - 导出器配置
 * @returns {PrintExporter} 打印导出器实例
 */
export function createPrintExporter(config = {}) {
    return new PrintExporter(config);
}

/**
 * 创建预设打印导出器 - 使用预设配置创建打印导出器
 * @param {string} preset - 预设名称 ('default', 'minimal', 'high-quality', 'draft', 'formal')
 * @param {Object} customConfig - 自定义配置
 * @returns {PrintExporter} 配置好的打印导出器实例
 */
export function createPresetPrintExporter(preset = 'default', customConfig = {}) {
    const presets = {
        'default': {
            orientation: 'portrait',
            paperSize: 'A4',
            showPreview: true,
            enablePageBreaks: true,
            printBackground: true,
            margins: { top: 20, right: 20, bottom: 20, left: 20 }
        },
        'minimal': {
            orientation: 'portrait',
            paperSize: 'A4',
            showPreview: false,
            enablePageBreaks: false,
            printBackground: false,
            margins: { top: 10, right: 10, bottom: 10, left: 10 }
        },
        'high-quality': {
            orientation: 'portrait',
            paperSize: 'A4',
            showPreview: true,
            enablePageBreaks: true,
            printBackground: true,
            printHeaders: true,
            margins: { top: 25, right: 25, bottom: 25, left: 25 }
        },
        'draft': {
            orientation: 'portrait',
            paperSize: 'A4',
            showPreview: false,
            enablePageBreaks: false,
            printBackground: false,
            scale: 0.8,
            margins: { top: 15, right: 15, bottom: 15, left: 15 }
        },
        'formal': {
            orientation: 'portrait',
            paperSize: 'A4',
            showPreview: true,
            enablePageBreaks: true,
            printBackground: true,
            printHeaders: true,
            margins: { top: 30, right: 25, bottom: 30, left: 25 }
        }
    };
    
    const presetConfig = presets[preset];
    if (!presetConfig) {
        throw new Error(`不支持的打印导出器预设: ${preset}`);
    }
    
    return createPrintExporter({
        name: `print-exporter-${preset}`,
        description: `${preset}预设打印导出器`,
        ...presetConfig,
        ...customConfig
    });
}

/**
 * 便捷打印函数 - 快速打印文档
 * @param {Object} content - 要打印的内容
 * @param {Object} options - 打印选项
 * @returns {Promise<Object>} 打印结果
 */
export async function printDocument(content, options = {}) {
    const exporter = createPrintExporter(options.exporterConfig);
    
    try {
        return await exporter.export(content, options);
    } finally {
        // 注意：不立即清理，因为打印窗口可能还在使用
        setTimeout(() => {
            exporter.cleanup();
        }, 5000);
    }
}
// #endregion