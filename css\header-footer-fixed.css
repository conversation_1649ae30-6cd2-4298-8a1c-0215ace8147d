/* @file 页眉页脚固定定位样式 */
/* @project SmartOffice */
/* 此文件包含页眉和页脚的固定定位相关样式 */

/* 文档容器样式 */
#document-container {
    padding-top: var(--fixed-header-height);
    padding-bottom: var(--fixed-footer-height);
    overflow: visible;
    position: relative;
    min-height: 100%;
}

/* 页眉样式 - 统一使用document-header类 */
.document-header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: var(--fixed-header-height);
    z-index: 10;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.document-header-image-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 页脚样式 - 统一使用document-footer类 */
.document-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: var(--fixed-footer-height);
    z-index: 10;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 页脚内容样式 */
.document-footer-content {
    width: 100% !important;
    text-align: center !important;
    font-size: 8pt !important;
    color: #666 !important;
    line-height: 1.2 !important;
}

/* 内容区域 - 确保内容在页眉和页脚之间滚动 */
.document-content {
    position: relative;
    z-index: 1;
    min-height: calc(100vh - 280px); /* 视口高度 - (页眉高度 + 页脚高度) */
    padding: 20px 25px;
    overflow-y: auto;
    /* 添加底部内边距，确保内容不被页脚遮挡 */
    padding-bottom: 45mm !important; /* 增加底部内边距，为页脚预留空间 */
}

/* 响应式调整 */
@media (max-width: 768px) {
    #document-container {
        padding-top: 200px; /* 移动端页眉高度增加 */
        padding-bottom: 150px;
    }
    
    .document-header {
        height: 180px;
    }
    
    .document-footer {
        height: 100px;
    }
    
    .document-content {
        min-height: calc(100vh - 300px);
    }
}

/* 打印样式 - 确保打印时页眉页脚正确显示 */
@media print {
    .document-header {
        position: absolute;
        top: 0;
        page-break-after: avoid;
    }
    
    .document-footer {
        position: absolute;
        bottom: 30mm !important; /* 保持与非打印时相同的位置 */
        page-break-before: avoid;
        height: 10mm !important;
        min-height: 10mm !important;
        padding: 2px 25px !important;
        /* 打印时固定在底部但留出足够边距 */
        position: fixed !important; 
    }
    
    .document-content {
        min-height: auto;
        padding-top: 180px;
        padding-bottom: 45mm; /* 为页脚预留足够空间 */
    }
    
    /* 确保整个文档可见 */
    #document-preview {
        overflow: visible !important;
    }
    
    /* 确保页脚内容可见 */
    .document-footer-content {
        font-size: 7pt !important;
        line-height: 1.1 !important;
        color: #333 !important;
    }
}

/* 导出样式确保PDF和图像导出时页脚位置一致 */
.pdf-export-container .document-footer,
.image-export-container .document-footer {
    position: absolute !important;
    bottom: 30mm !important; /* 与其他场景保持一致 */
    left: 0 !important;
    right: 0 !important;
    height: 10mm !important;
    min-height: 10mm !important;
    padding: 2px 25px !important;
    background-color: white !important;
    border-top: 1px solid #e5e7eb !important;
    z-index: 100 !important;
}

/* 多页文档中页脚样式 */
@page {
    margin-bottom: 35mm; /* 增加页面底部边距，为页脚预留更多空间 */
}
