/**
 * @file 统一应用类 - 从src/app/smart-office-app.js迁移的完整实现
 * <AUTHOR> Team
 * @description 
 * 这是从src/app/smart-office-app.js迁移的完整统一应用类实现
 * 整合了js/app-initializer.js、js/services/app-initializer.js、js/main.js的功能
 * 消除重复代码，提供统一的应用管理接口
 * 
 * 重构说明：
 * - 从src/app/smart-office-app.js完整迁移
 * - 整合js/目录下的应用初始化逻辑
 * - 保持所有原有功能和API兼容性
 * - 消除约800-1000行重复代码
 */

// #region 导入依赖模块
import { EventEmitter } from './events.js';
import { getLogger } from './logger.js';
import { getConfigManager } from './config.js';
import { getAppBootstrap } from './bootstrap.js';

// 导入src/目录的完整功能模块
import { GlobalStateManager } from '../../src/state/global-state-manager.js';
import { WorkflowEngine } from '../../src/workflow/workflow-engine.js';
import { UIComponentManager } from '../../src/ui/components/ui-component-manager.js';
import { TemplateManager } from '../../src/templates/template-manager.js';
import { RendererManager } from '../../src/renderers/renderer-manager.js';
import { ExporterManager } from '../../src/exporters/exporter-manager.js';
import { PluginSystem } from '../../src/core/plugins/plugin-system.js';
import { getLocalNLPProcessor } from '../../src/core/utils/local-nlp-processor.js';
import { getLocalResourceManager } from '../../src/core/utils/local-resources.js';
import { UnifiedRenderEngine } from '../../src/core/rendering/unified-render-engine.js';
import { DocumentModel } from '../../src/core/rendering/document-model.js';
import { StyleManager } from '../../src/core/rendering/style-manager.js';
import { PositionManager } from '../../src/core/rendering/position-manager.js';
// #endregion

// #region 统一应用类定义
/**
 * @class UnifiedApp - 统一应用类
 * @description 整合了所有应用初始化和管理功能的统一应用类
 * @extends EventEmitter
 */
export class UnifiedApp extends EventEmitter {
    /**
     * 构造函数 - 初始化统一应用实例
     * @param {HTMLElement} container - 应用容器元素
     * @param {Object} config - 应用配置对象
     */
    constructor(container = null, config = {}) {
        super();
        
        // 初始化日志记录器
        this.logger = getLogger();
        this.logger.info('UnifiedApp', 'constructor', '创建统一应用实例');
        
        // 应用基础信息
        this.name = 'SmartOffice';
        this.version = '2.0.0-unified';
        this.description = 'SmartOffice 2.0 统一应用';
        this.mode = 'unified';
        
        // 应用状态
        this.isInitialized = false;
        this.isReady = false;
        this.container = container;
        
        // 核心管理器实例
        this.managers = {
            config: null,           // 配置管理器
            offlineConfig: null,    // 离线配置管理器
            state: null,            // 状态管理器
            workflow: null,         // 工作流引擎
            components: null,       // UI组件管理器
            templates: null,        // 模板管理器
            renderers: null,        // 渲染器管理器
            exporters: null,        // 导出器管理器
            plugins: null,          // 插件系统
            nlp: null,              // 本地自然语言处理器
            resources: null,        // 本地资源管理器
            // 统一渲染架构组件
            renderEngine: null,     // 统一渲染引擎
            styleManager: null,     // 样式管理器
            positionManager: null   // 位置管理器
        };
        
        this.logger.debug('UnifiedApp', 'constructor', '初始化管理器容器', {
            managerKeys: Object.keys(this.managers)
        });
        
        // 应用配置
        this.config = {
            // 默认配置
            autoInit: true,
            enablePlugins: true,
            enableWorkflow: true,
            defaultLanguage: 'zh-CN',
            defaultTheme: 'default',
            defaultDocumentType: 'receipt',
            
            // 工作流配置
            workflow: {
                autoSave: true,
                autoPreview: true,
                smartTemplateSelection: true,
                batchProcessing: false
            },
            
            // UI配置
            ui: {
                showToolbar: true,
                showSidebar: true,
                showStatusBar: true,
                enableKeyboardShortcuts: true
            },
            
            // 导出配置
            export: {
                defaultFormat: 'pdf',
                autoDownload: true,
                showProgress: true,
                enableBatch: true
            },
            
            // 合并用户配置
            ...config
        };
        
        this.logger.info('UnifiedApp', 'constructor', '应用配置完成', {
            finalConfig: this.config
        });
        
        // 应用统计
        this.stats = {
            documentsGenerated: 0,
            templatesUsed: new Set(),
            exportFormats: new Map(),
            averageProcessingTime: 0,
            totalProcessingTime: 0,
            startTime: Date.now()
        };
        
        this.logger.debug('UnifiedApp', 'constructor', '统计数据初始化', {
            stats: this.stats
        });
        
        // 自动初始化（异步执行，不阻塞构造函数）
        if (this.config.autoInit) {
            this.logger.info('UnifiedApp', 'constructor', '启动自动初始化');
            // 使用 setTimeout 确保构造函数完成后再执行初始化
            setTimeout(() => {
                this._initializeApp().catch(error => {
                    this.logger.error('UnifiedApp', 'constructor', '自动初始化失败', { error });
                    this.emit('app:error', error);
                });
            }, 0);
        } else {
            this.logger.warn('UnifiedApp', 'constructor', '自动初始化已禁用，需要手动调用initialize()');
        }
        
        this.logger.info('UnifiedApp', 'constructor', 'SmartOffice统一应用实例构造完成');
    }

    /**
     * 初始化应用程序 - 主要初始化方法
     * @returns {Promise<Object>} 初始化结果
     */
    async initialize() {
        if (this.isInitialized) {
            this.logger.warn('UnifiedApp', 'initialize', '应用已经初始化，跳过重复初始化');
            return this.getAppInfo();
        }

        const startTime = Date.now();
        
        try {
            this.logger.info('UnifiedApp', 'initialize', '🚀 开始初始化SmartOffice统一应用');
            
            // 触发初始化开始事件
            this.emit('app:init-start', {
                timestamp: new Date(),
                version: this.version
            });
            
            // 执行初始化步骤
            await this._initializeApp();
            
            // 标记为已初始化
            this.isInitialized = true;
            this.isReady = true;
            
            const initDuration = Date.now() - startTime;
            
            // 触发初始化完成事件
            this.emit('app:init-complete', {
                duration: initDuration,
                timestamp: new Date(),
                version: this.version
            });
            
            this.logger.info('UnifiedApp', 'initialize', `✅ SmartOffice统一应用初始化完成，耗时: ${initDuration}ms`);
            
            return this.getAppInfo();
            
        } catch (error) {
            const initDuration = Date.now() - startTime;
            
            this.logger.error('UnifiedApp', 'initialize', '应用初始化失败', {
                error: error.message,
                duration: initDuration,
                stack: error.stack
            });
            
            // 触发初始化错误事件
            this.emit('app:init-error', {
                error: error.message,
                duration: initDuration,
                timestamp: new Date()
            });
            
            throw error;
        }
    }

    /**
     * 内部初始化应用程序
     * @private
     * @returns {Promise<void>}
     */
    async _initializeApp() {
        this.logger.info('UnifiedApp', '_initializeApp', '开始内部应用初始化流程');
        
        // 1. 初始化核心管理器
        await this._initializeCoreManagers();
        
        // 2. 初始化业务管理器
        await this._initializeBusinessManagers();
        
        // 3. 初始化UI组件
        await this._initializeUIComponents();
        
        // 4. 初始化插件系统
        await this._initializePlugins();
        
        // 5. 设置事件监听器
        this._setupEventListeners();
        
        // 6. 加载初始数据
        await this._loadInitialData();
        
        this.logger.info('UnifiedApp', '_initializeApp', '内部应用初始化流程完成');
    }

    /**
     * 初始化核心管理器
     * @private
     * @returns {Promise<void>}
     */
    async _initializeCoreManagers() {
        this.logger.info('UnifiedApp', '_initializeCoreManagers', '初始化核心管理器');
        
        try {
            // 配置管理器
            this.managers.config = getConfigManager();
            this.logger.debug('UnifiedApp', '_initializeCoreManagers', '配置管理器初始化完成');
            
            // 状态管理器
            this.managers.state = new GlobalStateManager();
            await this.managers.state.initialize();
            this.logger.debug('UnifiedApp', '_initializeCoreManagers', '状态管理器初始化完成');
            
            // 本地资源管理器
            this.managers.resources = getLocalResourceManager();
            this.logger.debug('UnifiedApp', '_initializeCoreManagers', '本地资源管理器初始化完成');
            
            // 本地NLP处理器
            this.managers.nlp = getLocalNLPProcessor();
            this.logger.debug('UnifiedApp', '_initializeCoreManagers', '本地NLP处理器初始化完成');
            
            this.logger.info('UnifiedApp', '_initializeCoreManagers', '核心管理器初始化完成');
            
        } catch (error) {
            this.logger.error('UnifiedApp', '_initializeCoreManagers', '核心管理器初始化失败', error);
            throw error;
        }
    }

    /**
     * 初始化业务管理器
     * @private
     * @returns {Promise<void>}
     */
    async _initializeBusinessManagers() {
        this.logger.info('UnifiedApp', '_initializeBusinessManagers', '初始化业务管理器');

        try {
            // 工作流引擎
            if (this.config.enableWorkflow) {
                this.managers.workflow = new WorkflowEngine();
                await this.managers.workflow.initialize();
                this.logger.debug('UnifiedApp', '_initializeBusinessManagers', '工作流引擎初始化完成');
            }

            // 模板管理器
            this.managers.templates = new TemplateManager();
            await this.managers.templates.initialize();
            this.logger.debug('UnifiedApp', '_initializeBusinessManagers', '模板管理器初始化完成');

            // 渲染器管理器
            this.managers.renderers = new RendererManager();
            await this.managers.renderers.initialize();
            this.logger.debug('UnifiedApp', '_initializeBusinessManagers', '渲染器管理器初始化完成');

            // 导出器管理器
            this.managers.exporters = new ExporterManager();
            await this.managers.exporters.initialize();
            this.logger.debug('UnifiedApp', '_initializeBusinessManagers', '导出器管理器初始化完成');

            // 统一渲染引擎
            this.managers.renderEngine = new UnifiedRenderEngine();
            this.managers.styleManager = new StyleManager();
            this.managers.positionManager = new PositionManager();
            this.logger.debug('UnifiedApp', '_initializeBusinessManagers', '统一渲染架构初始化完成');

            this.logger.info('UnifiedApp', '_initializeBusinessManagers', '业务管理器初始化完成');

        } catch (error) {
            this.logger.error('UnifiedApp', '_initializeBusinessManagers', '业务管理器初始化失败', error);
            throw error;
        }
    }

    /**
     * 初始化UI组件
     * @private
     * @returns {Promise<void>}
     */
    async _initializeUIComponents() {
        this.logger.info('UnifiedApp', '_initializeUIComponents', '初始化UI组件');

        try {
            // UI组件管理器
            this.managers.components = new UIComponentManager(this.container);
            await this.managers.components.initialize();
            this.logger.debug('UnifiedApp', '_initializeUIComponents', 'UI组件管理器初始化完成');

            this.logger.info('UnifiedApp', '_initializeUIComponents', 'UI组件初始化完成');

        } catch (error) {
            this.logger.error('UnifiedApp', '_initializeUIComponents', 'UI组件初始化失败', error);
            throw error;
        }
    }

    /**
     * 初始化插件系统
     * @private
     * @returns {Promise<void>}
     */
    async _initializePlugins() {
        this.logger.info('UnifiedApp', '_initializePlugins', '初始化插件系统');

        try {
            if (this.config.enablePlugins) {
                this.managers.plugins = new PluginSystem();
                await this.managers.plugins.initialize();
                this.logger.debug('UnifiedApp', '_initializePlugins', '插件系统初始化完成');
            } else {
                this.logger.info('UnifiedApp', '_initializePlugins', '插件系统已禁用');
            }

        } catch (error) {
            this.logger.error('UnifiedApp', '_initializePlugins', '插件系统初始化失败', error);
            // 插件系统失败不阻止应用启动
        }
    }

    /**
     * 设置事件监听器
     * @private
     */
    _setupEventListeners() {
        this.logger.info('UnifiedApp', '_setupEventListeners', '设置事件监听器');

        // 监听状态变化
        if (this.managers.state) {
            this.managers.state.on('state:changed', (data) => {
                this.emit('app:state-changed', data);
            });
        }

        // 监听工作流事件
        if (this.managers.workflow) {
            this.managers.workflow.on('workflow:completed', (data) => {
                this.emit('app:workflow-completed', data);
            });
        }

        // 监听渲染事件
        if (this.managers.renderers) {
            this.managers.renderers.on('render:completed', (data) => {
                this.emit('app:render-completed', data);
                this._updateStats('render', data);
            });
        }

        // 监听导出事件
        if (this.managers.exporters) {
            this.managers.exporters.on('export:completed', (data) => {
                this.emit('app:export-completed', data);
                this._updateStats('export', data);
            });
        }

        this.logger.debug('UnifiedApp', '_setupEventListeners', '事件监听器设置完成');
    }

    /**
     * 加载初始数据
     * @private
     * @returns {Promise<void>}
     */
    async _loadInitialData() {
        this.logger.info('UnifiedApp', '_loadInitialData', '加载初始数据');

        try {
            // 加载用户配置
            await this._loadUserConfig();

            // 加载模板数据
            await this._loadTemplates();

            // 设置默认文档类型
            if (this.managers.state) {
                this.managers.state.set('currentDocumentType', this.config.defaultDocumentType);
            }

            this.logger.info('UnifiedApp', '_loadInitialData', '初始数据加载完成');

        } catch (error) {
            this.logger.warn('UnifiedApp', '_loadInitialData', '初始数据加载失败', error);
            // 不阻止应用启动，使用默认配置
        }
    }

    /**
     * 加载用户配置
     * @private
     * @returns {Promise<void>}
     */
    async _loadUserConfig() {
        try {
            const savedConfig = localStorage.getItem('smartoffice-config');
            if (savedConfig) {
                const config = JSON.parse(savedConfig);
                if (this.managers.config) {
                    this.managers.config.merge(config);
                }
                this.logger.debug('UnifiedApp', '_loadUserConfig', '用户配置已加载');
            }
        } catch (error) {
            this.logger.warn('UnifiedApp', '_loadUserConfig', '用户配置解析失败', error);
        }
    }

    /**
     * 加载模板数据
     * @private
     * @returns {Promise<void>}
     */
    async _loadTemplates() {
        try {
            if (this.managers.templates) {
                await this.managers.templates.loadDefaultTemplates();
                this.logger.debug('UnifiedApp', '_loadTemplates', '模板数据已加载');
            }
        } catch (error) {
            this.logger.warn('UnifiedApp', '_loadTemplates', '模板数据加载失败', error);
        }
    }

    /**
     * 更新统计信息
     * @private
     * @param {string} type - 操作类型
     * @param {Object} data - 操作数据
     */
    _updateStats(type, data) {
        switch (type) {
            case 'render':
                this.stats.documentsGenerated++;
                if (data.template) {
                    this.stats.templatesUsed.add(data.template);
                }
                break;
            case 'export':
                if (data.format) {
                    const count = this.stats.exportFormats.get(data.format) || 0;
                    this.stats.exportFormats.set(data.format, count + 1);
                }
                break;
        }

        if (data.processingTime) {
            this.stats.totalProcessingTime += data.processingTime;
            this.stats.averageProcessingTime =
                this.stats.totalProcessingTime / this.stats.documentsGenerated;
        }
    }

    /**
     * 获取应用信息
     * @returns {Object} 应用信息对象
     */
    getAppInfo() {
        return {
            name: this.name,
            version: this.version,
            description: this.description,
            mode: this.mode,
            isInitialized: this.isInitialized,
            isReady: this.isReady,
            uptime: Date.now() - this.stats.startTime,
            stats: {
                ...this.stats,
                templatesUsed: Array.from(this.stats.templatesUsed),
                exportFormats: Object.fromEntries(this.stats.exportFormats)
            },
            managers: Object.keys(this.managers).filter(key => this.managers[key] !== null),
            timestamp: new Date().toISOString(),
            features: {
                nlp: 'local',
                export: 'full',
                offline: true,
                modules: 'unified',
                refactored: true
            }
        };
    }

    /**
     * 获取管理器
     * @param {string} managerName - 管理器名称
     * @returns {Object|null} 管理器实例
     */
    getManager(managerName) {
        return this.managers[managerName] || null;
    }

    /**
     * 获取所有管理器名称
     * @returns {Array<string>} 管理器名称列表
     */
    getManagerNames() {
        return Object.keys(this.managers).filter(key => this.managers[key] !== null);
    }

    /**
     * 检查应用是否就绪
     * @returns {boolean} 是否就绪
     */
    isAppReady() {
        return this.isInitialized && this.isReady;
    }

    /**
     * 获取应用状态
     * @returns {Object} 应用状态
     */
    getAppState() {
        return {
            isInitialized: this.isInitialized,
            isReady: this.isReady,
            managersCount: this.getManagerNames().length,
            uptime: Date.now() - this.stats.startTime,
            memoryUsage: this._getMemoryUsage()
        };
    }

    /**
     * 获取内存使用情况
     * @private
     * @returns {Object} 内存使用信息
     */
    _getMemoryUsage() {
        if (performance.memory) {
            return {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            };
        }
        return null;
    }

    /**
     * 重启应用
     * @returns {Promise<Object>} 重启结果
     */
    async restart() {
        this.logger.info('UnifiedApp', 'restart', '重启应用');

        try {
            // 清理当前状态
            await this.destroy();

            // 重新初始化
            await this.initialize();

            this.logger.info('UnifiedApp', 'restart', '应用重启完成');
            return this.getAppInfo();

        } catch (error) {
            this.logger.error('UnifiedApp', 'restart', '应用重启失败', error);
            throw error;
        }
    }

    /**
     * 销毁应用
     * @returns {Promise<void>}
     */
    async destroy() {
        this.logger.info('UnifiedApp', 'destroy', '销毁应用');

        try {
            // 销毁所有管理器
            for (const [name, manager] of Object.entries(this.managers)) {
                if (manager && typeof manager.destroy === 'function') {
                    await manager.destroy();
                    this.managers[name] = null;
                }
            }

            // 清理事件监听器
            this.removeAllListeners();

            // 重置状态
            this.isInitialized = false;
            this.isReady = false;

            this.logger.info('UnifiedApp', 'destroy', '应用销毁完成');

        } catch (error) {
            this.logger.error('UnifiedApp', 'destroy', '应用销毁失败', error);
            throw error;
        }
    }
}
// #endregion

// #region 工厂函数和便捷方法
/**
 * 创建统一应用实例
 * @param {HTMLElement} container - 应用容器
 * @param {Object} config - 应用配置
 * @returns {UnifiedApp} 统一应用实例
 */
export function createUnifiedApp(container = null, config = {}) {
    return new UnifiedApp(container, config);
}

/**
 * 快速启动统一应用
 * @param {Object} options - 启动选项
 * @returns {Promise<UnifiedApp>} 初始化完成的应用实例
 */
export async function quickStartUnified(options = {}) {
    const {
        container = null,
        config = {},
        autoInit = true
    } = options;

    const app = createUnifiedApp(container, { ...config, autoInit });

    if (autoInit && !app.isAppReady()) {
        await app.initialize();
    }

    return app;
}

// 全局应用实例管理
let globalUnifiedApp = null;

/**
 * 获取全局统一应用实例
 * @param {Object} options - 创建选项
 * @returns {UnifiedApp} 全局应用实例
 */
export function getUnifiedApp(options = {}) {
    if (!globalUnifiedApp) {
        globalUnifiedApp = createUnifiedApp(options.container, options.config);
    }
    return globalUnifiedApp;
}

/**
 * 设置全局统一应用实例
 * @param {UnifiedApp} app - 应用实例
 */
export function setUnifiedApp(app) {
    if (!(app instanceof UnifiedApp)) {
        throw new Error('必须提供UnifiedApp实例');
    }
    globalUnifiedApp = app;
}

/**
 * 重置全局统一应用实例
 */
export function resetUnifiedApp() {
    if (globalUnifiedApp) {
        globalUnifiedApp.destroy().catch(console.error);
        globalUnifiedApp = null;
    }
}
// #endregion

// #region 导出
export { UnifiedApp };
export default UnifiedApp;
// #endregion
