/**
 * @file 配置管理器 - 统一的应用配置管理
 * <AUTHOR> Team
 * @description 
 * 这个文件定义了配置管理器类，负责管理整个应用的配置信息
 * 从index.html中提取的配置相关代码被重构到这里
 * 
 * 功能特性：
 * - 分层配置管理（默认配置、用户配置、运行时配置）
 * - 配置变更监听和事件通知
 * - 配置验证和类型检查
 * - 配置持久化（localStorage）
 * - 配置导入导出
 */

// #region 配置管理器类定义
/**
 * @class ConfigManager - 配置管理器类
 * @description 管理应用程序的所有配置信息
 */
export class ConfigManager {
    /**
     * 构造函数 - 初始化配置管理器
     */
    constructor() {
        // 默认配置
        this.defaultConfig = {
            // 应用基础配置
            app: {
                name: 'SmartOffice',
                version: '2.0.0',
                language: 'zh-CN',
                theme: 'default',
                autoSave: true,
                enableDebug: false,
                enableAnalytics: false
            },
            
            // NLP处理配置
            nlp: {
                enabled: true,
                mode: 'local', // 'local', 'gemini'
                geminiEnabled: false,
                confidence: {
                    minimum: 0.3,
                    good: 0.6,
                    excellent: 0.8
                },
                timeout: 30000, // 30秒超时
                retryAttempts: 3
            },
            
            // 文档配置
            document: {
                defaultType: 'receipt',
                autoPreview: true,
                autoSave: true,
                saveInterval: 30000, // 30秒自动保存
                maxHistory: 50
            },
            
            // 导出配置
            export: {
                defaultFormat: 'pdf',
                quality: 'high',
                autoDownload: true,
                showProgress: true,
                enableBatch: true,
                maxConcurrent: 3
            },
            
            // UI配置
            ui: {
                showToolbar: true,
                showSidebar: true,
                showStatusBar: true,
                enableKeyboardShortcuts: true,
                enableAnimations: true,
                compactMode: false
            },
            
            // 公司配置
            company: {
                selected: 'gomyhire',
                currency: 'RM',
                paymentMethod: 'online',
                showSignature: false
            },
            
            // 模板配置
            template: {
                style: 'modern',
                enableHeaderFooter: true,
                enableStamp: true,
                bilingualMode: true
            },
            
            // 性能配置
            performance: {
                enableCache: true,
                cacheSize: 100,
                enableLazyLoading: true,
                enableOptimization: true
            }
        };
        
        // 当前配置（合并后的配置）
        this.config = JSON.parse(JSON.stringify(this.defaultConfig));
        
        // 配置变更监听器
        this.listeners = new Map();
        
        // 配置验证规则
        this.validationRules = this._initValidationRules();
        
        // 初始化配置
        this._initialize();
        
        console.log('[ConfigManager] 配置管理器已初始化');
    }

    /**
     * 初始化配置管理器
     * @private
     */
    _initialize() {
        // 从localStorage加载保存的配置
        this._loadFromStorage();
        
        // 验证配置
        this._validateConfig();
        
        // 设置自动保存
        if (this.config.app.autoSave) {
            this._setupAutoSave();
        }
    }

    /**
     * 初始化验证规则
     * @returns {Object} 验证规则对象
     * @private
     */
    _initValidationRules() {
        return {
            'app.language': {
                type: 'string',
                enum: ['zh-CN', 'en-US', 'zh-TW']
            },
            'app.theme': {
                type: 'string',
                enum: ['default', 'dark', 'light', 'classic', 'modern']
            },
            'nlp.mode': {
                type: 'string',
                enum: ['local', 'gemini']
            },
            'nlp.confidence.minimum': {
                type: 'number',
                min: 0,
                max: 1
            },
            'nlp.confidence.good': {
                type: 'number',
                min: 0,
                max: 1
            },
            'nlp.confidence.excellent': {
                type: 'number',
                min: 0,
                max: 1
            },
            'document.defaultType': {
                type: 'string',
                enum: ['receipt', 'invoice', 'quotation', 'driver_agreement']
            },
            'export.defaultFormat': {
                type: 'string',
                enum: ['pdf', 'image', 'html']
            },
            'export.quality': {
                type: 'string',
                enum: ['low', 'medium', 'high']
            },
            'company.selected': {
                type: 'string',
                enum: ['gomyhire', 'sky-mirror']
            },
            'company.currency': {
                type: 'string',
                enum: ['RM', 'RMB', 'USD', 'EUR']
            }
        };
    }

    /**
     * 获取配置值
     * @param {string} path - 配置路径，使用点号分隔
     * @param {*} defaultValue - 默认值
     * @returns {*} 配置值
     */
    get(path, defaultValue = undefined) {
        const keys = path.split('.');
        let value = this.config;
        
        for (const key of keys) {
            if (value && typeof value === 'object' && key in value) {
                value = value[key];
            } else {
                return defaultValue;
            }
        }
        
        return value;
    }

    /**
     * 设置配置值
     * @param {string} path - 配置路径，使用点号分隔
     * @param {*} value - 配置值
     * @returns {boolean} 是否设置成功
     */
    set(path, value) {
        try {
            // 验证配置值
            if (!this._validateValue(path, value)) {
                console.warn(`[ConfigManager] 配置值验证失败: ${path} = ${value}`);
                return false;
            }
            
            const keys = path.split('.');
            let current = this.config;
            
            // 导航到目标位置
            for (let i = 0; i < keys.length - 1; i++) {
                const key = keys[i];
                if (!(key in current) || typeof current[key] !== 'object') {
                    current[key] = {};
                }
                current = current[key];
            }
            
            // 获取旧值
            const lastKey = keys[keys.length - 1];
            const oldValue = current[lastKey];
            
            // 设置新值
            current[lastKey] = value;
            
            // 触发变更事件
            this._notifyListeners(path, value, oldValue);
            
            // 自动保存
            if (this.config.app.autoSave) {
                this._saveToStorage();
            }
            
            console.log(`[ConfigManager] 配置已更新: ${path} = ${value}`);
            return true;
            
        } catch (error) {
            console.error(`[ConfigManager] 设置配置失败: ${path}`, error);
            return false;
        }
    }

    /**
     * 批量设置配置
     * @param {Object} configObject - 配置对象
     * @returns {boolean} 是否设置成功
     */
    setMultiple(configObject) {
        try {
            const changes = [];
            
            // 收集所有变更
            for (const [path, value] of Object.entries(configObject)) {
                const oldValue = this.get(path);
                if (this._validateValue(path, value)) {
                    this.set(path, value);
                    changes.push({ path, value, oldValue });
                }
            }
            
            // 触发批量变更事件
            this._notifyListeners('*', changes, null);
            
            return true;
            
        } catch (error) {
            console.error('[ConfigManager] 批量设置配置失败:', error);
            return false;
        }
    }

    /**
     * 重置配置到默认值
     * @param {string} [path] - 要重置的配置路径，不提供则重置所有配置
     */
    reset(path) {
        if (path) {
            // 重置指定路径的配置
            const defaultValue = this._getDefaultValue(path);
            if (defaultValue !== undefined) {
                this.set(path, defaultValue);
            }
        } else {
            // 重置所有配置
            this.config = JSON.parse(JSON.stringify(this.defaultConfig));
            this._notifyListeners('*', this.config, null);
            
            if (this.config.app.autoSave) {
                this._saveToStorage();
            }
        }
        
        console.log(`[ConfigManager] 配置已重置: ${path || 'all'}`);
    }

    /**
     * 合并配置对象
     * @param {Object} configObject - 要合并的配置对象
     */
    merge(configObject) {
        try {
            this.config = this._deepMerge(this.config, configObject);
            this._validateConfig();
            this._notifyListeners('*', this.config, null);
            
            if (this.config.app.autoSave) {
                this._saveToStorage();
            }
            
            console.log('[ConfigManager] 配置已合并');
            
        } catch (error) {
            console.error('[ConfigManager] 合并配置失败:', error);
        }
    }

    /**
     * 添加配置变更监听器
     * @param {string} event - 事件名称（配置路径或'*'表示所有变更）
     * @param {Function} listener - 监听器函数
     */
    on(event, listener) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(listener);
        
        console.log(`[ConfigManager] 已添加配置监听器: ${event}`);
    }

    /**
     * 移除配置变更监听器
     * @param {string} event - 事件名称
     * @param {Function} listener - 监听器函数
     */
    off(event, listener) {
        if (this.listeners.has(event)) {
            const listeners = this.listeners.get(event);
            const index = listeners.indexOf(listener);
            if (index > -1) {
                listeners.splice(index, 1);
                console.log(`[ConfigManager] 已移除配置监听器: ${event}`);
            }
        }
    }

    /**
     * 导出配置
     * @returns {Object} 配置对象的深拷贝
     */
    export() {
        return JSON.parse(JSON.stringify(this.config));
    }

    /**
     * 导入配置
     * @param {Object} configObject - 要导入的配置对象
     * @param {boolean} [merge=true] - 是否合并现有配置
     */
    import(configObject, merge = true) {
        try {
            if (merge) {
                this.merge(configObject);
            } else {
                this.config = JSON.parse(JSON.stringify(configObject));
                this._validateConfig();
                this._notifyListeners('*', this.config, null);
            }
            
            console.log('[ConfigManager] 配置已导入');
            
        } catch (error) {
            console.error('[ConfigManager] 导入配置失败:', error);
        }
    }

    /**
     * 获取配置摘要信息
     * @returns {Object} 配置摘要
     */
    getSummary() {
        return {
            app: {
                name: this.config.app.name,
                version: this.config.app.version,
                language: this.config.app.language,
                theme: this.config.app.theme
            },
            nlp: {
                mode: this.config.nlp.mode,
                enabled: this.config.nlp.enabled
            },
            document: {
                defaultType: this.config.document.defaultType
            },
            export: {
                defaultFormat: this.config.export.defaultFormat,
                quality: this.config.export.quality
            },
            company: {
                selected: this.config.company.selected,
                currency: this.config.company.currency
            }
        };
    }

    /**
     * 验证配置值
     * @param {string} path - 配置路径
     * @param {*} value - 配置值
     * @returns {boolean} 是否有效
     * @private
     */
    _validateValue(path, value) {
        const rule = this.validationRules[path];
        if (!rule) {
            return true; // 没有验证规则，认为有效
        }
        
        // 类型检查
        if (rule.type && typeof value !== rule.type) {
            return false;
        }
        
        // 枚举检查
        if (rule.enum && !rule.enum.includes(value)) {
            return false;
        }
        
        // 数值范围检查
        if (rule.type === 'number') {
            if (rule.min !== undefined && value < rule.min) {
                return false;
            }
            if (rule.max !== undefined && value > rule.max) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 验证整个配置对象
     * @private
     */
    _validateConfig() {
        // 这里可以实现更复杂的配置验证逻辑
        console.log('[ConfigManager] 配置验证完成');
    }

    /**
     * 获取默认配置值
     * @param {string} path - 配置路径
     * @returns {*} 默认值
     * @private
     */
    _getDefaultValue(path) {
        const keys = path.split('.');
        let value = this.defaultConfig;
        
        for (const key of keys) {
            if (value && typeof value === 'object' && key in value) {
                value = value[key];
            } else {
                return undefined;
            }
        }
        
        return value;
    }

    /**
     * 深度合并对象
     * @param {Object} target - 目标对象
     * @param {Object} source - 源对象
     * @returns {Object} 合并后的对象
     * @private
     */
    _deepMerge(target, source) {
        const result = { ...target };
        
        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
                    result[key] = this._deepMerge(result[key] || {}, source[key]);
                } else {
                    result[key] = source[key];
                }
            }
        }
        
        return result;
    }

    /**
     * 通知配置变更监听器
     * @param {string} path - 配置路径
     * @param {*} newValue - 新值
     * @param {*} oldValue - 旧值
     * @private
     */
    _notifyListeners(path, newValue, oldValue) {
        // 通知特定路径的监听器
        const pathListeners = this.listeners.get(path) || [];
        pathListeners.forEach(listener => {
            try {
                listener({ path, newValue, oldValue, timestamp: new Date() });
            } catch (error) {
                console.error('[ConfigManager] 监听器执行失败:', error);
            }
        });
        
        // 通知全局监听器
        if (path !== '*') {
            const globalListeners = this.listeners.get('*') || [];
            globalListeners.forEach(listener => {
                try {
                    listener({ path, newValue, oldValue, timestamp: new Date() });
                } catch (error) {
                    console.error('[ConfigManager] 全局监听器执行失败:', error);
                }
            });
        }
    }

    /**
     * 从localStorage加载配置
     * @private
     */
    _loadFromStorage() {
        try {
            const saved = localStorage.getItem('smartoffice-config');
            if (saved) {
                const savedConfig = JSON.parse(saved);
                this.merge(savedConfig);
                console.log('[ConfigManager] 已从localStorage加载配置');
            }
        } catch (error) {
            console.warn('[ConfigManager] 从localStorage加载配置失败:', error);
        }
    }

    /**
     * 保存配置到localStorage
     * @private
     */
    _saveToStorage() {
        try {
            localStorage.setItem('smartoffice-config', JSON.stringify(this.config));
            console.log('[ConfigManager] 已保存配置到localStorage');
        } catch (error) {
            console.warn('[ConfigManager] 保存配置到localStorage失败:', error);
        }
    }

    /**
     * 设置自动保存
     * @private
     */
    _setupAutoSave() {
        // 每30秒自动保存一次
        setInterval(() => {
            this._saveToStorage();
        }, 30000);
        
        console.log('[ConfigManager] 自动保存已启用');
    }

    /**
     * 销毁配置管理器
     */
    destroy() {
        // 最后一次保存
        this._saveToStorage();
        
        // 清理监听器
        this.listeners.clear();
        
        console.log('[ConfigManager] 配置管理器已销毁');
    }
}
// #endregion
