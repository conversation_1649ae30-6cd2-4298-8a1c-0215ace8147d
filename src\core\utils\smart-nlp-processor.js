/**
 * @file 智能NLP处理器 - 优先Gemini，本地备用
 * @description 智能NLP处理器，优先调用Gemini进行文字/多模态分析，失败时自动降级到本地处理器
 * <AUTHOR> Team
 */

import { getGeminiNLPProcessor } from './gemini-nlp-processor.js';
import { getLocalNLPProcessor } from './local-nlp-processor.js';
import { getLogger } from './logger.js';

/**
 * @class SmartNLPProcessor
 * @description 智能NLP处理器类
 * 优先使用Gemini API进行高精度分析，失败时自动降级到本地处理器
 */
export class SmartNLPProcessor {
    constructor(options = {}) {
        this.logger = options.logger || getLogger();
        this.geminiConfig = options.geminiConfig || {};
        this.localConfig = options.localConfig || {};
        
        this.logger.info('SmartNLPProcessor', 'constructor', '🧠 初始化智能NLP处理器');
        
        // 初始化处理器
        this._initializeProcessors();
        
        // 统计信息
        this.stats = {
            totalRequests: 0,
            geminiSuccess: 0,
            geminiFailures: 0,
            localFallbacks: 0,
            averageResponseTime: 0,
            lastUsedProcessor: null
        };
        
        this.logger.info('SmartNLPProcessor', 'constructor', '✅ 智能NLP处理器初始化完成');
    }

    /**
     * @function _initializeProcessors
     * @description 初始化Gemini和本地处理器
     * @private
     */
    _initializeProcessors() {
        try {
            // 初始化Gemini处理器（如果有API密钥）
            if (this.geminiConfig.apiKey) {
                this.geminiProcessor = getGeminiNLPProcessor(this.geminiConfig);
                this.logger.debug('SmartNLPProcessor', '_initializeProcessors', '✅ Gemini处理器初始化成功');
            } else {
                this.logger.warn('SmartNLPProcessor', '_initializeProcessors', '⚠️ 未提供Gemini API密钥，将仅使用本地处理器');
            }
            
            // 初始化本地处理器（备用）
            this.localProcessor = getLocalNLPProcessor();
            this.logger.debug('SmartNLPProcessor', '_initializeProcessors', '✅ 本地处理器初始化成功');
            
        } catch (error) {
            this.logger.error('SmartNLPProcessor', '_initializeProcessors', '处理器初始化失败', { error });
            throw error;
        }
    }

    /**
     * @function processText
     * @description 处理文本，优先使用Gemini，失败时降级到本地处理器
     * @param {string} text - 输入文本
     * @param {string} documentType - 文档类型
     * @returns {Object} 处理结果
     */
    async processText(text, documentType = 'receipt') {
        const startTime = performance.now();
        this.stats.totalRequests++;
        
        this.logger.startPerformanceMark('smart_nlp_process_text', 'SmartNLPProcessor', 'processText');
        this.logger.info('SmartNLPProcessor', 'processText', '🔍 开始智能文本分析', {
            textLength: text.length,
            documentType,
            hasGemini: !!this.geminiProcessor,
            hasLocal: !!this.localProcessor
        });

        let result = null;
        let processorUsed = null;
        let error = null;

        // 优先尝试Gemini处理器
        if (this.geminiProcessor) {
            try {
                this.logger.debug('SmartNLPProcessor', 'processText', '🤖 尝试使用Gemini处理器');
                result = await this.geminiProcessor.processText(text, documentType);
                
                if (result && result.extractedData) {
                    processorUsed = 'gemini';
                    this.stats.geminiSuccess++;
                    this.logger.info('SmartNLPProcessor', 'processText', '✅ Gemini处理成功', {
                        confidence: result.confidence,
                        extractedFields: Object.keys(result.extractedData).length
                    });
                } else {
                    throw new Error('Gemini返回空结果');
                }
                
            } catch (geminiError) {
                this.stats.geminiFailures++;
                error = geminiError;
                this.logger.warn('SmartNLPProcessor', 'processText', '⚠️ Gemini处理失败，降级到本地处理器', {
                    error: geminiError.message
                });
            }
        }

        // 如果Gemini失败或不可用，使用本地处理器
        if (!result && this.localProcessor) {
            try {
                this.logger.debug('SmartNLPProcessor', 'processText', '🏠 使用本地处理器');
                result = await this.localProcessor.processText(text, documentType);
                
                if (result && result.extractedData) {
                    processorUsed = 'local';
                    this.stats.localFallbacks++;
                    this.logger.info('SmartNLPProcessor', 'processText', '✅ 本地处理成功', {
                        confidence: result.confidence,
                        extractedFields: Object.keys(result.extractedData).length
                    });
                } else {
                    throw new Error('本地处理器返回空结果');
                }
                
            } catch (localError) {
                this.logger.error('SmartNLPProcessor', 'processText', '❌ 本地处理器也失败了', {
                    error: localError.message,
                    originalGeminiError: error?.message
                });
                throw new Error(`所有NLP处理器都失败了。Gemini错误: ${error?.message || 'N/A'}，本地错误: ${localError.message}`);
            }
        }

        // 如果所有处理器都不可用
        if (!result) {
            throw new Error('没有可用的NLP处理器');
        }

        // 添加处理器信息到结果
        result.processorUsed = processorUsed;
        result.fallbackUsed = processorUsed === 'local' && !!this.geminiProcessor;

        // 更新统计信息
        const duration = performance.now() - startTime;
        this.stats.averageResponseTime = (this.stats.averageResponseTime * (this.stats.totalRequests - 1) + duration) / this.stats.totalRequests;
        this.stats.lastUsedProcessor = processorUsed;

        const processDuration = this.logger.endPerformanceMark('smart_nlp_process_text', 'SmartNLPProcessor', 'processText');
        this.logger.info('SmartNLPProcessor', 'processText', '✅ 智能文本分析完成', {
            duration: `${processDuration?.toFixed(2)}ms`,
            processorUsed,
            fallbackUsed: result.fallbackUsed,
            confidence: result.confidence,
            extractedFields: Object.keys(result.extractedData).length
        });

        return result;
    }

    /**
     * @function processImage
     * @description 处理图片，优先使用Gemini（仅Gemini支持图片处理）
     * @param {File|string} image - 图片文件或base64字符串
     * @param {string} text - 附加文本
     * @param {string} documentType - 文档类型
     * @returns {Object} 处理结果
     */
    async processImage(image, text = '', documentType = 'receipt') {
        const startTime = performance.now();
        this.stats.totalRequests++;
        
        this.logger.startPerformanceMark('smart_nlp_process_image', 'SmartNLPProcessor', 'processImage');
        this.logger.info('SmartNLPProcessor', 'processImage', '🖼️ 开始智能图片分析', {
            hasText: !!text,
            textLength: text.length,
            documentType,
            hasGemini: !!this.geminiProcessor
        });

        // 图片处理仅支持Gemini
        if (!this.geminiProcessor) {
            this.logger.warn('SmartNLPProcessor', 'processImage', '⚠️ 图片处理需要Gemini API，降级到文本分析');
            if (text) {
                return await this.processText(text, documentType);
            } else {
                throw new Error('图片处理需要Gemini API，且没有提供文本内容作为备用');
            }
        }

        let result = null;
        let processorUsed = 'gemini';

        try {
            this.logger.debug('SmartNLPProcessor', 'processImage', '🤖 使用Gemini处理图片');
            result = await this.geminiProcessor.processImage(image, text, documentType);
            
            if (result && result.extractedData) {
                this.stats.geminiSuccess++;
                this.logger.info('SmartNLPProcessor', 'processImage', '✅ Gemini图片处理成功', {
                    confidence: result.confidence,
                    extractedFields: Object.keys(result.extractedData).length
                });
            } else {
                throw new Error('Gemini图片处理返回空结果');
            }
            
        } catch (geminiError) {
            this.stats.geminiFailures++;
            this.logger.warn('SmartNLPProcessor', 'processImage', '⚠️ Gemini图片处理失败', {
                error: geminiError.message
            });

            // 如果有文本内容，尝试降级到文本处理
            if (text && text.trim()) {
                this.logger.info('SmartNLPProcessor', 'processImage', '🔄 降级到文本分析');
                result = await this.processText(text, documentType);
                processorUsed = result.processorUsed;
                result.fallbackUsed = true;
                result.fallbackReason = 'image_processing_failed';
            } else {
                throw new Error(`Gemini图片处理失败且没有文本内容作为备用: ${geminiError.message}`);
            }
        }

        // 添加处理器信息到结果
        result.processorUsed = processorUsed;
        result.imageProcessed = processorUsed === 'gemini';

        // 更新统计信息
        const duration = performance.now() - startTime;
        this.stats.averageResponseTime = (this.stats.averageResponseTime * (this.stats.totalRequests - 1) + duration) / this.stats.totalRequests;
        this.stats.lastUsedProcessor = processorUsed;

        const processDuration = this.logger.endPerformanceMark('smart_nlp_process_image', 'SmartNLPProcessor', 'processImage');
        this.logger.info('SmartNLPProcessor', 'processImage', '✅ 智能图片分析完成', {
            duration: `${processDuration?.toFixed(2)}ms`,
            processorUsed,
            imageProcessed: result.imageProcessed,
            fallbackUsed: result.fallbackUsed,
            confidence: result.confidence,
            extractedFields: Object.keys(result.extractedData).length
        });

        return result;
    }

    /**
     * @function processMultiModal
     * @description 处理多模态内容（文字+图片），优先使用Gemini
     * @param {string} text - 文本内容
     * @param {File|string} image - 图片文件或base64字符串
     * @param {string} documentType - 文档类型
     * @returns {Object} 处理结果
     */
    async processMultiModal(text, image, documentType = 'receipt') {
        const startTime = performance.now();
        this.stats.totalRequests++;
        
        this.logger.startPerformanceMark('smart_nlp_process_multimodal', 'SmartNLPProcessor', 'processMultiModal');
        this.logger.info('SmartNLPProcessor', 'processMultiModal', '🎭 开始智能多模态分析', {
            textLength: text.length,
            hasImage: !!image,
            documentType,
            hasGemini: !!this.geminiProcessor
        });

        // 多模态处理优先使用Gemini
        if (!this.geminiProcessor) {
            this.logger.warn('SmartNLPProcessor', 'processMultiModal', '⚠️ 多模态处理需要Gemini API，降级到文本分析');
            return await this.processText(text, documentType);
        }

        let result = null;
        let processorUsed = 'gemini';

        try {
            this.logger.debug('SmartNLPProcessor', 'processMultiModal', '🤖 使用Gemini处理多模态内容');
            
            // 如果Gemini支持多模态处理
            if (this.geminiProcessor.processMultiModal) {
                result = await this.geminiProcessor.processMultiModal(text, image, documentType);
            } else {
                // 降级到图片处理
                result = await this.geminiProcessor.processImage(image, text, documentType);
            }
            
            if (result && result.extractedData) {
                this.stats.geminiSuccess++;
                this.logger.info('SmartNLPProcessor', 'processMultiModal', '✅ Gemini多模态处理成功', {
                    confidence: result.confidence,
                    extractedFields: Object.keys(result.extractedData).length
                });
            } else {
                throw new Error('Gemini多模态处理返回空结果');
            }
            
        } catch (geminiError) {
            this.stats.geminiFailures++;
            this.logger.warn('SmartNLPProcessor', 'processMultiModal', '⚠️ Gemini多模态处理失败，降级到文本分析', {
                error: geminiError.message
            });

            // 降级到文本处理
            result = await this.processText(text, documentType);
            processorUsed = result.processorUsed;
            result.fallbackUsed = true;
            result.fallbackReason = 'multimodal_processing_failed';
        }

        // 添加处理器信息到结果
        result.processorUsed = processorUsed;
        result.multiModalProcessed = processorUsed === 'gemini';

        // 更新统计信息
        const duration = performance.now() - startTime;
        this.stats.averageResponseTime = (this.stats.averageResponseTime * (this.stats.totalRequests - 1) + duration) / this.stats.totalRequests;
        this.stats.lastUsedProcessor = processorUsed;

        const processDuration = this.logger.endPerformanceMark('smart_nlp_process_multimodal', 'SmartNLPProcessor', 'processMultiModal');
        this.logger.info('SmartNLPProcessor', 'processMultiModal', '✅ 智能多模态分析完成', {
            duration: `${processDuration?.toFixed(2)}ms`,
            processorUsed,
            multiModalProcessed: result.multiModalProcessed,
            fallbackUsed: result.fallbackUsed,
            confidence: result.confidence,
            extractedFields: Object.keys(result.extractedData).length
        });

        return result;
    }

    /**
     * @function getStats
     * @description 获取处理统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        const geminiSuccessRate = this.stats.totalRequests > 0 ? 
            (this.stats.geminiSuccess / this.stats.totalRequests * 100).toFixed(1) : '0.0';
        const fallbackRate = this.stats.totalRequests > 0 ? 
            (this.stats.localFallbacks / this.stats.totalRequests * 100).toFixed(1) : '0.0';

        return {
            ...this.stats,
            geminiSuccessRate: `${geminiSuccessRate}%`,
            fallbackRate: `${fallbackRate}%`,
            averageResponseTime: `${this.stats.averageResponseTime.toFixed(2)}ms`,
            hasGemini: !!this.geminiProcessor,
            hasLocal: !!this.localProcessor
        };
    }

    /**
     * @function reset
     * @description 重置统计信息
     */
    reset() {
        this.stats = {
            totalRequests: 0,
            geminiSuccess: 0,
            geminiFailures: 0,
            localFallbacks: 0,
            averageResponseTime: 0,
            lastUsedProcessor: null
        };
        
        this.logger.info('SmartNLPProcessor', 'reset', '🔄 统计信息已重置');
    }

    /**
     * @function setApiKey
     * @description 设置Gemini API密钥
     * @param {string} apiKey - API密钥
     */
    setApiKey(apiKey) {
        if (this.geminiProcessor) {
            this.geminiProcessor.setApiKey(apiKey);
            this.logger.info('SmartNLPProcessor', 'setApiKey', '🔑 Gemini API密钥已更新');
        } else {
            this.logger.warn('SmartNLPProcessor', 'setApiKey', '⚠️ Gemini处理器未初始化，无法设置API密钥');
        }
    }

    /**
     * @function getProcessorInfo
     * @description 获取处理器信息
     * @returns {Object} 处理器信息
     */
    getProcessorInfo() {
        return {
            type: 'smart',
            hasGemini: !!this.geminiProcessor,
            hasLocal: !!this.localProcessor,
            geminiInfo: this.geminiProcessor ? this.geminiProcessor.getStats() : null,
            localInfo: this.localProcessor ? this.localProcessor.getStats() : null,
            smartStats: this.getStats()
        };
    }
}

// 单例实例
let smartNLPProcessorInstance = null;

/**
 * @function createSmartNLPProcessor
 * @description 创建智能NLP处理器实例
 * @param {Object} options - 配置选项
 * @returns {SmartNLPProcessor} 处理器实例
 */
export function createSmartNLPProcessor(options = {}) {
    return new SmartNLPProcessor(options);
}

/**
 * @function getSmartNLPProcessor
 * @description 获取智能NLP处理器单例实例
 * @param {Object} options - 配置选项
 * @returns {SmartNLPProcessor} 处理器实例
 */
export function getSmartNLPProcessor(options = {}) {
    if (!smartNLPProcessorInstance) {
        smartNLPProcessorInstance = new SmartNLPProcessor(options);
    }
    return smartNLPProcessorInstance;
}

// 默认导出
export default SmartNLPProcessor; 