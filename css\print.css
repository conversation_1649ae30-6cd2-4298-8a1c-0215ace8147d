/**
 * @file 打印专用样式
 * @description 定义文档打印时的样式规则，优化打印布局、隐藏不必要元素
 */

/* 打印时隐藏页面上不必要的元素 */
@media print {
    /* 隐藏页面导航、工具栏、表单区域等 */
    header, 
    .preview-header,
    .document-editor-container,
    .zoom-controls,
    .tool-bar,
    .toggle-preview,
    button,
    input,
    select,
    textarea,
    .collapsible-panel,
    #edit-panel,
    #company-panel,
    #input-panel,
    .preview-controls {
        display: none !important;
    }
    
    /* 显示文档预览区域并设置样式 */
    #document-preview,
    #preview-container,
    #document-container,
    body, html {
        width: 100% !important;
        max-width: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
        overflow: visible !important;
        transform: none !important;
        box-shadow: none !important;
        background-color: white !important;
    }
    
    /* 确保文档容器占据整个页面空间 */
    #document-container {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        padding-top: 170px !important; /* 与屏幕显示保持一致 */
        padding-bottom: 60px !important; /* 与屏幕显示保持一致 */
        transform: none !important;
        box-shadow: none !important;
        border: none !important;
    }
    
    /* 在打印时强制不分页的元素 */
    .no-page-break {
        page-break-inside: avoid !important;
        break-inside: avoid !important;
    }
    
    /* 司机协议特殊处理 - 确保单页显示 */
    .template-driver-agreement {
        page-break-inside: avoid !important;
        break-inside: avoid !important;
    }
    
    /* 司机协议内容减小字体和间距 */
    .template-driver-agreement h1 {
        font-size: 1.3rem !important;
        margin-bottom: 0.5rem !important;
    }
    
    .template-driver-agreement h2 {
        font-size: 1rem !important;
        margin-top: 0.7rem !important;
        margin-bottom: 0.3rem !important;
    }
    
    .template-driver-agreement h3 {
        font-size: 0.9rem !important;
        margin-top: 0.5rem !important;
        margin-bottom: 0.2rem !important;
    }
    
    .template-driver-agreement p, 
    .template-driver-agreement li {
        font-size: 0.8rem !important;
        line-height: 1.2 !important;
        margin-bottom: 0.2rem !important;
    }
    
    /* 司机协议左右栏布局 */
    .agreement-columns {
        display: flex !important;
        flex-wrap: nowrap !important;
        justify-content: space-between !important;
    }
    
    .agreement-column {
        width: 49% !important;
        padding: 0 !important;
    }
    
    /* 司机协议缩小段落间距 */
    .agreement-section {
        margin-bottom: 0.3rem !important;
    }
    
    /* 隐藏空字段 */
    .hidden-field {
        display: none !important;
    }
    
    /* 减小文档内容的内边距 */
    .document-title-section,
    .client-section,
    .items-section,
    .document-footer {
        padding: 0.4rem !important;
    }
    
    /* 减小表格行高 */
    .service-item-table tr {
        line-height: 1.2 !important;
    }
    
    /* 隐藏电子生成提示的背景，只保留文本 */
    .electronic-generation-hint {
        background: none !important;
        font-size: 0.75rem !important;
        text-align: center !important;
        margin-top: 0.5rem !important;
    }
    
    /* 调整印章大小和位置 */
    .stamp-image-container {
        position: relative !important;
        display: inline-block !important;
        margin-top: 0.5rem !important;
    }
    
    .stamp-image {
        max-width: 120px !important;
        max-height: 120px !important;
    }
    
    /* 页眉页脚打印样式 */
    .document-header {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        height: 160px !important;
        z-index: 1000 !important;
        background-color: white !important;
    }
    
    .document-footer {
        position: fixed !important;
        bottom: 20px !important;
        left: 0 !important;
        right: 0 !important;
        height: 38px !important;
        z-index: 1000 !important;
        background-color: white !important;
    }
    
    /* 确保页眉页脚图片在打印时正确显示 */
    .header-image, .footer-image {
        max-width: 100% !important;
        height: auto !important;
        display: block !important;
    }
}