/**
 * @file 树形组件 - SmartOffice 高级UI组件
 * <AUTHOR> Team
 * @description 
 * 这个文件实现了树形组件，提供：
 * - 节点展开/折叠
 * - 节点选择
 * - 复选框选择
 * - 拖拽排序
 * - 节点搜索
 * - 懒加载
 * - 自定义渲染
 * - 虚拟滚动
 */

// #region 导入依赖模块
import { BaseComponent } from '../base-component.js';
import { UIEvents } from '../../../core/events/event-types.js';
import { getLogger } from '../../../core/utils/logger.js';
// #endregion

// #region 常量定义
/**
 * 节点状态
 */
const NODE_STATES = {
    EXPANDED: 'expanded',
    COLLAPSED: 'collapsed',
    LOADING: 'loading',
    SELECTED: 'selected',
    CHECKED: 'checked',
    INDETERMINATE: 'indeterminate',
    DISABLED: 'disabled',
    HIDDEN: 'hidden'
};

/**
 * 选择模式
 */
const SELECTION_MODES = {
    SINGLE: 'single',
    MULTIPLE: 'multiple',
    CHECKBOX: 'checkbox'
};

/**
 * 拖拽模式
 */
const DRAG_MODES = {
    NONE: 'none',
    SORT: 'sort',
    MOVE: 'move',
    COPY: 'copy'
};

/**
 * 树形大小
 */
const TREE_SIZES = {
    SMALL: 'small',
    MEDIUM: 'medium',
    LARGE: 'large'
};

/**
 * 默认配置
 */
const DEFAULT_CONFIG = {
    size: TREE_SIZES.MEDIUM,
    selectionMode: SELECTION_MODES.SINGLE,
    dragMode: DRAG_MODES.NONE,
    showCheckbox: false,
    showIcon: true,
    showLine: false,
    expandOnClick: true,
    selectOnClick: true,
    checkOnClick: false,
    autoExpandParent: true,
    defaultExpandAll: false,
    defaultExpandedKeys: [],
    defaultSelectedKeys: [],
    defaultCheckedKeys: [],
    searchable: false,
    searchPlaceholder: '搜索节点...',
    searchDelay: 300,
    lazyLoad: false,
    virtualScroll: false,
    itemHeight: 28,
    visibleCount: 10,
    indent: 24,
    disabled: false,
    allowDrop: null,
    allowDrag: null,
    filterNodeMethod: null,
    renderContent: null,
    loadData: null
};

/**
 * 默认图标
 */
const DEFAULT_ICONS = {
    expand: '▶',
    collapse: '▼',
    loading: '⟳',
    leaf: '•',
    folder: '📁',
    folderOpen: '📂',
    file: '📄'
};
// #endregion

// #region Tree 树形组件类
/**
 * @class Tree - 树形组件
 * @description 层次结构数据展示组件
 */
export class Tree extends BaseComponent {
    /**
     * 构造函数 - 初始化树形组件
     * @param {Object} config - 树形配置
     */
    constructor(config = {}) {
        super(config);
        
        this.logger = getLogger();
        this.logger.debug('Tree', 'constructor', '初始化树形组件');
        
        // 配置
        this.config = {
            ...DEFAULT_CONFIG,
            ...config
        };
        
        // 状态
        this.data = this.config.data || [];
        this.flatData = new Map(); // 扁平化数据
        this.expandedKeys = new Set(this.config.defaultExpandedKeys);
        this.selectedKeys = new Set(this.config.defaultSelectedKeys);
        this.checkedKeys = new Set(this.config.defaultCheckedKeys);
        this.indeterminateKeys = new Set();
        this.loadingKeys = new Set();
        this.filteredKeys = new Set();
        this.searchKeyword = '';
        this.dragState = null;
        
        // DOM元素
        this.element = null;
        this.searchElement = null;
        this.treeElement = null;
        this.virtualContainer = null;
        this.scrollTop = 0;
        
        // 创建树形
        this._createTree();
        
        // 处理数据
        this._processData();
        
        // 渲染树形
        this._renderTree();
        
        this.logger.info('Tree', 'constructor', '树形组件初始化完成', {
            config: this.config,
            nodeCount: this.flatData.size
        });
    }

    /**
     * 创建树形
     * @private
     */
    _createTree() {
        // 创建主容器
        this.element = document.createElement('div');
        this.element.className = `tree-container tree-${this.config.size}`;
        
        // 设置容器样式
        this.element.style.cssText = `
            font-family: var(--so-font-family, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            font-size: var(--so-font-size-base, 14px);
            line-height: var(--so-line-height-base, 1.5715);
            color: var(--so-text-color, #000000d9);
            background-color: var(--so-background-color, #ffffff);
            border: 1px solid var(--so-border-color, #d9d9d9);
            border-radius: var(--so-border-radius-base, 6px);
            overflow: hidden;
        `;
        
        // 创建搜索框
        if (this.config.searchable) {
            this._createSearchBox();
        }
        
        // 创建树形容器
        this.treeElement = document.createElement('div');
        this.treeElement.className = 'tree';
        
        if (this.config.virtualScroll) {
            this._createVirtualScroll();
        } else {
            this.treeElement.style.cssText = `
                overflow-y: auto;
                max-height: 400px;
            `;
        }
        
        this.element.appendChild(this.treeElement);
        
        // 绑定事件
        this._bindEvents();
    }

    /**
     * 创建搜索框
     * @private
     */
    _createSearchBox() {
        const searchContainer = document.createElement('div');
        searchContainer.className = 'tree-search';
        searchContainer.style.cssText = `
            padding: var(--so-padding-sm, 8px);
            border-bottom: 1px solid var(--so-border-color, #d9d9d9);
        `;
        
        this.searchElement = document.createElement('input');
        this.searchElement.type = 'text';
        this.searchElement.className = 'tree-search-input';
        this.searchElement.placeholder = this.config.searchPlaceholder;
        
        this.searchElement.style.cssText = `
            width: 100%;
            height: 32px;
            padding: 0 var(--so-padding-md, 12px);
            font-size: var(--so-font-size-base, 14px);
            color: var(--so-text-color, #000000d9);
            background-color: var(--so-background-color, #ffffff);
            border: 1px solid var(--so-border-color, #d9d9d9);
            border-radius: var(--so-border-radius-base, 6px);
            outline: none;
            box-sizing: border-box;
        `;
        
        searchContainer.appendChild(this.searchElement);
        this.element.appendChild(searchContainer);
    }

    /**
     * 创建虚拟滚动
     * @private
     */
    _createVirtualScroll() {
        this.virtualContainer = document.createElement('div');
        this.virtualContainer.className = 'tree-virtual-container';
        this.virtualContainer.style.cssText = `
            height: ${this.config.visibleCount * this.config.itemHeight}px;
            overflow-y: auto;
            position: relative;
        `;
        
        this.treeElement.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
        `;
        
        this.virtualContainer.appendChild(this.treeElement);
        this.element.appendChild(this.virtualContainer);
    }

    /**
     * 绑定事件
     * @private
     */
    _bindEvents() {
        // 搜索事件
        if (this.searchElement) {
            let searchTimer;
            this.searchElement.addEventListener('input', (e) => {
                clearTimeout(searchTimer);
                searchTimer = setTimeout(() => {
                    this._handleSearch(e.target.value);
                }, this.config.searchDelay);
            });
        }
        
        // 虚拟滚动事件
        if (this.virtualContainer) {
            this.virtualContainer.addEventListener('scroll', () => {
                this._handleVirtualScroll();
            });
        }
        
        // 树形点击事件
        this.treeElement.addEventListener('click', (e) => {
            this._handleTreeClick(e);
        });
        
        // 拖拽事件
        if (this.config.dragMode !== DRAG_MODES.NONE) {
            this.treeElement.addEventListener('dragstart', (e) => {
                this._handleDragStart(e);
            });
            
            this.treeElement.addEventListener('dragover', (e) => {
                this._handleDragOver(e);
            });
            
            this.treeElement.addEventListener('drop', (e) => {
                this._handleDrop(e);
            });
            
            this.treeElement.addEventListener('dragend', (e) => {
                this._handleDragEnd(e);
            });
        }
    }

    /**
     * 处理数据
     * @private
     */
    _processData() {
        this.flatData.clear();
        this._flattenData(this.data, null, 0);
        
        // 默认展开所有节点
        if (this.config.defaultExpandAll) {
            for (const [key, node] of this.flatData) {
                if (node.children && node.children.length > 0) {
                    this.expandedKeys.add(key);
                }
            }
        }
        
        // 自动展开父节点
        if (this.config.autoExpandParent) {
            this._autoExpandParents();
        }
        
        // 更新复选框状态
        if (this.config.showCheckbox) {
            this._updateCheckboxStates();
        }
    }

    /**
     * 扁平化数据
     * @param {Array} data - 数据数组
     * @param {string|null} parentKey - 父节点键
     * @param {number} level - 层级
     * @private
     */
    _flattenData(data, parentKey, level) {
        data.forEach((item, index) => {
            const key = item.key || `${parentKey || 'root'}-${index}`;
            const node = {
                ...item,
                key,
                parentKey,
                level,
                index,
                isLeaf: !item.children || item.children.length === 0,
                visible: true
            };
            
            this.flatData.set(key, node);
            
            if (item.children && item.children.length > 0) {
                this._flattenData(item.children, key, level + 1);
            }
        });
    }

    /**
     * 自动展开父节点
     * @private
     */
    _autoExpandParents() {
        const expandParent = (key) => {
            const node = this.flatData.get(key);
            if (node && node.parentKey) {
                this.expandedKeys.add(node.parentKey);
                expandParent(node.parentKey);
            }
        };
        
        // 展开选中节点的父节点
        for (const key of this.selectedKeys) {
            expandParent(key);
        }
        
        // 展开勾选节点的父节点
        for (const key of this.checkedKeys) {
            expandParent(key);
        }
    }

    /**
     * 更新复选框状态
     * @private
     */
    _updateCheckboxStates() {
        this.indeterminateKeys.clear();
        
        // 从叶子节点向上更新
        const updateParentState = (parentKey) => {
            const parent = this.flatData.get(parentKey);
            if (!parent || !parent.children) return;
            
            const childKeys = parent.children.map(child => 
                child.key || `${parentKey}-${parent.children.indexOf(child)}`
            );
            
            const checkedChildren = childKeys.filter(key => this.checkedKeys.has(key));
            const indeterminateChildren = childKeys.filter(key => this.indeterminateKeys.has(key));
            
            if (checkedChildren.length === childKeys.length) {
                // 全部选中
                this.checkedKeys.add(parentKey);
                this.indeterminateKeys.delete(parentKey);
            } else if (checkedChildren.length > 0 || indeterminateChildren.length > 0) {
                // 部分选中
                this.checkedKeys.delete(parentKey);
                this.indeterminateKeys.add(parentKey);
            } else {
                // 全部未选中
                this.checkedKeys.delete(parentKey);
                this.indeterminateKeys.delete(parentKey);
            }
            
            if (parent.parentKey) {
                updateParentState(parent.parentKey);
            }
        };
        
        // 更新所有父节点状态
        const processedParents = new Set();
        for (const [key, node] of this.flatData) {
            if (node.parentKey && !processedParents.has(node.parentKey)) {
                updateParentState(node.parentKey);
                processedParents.add(node.parentKey);
            }
        }
    }

    /**
     * 渲染树形
     * @private
     */
    _renderTree() {
        if (this.config.virtualScroll) {
            this._renderVirtualTree();
        } else {
            this._renderFullTree();
        }
    }

    /**
     * 渲染完整树形
     * @private
     */
    _renderFullTree() {
        this.treeElement.innerHTML = '';
        
        const visibleNodes = this._getVisibleNodes();
        const fragment = document.createDocumentFragment();
        
        visibleNodes.forEach(node => {
            const nodeElement = this._createNodeElement(node);
            fragment.appendChild(nodeElement);
        });
        
        this.treeElement.appendChild(fragment);
    }

    /**
     * 渲染虚拟树形
     * @private
     */
    _renderVirtualTree() {
        const visibleNodes = this._getVisibleNodes();
        const totalHeight = visibleNodes.length * this.config.itemHeight;
        
        // 设置总高度
        this.virtualContainer.style.height = `${Math.min(totalHeight, this.config.visibleCount * this.config.itemHeight)}px`;
        
        // 计算可见范围
        const scrollTop = this.virtualContainer.scrollTop;
        const startIndex = Math.floor(scrollTop / this.config.itemHeight);
        const endIndex = Math.min(startIndex + this.config.visibleCount + 1, visibleNodes.length);
        
        // 设置偏移
        this.treeElement.style.transform = `translateY(${startIndex * this.config.itemHeight}px)`;
        
        // 渲染可见节点
        this.treeElement.innerHTML = '';
        const fragment = document.createDocumentFragment();
        
        for (let i = startIndex; i < endIndex; i++) {
            const node = visibleNodes[i];
            if (node) {
                const nodeElement = this._createNodeElement(node);
                fragment.appendChild(nodeElement);
            }
        }
        
        this.treeElement.appendChild(fragment);
    }

    /**
     * 获取可见节点
     * @returns {Array} 可见节点数组
     * @private
     */
    _getVisibleNodes() {
        const visibleNodes = [];
        
        const traverse = (data, parentExpanded = true) => {
            data.forEach(item => {
                const key = item.key || `${item.parentKey || 'root'}-${item.index}`;
                const node = this.flatData.get(key);
                
                if (!node) return;
                
                // 检查节点是否可见
                let visible = parentExpanded;
                
                // 搜索过滤
                if (this.searchKeyword) {
                    visible = visible && this.filteredKeys.has(key);
                }
                
                if (visible) {
                    visibleNodes.push(node);
                }
                
                // 递归处理子节点
                if (item.children && item.children.length > 0) {
                    const expanded = this.expandedKeys.has(key);
                    traverse(item.children, visible && expanded);
                }
            });
        };
        
        traverse(this.data);
        return visibleNodes;
    }

    /**
     * 创建节点元素
     * @param {Object} node - 节点数据
     * @returns {HTMLElement} 节点元素
     * @private
     */
    _createNodeElement(node) {
        const nodeElement = document.createElement('div');
        nodeElement.className = 'tree-node';
        nodeElement.setAttribute('data-key', node.key);
        nodeElement.draggable = this.config.dragMode !== DRAG_MODES.NONE && !node.disabled;
        
        // 设置节点样式
        const isSelected = this.selectedKeys.has(node.key);
        const isDisabled = node.disabled || this.config.disabled;
        
        nodeElement.style.cssText = `
            display: flex;
            align-items: center;
            height: ${this.config.itemHeight}px;
            padding-left: ${node.level * this.config.indent}px;
            cursor: ${isDisabled ? 'not-allowed' : 'pointer'};
            user-select: none;
            transition: background-color 0.2s ease;
            ${isSelected ? 'background-color: var(--so-primary-color-light, #e6f7ff);' : ''}
            ${isDisabled ? 'opacity: 0.5;' : ''}
        `;
        
        // 展开/折叠图标
        const expandIcon = this._createExpandIcon(node);
        nodeElement.appendChild(expandIcon);
        
        // 复选框
        if (this.config.showCheckbox) {
            const checkbox = this._createCheckbox(node);
            nodeElement.appendChild(checkbox);
        }
        
        // 节点图标
        if (this.config.showIcon) {
            const icon = this._createNodeIcon(node);
            nodeElement.appendChild(icon);
        }
        
        // 节点内容
        const content = this._createNodeContent(node);
        nodeElement.appendChild(content);
        
        return nodeElement;
    }

    /**
     * 创建展开图标
     * @param {Object} node - 节点数据
     * @returns {HTMLElement} 图标元素
     * @private
     */
    _createExpandIcon(node) {
        const icon = document.createElement('span');
        icon.className = 'tree-expand-icon';
        
        icon.style.cssText = `
            width: 16px;
            height: 16px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--so-margin-xs, 4px);
            font-size: 12px;
            cursor: pointer;
            transition: transform 0.2s ease;
        `;
        
        if (node.isLeaf) {
            icon.style.visibility = 'hidden';
        } else {
            const isExpanded = this.expandedKeys.has(node.key);
            const isLoading = this.loadingKeys.has(node.key);
            
            if (isLoading) {
                icon.textContent = DEFAULT_ICONS.loading;
                icon.style.animation = 'spin 1s linear infinite';
            } else {
                icon.textContent = isExpanded ? DEFAULT_ICONS.collapse : DEFAULT_ICONS.expand;
                icon.style.transform = isExpanded ? 'rotate(90deg)' : 'rotate(0deg)';
            }
        }
        
        return icon;
    }

    /**
     * 创建复选框
     * @param {Object} node - 节点数据
     * @returns {HTMLElement} 复选框元素
     * @private
     */
    _createCheckbox(node) {
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.className = 'tree-checkbox';
        
        checkbox.checked = this.checkedKeys.has(node.key);
        checkbox.indeterminate = this.indeterminateKeys.has(node.key);
        checkbox.disabled = node.disabled || this.config.disabled;
        
        checkbox.style.cssText = `
            margin-right: var(--so-margin-sm, 8px);
        `;
        
        return checkbox;
    }

    /**
     * 创建节点图标
     * @param {Object} node - 节点数据
     * @returns {HTMLElement} 图标元素
     * @private
     */
    _createNodeIcon(node) {
        const icon = document.createElement('span');
        icon.className = 'tree-node-icon';
        
        icon.style.cssText = `
            margin-right: var(--so-margin-sm, 8px);
            font-size: 16px;
        `;
        
        if (node.icon) {
            icon.textContent = node.icon;
        } else if (node.isLeaf) {
            icon.textContent = node.type === 'file' ? DEFAULT_ICONS.file : DEFAULT_ICONS.leaf;
        } else {
            const isExpanded = this.expandedKeys.has(node.key);
            icon.textContent = isExpanded ? DEFAULT_ICONS.folderOpen : DEFAULT_ICONS.folder;
        }
        
        return icon;
    }

    /**
     * 创建节点内容
     * @param {Object} node - 节点数据
     * @returns {HTMLElement} 内容元素
     * @private
     */
    _createNodeContent(node) {
        const content = document.createElement('span');
        content.className = 'tree-node-content';
        
        content.style.cssText = `
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        `;
        
        if (this.config.renderContent && typeof this.config.renderContent === 'function') {
            const customContent = this.config.renderContent(node);
            if (typeof customContent === 'string') {
                content.textContent = customContent;
            } else if (customContent instanceof HTMLElement) {
                content.appendChild(customContent);
            }
        } else {
            content.textContent = node.title || node.label || node.name || node.key;
        }
        
        // 高亮搜索关键词
        if (this.searchKeyword && content.textContent) {
            this._highlightSearchKeyword(content, this.searchKeyword);
        }
        
        return content;
    }

    /**
     * 高亮搜索关键词
     * @param {HTMLElement} element - 元素
     * @param {string} keyword - 关键词
     * @private
     */
    _highlightSearchKeyword(element, keyword) {
        const text = element.textContent;
        const regex = new RegExp(`(${keyword})`, 'gi');
        const highlightedText = text.replace(regex, '<mark>$1</mark>');
        element.innerHTML = highlightedText;
    }

    /**
     * 处理树形点击
     * @param {Event} e - 事件对象
     * @private
     */
    _handleTreeClick(e) {
        const nodeElement = e.target.closest('.tree-node');
        if (!nodeElement) return;
        
        const key = nodeElement.getAttribute('data-key');
        const node = this.flatData.get(key);
        if (!node || node.disabled) return;
        
        // 处理展开图标点击
        if (e.target.classList.contains('tree-expand-icon')) {
            this._toggleExpand(key);
            return;
        }
        
        // 处理复选框点击
        if (e.target.classList.contains('tree-checkbox')) {
            this._toggleCheck(key, e.target.checked);
            return;
        }
        
        // 处理节点点击
        if (this.config.expandOnClick && !node.isLeaf) {
            this._toggleExpand(key);
        }
        
        if (this.config.selectOnClick) {
            this._selectNode(key, e.ctrlKey || e.metaKey);
        }
        
        if (this.config.checkOnClick && this.config.showCheckbox) {
            const checkbox = nodeElement.querySelector('.tree-checkbox');
            if (checkbox) {
                checkbox.checked = !checkbox.checked;
                this._toggleCheck(key, checkbox.checked);
            }
        }
    }

    /**
     * 处理搜索
     * @param {string} keyword - 搜索关键词
     * @private
     */
    _handleSearch(keyword) {
        this.searchKeyword = keyword.trim();
        this.filteredKeys.clear();
        
        if (!this.searchKeyword) {
            // 清空搜索，显示所有节点
            this._renderTree();
            return;
        }
        
        // 搜索匹配的节点
        const matchedKeys = new Set();
        
        for (const [key, node] of this.flatData) {
            if (this._isNodeMatched(node, this.searchKeyword)) {
                matchedKeys.add(key);
                
                // 添加所有父节点
                let parentKey = node.parentKey;
                while (parentKey) {
                    matchedKeys.add(parentKey);
                    const parent = this.flatData.get(parentKey);
                    parentKey = parent ? parent.parentKey : null;
                }
                
                // 添加所有子节点
                this._addChildrenToMatched(key, matchedKeys);
            }
        }
        
        this.filteredKeys = matchedKeys;
        
        // 自动展开匹配的节点路径
        for (const key of matchedKeys) {
            const node = this.flatData.get(key);
            if (node && !node.isLeaf) {
                this.expandedKeys.add(key);
            }
        }
        
        this._renderTree();
        
        // 触发搜索事件
        this.emit(UIEvents.TREE_SEARCHED, {
            keyword: this.searchKeyword,
            matchedCount: matchedKeys.size
        });
    }

    /**
     * 检查节点是否匹配搜索
     * @param {Object} node - 节点数据
     * @param {string} keyword - 关键词
     * @returns {boolean} 是否匹配
     * @private
     */
    _isNodeMatched(node, keyword) {
        if (this.config.filterNodeMethod && typeof this.config.filterNodeMethod === 'function') {
            return this.config.filterNodeMethod(keyword, node);
        }
        
        const text = (node.title || node.label || node.name || node.key || '').toLowerCase();
        return text.includes(keyword.toLowerCase());
    }

    /**
     * 添加子节点到匹配集合
     * @param {string} parentKey - 父节点键
     * @param {Set} matchedKeys - 匹配键集合
     * @private
     */
    _addChildrenToMatched(parentKey, matchedKeys) {
        for (const [key, node] of this.flatData) {
            if (node.parentKey === parentKey) {
                matchedKeys.add(key);
                this._addChildrenToMatched(key, matchedKeys);
            }
        }
    }

    /**
     * 处理虚拟滚动
     * @private
     */
    _handleVirtualScroll() {
        if (this.config.virtualScroll) {
            this._renderVirtualTree();
        }
    }

    /**
     * 处理拖拽开始
     * @param {Event} e - 事件对象
     * @private
     */
    _handleDragStart(e) {
        const nodeElement = e.target.closest('.tree-node');
        if (!nodeElement) return;
        
        const key = nodeElement.getAttribute('data-key');
        const node = this.flatData.get(key);
        
        if (!node || node.disabled) {
            e.preventDefault();
            return;
        }
        
        // 检查是否允许拖拽
        if (this.config.allowDrag && typeof this.config.allowDrag === 'function') {
            if (!this.config.allowDrag(node)) {
                e.preventDefault();
                return;
            }
        }
        
        this.dragState = {
            dragNode: node,
            dragElement: nodeElement
        };
        
        e.dataTransfer.effectAllowed = this.config.dragMode;
        e.dataTransfer.setData('text/plain', key);
        
        nodeElement.style.opacity = '0.5';
        
        this.emit(UIEvents.TREE_DRAG_START, {
            node,
            event: e
        });
    }

    /**
     * 处理拖拽悬停
     * @param {Event} e - 事件对象
     * @private
     */
    _handleDragOver(e) {
        if (!this.dragState) return;
        
        e.preventDefault();
        
        const nodeElement = e.target.closest('.tree-node');
        if (!nodeElement) return;
        
        const key = nodeElement.getAttribute('data-key');
        const node = this.flatData.get(key);
        
        if (!node || node.disabled || node.key === this.dragState.dragNode.key) {
            return;
        }
        
        // 检查是否允许放置
        if (this.config.allowDrop && typeof this.config.allowDrop === 'function') {
            if (!this.config.allowDrop(this.dragState.dragNode, node)) {
                return;
            }
        }
        
        // 清除之前的拖拽样式
        this.treeElement.querySelectorAll('.tree-node-drag-over').forEach(el => {
            el.classList.remove('tree-node-drag-over');
        });
        
        // 添加拖拽样式
        nodeElement.classList.add('tree-node-drag-over');
        
        e.dataTransfer.dropEffect = this.config.dragMode;
    }

    /**
     * 处理拖拽放置
     * @param {Event} e - 事件对象
     * @private
     */
    _handleDrop(e) {
        if (!this.dragState) return;
        
        e.preventDefault();
        
        const nodeElement = e.target.closest('.tree-node');
        if (!nodeElement) return;
        
        const key = nodeElement.getAttribute('data-key');
        const dropNode = this.flatData.get(key);
        
        if (!dropNode || dropNode.disabled || dropNode.key === this.dragState.dragNode.key) {
            return;
        }
        
        // 检查是否允许放置
        if (this.config.allowDrop && typeof this.config.allowDrop === 'function') {
            if (!this.config.allowDrop(this.dragState.dragNode, dropNode)) {
                return;
            }
        }
        
        // 执行拖拽操作
        this._performDrop(this.dragState.dragNode, dropNode);
        
        this.emit(UIEvents.TREE_DROP, {
            dragNode: this.dragState.dragNode,
            dropNode,
            event: e
        });
    }

    /**
     * 处理拖拽结束
     * @param {Event} e - 事件对象
     * @private
     */
    _handleDragEnd(e) {
        if (this.dragState) {
            this.dragState.dragElement.style.opacity = '';
            this.dragState = null;
        }
        
        // 清除拖拽样式
        this.treeElement.querySelectorAll('.tree-node-drag-over').forEach(el => {
            el.classList.remove('tree-node-drag-over');
        });
        
        this.emit(UIEvents.TREE_DRAG_END, {
            event: e
        });
    }

    /**
     * 执行拖拽放置
     * @param {Object} dragNode - 拖拽节点
     * @param {Object} dropNode - 放置节点
     * @private
     */
    _performDrop(dragNode, dropNode) {
        // 这里应该根据具体需求实现拖拽逻辑
        // 例如：移动节点、复制节点、排序等
        console.log('Perform drop:', dragNode, dropNode);
    }

    // #region 公共方法
    /**
     * 展开节点
     * @param {string} key - 节点键
     */
    expand(key) {
        const node = this.flatData.get(key);
        if (!node || node.isLeaf) return;
        
        this.expandedKeys.add(key);
        
        // 懒加载
        if (this.config.lazyLoad && !node.loaded && this.config.loadData) {
            this._loadNodeData(key);
        } else {
            this._renderTree();
        }
        
        this.emit(UIEvents.TREE_NODE_EXPANDED, {
            node,
            expandedKeys: Array.from(this.expandedKeys)
        });
    }

    /**
     * 折叠节点
     * @param {string} key - 节点键
     */
    collapse(key) {
        const node = this.flatData.get(key);
        if (!node || node.isLeaf) return;
        
        this.expandedKeys.delete(key);
        this._renderTree();
        
        this.emit(UIEvents.TREE_NODE_COLLAPSED, {
            node,
            expandedKeys: Array.from(this.expandedKeys)
        });
    }

    /**
     * 切换展开状态
     * @param {string} key - 节点键
     * @private
     */
    _toggleExpand(key) {
        if (this.expandedKeys.has(key)) {
            this.collapse(key);
        } else {
            this.expand(key);
        }
    }

    /**
     * 选择节点
     * @param {string} key - 节点键
     * @param {boolean} multiple - 是否多选
     * @private
     */
    _selectNode(key, multiple = false) {
        const node = this.flatData.get(key);
        if (!node || node.disabled) return;
        
        if (this.config.selectionMode === SELECTION_MODES.SINGLE || !multiple) {
            this.selectedKeys.clear();
        }
        
        if (this.selectedKeys.has(key)) {
            this.selectedKeys.delete(key);
        } else {
            this.selectedKeys.add(key);
        }
        
        this._renderTree();
        
        this.emit(UIEvents.TREE_NODE_SELECTED, {
            node,
            selected: this.selectedKeys.has(key),
            selectedKeys: Array.from(this.selectedKeys)
        });
    }

    /**
     * 切换复选框状态
     * @param {string} key - 节点键
     * @param {boolean} checked - 是否选中
     * @private
     */
    _toggleCheck(key, checked) {
        const node = this.flatData.get(key);
        if (!node || node.disabled) return;
        
        if (checked) {
            this.checkedKeys.add(key);
            // 选中所有子节点
            this._checkChildren(key, true);
        } else {
            this.checkedKeys.delete(key);
            // 取消选中所有子节点
            this._checkChildren(key, false);
        }
        
        // 更新父节点状态
        this._updateParentCheckState(node.parentKey);
        
        this._renderTree();
        
        this.emit(UIEvents.TREE_NODE_CHECKED, {
            node,
            checked,
            checkedKeys: Array.from(this.checkedKeys)
        });
    }

    /**
     * 选中/取消选中子节点
     * @param {string} parentKey - 父节点键
     * @param {boolean} checked - 是否选中
     * @private
     */
    _checkChildren(parentKey, checked) {
        for (const [key, node] of this.flatData) {
            if (node.parentKey === parentKey) {
                if (checked) {
                    this.checkedKeys.add(key);
                } else {
                    this.checkedKeys.delete(key);
                }
                this._checkChildren(key, checked);
            }
        }
    }

    /**
     * 更新父节点复选框状态
     * @param {string} parentKey - 父节点键
     * @private
     */
    _updateParentCheckState(parentKey) {
        if (!parentKey) return;
        
        const parent = this.flatData.get(parentKey);
        if (!parent) return;
        
        const childKeys = [];
        for (const [key, node] of this.flatData) {
            if (node.parentKey === parentKey) {
                childKeys.push(key);
            }
        }
        
        const checkedChildren = childKeys.filter(key => this.checkedKeys.has(key));
        
        if (checkedChildren.length === childKeys.length) {
            // 全部选中
            this.checkedKeys.add(parentKey);
            this.indeterminateKeys.delete(parentKey);
        } else if (checkedChildren.length > 0) {
            // 部分选中
            this.checkedKeys.delete(parentKey);
            this.indeterminateKeys.add(parentKey);
        } else {
            // 全部未选中
            this.checkedKeys.delete(parentKey);
            this.indeterminateKeys.delete(parentKey);
        }
        
        // 递归更新父节点
        this._updateParentCheckState(parent.parentKey);
    }

    /**
     * 加载节点数据
     * @param {string} key - 节点键
     * @private
     */
    async _loadNodeData(key) {
        const node = this.flatData.get(key);
        if (!node || !this.config.loadData) return;
        
        this.loadingKeys.add(key);
        this._renderTree();
        
        try {
            const children = await this.config.loadData(node);
            
            if (children && Array.isArray(children)) {
                // 更新节点数据
                node.children = children;
                node.isLeaf = children.length === 0;
                node.loaded = true;
                
                // 重新处理数据
                this._processData();
                this._renderTree();
                
                this.emit(UIEvents.TREE_NODE_LOADED, {
                    node,
                    children
                });
            }
        } catch (error) {
            this.logger.error('Tree', '_loadNodeData', '加载节点数据失败', error);
            
            this.emit(UIEvents.TREE_LOAD_ERROR, {
                node,
                error
            });
        } finally {
            this.loadingKeys.delete(key);
            this._renderTree();
        }
    }

    /**
     * 设置数据
     * @param {Array} data - 树形数据
     */
    setData(data) {
        this.data = data || [];
        this._processData();
        this._renderTree();
        
        this.emit(UIEvents.TREE_DATA_CHANGED, {
            data: this.data
        });
    }

    /**
     * 获取选中的节点
     * @returns {Array} 选中的节点数组
     */
    getSelectedNodes() {
        return Array.from(this.selectedKeys).map(key => this.flatData.get(key)).filter(Boolean);
    }

    /**
     * 获取选中的键
     * @returns {Array} 选中的键数组
     */
    getSelectedKeys() {
        return Array.from(this.selectedKeys);
    }

    /**
     * 设置选中的键
     * @param {Array} keys - 键数组
     */
    setSelectedKeys(keys) {
        this.selectedKeys = new Set(keys || []);
        this._renderTree();
    }

    /**
     * 获取勾选的节点
     * @returns {Array} 勾选的节点数组
     */
    getCheckedNodes() {
        return Array.from(this.checkedKeys).map(key => this.flatData.get(key)).filter(Boolean);
    }

    /**
     * 获取勾选的键
     * @returns {Array} 勾选的键数组
     */
    getCheckedKeys() {
        return Array.from(this.checkedKeys);
    }

    /**
     * 设置勾选的键
     * @param {Array} keys - 键数组
     */
    setCheckedKeys(keys) {
        this.checkedKeys = new Set(keys || []);
        this._updateCheckboxStates();
        this._renderTree();
    }

    /**
     * 展开所有节点
     */
    expandAll() {
        for (const [key, node] of this.flatData) {
            if (!node.isLeaf) {
                this.expandedKeys.add(key);
            }
        }
        this._renderTree();
    }

    /**
     * 折叠所有节点
     */
    collapseAll() {
        this.expandedKeys.clear();
        this._renderTree();
    }

    /**
     * 搜索节点
     * @param {string} keyword - 搜索关键词
     */
    search(keyword) {
        if (this.searchElement) {
            this.searchElement.value = keyword;
        }
        this._handleSearch(keyword);
    }

    /**
     * 清除搜索
     */
    clearSearch() {
        this.search('');
    }

    /**
     * 获取节点
     * @param {string} key - 节点键
     * @returns {Object|null} 节点数据
     */
    getNode(key) {
        return this.flatData.get(key) || null;
    }

    /**
     * 添加节点
     * @param {Object} nodeData - 节点数据
     * @param {string} parentKey - 父节点键
     */
    addNode(nodeData, parentKey = null) {
        if (parentKey) {
            const parent = this.flatData.get(parentKey);
            if (parent) {
                if (!parent.children) {
                    parent.children = [];
                }
                parent.children.push(nodeData);
                parent.isLeaf = false;
            }
        } else {
            this.data.push(nodeData);
        }
        
        this._processData();
        this._renderTree();
        
        this.emit(UIEvents.TREE_NODE_ADDED, {
            node: nodeData,
            parentKey
        });
    }

    /**
     * 移除节点
     * @param {string} key - 节点键
     */
    removeNode(key) {
        const node = this.flatData.get(key);
        if (!node) return;
        
        // 从数据中移除
        if (node.parentKey) {
            const parent = this.flatData.get(node.parentKey);
            if (parent && parent.children) {
                const index = parent.children.findIndex(child => 
                    (child.key || `${node.parentKey}-${parent.children.indexOf(child)}`) === key
                );
                if (index !== -1) {
                    parent.children.splice(index, 1);
                    parent.isLeaf = parent.children.length === 0;
                }
            }
        } else {
            const index = this.data.findIndex(item => 
                (item.key || `root-${this.data.indexOf(item)}`) === key
            );
            if (index !== -1) {
                this.data.splice(index, 1);
            }
        }
        
        // 清理状态
        this.expandedKeys.delete(key);
        this.selectedKeys.delete(key);
        this.checkedKeys.delete(key);
        this.indeterminateKeys.delete(key);
        this.loadingKeys.delete(key);
        
        this._processData();
        this._renderTree();
        
        this.emit(UIEvents.TREE_NODE_REMOVED, {
            node,
            key
        });
    }

    /**
     * 更新节点
     * @param {string} key - 节点键
     * @param {Object} nodeData - 新的节点数据
     */
    updateNode(key, nodeData) {
        const node = this.flatData.get(key);
        if (!node) return;
        
        Object.assign(node, nodeData);
        this._renderTree();
        
        this.emit(UIEvents.TREE_NODE_UPDATED, {
            node,
            key
        });
    }

    /**
     * 获取元素
     * @returns {HTMLElement} 树形元素
     */
    getElement() {
        return this.element;
    }

    /**
     * 销毁树形
     */
    destroy() {
        // 移除DOM元素
        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }
        
        // 清理引用
        this.element = null;
        this.searchElement = null;
        this.treeElement = null;
        this.virtualContainer = null;
        
        // 清理数据
        this.data = [];
        this.flatData.clear();
        this.expandedKeys.clear();
        this.selectedKeys.clear();
        this.checkedKeys.clear();
        this.indeterminateKeys.clear();
        this.loadingKeys.clear();
        this.filteredKeys.clear();
        
        // 调用父类销毁方法
        super.destroy();
        
        this.logger.info('Tree', 'destroy', '树形已销毁');
    }
    // #endregion
}
// #endregion

// #region 导出常量
export { 
    NODE_STATES, 
    SELECTION_MODES, 
    DRAG_MODES, 
    TREE_SIZES 
};
// #endregion

// #region 导出
export default Tree;
// #endregion