/**
 * @file 统一渲染器 - 从src/renderers/unified-renderer.js迁移的完整实现
 * <AUTHOR> Team
 * @description 
 * 这是从src/renderers/unified-renderer.js迁移的完整统一渲染器实现
 * 整合了BaseRenderer和UnifiedRenderer的所有功能，消除重复代码
 * 
 * 重构说明：
 * - 从src/renderers/unified-renderer.js完整迁移
 * - 保持所有原有功能和API兼容性
 * - 整合到modules/目录的统一架构中
 * - 消除与base-renderer.js的重复代码
 */

// #region 导入依赖模块
import { EventEmitter } from '../core/events.js';
import { getLogger } from '../core/logger.js';
import { Validator } from '../../src/core/utils/validation.js';
import { DateUtils } from '../../src/core/utils/date-utils.js';
import { DocumentModel } from '../../src/core/rendering/document-model.js';
import { StyleManager } from '../../src/core/rendering/style-manager.js';
import { PositionManager } from '../../src/core/rendering/position-manager.js';
// #endregion

// #region 事件类型定义
const EventTypes = {
    RENDERER: {
        INITIALIZED: 'renderer:initialized',
        ERROR: 'renderer:error',
        WARNING: 'renderer:warning',
        RENDER_START: 'render:start',
        RENDER_COMPLETE: 'render:complete',
        RENDER_PROGRESS: 'render:progress'
    }
};
// #endregion

// #region 统一渲染器类定义
/**
 * @class UnifiedRenderer - 统一渲染器基类
 * @description 重构后的统一渲染器基类，整合了BaseRenderer和原UnifiedRenderer的功能
 * @extends EventEmitter
 */
export class UnifiedRenderer extends EventEmitter {
    /**
     * 构造函数 - 初始化渲染器实例
     * @param {Object} config - 渲染器配置对象
     */
    constructor(config = {}) {
        super();

        // 初始化日志记录器
        this.logger = getLogger();
        this.logger.info('UnifiedRenderer', 'constructor', '创建统一渲染器');

        // 基础配置 - 整合了原BaseRenderer的配置结构
        this.name = config.name || 'unified-renderer';
        this.type = config.type || 'base';
        this.version = config.version || '2.0.0';
        this.description = config.description || '统一渲染器基类';

        // 元数据信息
        this.metadata = {
            created: new Date(),
            lastModified: new Date(),
            author: 'SmartOffice Team',
            ...config.metadata
        };

        // 渲染器配置 - 整合了两个类的配置选项
        this.renderConfig = {
            // 输出格式配置
            outputFormat: config.format || 'html', // html, pdf, image, print等
            outputQuality: config.quality || 'high', // low, medium, high
            outputCompression: false, // 是否压缩输出
            theme: config.theme || 'default',

            // 性能配置
            enableCache: config.enableCache !== undefined ? config.enableCache : true,
            maxCacheSize: 100, // 最大缓存项数
            cacheTimeout: 300000, // 缓存超时时间（毫秒）
            enableParallel: config.enableParallel || false,
            enableOptimization: config.enableOptimization !== undefined ? config.enableOptimization : true,

            // 调试配置
            enableDebug: config.enableDebug || false,
            enableProfiling: config.enableProfiling || false,
            logLevel: 'info', // 日志级别

            // 错误处理
            errorHandling: config.errorHandling || 'throw', // 'throw', 'log', 'ignore'

            // 渲染选项
            preserveWhitespace: false,
            minifyOutput: true,
            includeMetadata: true,
            validateOutput: true,

            ...config.renderConfig
        };

        // 渲染器状态 - 整合了BaseRenderer的状态管理
        this.state = {
            isInitialized: false,
            isRendering: false,
            lastRenderTime: null,
            renderCount: 0,
            errorCount: 0
        };

        // 缓存管理 - 整合了BaseRenderer的缓存机制
        this.cache = new Map();
        this.cacheStats = {
            hits: 0,
            misses: 0,
            total: 0
        };

        // 性能统计 - 整合了两个类的性能统计
        this.performanceStats = {
            totalRenderTime: 0,
            averageRenderTime: 0,
            minRenderTime: Infinity,
            maxRenderTime: 0,
            renderHistory: [],
            totalRenders: 0,
            successfulRenders: 0,
            failedRenders: 0
        };

        // 管理器实例
        this.styleManager = null;
        this.positionManager = null;

        // 共享渲染服务
        this.sharedStyleService = null;
        this.sharedLayoutService = null;
        this.sharedPositionService = null;

        // 错误收集
        this.errors = [];
        this.warnings = [];

        // 渲染钩子
        this.hooks = {
            beforeRender: [],
            afterRender: [],
            onError: [],
            onWarning: []
        };

        // 初始化渲染器
        this._initialize();
    }

    /**
     * 初始化渲染器 - 整合了BaseRenderer的初始化逻辑
     * @private
     */
    _initialize() {
        try {
            // 验证配置
            this._validateConfig();

            // 初始化共享服务
            this._initializeSharedServices();

            // 设置缓存清理定时器
            if (this.renderConfig.enableCache) {
                this._setupCacheCleanup();
            }

            // 标记为已初始化
            this.state.isInitialized = true;

            // 触发初始化完成事件
            this.emit(EventTypes.RENDERER.INITIALIZED, {
                renderer: this.name,
                type: this.type,
                timestamp: new Date()
            });

            this.logger.info('UnifiedRenderer', '_initialize', '统一渲染器初始化完成');

        } catch (error) {
            this.emit(EventTypes.RENDERER.ERROR, {
                renderer: this.name,
                error: error.message,
                timestamp: new Date()
            });
            throw new Error(`渲染器初始化失败: ${error.message}`);
        }
    }
    
    /**
     * 验证配置
     * @private
     */
    _validateConfig() {
        const requiredFields = ['name', 'type', 'version'];
        
        for (const field of requiredFields) {
            if (!this[field]) {
                throw new Error(`渲染器配置缺少必需字段: ${field}`);
            }
        }
        
        // 验证格式支持
        const supportedFormats = ['html', 'pdf', 'image', 'print'];
        if (!supportedFormats.includes(this.renderConfig.outputFormat)) {
            throw new Error(`不支持的渲染格式: ${this.renderConfig.outputFormat}`);
        }
    }
    
    /**
     * 初始化共享渲染服务
     * @private
     */
    _initializeSharedServices() {
        // 这里可以初始化共享的渲染服务
        this.logger.info('UnifiedRenderer', '_initializeSharedServices', '共享渲染服务初始化完成');
    }
    
    /**
     * 设置缓存清理定时器
     * @private
     */
    _setupCacheCleanup() {
        // 每5分钟清理一次过期缓存
        setInterval(() => {
            this._cleanupCache();
        }, 5 * 60 * 1000);
    }
    
    /**
     * 清理过期缓存
     * @private
     */
    _cleanupCache() {
        const now = Date.now();
        const maxAge = this.renderConfig.cacheTimeout;
        
        for (const [key, entry] of this.cache.entries()) {
            if (now - entry.timestamp > maxAge) {
                this.cache.delete(key);
            }
        }
        
        this.logger.debug('UnifiedRenderer', '_cleanupCache', `清理缓存，当前缓存条目: ${this.cache.size}`);
    }

    /**
     * 设置样式管理器
     * @param {StyleManager} styleManager - 样式管理器实例
     */
    setStyleManager(styleManager) {
        if (!(styleManager instanceof StyleManager)) {
            throw new Error('样式管理器必须是StyleManager实例');
        }
        
        this.styleManager = styleManager;
        this.logger.info('UnifiedRenderer', 'setStyleManager', '样式管理器已设置');
    }
    
    /**
     * 设置位置管理器
     * @param {PositionManager} positionManager - 位置管理器实例
     */
    setPositionManager(positionManager) {
        if (!(positionManager instanceof PositionManager)) {
            throw new Error('位置管理器必须是PositionManager实例');
        }

        this.positionManager = positionManager;
        this.logger.info('UnifiedRenderer', 'setPositionManager', '位置管理器已设置');
    }

    /**
     * 渲染文档 - 主要渲染方法
     * @param {DocumentModel|Object} document - 文档模型或文档数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<Object>} 渲染结果
     */
    async render(document, options = {}) {
        if (this.state.isRendering) {
            throw new Error('渲染器正在执行中，请等待当前渲染完成');
        }

        const startTime = Date.now();
        this.state.isRendering = true;
        this.performanceStats.totalRenders++;

        try {
            this.logger.info('UnifiedRenderer', 'render', '开始渲染文档');

            // 准备渲染
            const preparedData = await this._prepareRender(document, options);

            // 执行渲染
            const result = await this._executeRender(preparedData);

            // 后处理
            const finalResult = await this._postProcessRender(result, preparedData);

            // 更新统计
            const renderTime = Date.now() - startTime;
            this._updateStats(renderTime, true);

            this.logger.info('UnifiedRenderer', 'render', `渲染完成，耗时: ${renderTime}ms`);

            return {
                success: true,
                result: finalResult,
                renderTime,
                metadata: {
                    renderer: this.name,
                    version: this.version,
                    format: this.renderConfig.outputFormat,
                    timestamp: new Date().toISOString()
                }
            };

        } catch (error) {
            const renderTime = Date.now() - startTime;
            this._updateStats(renderTime, false);
            this._handleError(error);

            this.logger.error('UnifiedRenderer', 'render', '渲染失败', error);

            return {
                success: false,
                error: error.message,
                renderTime,
                metadata: {
                    renderer: this.name,
                    version: this.version,
                    format: this.renderConfig.outputFormat,
                    timestamp: new Date().toISOString()
                }
            };

        } finally {
            this.state.isRendering = false;
            this.state.lastRenderTime = new Date();
        }
    }

    /**
     * 准备渲染数据
     * @param {DocumentModel|Object} document - 文档
     * @param {Object} options - 选项
     * @returns {Promise<Object>} 准备好的数据
     * @protected
     */
    async _prepareRender(document, options) {
        // 执行前置钩子
        await this._executeHooks('beforeRender', { document, options });

        // 确保文档是DocumentModel实例
        const documentModel = document instanceof DocumentModel
            ? document
            : new DocumentModel(document);

        // 验证文档
        const validation = this.validateDocument(documentModel);
        if (!validation.isValid) {
            throw new Error(`文档验证失败: ${validation.errors.join(', ')}`);
        }

        // 合并选项
        const mergedOptions = {
            ...this.renderConfig,
            ...options,
            format: options.format || this.renderConfig.outputFormat,
            quality: options.quality || this.renderConfig.outputQuality,
            theme: options.theme || this.renderConfig.theme
        };

        // 检查缓存
        const cacheKey = this._generateCacheKey(documentModel, mergedOptions);
        if (this.renderConfig.enableCache && this.cache.has(cacheKey)) {
            this.cacheStats.hits++;
            this.logger.info('UnifiedRenderer', '_prepareRender', `缓存命中: ${cacheKey}`);
            return {
                fromCache: true,
                result: this.cache.get(cacheKey)
            };
        }

        this.cacheStats.misses++;

        return {
            document: documentModel,
            options: mergedOptions,
            cacheKey,
            fromCache: false
        };
    }

    /**
     * 执行渲染 - 子类必须实现的抽象方法
     * @param {Object} preparedData - 准备好的数据
     * @returns {Promise<Object>} 渲染结果
     * @protected
     * @abstract
     */
    async _executeRender(preparedData) {
        throw new Error('_executeRender 方法必须在子类中实现');
    }

    /**
     * 后处理渲染结果
     * @param {Object} result - 渲染结果
     * @param {Object} preparedData - 准备好的数据
     * @returns {Promise<Object>} 处理后的结果
     * @private
     */
    async _postProcessRender(result, preparedData) {
        // 执行后置钩子
        await this._executeHooks('afterRender', { result, preparedData });

        // 缓存结果
        if (this.renderConfig.enableCache && !preparedData.fromCache) {
            this.cache.set(preparedData.cacheKey, {
                result,
                timestamp: Date.now()
            });
        }

        // 添加元数据
        return {
            ...result,
            metadata: {
                renderer: this.name,
                version: this.version,
                format: this.renderConfig.outputFormat,
                renderTime: Date.now() - preparedData.timestamp,
                timestamp: new Date().toISOString()
            }
        };
    }

    /**
     * 验证文档
     * @param {DocumentModel} document - 文档模型
     * @returns {Object} 验证结果
     */
    validateDocument(document) {
        const errors = [];

        if (!document) {
            errors.push('文档对象不能为空');
        } else {
            if (!document.type) {
                errors.push('文档必须指定类型');
            }
            if (!document.data) {
                errors.push('文档必须包含数据');
            }
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 生成缓存键
     * @param {DocumentModel} document - 文档模型
     * @param {Object} options - 渲染选项
     * @returns {string} 缓存键
     * @private
     */
    _generateCacheKey(document, options) {
        const keyData = {
            documentId: document.id || 'unknown',
            documentType: document.type,
            documentVersion: document.version || '1.0.0',
            options: JSON.stringify(options)
        };

        return `${this.name}_${JSON.stringify(keyData)}`;
    }

    /**
     * 执行钩子
     * @param {string} hookName - 钩子名称
     * @param {Object} data - 钩子数据
     * @private
     */
    async _executeHooks(hookName, data) {
        if (this.hooks[hookName]) {
            for (const hook of this.hooks[hookName]) {
                try {
                    await hook(data);
                } catch (error) {
                    this.logger.error('UnifiedRenderer', '_executeHooks', `钩子执行失败: ${hookName}`, error);
                }
            }
        }
    }

    /**
     * 更新统计信息
     * @param {number} renderTime - 渲染时间
     * @param {boolean} success - 是否成功
     * @private
     */
    _updateStats(renderTime, success) {
        this.performanceStats.totalRenderTime += renderTime;

        if (success) {
            this.performanceStats.successfulRenders++;
        } else {
            this.performanceStats.failedRenders++;
        }

        // 更新平均渲染时间
        this.performanceStats.averageRenderTime =
            this.performanceStats.totalRenderTime / this.performanceStats.totalRenders;

        // 更新最小/最大渲染时间
        this.performanceStats.minRenderTime = Math.min(this.performanceStats.minRenderTime, renderTime);
        this.performanceStats.maxRenderTime = Math.max(this.performanceStats.maxRenderTime, renderTime);

        // 记录渲染历史
        this.performanceStats.renderHistory.push({
            renderTime,
            success,
            timestamp: new Date()
        });

        // 保持历史记录在合理范围内
        if (this.performanceStats.renderHistory.length > 100) {
            this.performanceStats.renderHistory.shift();
        }
    }

    /**
     * 处理错误
     * @param {Error} error - 错误对象
     * @private
     */
    _handleError(error) {
        this.errors.push({
            error: error.message,
            timestamp: new Date(),
            stack: error.stack
        });

        // 执行错误钩子
        this._executeHooks('onError', { error });

        // 触发错误事件
        this.emit(EventTypes.RENDERER.ERROR, {
            renderer: this.name,
            error: error.message,
            timestamp: new Date()
        });
    }

    /**
     * 获取性能统计
     * @returns {Object} 性能统计信息
     */
    getPerformanceStats() {
        return {
            ...this.performanceStats,
            cacheSize: this.cache.size,
            cacheStats: this.cacheStats,
            isRendering: this.state.isRendering,
            renderer: this.name
        };
    }

    /**
     * 获取渲染器信息
     * @returns {Object} 渲染器信息
     */
    getInfo() {
        return {
            name: this.name,
            version: this.version,
            type: this.type,
            format: this.renderConfig.outputFormat,
            state: this.state,
            stats: this.performanceStats,
            cacheSize: this.cache.size,
            errorCount: this.errors.length,
            warningCount: this.warnings.length
        };
    }

    /**
     * 清理渲染器
     */
    destroy() {
        this.logger.info('UnifiedRenderer', 'destroy', '销毁统一渲染器');

        // 清理缓存
        this.cache.clear();

        // 清理错误和警告
        this.errors = [];
        this.warnings = [];

        // 清理钩子
        for (const hookName in this.hooks) {
            this.hooks[hookName] = [];
        }

        // 移除所有事件监听器
        this.removeAllListeners();

        // 重置状态
        this.state.isInitialized = false;
        this.state.isRendering = false;
    }
}
// #endregion

// #region 导出
export { EventTypes };
export default UnifiedRenderer;
// #endregion
