/**
 * @file 导出服务 - 文档导出和格式转换服务模块
 * <AUTHOR> Team
 * @description 
 * 这个文件定义了导出服务类，负责文档的导出和格式转换
 * 从index.html中提取的导出处理逻辑被重构到这里
 * 
 * 功能特性：
 * - 多格式导出（PDF、图片、HTML）
 * - 批量导出处理
 * - 导出进度跟踪
 * - 导出质量控制
 * - 导出历史记录
 */

// #region 导出服务类定义
/**
 * @class ExportService - 导出服务类
 * @description 提供文档导出和格式转换功能
 */
export class ExportService {
    /**
     * 构造函数 - 初始化导出服务
     * @param {Object} options - 服务配置选项
     */
    constructor(options = {}) {
        // 基础配置
        this.config = options.config;
        this.eventBus = options.eventBus;
        this.documentService = options.documentService;
        
        // 导出配置
        this.exportConfig = {
            defaultFormat: 'pdf',
            quality: 'high',
            autoDownload: true,
            showProgress: true,
            enableBatch: true,
            maxConcurrent: 3,
            timeout: 60000, // 60秒超时
            ...options.exportConfig
        };
        
        // 服务状态
        this.isInitialized = false;
        this.isExporting = false;
        this.exportQueue = [];
        this.activeExports = new Map();
        
        // 渲染器实例
        this.renderers = new Map();
        
        // 导出历史
        this.exportHistory = [];
        this.maxHistorySize = 100;
        
        // 统计信息
        this.stats = {
            totalExports: 0,
            successfulExports: 0,
            failedExports: 0,
            averageExportTime: 0,
            exportsByFormat: {
                pdf: 0,
                image: 0,
                html: 0
            }
        };
        
        console.log('[ExportService] 导出服务已创建');
    }

    /**
     * 初始化导出服务
     * @returns {Promise<void>}
     */
    async initialize() {
        try {
            console.log('[ExportService] 初始化导出服务...');
            
            // 初始化渲染器
            await this._initializeRenderers();
            
            // 设置事件监听
            this._setupEventListeners();
            
            // 恢复导出历史
            this._restoreExportHistory();
            
            this.isInitialized = true;
            
            // 触发初始化完成事件
            if (this.eventBus) {
                this.eventBus.emit('export:initialized', {
                    service: 'ExportService',
                    timestamp: new Date()
                });
            }
            
            console.log('[ExportService] 导出服务初始化完成');
            
        } catch (error) {
            console.error('[ExportService] 导出服务初始化失败:', error);
            throw error;
        }
    }

    /**
     * 导出文档
     * @param {string} documentId - 文档ID
     * @param {Object} options - 导出选项
     * @returns {Promise<Object>} 导出结果
     */
    async exportDocument(documentId, options = {}) {
        if (!this.isInitialized) {
            throw new Error('导出服务未初始化');
        }
        
        const exportId = this._generateExportId();
        const startTime = Date.now();
        
        try {
            console.log(`[ExportService] 开始导出文档: ${documentId}`);
            
            // 获取文档
            const document = this.documentService.getDocument(documentId);
            if (!document) {
                throw new Error(`未找到文档: ${documentId}`);
            }
            
            // 合并导出选项
            const exportOptions = {
                format: this.exportConfig.defaultFormat,
                quality: this.exportConfig.quality,
                autoDownload: this.exportConfig.autoDownload,
                ...options
            };
            
            // 创建导出任务
            const exportTask = {
                id: exportId,
                documentId: documentId,
                document: document,
                options: exportOptions,
                status: 'processing',
                startTime: startTime,
                progress: 0
            };
            
            // 添加到活动导出列表
            this.activeExports.set(exportId, exportTask);
            
            // 触发导出开始事件
            if (this.eventBus) {
                this.eventBus.emit('export:started', {
                    exportId: exportId,
                    documentId: documentId,
                    format: exportOptions.format,
                    timestamp: new Date()
                });
            }
            
            // 执行导出
            const result = await this._performExport(exportTask);
            
            // 更新任务状态
            exportTask.status = 'completed';
            exportTask.endTime = Date.now();
            exportTask.progress = 100;
            exportTask.result = result;
            
            // 添加到历史记录
            this._addToHistory(exportTask);
            
            // 更新统计
            this._updateStats(exportTask.endTime - exportTask.startTime, true, exportOptions.format);
            
            // 自动下载
            if (exportOptions.autoDownload && result.blob) {
                this._downloadFile(result.blob, result.filename);
            }
            
            // 触发导出完成事件
            if (this.eventBus) {
                this.eventBus.emit('export:completed', {
                    exportId: exportId,
                    documentId: documentId,
                    result: result,
                    timestamp: new Date()
                });
            }
            
            console.log(`[ExportService] 文档导出完成: ${documentId}`);
            return result;
            
        } catch (error) {
            // 更新统计
            this._updateStats(Date.now() - startTime, false, options.format);
            
            // 触发导出失败事件
            if (this.eventBus) {
                this.eventBus.emit('export:failed', {
                    exportId: exportId,
                    documentId: documentId,
                    error: error.message,
                    timestamp: new Date()
                });
            }
            
            console.error('[ExportService] 文档导出失败:', error);
            throw error;
            
        } finally {
            // 从活动导出列表中移除
            this.activeExports.delete(exportId);
        }
    }

    /**
     * 批量导出文档
     * @param {Array} documentIds - 文档ID列表
     * @param {Object} options - 导出选项
     * @returns {Promise<Array>} 导出结果列表
     */
    async batchExport(documentIds, options = {}) {
        if (!this.exportConfig.enableBatch) {
            throw new Error('批量导出功能未启用');
        }
        
        console.log(`[ExportService] 开始批量导出: ${documentIds.length} 个文档`);
        
        const results = [];
        const batchId = this._generateBatchId();
        
        try {
            // 触发批量导出开始事件
            if (this.eventBus) {
                this.eventBus.emit('export:batch-started', {
                    batchId: batchId,
                    documentIds: documentIds,
                    timestamp: new Date()
                });
            }
            
            // 分批处理，控制并发数量
            const batches = this._chunkArray(documentIds, this.exportConfig.maxConcurrent);
            
            for (const batch of batches) {
                const batchPromises = batch.map(documentId => 
                    this.exportDocument(documentId, { ...options, autoDownload: false })
                        .catch(error => ({ error: error.message, documentId }))
                );
                
                const batchResults = await Promise.all(batchPromises);
                results.push(...batchResults);
                
                // 更新进度
                const progress = (results.length / documentIds.length) * 100;
                if (this.eventBus) {
                    this.eventBus.emit('export:batch-progress', {
                        batchId: batchId,
                        progress: progress,
                        completed: results.length,
                        total: documentIds.length,
                        timestamp: new Date()
                    });
                }
            }
            
            // 创建批量下载包
            if (options.createZip && results.some(r => r.blob)) {
                const zipBlob = await this._createZipFile(results);
                this._downloadFile(zipBlob, `batch_export_${batchId}.zip`);
            }
            
            // 触发批量导出完成事件
            if (this.eventBus) {
                this.eventBus.emit('export:batch-completed', {
                    batchId: batchId,
                    results: results,
                    timestamp: new Date()
                });
            }
            
            console.log(`[ExportService] 批量导出完成: ${results.length} 个文档`);
            return results;
            
        } catch (error) {
            console.error('[ExportService] 批量导出失败:', error);
            throw error;
        }
    }

    /**
     * 获取导出进度
     * @param {string} exportId - 导出ID
     * @returns {Object|null} 导出进度信息
     */
    getExportProgress(exportId) {
        const exportTask = this.activeExports.get(exportId);
        if (!exportTask) {
            return null;
        }
        
        return {
            id: exportTask.id,
            documentId: exportTask.documentId,
            status: exportTask.status,
            progress: exportTask.progress,
            startTime: exportTask.startTime,
            elapsedTime: Date.now() - exportTask.startTime
        };
    }

    /**
     * 取消导出
     * @param {string} exportId - 导出ID
     * @returns {boolean} 是否成功取消
     */
    cancelExport(exportId) {
        const exportTask = this.activeExports.get(exportId);
        if (!exportTask) {
            return false;
        }
        
        exportTask.status = 'cancelled';
        this.activeExports.delete(exportId);
        
        // 触发导出取消事件
        if (this.eventBus) {
            this.eventBus.emit('export:cancelled', {
                exportId: exportId,
                documentId: exportTask.documentId,
                timestamp: new Date()
            });
        }
        
        console.log(`[ExportService] 导出已取消: ${exportId}`);
        return true;
    }

    /**
     * 获取导出历史
     * @param {Object} filters - 过滤条件
     * @returns {Array} 导出历史列表
     */
    getExportHistory(filters = {}) {
        let history = [...this.exportHistory];
        
        // 应用过滤条件
        if (filters.format) {
            history = history.filter(item => item.options.format === filters.format);
        }
        
        if (filters.status) {
            history = history.filter(item => item.status === filters.status);
        }
        
        if (filters.dateFrom) {
            history = history.filter(item => item.startTime >= filters.dateFrom.getTime());
        }
        
        if (filters.dateTo) {
            history = history.filter(item => item.startTime <= filters.dateTo.getTime());
        }
        
        // 按时间倒序排列
        history.sort((a, b) => b.startTime - a.startTime);
        
        return history;
    }

    /**
     * 获取服务统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.stats,
            activeExports: this.activeExports.size,
            queueSize: this.exportQueue.length,
            historySize: this.exportHistory.length,
            isInitialized: this.isInitialized,
            isExporting: this.isExporting
        };
    }

    /**
     * 初始化渲染器
     * @private
     */
    async _initializeRenderers() {
        try {
            // 动态导入渲染器
            const { HTMLRenderer } = await import('../renderers/html-renderer.js');
            const { PDFRenderer } = await import('../renderers/pdf-renderer.js');
            
            // 创建渲染器实例
            this.renderers.set('html', new HTMLRenderer());
            this.renderers.set('pdf', new PDFRenderer());
            this.renderers.set('image', new HTMLRenderer()); // 图片导出使用HTML渲染器
            
            console.log('[ExportService] 渲染器初始化完成');
            
        } catch (error) {
            console.error('[ExportService] 渲染器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 执行导出
     * @param {Object} exportTask - 导出任务
     * @returns {Promise<Object>} 导出结果
     * @private
     */
    async _performExport(exportTask) {
        const { document, options } = exportTask;
        
        // 更新进度
        exportTask.progress = 10;
        this._notifyProgress(exportTask);
        
        // 获取渲染器
        const renderer = this.renderers.get(options.format);
        if (!renderer) {
            throw new Error(`不支持的导出格式: ${options.format}`);
        }
        
        // 更新进度
        exportTask.progress = 30;
        this._notifyProgress(exportTask);
        
        // 执行渲染
        const renderResult = await renderer.render(document, options);
        
        // 更新进度
        exportTask.progress = 70;
        this._notifyProgress(exportTask);
        
        // 处理渲染结果
        let result;
        switch (options.format) {
            case 'pdf':
                result = await this._processPDFResult(renderResult, options);
                break;
            case 'image':
                result = await this._processImageResult(renderResult, options);
                break;
            case 'html':
                result = await this._processHTMLResult(renderResult, options);
                break;
            default:
                throw new Error(`不支持的导出格式: ${options.format}`);
        }
        
        // 更新进度
        exportTask.progress = 90;
        this._notifyProgress(exportTask);
        
        return result;
    }

    /**
     * 处理PDF结果
     * @param {Object} renderResult - 渲染结果
     * @param {Object} options - 导出选项
     * @returns {Promise<Object>} 处理后的结果
     * @private
     */
    async _processPDFResult(renderResult, options) {
        return {
            format: 'pdf',
            blob: renderResult.blob,
            filename: `document_${Date.now()}.pdf`,
            size: renderResult.blob.size,
            metadata: renderResult.metadata
        };
    }

    /**
     * 处理图片结果
     * @param {Object} renderResult - 渲染结果
     * @param {Object} options - 导出选项
     * @returns {Promise<Object>} 处理后的结果
     * @private
     */
    async _processImageResult(renderResult, options) {
        // 将HTML转换为图片
        const canvas = await this._htmlToCanvas(renderResult.content, options);
        const blob = await this._canvasToBlob(canvas, options.imageFormat || 'png');
        
        return {
            format: 'image',
            blob: blob,
            filename: `document_${Date.now()}.${options.imageFormat || 'png'}`,
            size: blob.size,
            width: canvas.width,
            height: canvas.height
        };
    }

    /**
     * 处理HTML结果
     * @param {Object} renderResult - 渲染结果
     * @param {Object} options - 导出选项
     * @returns {Promise<Object>} 处理后的结果
     * @private
     */
    async _processHTMLResult(renderResult, options) {
        const blob = new Blob([renderResult.content], { type: 'text/html' });
        
        return {
            format: 'html',
            blob: blob,
            filename: `document_${Date.now()}.html`,
            size: blob.size,
            content: renderResult.content
        };
    }

    /**
     * HTML转Canvas
     * @param {string} html - HTML内容
     * @param {Object} options - 选项
     * @returns {Promise<HTMLCanvasElement>} Canvas元素
     * @private
     */
    async _htmlToCanvas(html, options) {
        // 这里需要使用html2canvas库
        if (typeof window === 'undefined' || !window.html2canvas) {
            throw new Error('html2canvas库未加载');
        }
        
        // 创建临时元素
        const tempElement = document.createElement('div');
        tempElement.innerHTML = html;
        tempElement.style.position = 'absolute';
        tempElement.style.left = '-9999px';
        tempElement.style.top = '-9999px';
        document.body.appendChild(tempElement);
        
        try {
            const canvas = await window.html2canvas(tempElement, {
                scale: options.scale || 2,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff'
            });
            
            return canvas;
            
        } finally {
            document.body.removeChild(tempElement);
        }
    }

    /**
     * Canvas转Blob
     * @param {HTMLCanvasElement} canvas - Canvas元素
     * @param {string} format - 图片格式
     * @returns {Promise<Blob>} 图片Blob
     * @private
     */
    async _canvasToBlob(canvas, format) {
        return new Promise((resolve) => {
            canvas.toBlob(resolve, `image/${format}`, 0.9);
        });
    }

    /**
     * 下载文件
     * @param {Blob} blob - 文件Blob
     * @param {string} filename - 文件名
     * @private
     */
    _downloadFile(blob, filename) {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // 清理URL对象
        setTimeout(() => URL.revokeObjectURL(url), 1000);
    }

    /**
     * 创建ZIP文件
     * @param {Array} results - 导出结果列表
     * @returns {Promise<Blob>} ZIP文件Blob
     * @private
     */
    async _createZipFile(results) {
        // 这里需要使用JSZip库
        if (typeof window === 'undefined' || !window.JSZip) {
            throw new Error('JSZip库未加载');
        }
        
        const zip = new window.JSZip();
        
        for (const result of results) {
            if (result.blob) {
                zip.file(result.filename, result.blob);
            }
        }
        
        return await zip.generateAsync({ type: 'blob' });
    }

    /**
     * 数组分块
     * @param {Array} array - 原数组
     * @param {number} size - 块大小
     * @returns {Array} 分块后的数组
     * @private
     */
    _chunkArray(array, size) {
        const chunks = [];
        for (let i = 0; i < array.length; i += size) {
            chunks.push(array.slice(i, i + size));
        }
        return chunks;
    }

    /**
     * 通知进度
     * @param {Object} exportTask - 导出任务
     * @private
     */
    _notifyProgress(exportTask) {
        if (this.eventBus) {
            this.eventBus.emit('export:progress', {
                exportId: exportTask.id,
                documentId: exportTask.documentId,
                progress: exportTask.progress,
                timestamp: new Date()
            });
        }
    }

    /**
     * 添加到历史记录
     * @param {Object} exportTask - 导出任务
     * @private
     */
    _addToHistory(exportTask) {
        this.exportHistory.unshift({
            id: exportTask.id,
            documentId: exportTask.documentId,
            options: exportTask.options,
            status: exportTask.status,
            startTime: exportTask.startTime,
            endTime: exportTask.endTime,
            result: exportTask.result ? {
                format: exportTask.result.format,
                filename: exportTask.result.filename,
                size: exportTask.result.size
            } : null
        });
        
        // 限制历史记录大小
        if (this.exportHistory.length > this.maxHistorySize) {
            this.exportHistory = this.exportHistory.slice(0, this.maxHistorySize);
        }
    }

    /**
     * 更新统计信息
     * @param {number} exportTime - 导出时间
     * @param {boolean} success - 是否成功
     * @param {string} format - 导出格式
     * @private
     */
    _updateStats(exportTime, success, format) {
        this.stats.totalExports++;
        
        if (success) {
            this.stats.successfulExports++;
            
            // 更新平均导出时间
            this.stats.averageExportTime = 
                (this.stats.averageExportTime * (this.stats.successfulExports - 1) + exportTime) / 
                this.stats.successfulExports;
        } else {
            this.stats.failedExports++;
        }
        
        // 更新格式统计
        if (format && this.stats.exportsByFormat[format] !== undefined) {
            this.stats.exportsByFormat[format]++;
        }
    }

    /**
     * 恢复导出历史
     * @private
     */
    _restoreExportHistory() {
        try {
            const savedHistory = localStorage.getItem('smartoffice-export-history');
            if (savedHistory) {
                this.exportHistory = JSON.parse(savedHistory);
                console.log('[ExportService] 导出历史已恢复');
            }
        } catch (error) {
            console.warn('[ExportService] 导出历史恢复失败:', error);
        }
    }

    /**
     * 设置事件监听
     * @private
     */
    _setupEventListeners() {
        if (this.eventBus) {
            // 监听配置变更
            this.eventBus.on('config:changed', (event) => {
                if (event.path.startsWith('export.')) {
                    this._handleConfigChange(event);
                }
            });
            
            // 监听窗口关闭事件
            window.addEventListener('beforeunload', () => {
                this._saveExportHistory();
            });
        }
    }

    /**
     * 生成导出ID
     * @returns {string} 导出ID
     * @private
     */
    _generateExportId() {
        return `export_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 生成批量ID
     * @returns {string} 批量ID
     * @private
     */
    _generateBatchId() {
        return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 保存导出历史
     * @private
     */
    _saveExportHistory() {
        try {
            localStorage.setItem('smartoffice-export-history', JSON.stringify(this.exportHistory));
        } catch (error) {
            console.warn('[ExportService] 保存导出历史失败:', error);
        }
    }

    /**
     * 处理配置变更
     * @param {Object} event - 配置变更事件
     * @private
     */
    _handleConfigChange(event) {
        console.log('[ExportService] 配置已变更:', event.path);
        // 可以在这里处理配置变更的逻辑
    }

    /**
     * 销毁导出服务
     */
    destroy() {
        // 保存导出历史
        this._saveExportHistory();
        
        // 取消所有活动导出
        for (const exportId of this.activeExports.keys()) {
            this.cancelExport(exportId);
        }
        
        // 清理数据
        this.renderers.clear();
        this.activeExports.clear();
        this.exportQueue = [];
        this.exportHistory = [];
        this.isInitialized = false;
        
        console.log('[ExportService] 导出服务已销毁');
    }
}
// #endregion
