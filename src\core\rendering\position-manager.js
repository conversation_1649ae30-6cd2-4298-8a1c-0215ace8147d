/**
 * SmartOffice 2.0 位置管理器
 * 统一管理所有元素的位置计算，确保预览和导出的位置一致性
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-12-19
 */

/**
 * 位置管理器类
 * 负责元素的位置计算、布局管理和坐标转换
 */
export class PositionManager {
    /**
     * 构造函数
     * @param {Object} config - 位置配置
     */
    constructor(config = {}) {
        this.config = {
            // A4纸张尺寸（毫米）
            pageWidth: 210,
            pageHeight: 297,
            
            // 页边距（毫米）
            marginTop: 20,
            marginBottom: 20,
            marginLeft: 15,
            marginRight: 15,
            
            // DPI设置
            dpi: 96,
            
            // 缩放比例
            scale: 1,
            
            // 坐标系统
            coordinateSystem: 'top-left', // 'top-left', 'bottom-left'
            
            // 单位转换
            units: {
                mm: 1,
                px: 25.4 / 96, // 1px = 25.4/96 mm
                pt: 25.4 / 72, // 1pt = 25.4/72 mm
                in: 25.4       // 1in = 25.4 mm
            },
            
            ...config
        };
        
        // 位置缓存
        this.positionCache = new Map();
        
        // 布局容器
        this.layoutContainers = new Map();
        
        // 元素注册表
        this.elementRegistry = new Map();
        
        // 碰撞检测
        this.collisionDetection = true;
        
        // 自动布局
        this.autoLayout = false;
        
        // 计算可用区域
        this.availableArea = this._calculateAvailableArea();
        
        console.log('[PositionManager] 位置管理器初始化完成');
    }
    
    /**
     * 注册元素
     * @param {string} id - 元素ID
     * @param {Object} element - 元素信息
     * @returns {Object} 注册结果
     */
    registerElement(id, element) {
        try {
            const elementInfo = {
                id,
                type: element.type || 'unknown',
                position: element.position || { x: 0, y: 0 },
                size: element.size || { width: 0, height: 0 },
                zIndex: element.zIndex || 0,
                constraints: element.constraints || {},
                metadata: element.metadata || {},
                timestamp: Date.now()
            };
            
            // 验证元素信息
            const validation = this._validateElement(elementInfo);
            if (!validation.isValid) {
                throw new Error(`元素验证失败: ${validation.errors.join(', ')}`);
            }
            
            // 计算绝对位置
            elementInfo.absolutePosition = this._calculateAbsolutePosition(elementInfo);
            
            // 注册元素
            this.elementRegistry.set(id, elementInfo);
            
            // 清除相关缓存
            this._clearPositionCache(id);
            
            console.log(`[PositionManager] 元素已注册: ${id}`);
            
            return {
                success: true,
                element: elementInfo
            };
            
        } catch (error) {
            console.error(`[PositionManager] 元素注册失败: ${id}`, error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * 更新元素位置
     * @param {string} id - 元素ID
     * @param {Object} position - 新位置
     * @returns {Object} 更新结果
     */
    updateElementPosition(id, position) {
        if (!this.elementRegistry.has(id)) {
            return {
                success: false,
                error: `元素不存在: ${id}`
            };
        }
        
        try {
            const element = this.elementRegistry.get(id);
            
            // 更新位置
            element.position = { ...element.position, ...position };
            element.absolutePosition = this._calculateAbsolutePosition(element);
            element.timestamp = Date.now();
            
            // 碰撞检测
            if (this.collisionDetection) {
                const collisions = this._detectCollisions(element);
                if (collisions.length > 0) {
                    console.warn(`[PositionManager] 检测到碰撞: ${id}`, collisions);
                }
            }
            
            // 清除缓存
            this._clearPositionCache(id);
            
            console.log(`[PositionManager] 元素位置已更新: ${id}`);
            
            return {
                success: true,
                element,
                collisions: this.collisionDetection ? this._detectCollisions(element) : []
            };
            
        } catch (error) {
            console.error(`[PositionManager] 位置更新失败: ${id}`, error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * 获取元素位置
     * @param {string} id - 元素ID
     * @param {string} unit - 单位 ('mm', 'px', 'pt', 'in')
     * @returns {Object} 位置信息
     */
    getElementPosition(id, unit = 'mm') {
        if (!this.elementRegistry.has(id)) {
            return null;
        }
        
        const cacheKey = `${id}-${unit}`;
        
        // 检查缓存
        if (this.positionCache.has(cacheKey)) {
            return this.positionCache.get(cacheKey);
        }
        
        const element = this.elementRegistry.get(id);
        const position = this._convertPosition(element.absolutePosition, 'mm', unit);
        const size = this._convertSize(element.size, 'mm', unit);
        
        const result = {
            id,
            position,
            size,
            zIndex: element.zIndex,
            unit,
            bounds: {
                left: position.x,
                top: position.y,
                right: position.x + size.width,
                bottom: position.y + size.height
            }
        };
        
        // 缓存结果
        this.positionCache.set(cacheKey, result);
        
        return result;
    }
    
    /**
     * 计算相对位置
     * @param {string} elementId - 元素ID
     * @param {string} referenceId - 参考元素ID
     * @param {string} unit - 单位
     * @returns {Object} 相对位置
     */
    getRelativePosition(elementId, referenceId, unit = 'mm') {
        const element = this.getElementPosition(elementId, unit);
        const reference = this.getElementPosition(referenceId, unit);
        
        if (!element || !reference) {
            return null;
        }
        
        return {
            x: element.position.x - reference.position.x,
            y: element.position.y - reference.position.y,
            unit
        };
    }
    
    /**
     * 批量布局元素
     * @param {Array} elements - 元素列表
     * @param {Object} layoutOptions - 布局选项
     * @returns {Object} 布局结果
     */
    layoutElements(elements, layoutOptions = {}) {
        const options = {
            type: 'flow', // 'flow', 'grid', 'absolute'
            direction: 'vertical', // 'vertical', 'horizontal'
            spacing: 5, // mm
            alignment: 'left', // 'left', 'center', 'right', 'justify'
            container: null,
            ...layoutOptions
        };
        
        try {
            let layoutResult;
            
            switch (options.type) {
                case 'flow':
                    layoutResult = this._flowLayout(elements, options);
                    break;
                case 'grid':
                    layoutResult = this._gridLayout(elements, options);
                    break;
                case 'absolute':
                    layoutResult = this._absoluteLayout(elements, options);
                    break;
                default:
                    throw new Error(`不支持的布局类型: ${options.type}`);
            }
            
            // 应用布局结果
            for (const element of layoutResult.elements) {
                this.updateElementPosition(element.id, element.position);
            }
            
            console.log(`[PositionManager] 批量布局完成: ${elements.length}个元素`);
            
            return {
                success: true,
                layout: layoutResult,
                elements: layoutResult.elements
            };
            
        } catch (error) {
            console.error('[PositionManager] 批量布局失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * 检测碰撞
     * @param {string|Object} element - 元素ID或元素对象
     * @returns {Array} 碰撞列表
     */
    detectCollisions(element) {
        const targetElement = typeof element === 'string' 
            ? this.elementRegistry.get(element)
            : element;
            
        if (!targetElement) {
            return [];
        }
        
        return this._detectCollisions(targetElement);
    }
    
    /**
     * 获取可用区域
     * @param {string} unit - 单位
     * @returns {Object} 可用区域
     */
    getAvailableArea(unit = 'mm') {
        return this._convertSize(this.availableArea, 'mm', unit);
    }
    
    /**
     * 坐标转换
     * @param {Object} position - 位置对象
     * @param {string} fromUnit - 源单位
     * @param {string} toUnit - 目标单位
     * @returns {Object} 转换后的位置
     */
    convertPosition(position, fromUnit, toUnit) {
        return this._convertPosition(position, fromUnit, toUnit);
    }
    
    /**
     * 尺寸转换
     * @param {Object} size - 尺寸对象
     * @param {string} fromUnit - 源单位
     * @param {string} toUnit - 目标单位
     * @returns {Object} 转换后的尺寸
     */
    convertSize(size, fromUnit, toUnit) {
        return this._convertSize(size, fromUnit, toUnit);
    }
    
    /**
     * 获取页面信息
     * @param {string} unit - 单位
     * @returns {Object} 页面信息
     */
    getPageInfo(unit = 'mm') {
        const pageSize = this._convertSize(
            { width: this.config.pageWidth, height: this.config.pageHeight },
            'mm',
            unit
        );
        
        const margins = {
            top: this._convertValue(this.config.marginTop, 'mm', unit),
            bottom: this._convertValue(this.config.marginBottom, 'mm', unit),
            left: this._convertValue(this.config.marginLeft, 'mm', unit),
            right: this._convertValue(this.config.marginRight, 'mm', unit)
        };
        
        const availableArea = this.getAvailableArea(unit);
        
        return {
            pageSize,
            margins,
            availableArea,
            unit,
            dpi: this.config.dpi,
            scale: this.config.scale
        };
    }
    
    /**
     * 设置页面配置
     * @param {Object} config - 页面配置
     */
    setPageConfig(config) {
        this.config = { ...this.config, ...config };
        this.availableArea = this._calculateAvailableArea();
        this._clearAllCache();
        
        console.log('[PositionManager] 页面配置已更新');
    }
    
    /**
     * 获取元素列表
     * @param {Object} filter - 过滤条件
     * @returns {Array} 元素列表
     */
    getElements(filter = {}) {
        const elements = Array.from(this.elementRegistry.values());
        
        if (Object.keys(filter).length === 0) {
            return elements;
        }
        
        return elements.filter(element => {
            for (const [key, value] of Object.entries(filter)) {
                if (element[key] !== value) {
                    return false;
                }
            }
            return true;
        });
    }
    
    /**
     * 移除元素
     * @param {string} id - 元素ID
     * @returns {boolean} 是否成功
     */
    removeElement(id) {
        if (this.elementRegistry.has(id)) {
            this.elementRegistry.delete(id);
            this._clearPositionCache(id);
            console.log(`[PositionManager] 元素已移除: ${id}`);
            return true;
        }
        return false;
    }
    
    /**
     * 清除所有元素
     */
    clearElements() {
        this.elementRegistry.clear();
        this._clearAllCache();
        console.log('[PositionManager] 所有元素已清除');
    }
    
    /**
     * 获取统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        const elements = Array.from(this.elementRegistry.values());
        const typeCount = {};
        
        elements.forEach(element => {
            typeCount[element.type] = (typeCount[element.type] || 0) + 1;
        });
        
        return {
            totalElements: elements.length,
            typeCount,
            cacheSize: this.positionCache.size,
            availableArea: this.availableArea,
            pageInfo: this.getPageInfo()
        };
    }
    
    /**
     * 导出位置数据
     * @param {string} format - 导出格式 ('json', 'csv')
     * @returns {string} 导出数据
     */
    exportPositions(format = 'json') {
        const elements = Array.from(this.elementRegistry.values());
        
        if (format === 'json') {
            return JSON.stringify({
                config: this.config,
                elements: elements.map(element => ({
                    id: element.id,
                    type: element.type,
                    position: element.position,
                    absolutePosition: element.absolutePosition,
                    size: element.size,
                    zIndex: element.zIndex
                })),
                timestamp: Date.now()
            }, null, 2);
        } else if (format === 'csv') {
            const headers = ['ID', 'Type', 'X', 'Y', 'Width', 'Height', 'ZIndex'];
            const rows = elements.map(element => [
                element.id,
                element.type,
                element.absolutePosition.x,
                element.absolutePosition.y,
                element.size.width,
                element.size.height,
                element.zIndex
            ]);
            
            return [headers, ...rows].map(row => row.join(',')).join('\n');
        }
        
        throw new Error(`不支持的导出格式: ${format}`);
    }
    
    /**
     * 销毁位置管理器
     */
    destroy() {
        this.elementRegistry.clear();
        this.positionCache.clear();
        this.layoutContainers.clear();
        
        console.log('[PositionManager] 位置管理器已销毁');
    }
    
    // ==================== 私有方法 ====================
    
    /**
     * 计算可用区域
     * @returns {Object} 可用区域
     * @private
     */
    _calculateAvailableArea() {
        return {
            width: this.config.pageWidth - this.config.marginLeft - this.config.marginRight,
            height: this.config.pageHeight - this.config.marginTop - this.config.marginBottom
        };
    }
    
    /**
     * 验证元素
     * @param {Object} element - 元素对象
     * @returns {Object} 验证结果
     * @private
     */
    _validateElement(element) {
        const errors = [];
        
        if (!element.id) {
            errors.push('元素ID不能为空');
        }
        
        if (!element.position || typeof element.position.x !== 'number' || typeof element.position.y !== 'number') {
            errors.push('元素位置必须包含有效的x和y坐标');
        }
        
        if (!element.size || typeof element.size.width !== 'number' || typeof element.size.height !== 'number') {
            errors.push('元素尺寸必须包含有效的width和height');
        }
        
        if (element.size && (element.size.width < 0 || element.size.height < 0)) {
            errors.push('元素尺寸不能为负数');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    
    /**
     * 计算绝对位置
     * @param {Object} element - 元素对象
     * @returns {Object} 绝对位置
     * @private
     */
    _calculateAbsolutePosition(element) {
        let x = element.position.x;
        let y = element.position.y;
        
        // 如果是相对位置，转换为绝对位置
        if (element.position.relative) {
            x += this.config.marginLeft;
            y += this.config.marginTop;
        }
        
        // 应用缩放
        x *= this.config.scale;
        y *= this.config.scale;
        
        return { x, y };
    }
    
    /**
     * 检测碰撞
     * @param {Object} targetElement - 目标元素
     * @returns {Array} 碰撞列表
     * @private
     */
    _detectCollisions(targetElement) {
        const collisions = [];
        const targetBounds = this._getElementBounds(targetElement);
        
        for (const [id, element] of this.elementRegistry) {
            if (id === targetElement.id) continue;
            
            const elementBounds = this._getElementBounds(element);
            
            if (this._boundsIntersect(targetBounds, elementBounds)) {
                collisions.push({
                    elementId: id,
                    element,
                    intersection: this._calculateIntersection(targetBounds, elementBounds)
                });
            }
        }
        
        return collisions;
    }
    
    /**
     * 获取元素边界
     * @param {Object} element - 元素对象
     * @returns {Object} 边界信息
     * @private
     */
    _getElementBounds(element) {
        const pos = element.absolutePosition;
        const size = element.size;
        
        return {
            left: pos.x,
            top: pos.y,
            right: pos.x + size.width,
            bottom: pos.y + size.height,
            width: size.width,
            height: size.height
        };
    }
    
    /**
     * 检测边界是否相交
     * @param {Object} bounds1 - 边界1
     * @param {Object} bounds2 - 边界2
     * @returns {boolean} 是否相交
     * @private
     */
    _boundsIntersect(bounds1, bounds2) {
        return !(bounds1.right <= bounds2.left || 
                bounds1.left >= bounds2.right || 
                bounds1.bottom <= bounds2.top || 
                bounds1.top >= bounds2.bottom);
    }
    
    /**
     * 计算相交区域
     * @param {Object} bounds1 - 边界1
     * @param {Object} bounds2 - 边界2
     * @returns {Object} 相交区域
     * @private
     */
    _calculateIntersection(bounds1, bounds2) {
        const left = Math.max(bounds1.left, bounds2.left);
        const top = Math.max(bounds1.top, bounds2.top);
        const right = Math.min(bounds1.right, bounds2.right);
        const bottom = Math.min(bounds1.bottom, bounds2.bottom);
        
        return {
            left,
            top,
            right,
            bottom,
            width: Math.max(0, right - left),
            height: Math.max(0, bottom - top)
        };
    }
    
    /**
     * 流式布局
     * @param {Array} elements - 元素列表
     * @param {Object} options - 布局选项
     * @returns {Object} 布局结果
     * @private
     */
    _flowLayout(elements, options) {
        const layoutElements = [];
        let currentX = 0;
        let currentY = 0;
        let maxHeight = 0;
        
        for (const element of elements) {
            const elementInfo = this.elementRegistry.get(element.id) || element;
            
            if (options.direction === 'horizontal') {
                // 水平流式布局
                if (currentX + elementInfo.size.width > this.availableArea.width) {
                    currentX = 0;
                    currentY += maxHeight + options.spacing;
                    maxHeight = 0;
                }
                
                layoutElements.push({
                    ...elementInfo,
                    position: { x: currentX, y: currentY }
                });
                
                currentX += elementInfo.size.width + options.spacing;
                maxHeight = Math.max(maxHeight, elementInfo.size.height);
                
            } else {
                // 垂直流式布局
                layoutElements.push({
                    ...elementInfo,
                    position: { x: currentX, y: currentY }
                });
                
                currentY += elementInfo.size.height + options.spacing;
            }
        }
        
        return {
            type: 'flow',
            elements: layoutElements,
            bounds: {
                width: options.direction === 'horizontal' ? this.availableArea.width : Math.max(...layoutElements.map(e => e.size.width)),
                height: currentY + (options.direction === 'horizontal' ? maxHeight : 0)
            }
        };
    }
    
    /**
     * 网格布局
     * @param {Array} elements - 元素列表
     * @param {Object} options - 布局选项
     * @returns {Object} 布局结果
     * @private
     */
    _gridLayout(elements, options) {
        const { columns = 3, rows = Math.ceil(elements.length / columns) } = options;
        const cellWidth = (this.availableArea.width - (columns - 1) * options.spacing) / columns;
        const cellHeight = (this.availableArea.height - (rows - 1) * options.spacing) / rows;
        
        const layoutElements = elements.map((element, index) => {
            const elementInfo = this.elementRegistry.get(element.id) || element;
            const col = index % columns;
            const row = Math.floor(index / columns);
            
            const x = col * (cellWidth + options.spacing);
            const y = row * (cellHeight + options.spacing);
            
            return {
                ...elementInfo,
                position: { x, y },
                gridPosition: { col, row }
            };
        });
        
        return {
            type: 'grid',
            elements: layoutElements,
            grid: { columns, rows, cellWidth, cellHeight },
            bounds: {
                width: this.availableArea.width,
                height: rows * cellHeight + (rows - 1) * options.spacing
            }
        };
    }
    
    /**
     * 绝对布局
     * @param {Array} elements - 元素列表
     * @param {Object} options - 布局选项
     * @returns {Object} 布局结果
     * @private
     */
    _absoluteLayout(elements, options) {
        const layoutElements = elements.map(element => {
            const elementInfo = this.elementRegistry.get(element.id) || element;
            return {
                ...elementInfo,
                position: element.position || elementInfo.position
            };
        });
        
        return {
            type: 'absolute',
            elements: layoutElements,
            bounds: this.availableArea
        };
    }
    
    /**
     * 位置转换
     * @param {Object} position - 位置对象
     * @param {string} fromUnit - 源单位
     * @param {string} toUnit - 目标单位
     * @returns {Object} 转换后的位置
     * @private
     */
    _convertPosition(position, fromUnit, toUnit) {
        if (fromUnit === toUnit) {
            return { ...position };
        }
        
        return {
            x: this._convertValue(position.x, fromUnit, toUnit),
            y: this._convertValue(position.y, fromUnit, toUnit)
        };
    }
    
    /**
     * 尺寸转换
     * @param {Object} size - 尺寸对象
     * @param {string} fromUnit - 源单位
     * @param {string} toUnit - 目标单位
     * @returns {Object} 转换后的尺寸
     * @private
     */
    _convertSize(size, fromUnit, toUnit) {
        if (fromUnit === toUnit) {
            return { ...size };
        }
        
        return {
            width: this._convertValue(size.width, fromUnit, toUnit),
            height: this._convertValue(size.height, fromUnit, toUnit)
        };
    }
    
    /**
     * 数值转换
     * @param {number} value - 数值
     * @param {string} fromUnit - 源单位
     * @param {string} toUnit - 目标单位
     * @returns {number} 转换后的数值
     * @private
     */
    _convertValue(value, fromUnit, toUnit) {
        if (fromUnit === toUnit) {
            return value;
        }
        
        // 先转换为毫米
        const mmValue = value * this.config.units[fromUnit];
        
        // 再转换为目标单位
        return mmValue / this.config.units[toUnit];
    }
    
    /**
     * 清除位置缓存
     * @param {string} elementId - 元素ID
     * @private
     */
    _clearPositionCache(elementId) {
        const keysToDelete = [];
        for (const key of this.positionCache.keys()) {
            if (key.startsWith(elementId + '-')) {
                keysToDelete.push(key);
            }
        }
        
        keysToDelete.forEach(key => this.positionCache.delete(key));
    }
    
    /**
     * 清除所有缓存
     * @private
     */
    _clearAllCache() {
        this.positionCache.clear();
    }
}