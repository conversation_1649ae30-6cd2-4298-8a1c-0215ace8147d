/**
 * @file 基础模板引擎 - 提供模板注册、渲染和管理的核心功能
 * <AUTHOR> Team
 * @description 
 * 这个文件定义了模板引擎的核心架构，包括：
 * - 基础模板类 BaseTemplate，所有模板的基础接口
 * - 模板注册表 TemplateRegistry，管理所有已注册的模板
 * - 模板渲染器 TemplateRenderer，负责模板的渲染和输出
 * - 模板配置管理，支持主题、语言、样式等配置
 */

// #region 导入依赖模块
import { EventEmitter } from '../core/events/event-emitter.js';
import { Validator } from '../core/utils/validation.js';
import { StringUtils } from '../core/utils/string-utils.js';
import { DateUtils } from '../core/utils/date-utils.js';
// #endregion

// #region 基础模板类定义
/**
 * @class BaseTemplate - 基础模板类
 * @description 所有文档模板的基础类，定义了模板的核心接口和功能
 */
export class BaseTemplate {
    /**
     * 构造函数 - 初始化模板实例
     * @param {Object} config - 模板配置对象
     * @param {string} config.name - 模板名称，用于识别模板
     * @param {string} config.type - 模板类型，如 'receipt', 'invoice' 等
     * @param {string} config.version - 模板版本号
     * @param {Object} config.metadata - 模板元数据，包含作者、描述等信息
     */
    constructor(config = {}) {
        // 基础属性初始化
        this.name = config.name || 'untitled-template'; // 模板名称
        this.type = config.type || 'generic'; // 模板类型
        this.version = config.version || '1.0.0'; // 模板版本
        this.metadata = config.metadata || {}; // 模板元数据
        
        // 渲染配置初始化
        this.renderConfig = {
            theme: config.theme || 'default', // 主题名称
            language: config.language || 'zh-CN', // 语言设置
            currency: config.currency || 'RM', // 货币设置
            dateFormat: config.dateFormat || 'YYYY-MM-DD', // 日期格式
            ...config.renderConfig // 其他渲染配置
        };
        
        // 模板内容和结构
        this.template = config.template || ''; // 模板字符串
        this.styles = config.styles || {}; // 样式配置
        this.scripts = config.scripts || []; // 脚本配置
        
        // 验证规则和约束
        this.validationRules = config.validationRules || {}; // 数据验证规则
        this.requiredFields = config.requiredFields || []; // 必填字段列表
        
        // 状态管理
        this.isRegistered = false; // 是否已注册到注册表
        this.lastRendered = null; // 最后渲染时间
        this.renderCount = 0; // 渲染次数统计
        
        // 事件发射器，用于模板事件通知
        this.events = new EventEmitter();
        
        // 初始化完成后的处理
        this._initialize();
    }

    /**
     * 初始化模板 - 执行模板的初始化逻辑
     * @private
     * @description 子类可以重写此方法来执行特定的初始化逻辑
     */
    _initialize() {
        // 验证模板配置
        this._validateConfig();
        
        // 设置默认样式
        this._setupDefaultStyles();
        
        // 发射初始化完成事件
        this.events.emit('template:initialized', {
            name: this.name,
            type: this.type,
            timestamp: new Date()
        });
    }

    /**
     * 验证模板配置 - 确保模板配置的有效性
     * @private
     * @throws {Error} 当配置无效时抛出错误
     */
    _validateConfig() {
        if (!this.name || typeof this.name !== 'string') {
            throw new Error('模板名称必须是有效的字符串');
        }
        
        if (!this.type || typeof this.type !== 'string') {
            throw new Error('模板类型必须是有效的字符串');
        }
        
        // 验证版本号格式
        const versionPattern = /^\d+\.\d+\.\d+$/;
        if (!versionPattern.test(this.version)) {
            throw new Error('版本号格式无效，应为 x.y.z 格式');
        }
    }

    /**
     * 设置默认样式 - 为模板设置基础样式
     * @private
     * @description 子类可以重写此方法来设置特定的默认样式
     */
    _setupDefaultStyles() {
        // 基础样式配置
        this.styles = {
            fontFamily: 'Arial, sans-serif', // 默认字体
            fontSize: '14px', // 默认字体大小
            lineHeight: '1.5', // 行高
            color: '#333333', // 文字颜色
            backgroundColor: '#ffffff', // 背景颜色
            ...this.styles // 保留用户自定义样式
        };
    }

    /**
     * 渲染模板 - 根据数据渲染模板内容
     * @param {Object} data - 渲染数据对象
     * @param {Object} options - 渲染选项配置
     * @returns {Promise<string>} 渲染后的HTML字符串
     * @description 这是模板的核心方法，负责将数据转换为可视化内容
     */
    async render(data = {}, options = {}) {
        try {
            // 记录渲染开始
            const startTime = Date.now();
            this.renderCount++;
            
            // 发射渲染开始事件
            this.events.emit('template:render:start', {
                template: this.name,
                data,
                options,
                timestamp: new Date()
            });

            // 验证输入数据
            await this._validateData(data);
            
            // 预处理数据
            const processedData = await this._preprocessData(data);
            
            // 合并渲染选项
            const renderOptions = {
                ...this.renderConfig,
                ...options
            };
            
            // 执行实际渲染
            const renderedContent = await this._doRender(processedData, renderOptions);
            
            // 后处理渲染结果
            const finalContent = await this._postprocessContent(renderedContent, renderOptions);
            
            // 更新渲染统计信息
            this.lastRendered = new Date();
            const renderTime = Date.now() - startTime;
            
            // 发射渲染完成事件
            this.events.emit('template:render:complete', {
                template: this.name,
                renderTime,
                contentLength: finalContent.length,
                timestamp: new Date()
            });
            
            return finalContent;
            
        } catch (error) {
            // 发射渲染错误事件
            this.events.emit('template:render:error', {
                template: this.name,
                error: error.message,
                timestamp: new Date()
            });
            
            throw new Error(`模板渲染失败: ${error.message}`);
        }
    }

    /**
     * 验证渲染数据 - 检查数据是否符合模板要求
     * @param {Object} data - 要验证的数据对象
     * @private
     * @throws {Error} 当数据验证失败时抛出错误
     */
    async _validateData(data) {
        // 检查必填字段
        for (const field of this.requiredFields) {
            if (!data.hasOwnProperty(field) || data[field] === null || data[field] === undefined) {
                throw new Error(`缺少必填字段: ${field}`);
            }
        }
        
        // 执行自定义验证规则
        for (const [field, rules] of Object.entries(this.validationRules)) {
            if (data.hasOwnProperty(field)) {
                try {
                    await this._validateField(field, data[field], rules);
                } catch (error) {
                    throw new Error(`字段 ${field} 验证失败: ${error.message}`);
                }
            }
        }
    }

    /**
     * 验证单个字段 - 根据规则验证字段值
     * @param {string} fieldName - 字段名称
     * @param {any} value - 字段值
     * @param {Object} rules - 验证规则
     * @private
     */
    async _validateField(fieldName, value, rules) {
        // 类型验证
        if (rules.type && typeof value !== rules.type) {
            throw new Error(`字段类型错误，期望 ${rules.type}，实际 ${typeof value}`);
        }
        
        // 长度验证
        if (rules.minLength && value.length < rules.minLength) {
            throw new Error(`字段长度不足，最少需要 ${rules.minLength} 个字符`);
        }
        
        if (rules.maxLength && value.length > rules.maxLength) {
            throw new Error(`字段长度超限，最多允许 ${rules.maxLength} 个字符`);
        }
        
        // 正则表达式验证
        if (rules.pattern && !rules.pattern.test(value)) {
            throw new Error(`字段格式不正确`);
        }
        
        // 自定义验证函数
        if (rules.validator && typeof rules.validator === 'function') {
            const isValid = await rules.validator(value);
            if (!isValid) {
                throw new Error(`自定义验证失败`);
            }
        }
    }

    /**
     * 预处理数据 - 在渲染前对数据进行处理和转换
     * @param {Object} data - 原始数据对象
     * @returns {Promise<Object>} 处理后的数据对象
     * @private
     * @description 子类可以重写此方法来实现特定的数据预处理逻辑
     */
    async _preprocessData(data) {
        const processedData = { ...data };
        
        // 日期格式化处理
        for (const [key, value] of Object.entries(processedData)) {
            if (value instanceof Date) {
                processedData[key] = DateUtils.format(value, this.renderConfig.dateFormat, this.renderConfig.language);
            }
        }
        
        // 数字和货币格式化处理
        if (processedData.amount && typeof processedData.amount === 'number') {
            processedData.formattedAmount = StringUtils.formatCurrency(
                processedData.amount,
                this.renderConfig.currency,
                2,
                this.renderConfig.language
            );
        }
        
        return processedData;
    }

    /**
     * 执行实际渲染 - 这是子类必须实现的核心渲染方法
     * @param {Object} data - 预处理后的数据对象
     * @param {Object} options - 渲染选项配置
     * @returns {Promise<string>} 渲染结果字符串
     * @protected
     * @abstract
     * @description 子类必须重写此方法来实现具体的渲染逻辑
     */
    async _doRender(data, options) {
        throw new Error('子类必须实现 _doRender 方法');
    }

    /**
     * 后处理渲染内容 - 对渲染结果进行最终处理
     * @param {string} content - 渲染后的内容
     * @param {Object} options - 渲染选项配置
     * @returns {Promise<string>} 最终的渲染结果
     * @private
     * @description 子类可以重写此方法来实现特定的后处理逻辑
     */
    async _postprocessContent(content, options) {
        let processedContent = content;
        
        // 移除多余的空白字符
        if (options.minify) {
            processedContent = processedContent.replace(/\s+/g, ' ').trim();
        }
        
        // 添加样式包装
        if (options.includeStyles !== false) {
            processedContent = this._wrapWithStyles(processedContent);
        }
        
        return processedContent;
    }

    /**
     * 包装样式 - 为内容添加样式包装
     * @param {string} content - 原始内容
     * @returns {string} 包装后的内容
     * @private
     */
    _wrapWithStyles(content) {
        const styleString = Object.entries(this.styles)
            .map(([key, value]) => `${StringUtils.toKebabCase(key)}: ${value}`)
            .join('; ');
            
        return `<div style="${styleString}">${content}</div>`;
    }

    /**
     * 更新模板配置 - 动态更新模板的配置选项
     * @param {Object} newConfig - 新的配置对象
     * @description 允许在运行时动态修改模板配置
     */
    updateConfig(newConfig) {
        // 合并渲染配置
        if (newConfig.renderConfig) {
            this.renderConfig = {
                ...this.renderConfig,
                ...newConfig.renderConfig
            };
        }
        
        // 更新样式配置
        if (newConfig.styles) {
            this.styles = {
                ...this.styles,
                ...newConfig.styles
            };
        }
        
        // 更新验证规则
        if (newConfig.validationRules) {
            this.validationRules = {
                ...this.validationRules,
                ...newConfig.validationRules
            };
        }
        
        // 发射配置更新事件
        this.events.emit('template:config:updated', {
            template: this.name,
            changes: newConfig,
            timestamp: new Date()
        });
    }

    /**
     * 获取模板信息 - 返回模板的基本信息和统计数据
     * @returns {Object} 模板信息对象
     */
    getInfo() {
        return {
            name: this.name,
            type: this.type,
            version: this.version,
            metadata: this.metadata,
            isRegistered: this.isRegistered,
            renderCount: this.renderCount,
            lastRendered: this.lastRendered,
            config: {
                theme: this.renderConfig.theme,
                language: this.renderConfig.language,
                currency: this.renderConfig.currency
            }
        };
    }

    /**
     * 克隆模板 - 创建模板的副本
     * @param {string} newName - 新模板的名称
     * @returns {BaseTemplate} 克隆的模板实例
     */
    clone(newName) {
        const clonedConfig = {
            name: newName || `${this.name}-copy`,
            type: this.type,
            version: this.version,
            metadata: { ...this.metadata },
            renderConfig: { ...this.renderConfig },
            template: this.template,
            styles: { ...this.styles },
            scripts: [...this.scripts],
            validationRules: { ...this.validationRules },
            requiredFields: [...this.requiredFields]
        };
        
        return new this.constructor(clonedConfig);
    }

    /**
     * 序列化模板 - 将模板转换为可存储的JSON格式
     * @returns {string} 序列化后的JSON字符串
     */
    serialize() {
        const data = {
            name: this.name,
            type: this.type,
            version: this.version,
            metadata: this.metadata,
            renderConfig: this.renderConfig,
            template: this.template,
            styles: this.styles,
            scripts: this.scripts,
            validationRules: this.validationRules,
            requiredFields: this.requiredFields
        };
        
        return JSON.stringify(data, null, 2);
    }

    /**
     * 从JSON数据反序列化模板 - 静态方法，从JSON创建模板实例
     * @param {string} jsonData - JSON字符串数据
     * @returns {BaseTemplate} 反序列化的模板实例
     * @static
     */
    static deserialize(jsonData) {
        try {
            const config = JSON.parse(jsonData);
            return new BaseTemplate(config);
        } catch (error) {
            throw new Error(`模板反序列化失败: ${error.message}`);
        }
    }

    /**
     * 销毁模板 - 清理模板资源和事件监听器
     * @description 在模板不再使用时调用，进行资源清理
     */
    destroy() {
        // 清理事件监听器
        this.events.clear();
        
        // 重置状态
        this.isRegistered = false;
        
        // 发射销毁事件
        this.events.emit('template:destroyed', {
            template: this.name,
            timestamp: new Date()
        });
    }
}
// #endregion

// #region 模板注册表类定义
/**
 * @class TemplateRegistry - 模板注册表
 * @description 管理系统中所有已注册的模板，提供模板的注册、查找、管理功能
 */
export class TemplateRegistry {
    /**
     * 构造函数 - 初始化模板注册表
     */
    constructor() {
        this.templates = new Map(); // 存储已注册的模板，key为模板名称，value为模板实例
        this.typeIndex = new Map(); // 按类型索引模板，key为类型，value为模板名称数组
        this.events = new EventEmitter(); // 事件发射器，用于注册表事件通知
        
        // 统计信息
        this.stats = {
            totalRegistered: 0, // 总注册数量
            registrationHistory: [], // 注册历史记录
            lastAccess: null // 最后访问时间
        };
    }

    /**
     * 注册模板 - 将模板添加到注册表中
     * @param {BaseTemplate} template - 要注册的模板实例
     * @throws {Error} 当模板无效或已存在时抛出错误
     * @description 注册模板到系统中，使其可以被查找和使用
     */
    register(template) {
        // 验证模板实例
        if (!(template instanceof BaseTemplate)) {
            throw new Error('只能注册 BaseTemplate 实例或其子类');
        }
        
        // 检查名称冲突
        if (this.templates.has(template.name)) {
            throw new Error(`模板名称 "${template.name}" 已存在`);
        }
        
        // 注册模板
        this.templates.set(template.name, template);
        template.isRegistered = true;
        
        // 更新类型索引
        if (!this.typeIndex.has(template.type)) {
            this.typeIndex.set(template.type, []);
        }
        this.typeIndex.get(template.type).push(template.name);
        
        // 更新统计信息
        this.stats.totalRegistered++;
        this.stats.registrationHistory.push({
            name: template.name,
            type: template.type,
            timestamp: new Date()
        });
        
        // 发射注册事件
        this.events.emit('registry:template:registered', {
            template: template.name,
            type: template.type,
            timestamp: new Date()
        });
    }

    /**
     * 注销模板 - 从注册表中移除模板
     * @param {string} templateName - 要注销的模板名称
     * @returns {boolean} 是否成功注销
     */
    unregister(templateName) {
        if (!this.templates.has(templateName)) {
            return false;
        }
        
        const template = this.templates.get(templateName);
        
        // 从模板映射中移除
        this.templates.delete(templateName);
        template.isRegistered = false;
        
        // 从类型索引中移除
        const typeTemplates = this.typeIndex.get(template.type);
        if (typeTemplates) {
            const index = typeTemplates.indexOf(templateName);
            if (index > -1) {
                typeTemplates.splice(index, 1);
            }
            
            // 如果类型下没有模板了，移除类型索引
            if (typeTemplates.length === 0) {
                this.typeIndex.delete(template.type);
            }
        }
        
        // 更新统计信息
        this.stats.totalRegistered--;
        
        // 发射注销事件
        this.events.emit('registry:template:unregistered', {
            template: templateName,
            type: template.type,
            timestamp: new Date()
        });
        
        return true;
    }

    /**
     * 获取模板 - 根据名称获取已注册的模板
     * @param {string} templateName - 模板名称
     * @returns {BaseTemplate|null} 模板实例，如果不存在则返回null
     */
    get(templateName) {
        this.stats.lastAccess = new Date();
        return this.templates.get(templateName) || null;
    }

    /**
     * 检查模板是否存在 - 检查指定名称的模板是否已注册
     * @param {string} templateName - 模板名称
     * @returns {boolean} 是否存在
     */
    has(templateName) {
        return this.templates.has(templateName);
    }

    /**
     * 按类型获取模板 - 获取指定类型的所有模板
     * @param {string} templateType - 模板类型
     * @returns {BaseTemplate[]} 模板实例数组
     */
    getByType(templateType) {
        const templateNames = this.typeIndex.get(templateType) || [];
        return templateNames.map(name => this.templates.get(name)).filter(Boolean);
    }

    /**
     * 获取所有模板名称 - 返回所有已注册模板的名称列表
     * @returns {string[]} 模板名称数组
     */
    getNames() {
        return Array.from(this.templates.keys());
    }

    /**
     * 获取所有模板类型 - 返回所有已注册模板的类型列表
     * @returns {string[]} 模板类型数组
     */
    getTypes() {
        return Array.from(this.typeIndex.keys());
    }

    /**
     * 获取注册表统计信息 - 返回注册表的统计数据
     * @returns {Object} 统计信息对象
     */
    getStats() {
        return {
            ...this.stats,
            currentCount: this.templates.size,
            typeCount: this.typeIndex.size,
            typeDistribution: Object.fromEntries(
                Array.from(this.typeIndex.entries()).map(([type, templates]) => [type, templates.length])
            )
        };
    }

    /**
     * 清空注册表 - 移除所有已注册的模板
     * @description 谨慎使用，会移除所有模板
     */
    clear() {
        // 标记所有模板为未注册状态
        for (const template of this.templates.values()) {
            template.isRegistered = false;
        }
        
        // 清空映射
        this.templates.clear();
        this.typeIndex.clear();
        
        // 重置统计信息
        this.stats.totalRegistered = 0;
        this.stats.registrationHistory = [];
        
        // 发射清空事件
        this.events.emit('registry:cleared', {
            timestamp: new Date()
        });
    }
}
// #endregion

// #region 模板渲染器类定义
/**
 * @class TemplateRenderer - 模板渲染器
 * @description 负责模板的渲染和输出，支持批量渲染、缓存等高级功能
 */
export class TemplateRenderer {
    /**
     * 构造函数 - 初始化模板渲染器
     * @param {TemplateRegistry} registry - 模板注册表实例
     */
    constructor(registry) {
        if (!(registry instanceof TemplateRegistry)) {
            throw new Error('必须提供有效的模板注册表实例');
        }
        
        this.registry = registry; // 模板注册表引用
        this.cache = new Map(); // 渲染结果缓存
        this.events = new EventEmitter(); // 事件发射器
        
        // 渲染配置
        this.config = {
            enableCache: true, // 是否启用缓存
            cacheTimeout: 60 * 1000, // 缓存超时时间（毫秒）
            maxCacheSize: 100, // 最大缓存条目数
            concurrentLimit: 5 // 并发渲染限制
        };
        
        // 统计信息
        this.stats = {
            totalRenders: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageRenderTime: 0,
            renderHistory: []
        };
    }

    /**
     * 渲染模板 - 根据模板名称和数据渲染内容
     * @param {string} templateName - 模板名称
     * @param {Object} data - 渲染数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} 渲染结果
     */
    async render(templateName, data = {}, options = {}) {
        const startTime = Date.now();
        
        try {
            // 获取模板实例
            const template = this.registry.get(templateName);
            if (!template) {
                throw new Error(`模板 "${templateName}" 未找到`);
            }
            
            // 检查缓存
            if (this.config.enableCache && options.useCache !== false) {
                const cacheKey = this._generateCacheKey(templateName, data, options);
                const cachedResult = this._getFromCache(cacheKey);
                
                if (cachedResult) {
                    this.stats.cacheHits++;
                    return cachedResult;
                }
                this.stats.cacheMisses++;
            }
            
            // 执行渲染
            const result = await template.render(data, options);
            
            // 存储到缓存
            if (this.config.enableCache && options.useCache !== false) {
                const cacheKey = this._generateCacheKey(templateName, data, options);
                this._storeToCache(cacheKey, result);
            }
            
            // 更新统计信息
            const renderTime = Date.now() - startTime;
            this._updateStats(templateName, renderTime);
            
            return result;
            
        } catch (error) {
            // 发射渲染错误事件
            this.events.emit('renderer:error', {
                template: templateName,
                error: error.message,
                timestamp: new Date()
            });
            
            throw error;
        }
    }

    /**
     * 批量渲染 - 同时渲染多个模板
     * @param {Array} renderTasks - 渲染任务数组，每个任务包含 {template, data, options}
     * @returns {Promise<Array>} 渲染结果数组
     */
    async renderBatch(renderTasks) {
        if (!Array.isArray(renderTasks)) {
            throw new Error('渲染任务必须是数组');
        }
        
        // 分批处理，避免并发过多
        const batches = [];
        for (let i = 0; i < renderTasks.length; i += this.config.concurrentLimit) {
            batches.push(renderTasks.slice(i, i + this.config.concurrentLimit));
        }
        
        const results = [];
        for (const batch of batches) {
            const batchPromises = batch.map(async (task, index) => {
                try {
                    const result = await this.render(task.template, task.data, task.options);
                    return { success: true, result, index: i + index };
                } catch (error) {
                    return { success: false, error: error.message, index: i + index };
                }
            });
            
            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);
        }
        
        return results;
    }

    /**
     * 生成缓存键 - 根据输入参数生成唯一的缓存键
     * @param {string} templateName - 模板名称
     * @param {Object} data - 渲染数据
     * @param {Object} options - 渲染选项
     * @returns {string} 缓存键
     * @private
     */
    _generateCacheKey(templateName, data, options) {
        const keyData = {
            template: templateName,
            data: JSON.stringify(data),
            options: JSON.stringify(options)
        };
        
        return StringUtils.hash(JSON.stringify(keyData));
    }

    /**
     * 从缓存获取结果 - 根据缓存键获取缓存的渲染结果
     * @param {string} cacheKey - 缓存键
     * @returns {string|null} 缓存的结果，如果不存在或过期则返回null
     * @private
     */
    _getFromCache(cacheKey) {
        const cacheEntry = this.cache.get(cacheKey);
        
        if (!cacheEntry) {
            return null;
        }
        
        // 检查是否过期
        if (Date.now() - cacheEntry.timestamp > this.config.cacheTimeout) {
            this.cache.delete(cacheKey);
            return null;
        }
        
        return cacheEntry.result;
    }

    /**
     * 存储到缓存 - 将渲染结果存储到缓存中
     * @param {string} cacheKey - 缓存键
     * @param {string} result - 渲染结果
     * @private
     */
    _storeToCache(cacheKey, result) {
        // 检查缓存大小限制
        if (this.cache.size >= this.config.maxCacheSize) {
            // 移除最旧的缓存条目
            const oldestKey = this.cache.keys().next().value;
            this.cache.delete(oldestKey);
        }
        
        this.cache.set(cacheKey, {
            result,
            timestamp: Date.now()
        });
    }

    /**
     * 更新统计信息 - 更新渲染器的统计数据
     * @param {string} templateName - 模板名称
     * @param {number} renderTime - 渲染耗时
     * @private
     */
    _updateStats(templateName, renderTime) {
        this.stats.totalRenders++;
        
        // 更新平均渲染时间
        this.stats.averageRenderTime = 
            (this.stats.averageRenderTime * (this.stats.totalRenders - 1) + renderTime) / this.stats.totalRenders;
        
        // 记录渲染历史
        this.stats.renderHistory.push({
            template: templateName,
            renderTime,
            timestamp: new Date()
        });
        
        // 限制历史记录大小
        if (this.stats.renderHistory.length > 1000) {
            this.stats.renderHistory = this.stats.renderHistory.slice(-500);
        }
    }

    /**
     * 清空缓存 - 清除所有缓存的渲染结果
     */
    clearCache() {
        this.cache.clear();
        
        this.events.emit('renderer:cache:cleared', {
            timestamp: new Date()
        });
    }

    /**
     * 获取统计信息 - 返回渲染器的统计数据
     * @returns {Object} 统计信息对象
     */
    getStats() {
        return {
            ...this.stats,
            cacheSize: this.cache.size,
            cacheHitRate: this.stats.totalRenders > 0 
                ? (this.stats.cacheHits / this.stats.totalRenders * 100).toFixed(2) + '%'
                : '0%'
        };
    }

    /**
     * 更新配置 - 更新渲染器的配置选项
     * @param {Object} newConfig - 新的配置对象
     */
    updateConfig(newConfig) {
        this.config = {
            ...this.config,
            ...newConfig
        };
        
        // 如果禁用了缓存，清空现有缓存
        if (!newConfig.enableCache) {
            this.clearCache();
        }
        
        this.events.emit('renderer:config:updated', {
            config: this.config,
            timestamp: new Date()
        });
    }
}
// #endregion

// #region 默认导出
// 创建默认的模板注册表和渲染器实例
export const defaultRegistry = new TemplateRegistry();
export const defaultRenderer = new TemplateRenderer(defaultRegistry);
// #endregion 