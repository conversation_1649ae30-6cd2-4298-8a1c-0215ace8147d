/**
 * @file A4模板优化CSS
 * @description 针对A4纸张打印和预览的优化样式，确保内容布局准确，元素定位精确，不遮掩抬头/页脚图片。
 */

/* #region 基础与变量定义 */
:root {
    /* A4纸张标准尺寸 (毫米转换为像素，假设96 DPI) */
    --a4-width-px: 794px; /* 210mm * 96/25.4 */
    --a4-height-px: 1123px; /* 297mm * 96/25.4 */

    /* 页边距 (毫米转换为像素) - 增加边距确保内容不被遮掩 */
    --margin-top-px: 170px; /* 页眉高度160px + 10px安全间距 */
    --margin-bottom-px: 60px; /* 页脚高度38px + 22px安全间距 */
    --margin-left-px: 37.8px; /* 10mm */
    --margin-right-px: 37.8px; /* 10mm */

    /* 内容区域尺寸 */
    --content-width-px: calc(var(--a4-width-px) - var(--margin-left-px) - var(--margin-right-px));
    --content-height-px: calc(var(--a4-height-px) - var(--margin-top-px) - var(--margin-bottom-px));

    /* 预览容器缩放比例 */
    --preview-scale-factor: 0.8; /* 默认80% */

    /* 响应式缩放比例 */
    --preview-scale-min: 0.45; /* 最小45% */
    --preview-scale-max: 0.8;  /* 最大80% */

    /* 盖章定位 */
    --stamp-bottom-offset: 20%; /* 调整到80%位置 (100% - 80% = 20%) */
    --stamp-right-offset: 8%;
    --stamp-z-index: 20;

    /* 页眉页脚高度 - 增加高度确保图片完整显示 */
    --header-height-px: 160px; /* 页眉高度 */
    --footer-height-px: 38px; /* 页脚高度(10mm转换为像素) */

    /* 内容区域安全间距 - 初始值，主要由JS动态调整 */
    --content-safe-top: 10px; /* 初始较小值 */
    --content-safe-bottom: 10px; /* 初始较小值 */

    /* 结语与电子提示定位 */
    --conclusion-bottom-offset: calc(var(--footer-height-px) + 25px); /* 页脚高度 + 25px间距 */
    --e-generation-bottom-offset: calc(var(--footer-height-px) + 10px); /* 页脚高度 + 10px间距 */
    --e-generation-right-offset: 20px;

    /* 字体大小优化 - 确保在A4纸张上可读性 */
    --base-font-size: 12px;
    --small-font-size: 10px;
    --title-font-size: 18px;
    --subtitle-font-size: 14px;
}
/* #endregion */

/* #region 预览容器优化 */
.preview-container,
.driver-agreement-preview-container,
#document-preview {
    width: var(--a4-width-px);
    min-height: var(--a4-height-px); /* 最小A4高度，允许内容撑开 */
    border: 1px solid #ccc;
    margin: 20px auto;
    overflow: visible; /* 允许内容和指示器正常显示 */
    position: relative; /* 为绝对定位的子元素提供基准 */
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    background-color: white; /* 确保背景为白色 */
    transform: scale(var(--preview-scale-factor));
    transform-origin: top center;
}

/* 注释：document-container样式已移动到A4内容布局区域统一管理 */

/* 响应式缩放调整 - 针对不同屏幕尺寸 */
@media (max-width: 1400px) {
    .preview-container,
    .driver-agreement-preview-container,
    #document-preview {
        --preview-scale-factor: 0.7;
    }
}

@media (max-width: 1200px) {
    .preview-container,
    .driver-agreement-preview-container,
    #document-preview {
        --preview-scale-factor: 0.6;
    }
}

@media (max-width: 992px) {
    .preview-container,
    .driver-agreement-preview-container,
    #document-preview {
        --preview-scale-factor: 0.5;
    }
}

@media (max-width: 768px) {
    .preview-container,
    .driver-agreement-preview-container,
    #document-preview {
        --preview-scale-factor: var(--preview-scale-min); /* 移动端使用最小缩放以适应屏幕 */
        margin: 10px auto; /* 调整外边距 */
    }
}
/* #endregion */

/* #region A4内容布局与边距 */
.a4-content-area,
.driver-agreement-content-area {
    width: var(--a4-width-px);
    height: var(--a4-height-px); /* 修改：使用固定高度 */
    padding: var(--margin-top-px) var(--margin-right-px) var(--margin-bottom-px) var(--margin-left-px);
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    position: relative; /* 为绝对定位的页眉页脚提供基准 */
    background-color: white;
    font-size: var(--base-font-size);
    line-height: 1.5;
}

/* document-container 单独设置，避免与通用样式冲突 */
#document-container {
    width: var(--a4-width-px) !important;
    min-height: var(--a4-height-px) !important; /* 改为最小高度，允许内容撑开 */
    max-width: var(--a4-width-px) !important;
    overflow: visible; /* 预览时允许可见，确保所有内容都能看到 */
    padding: var(--margin-top-px) var(--margin-right-px) var(--margin-bottom-px) var(--margin-left-px);
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    position: relative;
    background-color: white;
    font-size: var(--base-font-size);
    line-height: 1.5;
}

/* 导出时强制A4高度 */
.pdf-export-container #document-container,
.image-export-container #document-container {
    height: var(--a4-height-px) !important; /* 导出时严格A4高度 */
    overflow: hidden !important; /* 导出时隐藏溢出内容 */
}
/* #endregion */

/* #region 页眉与页脚优化 */
.company-header,
.driver-agreement-header,
.document-header-image-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%; /* 确保宽度与容器一致 */
    /* height: var(--header-height-px); 移除固定高度，由内容决定 */
    display: flex;
    align-items: center; /* 垂直居中 */
    justify-content: center; /* 水平居中 */
    box-sizing: border-box;
    z-index: 10; /* 确保在内容之上 */
    overflow: hidden; /* 防止图片溢出 */
    padding: 5px 0; /* 添加少量上下内边距，避免图片紧贴边缘 */
}

.company-header img,
.driver-agreement-header img,
.document-header-image-container img,
.template-header-image {
    max-height: var(--header-height-px); /* 限制图片最大高度，原 --header-height-px 仍可作为参考 */
    max-width: 100%; /* 图片宽度不超过页眉 */
    object-fit: contain; /* 改为contain确保图片完整显示，不裁剪 */
    object-position: center; /* 居中显示 */
}

.company-footer,
.driver-agreement-footer,
.company-footer-image-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%; /* 确保宽度与容器一致 */
    height: auto; /* 高度由内容决定 */
    /* height: var(--footer-height-px); 移除固定高度，由内容决定 */
    display: flex;
    justify-content: center; /* 水平居中 */
    align-items: center; /* 垂直居中 */
    box-sizing: border-box;
    z-index: 10; /* 确保在内容之上 */
    overflow: hidden; /* 防止图片溢出 */
    padding: 5px 0; /* 添加少量上下内边距 */
}

.company-footer img,
.driver-agreement-footer img,
.company-footer-image-container img,
.template-footer-image {
    max-height: var(--footer-height-px); /* 限制图片最大高度，原 --footer-height-px 仍可作为参考 */
    max-width: 100%;
    object-fit: contain; /* 改为contain确保图片完整显示，不裁剪 */
    object-position: center; /* 居中显示 */
}
/* #endregion */

/* #region 内容主体区域优化 */
.main-content-wrapper {
    flex: 1; /* 占据剩余空间，但允许内容决定高度 */
    display: flex;
    flex-direction: column;
    /* padding-top: var(--content-safe-top); 移除固定的padding-top */
    /* padding-bottom: var(--content-safe-bottom); 移除固定的padding-bottom */
    /* JavaScript将动态计算并设置padding */
    box-sizing: border-box;
    width: 100%; /* 使用全宽 */
    margin: 0; /* 移除外边距 */
    min-height: 0; /* 允许flex项目缩小 */
    overflow: visible; /* 允许内容正常显示 */
}

/* 文档内容区域 */
.document-title-section,
.client-section,
.items-section,
.details-section {
    margin-bottom: 15px; /* 统一的区块间距 */
    flex-shrink: 0; /* 防止在flex布局中被压缩 */
}

/* 确保内容不会与页眉页脚重叠 */
.document-title-section {
    margin-top: 0; /* 移除顶部外边距，依赖padding-top */
}

.details-section {
    margin-bottom: var(--content-safe-bottom); /* 确保与页脚有足够间距 */
}
/* #endregion */

/* #region 标题区域优化 */
.title-section,
.document-title-section {
    text-align: center;
    margin-bottom: 20px; /* 标题和下方内容的间距 */
    flex-shrink: 0; /* 防止标题在flex布局中被压缩 */
    padding-top: 0; /* 移除额外的顶部内边距 */
}

.title-section h1,
.title-section h2,
.document-title-section h1 {
    margin: 8px 0;
    font-weight: bold;
    font-size: var(--title-font-size);
    line-height: 1.3;
}

.document-number-container {
    font-size: var(--small-font-size);
    margin-top: 10px;
}
/* #endregion */

/* #region 客户信息与买方信息布局优化 */
.customer-buyer-info,
.client-section .grid {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    gap: 20px; /* 左右两块之间的间距 */
}

.customer-info-left,
.buyer-info-right,
.customer-info,
.buyer-info {
    width: calc(50% - 10px); /* 左右各占50%，减去gap的一半 */
    padding: 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    box-sizing: border-box;
    background-color: #f9fafb;
}

.customer-info h3,
.buyer-info h3 {
    font-size: var(--small-font-size);
    font-weight: 600;
    margin-bottom: 8px;
    color: #6b7280;
}

.customer-info .p-4,
.buyer-info .p-4 {
    padding: 8px;
    background-color: white;
    border-radius: 4px;
    font-size: var(--small-font-size);
}

/* 移动端适配：客户信息和买方信息垂直排列 */
@media (max-width: 900px) { /* Tailwind的md断点通常是768px，这里用900px确保A4内容在缩放后也能良好显示 */
    .customer-buyer-info,
    .client-section .grid {
        flex-direction: column;
        gap: 10px; /* 垂直排列时间距缩小 */
    }

    .customer-info-left,
    .buyer-info-right,
    .customer-info,
    .buyer-info {
        width: 100%; /* 垂直排列时占满宽度 */
    }
}
/* #endregion */

/* #region 表格样式优化 */
.data-table,
.items-section table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
    font-size: var(--small-font-size);
}

.data-table th,
.data-table td,
.items-section table th,
.items-section table td {
    border: 1px solid #d1d5db;
    padding: 8px 12px;
    text-align: left;
    vertical-align: top;
}

.data-table th,
.items-section table th {
    background-color: #f3f4f6;
    font-weight: 600;
    font-size: var(--small-font-size);
    color: #374151;
}

.data-table tbody tr:nth-child(even),
.items-section table tbody tr:nth-child(even) {
    background-color: #f9fafb;
}

.data-table tfoot,
.items-section table tfoot {
    background-color: #f3f4f6;
    font-weight: 600;
}

/* 金额列右对齐 */
.amount-column,
.data-table .text-right,
.items-section table .text-right {
    text-align: right;
}

/* 表格响应式优化 */
@media (max-width: 768px) {
    .data-table,
    .items-section table {
        font-size: 10px;
    }
    
    .data-table th,
    .data-table td,
    .items-section table th,
    .items-section table td {
        padding: 6px 8px;
    }
}
/* #endregion */

/* #region 详细信息区域优化 */
.details-section {
    margin-bottom: 20px;
    font-size: var(--small-font-size);
}

.notes-section,
.payment-method-section {
    margin-bottom: 15px;
}

.notes-section h4,
.payment-method-section h4 {
    font-size: var(--small-font-size);
    font-weight: 600;
    margin-bottom: 6px;
    color: #6b7280;
    text-transform: uppercase;
}

.notes-section p,
.payment-method-section p {
    font-size: var(--small-font-size);
    line-height: 1.4;
    color: #374151;
    margin: 0;
}

/* 签名区域 */
.signature-section {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e5e7eb;
}

.signature-line {
    border-bottom: 1px solid #374151;
    height: 40px;
    margin-bottom: 8px;
}

.company-name-signature {
    font-size: var(--small-font-size);
    font-weight: 600;
    color: #374151;
}
/* #endregion */

/* #region 结语与电子生成提示优化 */
.conclusion-section {
    margin-top: auto; /* 将结语推到底部，但在页脚之上 */
    padding: 10px 0; /* 上下内边距 */
    text-align: center; /* 居中对齐 */
    position: absolute; /* 相对于容器定位 */
    bottom: var(--conclusion-bottom-offset);
    left: var(--margin-left-px);
    right: var(--margin-right-px);
    font-size: var(--small-font-size);
    line-height: 1.4;
    color: #6b7280;
}

.conclusion-text {
    margin: 4px 0;
    white-space: pre-wrap;
    word-break: break-word;
}

.e-generation-notice,
.electronic-generated-notice {
    position: absolute; /* 相对于容器定位 */
    bottom: var(--e-generation-bottom-offset);
    right: var(--e-generation-right-offset);
    font-size: var(--small-font-size);
    color: #9ca3af;
    z-index: 15;
    font-style: italic;
}
/* #endregion */

/* #region 盖章定位 */
.company-stamp,
.driver-agreement-stamp {
    position: absolute; /* 相对于预览容器 .preview-container 定位 */
    bottom: var(--stamp-bottom-offset);
    right: var(--stamp-right-offset);
    width: 150px; /* 根据实际盖章图片调整 */
    height: 150px; /* 根据实际盖章图片调整 */
    z-index: var(--stamp-z-index);
    opacity: 0.8; /* 轻微透明，使其看起来更真实 */
}

.company-stamp img,
.driver-agreement-stamp img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}
/* #endregion */

/* #region 打印样式 */
@media print {
    body, html {
        margin: 0;
        padding: 0;
        background-color: white; /* 打印时背景强制为白色 */
    }

    .preview-container,
    .driver-agreement-preview-container,
    #document-preview {
        width: var(--a4-width-px) !important; /* 打印时强制A4宽度 */
        height: var(--a4-height-px) !important; /* 打印时强制A4高度 */
        transform: scale(1) !important; /* 打印时无缩放 */
        border: none !important; /* 打印时无边框 */
        box-shadow: none !important; /* 打印时无阴影 */
        margin: 0 !important; /* 打印时无外边距 */
        overflow: hidden !important; /* 打印时隐藏溢出，确保A4边界 */
    }

    #document-container {
        height: var(--a4-height-px) !important; /* 打印时强制A4高度 */
        overflow: hidden !important; /* 打印时隐藏溢出内容 */
    }

    .a4-content-area,
    .driver-agreement-content-area {
        padding: var(--margin-top-px) var(--margin-right-px) var(--margin-bottom-px) var(--margin-left-px) !important;
    }

    .main-content-wrapper {
        overflow-y: visible !important; /* 打印时显示所有内容 */
    }

    /* 隐藏非打印元素 */
    .no-print, .no-print * {
        display: none !important;
    }

    /* 确保页眉页脚在打印时正确显示 */
    .company-header, .driver-agreement-header,
    .company-footer, .driver-agreement-footer {
        position: absolute !important; /* 确保打印时的绝对定位 */
    }

    .company-stamp, .driver-agreement-stamp,
    .e-generation-notice, .conclusion-section {
        position: absolute !important; /* 确保打印时的绝对定位 */
    }
}
/* #endregion */

/* #region 打印范围标识系统 */
/* 打印范围指示器容器 */
.print-range-indicator {
    position: absolute;
    top: 0;
    left: 0;
    width: var(--a4-width-px);
    height: var(--a4-height-px);
    pointer-events: none; /* 不影响用户交互 */
    z-index: 100; /* 确保在所有内容之上 */
    opacity: 0.9; /* 半透明显示 */
    transition: opacity 0.3s ease;
}

/* 打印范围指示器开关状态 */
.print-range-indicator.hidden {
    opacity: 0;
    pointer-events: none;
    visibility: hidden;
}

.print-range-indicator.visible {
    opacity: 0.9;
    visibility: visible;
}

/* 打印边界线 - 显示实际打印区域边界 */
.print-boundary-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* 上边界线 */
.print-boundary-top {
    position: absolute;
    top: var(--margin-top-px);
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #ff4757 0%, #ff6b6b 50%, #ff4757 100%);
    box-shadow: 0 2px 6px rgba(255, 71, 87, 0.4);
    border-radius: 1px;
}

/* 下边界线 */
.print-boundary-bottom {
    position: absolute;
    bottom: var(--margin-bottom-px);
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #ff4757 0%, #ff6b6b 50%, #ff4757 100%);
    box-shadow: 0 -2px 6px rgba(255, 71, 87, 0.4);
    border-radius: 1px;
}

/* 左边界线 */
.print-boundary-left {
    position: absolute;
    top: 0;
    left: var(--margin-left-px);
    width: 3px;
    height: 100%;
    background: linear-gradient(180deg, #2ed573 0%, #4ecdc4 50%, #2ed573 100%);
    box-shadow: 2px 0 6px rgba(46, 213, 115, 0.4);
    border-radius: 1px;
}

/* 右边界线 */
.print-boundary-right {
    position: absolute;
    top: 0;
    right: var(--margin-right-px);
    width: 3px;
    height: 100%;
    background: linear-gradient(180deg, #2ed573 0%, #4ecdc4 50%, #2ed573 100%);
    box-shadow: -2px 0 6px rgba(46, 213, 115, 0.4);
    border-radius: 1px;
}

/* 打印安全区域高亮 */
.print-safe-area {
    position: absolute;
    top: var(--margin-top-px);
    left: var(--margin-left-px);
    width: var(--content-width-px);
    height: var(--content-height-px);
    border: 2px dashed #3742fa;
    background: rgba(55, 66, 250, 0.08);
    box-sizing: border-box;
    border-radius: 4px;
}

/* 页面分割线 - 用于多页文档 */
.page-break-indicator {
    position: absolute;
    left: 0;
    width: 100%;
    height: 4px;
    background: repeating-linear-gradient(
        90deg,
        #ff9f43 0px,
        #ff9f43 12px,
        transparent 12px,
        transparent 24px
    );
    z-index: 99;
    border-radius: 2px;
    box-shadow: 0 2px 4px rgba(255, 159, 67, 0.3);
}

/* A4页面高度分割线 */
.page-break-a4 {
    top: var(--a4-height-px);
}

/* 打印范围标签 */
.print-range-labels {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    font-family: 'Arial', sans-serif;
    font-size: 11px;
    font-weight: 600;
    color: #2c3e50;
}

/* 上边距标签 */
.margin-label-top {
    position: absolute;
    top: calc(var(--margin-top-px) / 2 - 8px);
    left: 12px;
    background: rgba(255, 255, 255, 0.95);
    padding: 3px 8px;
    border-radius: 4px;
    border: 2px solid #ff4757;
    color: #ff4757;
    font-weight: 700;
    box-shadow: 0 2px 6px rgba(255, 71, 87, 0.3);
}

/* 下边距标签 */
.margin-label-bottom {
    position: absolute;
    bottom: calc(var(--margin-bottom-px) / 2 - 8px);
    left: 12px;
    background: rgba(255, 255, 255, 0.95);
    padding: 3px 8px;
    border-radius: 4px;
    border: 2px solid #ff4757;
    color: #ff4757;
    font-weight: 700;
    box-shadow: 0 2px 6px rgba(255, 71, 87, 0.3);
}

/* 左边距标签 */
.margin-label-left {
    position: absolute;
    top: 50%;
    left: calc(var(--margin-left-px) / 2 - 18px);
    transform: translateY(-50%) rotate(-90deg);
    background: rgba(255, 255, 255, 0.95);
    padding: 3px 8px;
    border-radius: 4px;
    border: 2px solid #2ed573;
    color: #2ed573;
    font-weight: 700;
    white-space: nowrap;
    box-shadow: 0 2px 6px rgba(46, 213, 115, 0.3);
}

/* 右边距标签 */
.margin-label-right {
    position: absolute;
    top: 50%;
    right: calc(var(--margin-right-px) / 2 - 18px);
    transform: translateY(-50%) rotate(90deg);
    background: rgba(255, 255, 255, 0.95);
    padding: 3px 8px;
    border-radius: 4px;
    border: 2px solid #2ed573;
    color: #2ed573;
    font-weight: 700;
    white-space: nowrap;
    box-shadow: 0 2px 6px rgba(46, 213, 115, 0.3);
}

/* 打印区域尺寸标签 */
.print-area-size-label {
    position: absolute;
    top: calc(var(--margin-top-px) + 12px);
    right: calc(var(--margin-right-px) + 12px);
    background: rgba(55, 66, 250, 0.95);
    color: white;
    padding: 5px 10px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 700;
    box-shadow: 0 3px 8px rgba(55, 66, 250, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 页面信息标签 */
.page-info-label {
    position: absolute;
    bottom: calc(var(--margin-bottom-px) + 12px);
    right: calc(var(--margin-right-px) + 12px);
    background: rgba(255, 159, 67, 0.95);
    color: white;
    padding: 5px 10px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 700;
    box-shadow: 0 3px 8px rgba(255, 159, 67, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 打印范围控制按钮 - 移除，使用外部按钮 */

/* 打印范围图例 */
.print-range-legend {
    position: absolute;
    bottom: 12px;
    left: 12px;
    background: rgba(255, 255, 255, 0.98);
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    padding: 10px 12px;
    font-size: 11px;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    max-width: 220px;
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
}

.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    color: #2c3e50;
}

.legend-item:last-child {
    margin-bottom: 0;
}

.legend-color {
    width: 14px;
    height: 14px;
    margin-right: 8px;
    border-radius: 3px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.legend-color.boundary {
    background: linear-gradient(135deg, #ff4757, #ff6b6b);
}

.legend-color.margin {
    background: linear-gradient(135deg, #2ed573, #4ecdc4);
}

.legend-color.safe-area {
    background: rgba(55, 66, 250, 0.15);
    border: 2px dashed #3742fa;
}

.legend-color.page-break {
    background: repeating-linear-gradient(
        90deg,
        #ff9f43 0px,
        #ff9f43 4px,
        transparent 4px,
        transparent 8px
    );
}

/* 响应式调整 - 在小屏幕上简化显示 */
@media (max-width: 768px) {
    .print-range-labels {
        font-size: 8px;
    }
    
    .margin-label-top,
    .margin-label-bottom,
    .margin-label-left,
    .margin-label-right {
        padding: 1px 4px;
        font-size: 8px;
    }
    
    .print-area-size-label,
    .page-info-label {
        font-size: 9px;
        padding: 3px 6px;
    }
    
    .print-range-toggle {
        font-size: 10px;
        padding: 6px 8px;
    }
    
    .print-range-legend {
        font-size: 8px;
        padding: 6px;
        max-width: 150px;
    }
}
/* #endregion */