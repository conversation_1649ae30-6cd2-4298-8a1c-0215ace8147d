<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartOffice 2.0 - 智能办公文档管理系统 (传统架构)</title>
    <meta name="description" content="SmartOffice 2.0 智能办公文档管理系统 - 支持发票、收据、司机协议等多种文档模板的生成和管理">
    <meta name="keywords" content="智能办公,文档管理,发票生成,收据模板,司机协议,PDF导出">
    <meta name="author" content="SmartOffice Team">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iOCIgZmlsbD0iIzM5OGVmNCIvPgo8cGF0aCBkPSJNOCAxMGg4djJIOHYtMnptMCA0aDEydjJIOHYtMnptMCA0aDEwdjJIOHYtMnptMTIgLTEwaDR2MTJoLTR2LTEyeiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+">
    
    <!-- CSS样式文件 -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- 本地CSS文件 -->
    <link rel="stylesheet" href="css/core-styles.css">
    <link rel="stylesheet" href="css/unified-styles.css">
    <link rel="stylesheet" href="css/print.css" media="print">
    
    <style>
        /* 传统架构特定样式 */
        .traditional-mode-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #10b981;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 9999;
            font-family: monospace;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .loading-content {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f4f6;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .module-status {
            margin-top: 1rem;
            font-size: 14px;
            color: #6b7280;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 传统架构模式指示器 -->
    <div class="traditional-mode-indicator">
        <i class="fas fa-cog"></i> 传统架构模式
    </div>
    
    <!-- 加载覆盖层 -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h3 style="margin: 0 0 0.5rem 0; color: #1f2937;">SmartOffice 2.0 正在启动</h3>
            <p style="margin: 0; color: #6b7280;">传统架构版本 - 正在加载模块...</p>
            <div id="moduleStatus" class="module-status">
                初始化命名空间...
            </div>
        </div>
    </div>
    
    <!-- 主应用容器 -->
    <div id="app" class="min-h-screen" style="display: none;">
        <!-- 应用内容将在这里动态生成 -->
        <div class="container mx-auto px-4 py-8">
            <div class="text-center">
                <h1 class="text-4xl font-bold text-gray-900 mb-4">
                    <i class="fas fa-file-alt text-blue-600"></i>
                    SmartOffice 2.0
                </h1>
                <p class="text-xl text-gray-600 mb-8">智能办公文档管理系统 - 传统架构版本</p>
                
                <div class="bg-white rounded-lg shadow-md p-6 max-w-2xl mx-auto">
                    <h2 class="text-2xl font-semibold mb-4">系统信息</h2>
                    <div id="systemInfo" class="text-left space-y-2">
                        <!-- 系统信息将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript文件加载 -->
    <!-- 1. 基础资源 -->
    <script src="assets/image-base64.js"></script>
    
    <!-- 2. 命名空间初始化 -->
    <script src="lib/namespace.js"></script>
    
    <!-- 3. 核心工具 (无依赖) -->
    <script src="lib/core/logger.js"></script>
    <script src="lib/core/event-bus.js"></script>
    
    <!-- 4. 应用启动脚本 -->
    <script>
        // 传统架构应用启动脚本
        (function() {
            'use strict';
            
            // 更新加载状态
            function updateLoadingStatus(message) {
                const statusElement = document.getElementById('moduleStatus');
                if (statusElement) {
                    statusElement.textContent = message;
                }
                console.log('🔄 ' + message);
            }
            
            // 显示系统信息
            function displaySystemInfo() {
                const systemInfoElement = document.getElementById('systemInfo');
                if (!systemInfoElement) return;
                
                const info = {
                    '版本': SmartOffice.version,
                    '构建日期': SmartOffice.buildDate,
                    '运行模式': SmartOffice.mode,
                    '协议': SmartOffice.Environment.protocol,
                    '浏览器': SmartOffice.Environment.browser.name || '未知',
                    '已加载模块': SmartOffice.Modules.loaded.size,
                    '总模块数': SmartOffice.Modules.dependencies.size
                };
                
                let html = '';
                for (const [key, value] of Object.entries(info)) {
                    html += `
                        <div class="flex justify-between py-1 border-b border-gray-200">
                            <span class="font-medium text-gray-700">${key}:</span>
                            <span class="text-gray-900">${value}</span>
                        </div>
                    `;
                }
                
                systemInfoElement.innerHTML = html;
            }
            
            // 隐藏加载覆盖层
            function hideLoadingOverlay() {
                const overlay = document.getElementById('loadingOverlay');
                const app = document.getElementById('app');
                
                if (overlay) {
                    overlay.style.display = 'none';
                }
                
                if (app) {
                    app.style.display = 'block';
                }
            }
            
            // 应用初始化
            async function initializeApp() {
                try {
                    updateLoadingStatus('检查命名空间...');
                    
                    // 检查SmartOffice命名空间
                    if (!window.SmartOffice) {
                        throw new Error('SmartOffice命名空间未初始化');
                    }
                    
                    updateLoadingStatus('初始化日志系统...');
                    
                    // 获取日志管理器
                    const logger = SmartOffice.Core.getLogger();
                    logger.info('App', 'initializeApp', '开始初始化SmartOffice 2.0传统架构版本');
                    
                    updateLoadingStatus('初始化事件系统...');
                    
                    // 获取事件总线
                    const eventBus = SmartOffice.Core.getEventBus();
                    eventBus.setDebugMode(true);
                    
                    updateLoadingStatus('检查模块加载状态...');
                    
                    // 检查模块加载状态
                    const loadStatus = SmartOffice.Modules.getLoadStatus();
                    logger.info('App', 'initializeApp', '模块加载状态', loadStatus);
                    
                    updateLoadingStatus('应用初始化完成');
                    
                    // 触发应用就绪事件
                    eventBus.emit('app:ready', {
                        version: SmartOffice.version,
                        mode: SmartOffice.mode,
                        timestamp: new Date().toISOString()
                    });
                    
                    // 显示系统信息
                    displaySystemInfo();
                    
                    // 延迟隐藏加载覆盖层
                    setTimeout(() => {
                        hideLoadingOverlay();
                        logger.info('App', 'initializeApp', '✅ SmartOffice 2.0传统架构版本启动完成');
                    }, 1000);
                    
                } catch (error) {
                    console.error('❌ 应用初始化失败:', error);
                    updateLoadingStatus('初始化失败: ' + error.message);
                    
                    // 显示错误信息
                    setTimeout(() => {
                        alert('应用初始化失败: ' + error.message);
                    }, 1000);
                }
            }
            
            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initializeApp);
            } else {
                initializeApp();
            }
            
        })();
    </script>
</body>
</html>
