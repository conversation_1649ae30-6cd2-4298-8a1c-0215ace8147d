/* @file 核心样式文件 */
/* @project SmartOffice */
/* 此文件包含应用程序的核心样式规则 */

/* #region 导入规则 - 必须在文件顶部 */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@30'); /* 引入 Google Fonts 的 Noto Sans SC 字体 (仅权重300) */
/* #endregion 导入规则 */

/* #region 全局样式 */
/* 在这里添加全局样式规则 */
/* 模板强调条样式 */
.template-accent-bar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(to right, #3b82f6, #8b5cf6);
    -webkit-print-color-adjust: exact; /* 强制 Chrome/Safari 打印背景和颜色 */
    print-color-adjust: exact; /* 标准属性，强制打印背景和颜色 */
}
/* 中文注释：让logo和footer图片自适应模板宽度 */ /* 注释：说明下方 CSS 规则的作用 */
.header-logo, /* 选择页眉 logo 元素 */
.footer-image { /* 选择页脚图片元素 */
    display: block; /* 设置为块级元素，使其独占一行 */
    max-width: 100%; /* 设置最大宽度为父容器的 100% */
    width: 100%; /* 设置宽度为父容器的 100% */
    height: auto; /* 设置高度自适应，保持图片比例 */
    margin: 0 auto; /* 设置上下外边距为 0，左右自动居中 */
    object-fit: contain; /* 保持图片纵横比，完整显示图片并适应容器 */
}
:root { /* 定义 CSS 变量，用于全局样式控制 */
    --primary-color: #1e40af; /* 定义主颜色变量 (深蓝色) */
    --secondary-color: #3b82f6; /* 定义次要颜色变量 (蓝色) */
    --accent-color: #f59e0b; /* 定义强调色变量 (琥珀色) */
    --light-color: #f3f4f6; /* 定义浅色变量 (浅灰色) */
    --dark-color: #1f2937; /* 定义深色变量 (深灰色) */
    --fixed-header-height: 160px; /* 页眉固定高度 */
    --fixed-footer-height: 38px; /* 页脚固定高度(10mm转换为像素) */
}

body { /* 定义 body 元素的样式 */
    font-family: 'Roboto', 'Noto Sans SC', sans-serif; /* 设置字体栈，优先使用 Roboto，中文使用 Noto Sans SC，最后使用无衬线字体 */
    background-color: #f9fafb; /* 设置页面背景颜色为非常浅的灰色 */
    /* 使用跨浏览器滚动条样式 */ /* 注释：说明下方是处理滚动条样式的代码 */
    -ms-overflow-style: none; /* 隐藏 IE 和 Edge 浏览器的滚动条 */
    overflow: -moz-scrollbars-auto; /* 为 Firefox 设置滚动条样式 (可能已过时，但作为兼容性保留) */
}

.template-option { /* 定义模板选项元素的样式 */
    transition: all 0.3s ease; /* 设置所有 CSS 属性变化的过渡效果，持续 0.3 秒，缓动函数为 ease */
}

.template-option:hover { /* 定义模板选项元素鼠标悬停时的样式 */
    transform: translateY(-2px); /* 向上移动 2 像素，产生轻微上浮效果 */
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); /* 添加悬停时的阴影效果，增强立体感 */
}

/* Document Template Styles */ /* 注释：说明下方是不同文档模板的样式 */
.template-classic { /* 定义经典模板的样式 */
    font-family: 'Times New Roman', 'SimSun', serif; /* 设置字体栈，优先 Times New Roman，中文使用宋体，最后使用衬线字体 */
    color: #333; /* 设置文字颜色为深灰色 */
}

.template-classic .header { /* 定义经典模板内 header 元素的样式 */
    border-bottom: 1px solid #ddd; /* 设置 1 像素宽的灰色实线下边框 */
}

.template-classic .table th { /* 定义经典模板内表格表头 (th) 元素的样式 */
    background-color: #f5f5f5; /* 设置背景颜色为浅灰色 */
}

.template-classic .table td, /* 定义经典模板内表格单元格 (td) 元素的样式 */
.template-classic .table th { /* 同时应用于经典模板内表格表头 (th) 元素的样式 */
    border: 1px solid #ddd; /* 设置 1 像素宽的灰色实线边框 */
}

.template-modern { /* 定义现代模板的样式 */
    font-family: 'Roboto', 'Noto Sans SC', sans-serif; /* 设置字体栈，同 body */
    color: #2d3748; /* 设置文字颜色为较深的灰色 */
}

.template-modern .header { /* 定义现代模板内 header 元素的样式 */
    border-bottom: 2px solid var(--secondary-color); /* 设置 2 像素宽的次要颜色实线下边框 */
}

.template-modern .table th { /* 定义现代模板内表格表头 (th) 元素的样式 */
    background-color: var(--secondary-color); /* 设置背景颜色为次要颜色 */
    color: white; /* 设置文字颜色为白色 */
}

.template-modern .table td, /* 定义现代模板内表格单元格 (td) 元素的样式 */
.template-modern .table th { /* 同时应用于现代模板内表格表头 (th) 元素的样式 */
    border: none; /* 移除边框 */
    border-bottom: 1px solid #e2e8f0; /* 设置 1 像素宽的浅灰色实线下边框 */
}

.template-elegant { /* 定义优雅模板的样式 */
    font-family: 'Georgia', 'STZhongsong', serif; /* 设置字体栈，优先 Georgia，中文使用华文中宋，最后使用衬线字体 */
    color: #1a202c; /* 设置文字颜色为非常深的灰色 */
}

.template-elegant .header { /* 定义优雅模板内 header 元素的样式 */
    border-bottom: double 3px #805ad5; /* 设置 3 像素宽的紫色双实线下边框 */
}

.template-elegant .table th { /* 定义优雅模板内表格表头 (th) 元素的样式 */
    background-color: #faf5ff; /* 设置背景颜色为非常浅的紫色 */
    color: #6b46c1; /* 设置文字颜色为紫色 */
}

.template-elegant .table td, /* 定义优雅模板内表格单元格 (td) 元素的样式 */
.template-elegant .table th { /* 同时应用于优雅模板内表格表头 (th) 元素的样式 */
    border: 1px solid #e9d8fd; /* 设置 1 像素宽的浅紫色实线边框 */
}

.template-tourism { /* 定义旅游模板的样式 */
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; /* 设置字体栈，优先 Segoe UI，中文使用微软雅黑，最后使用无衬线字体 */
    color: #2c5282; /* 设置文字颜色为深蓝色 */
}

.template-tourism .header { /* 定义旅游模板内 header 元素的样式 */
    background: linear-gradient(to right, #90cdf4, #4299e1); /* 设置从浅蓝到深蓝的水平线性渐变背景 */
    color: white; /* 设置文字颜色为白色 */
}

.template-tourism .table th { /* 定义旅游模板内表格表头 (th) 元素的样式 */
    background-color: #4299e1; /* 设置背景颜色为深蓝色 */
    color: white; /* 设置文字颜色为白色 */
}

.template-tourism .table td, /* 定义旅游模板内表格单元格 (td) 元素的样式 */
.template-tourism .table th { /* 同时应用于旅游模板内表格表头 (th) 元素的样式 */
    border: 1px solid #bee3f8; /* 设置 1 像素宽的浅蓝色实线边框 */
}

#document-preview { /* 定义文档预览区域的样式 */
    transition: background-color 0.3s, transform 0.3s; /* 设置背景颜色和变换的过渡效果，持续 0.3 秒 */
    width: 100%; /* 设置宽度为父容器的 100% */
    max-width: 595px; /* 限制最大宽度为 595px (A4 宽度的像素值，210mm 约等于 595px) */
    min-height: 842px; /* 设置最小高度为 A4 高度 (297mm 约等于 842px) */
    height: auto; /* 设置高度自适应，允许内容超过 A4 高度 */
    margin: 0 auto 30px; /* 设置上下外边距，底部增加间距 */
    padding: 0; /* 保留 padding:0; 由 #document-container 控制具体内容的边距 */
    box-shadow: 0 4px 15px -1px rgba(0, 0, 0, 0.1), 0 6px 8px -1px rgba(0, 0, 0, 0.05); /* 增强盒子阴影效果 */
    transform: scale(0.9); /* 缩小整体比例为 90% */
    transform-origin: top center; /* 设置缩放的原点为顶部中心 */
    background-color: white; /* 确保背景为白色 */
    /* 添加A4纸张比例 */
    aspect-ratio: 1 / 1.414; /* A4纸张的宽高比为1:1.414 */
    /* 添加打印标记 */
    position: relative; /* 设置相对定位，用于打印标记 */
    overflow: visible; /* 允许页眉页脚正常显示 */
}

/* 模板图片样式已移至外部CSS文件 template-image.css */ /* 注释：说明模板图片样式在外部文件中定义 */

/* Form Animation */ /* 注释：说明下方是表单动画相关的样式 */
.form-control:focus { /* 定义表单控件获得焦点时的样式 */
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3); /* 添加蓝色外发光效果 (模拟 focus 轮廓) */
}

/* 文档容器样式 */
#document-container {
    /* padding: 0; */ /* 将由子元素或这里的 padding-top/bottom 控制 */
    padding-top: var(--fixed-header-height); /* 为固定页眉留出空间 */
    padding-bottom: var(--fixed-footer-height); /* 为页脚留出空间 */
    min-height: 100%; /* 确保至少填满整个可视区域 */
    height: auto; /* 自动高度，根据内容自适应 */
    display: flex; /* 使用弹性布局 */
    flex-direction: column; /* 垂直方向排列 */
    font-size: 11pt; /* 设置标准文档字体大小 */
    line-height: 1.5; /* 设置行高 */
    color: #333333; /* 设置文本颜色 */
    position: relative; /* 为绝对定位的子元素提供参考点 */
    box-sizing: border-box; /* 确保 padding 不会增加总宽度/高度 */
    overflow: visible; /* 确保页眉页脚可以正常显示 */
}

/* 页眉容器样式 */
.document-header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: var(--fixed-header-height);
    z-index: 10;
    background: white;
}

/* 固定页眉样式 - 与header-footer-fixed.css保持一致 */
.document-header-image-container {
    position: absolute; /* 改为绝对定位，相对于document-container */
    top: 0;
    left: 0;
    right: 0;
    height: var(--fixed-header-height);
    background-color: white; /* 或者与 #document-preview 背景色一致 */
    z-index: 100; /* 与其他页眉页脚保持一致的z-index */
    display: flex;
    align-items: center; /* 垂直居中图片 */
    justify-content: center; /* 水平居中图片 */
    padding: 5px; /* 轻微内边距，防止图片紧贴边缘 */
    box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* 轻微底部阴影，增加层次感 */
    box-sizing: border-box;
}

.document-header-image-container img {
    max-height: calc(var(--fixed-header-height) - 10px); /* 图片最大高度为容器高度减去上下padding */
    max-width: 100%;
    object-fit: contain;
}


/* 页脚容器样式 */
.document-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: var(--fixed-footer-height);
    z-index: 10;
    background: white;
}

/* 统一页脚样式 - 合并文档页脚和公司页脚图片 */
.unified-document-footer.company-footer-image-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: auto; /* 改为自动高度，适应内容 */
    min-height: var(--fixed-footer-height);
    background-color: white;
    z-index: 1000;
    display: flex;
    flex-direction: column; /* 垂直排列：图片在上，文档内容在下 */
    align-items: center; /* 水平居中 */
    justify-content: flex-end; /* 底部对齐 */
    padding: 5px 5px 2px 5px;
    box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
    box-sizing: border-box;
    margin-bottom: 0;
}

/* 统一页脚中的图片样式 */
.unified-document-footer.company-footer-image-container img {
    max-height: calc(var(--fixed-footer-height) - 20px); /* 为文档内容预留空间 */
    max-width: 100%;
    object-fit: contain;
    margin-bottom: 2px; /* 与文档内容的间距 */
}

/* 统一页脚中的文档内容样式 */
.unified-document-footer .document-footer-content {
    width: 100%;
    text-align: center;
    font-size: 8pt;
    color: #666;
    line-height: 1.2;
    padding: 2px 5px;
    background: rgba(248, 249, 250, 0.8); /* 轻微背景，提高可读性 */
    border-top: 1px solid #e9ecef;
}


/* 确保 #document-preview 内的 .document-header-image-container 和 .company-footer-image-container 也应用这些固定样式 */
/* 这是为了处理当这些元素被嵌套在滚动容器内的情况，虽然理想情况它们应该在 #document-preview 的直接子级 */
#document-preview .document-header-image-container {
    position: fixed;
    /* top: calc(90% * var(--fixed-header-height) * 0); */ /* 这里的计算需要根据 #document-preview 的 transform: scale(0.9) 进行调整 */
                                                /* 并且考虑到 #document-preview 的 margin: 0 auto */
                                                /* 这部分比较复杂，需要精确计算或JS辅助，暂时先用一个简化的方式 */
    /* left 和 right 需要考虑 #document-preview 的居中和缩放 */
    /* width: calc(595px * 0.9); */ /* A4 width * scale */ 
    /* left: 50%; */
    /* transform: translateX(-50%); */
    /* ... 其他固定页眉的属性 ... */
    /* 鉴于 fixed 定位会脱离 transform 的影响，此处的特定规则可能不需要或需要调整 */ 
    /* 如果 .document-header-image-container 直接是 body 的子元素或 #document-preview 的兄弟元素，则上述全局 .document-header-image-container 规则即可 */ 
}

#document-preview .company-footer-image-container {
    position: fixed;
    /* bottom: 0; */ /* 页脚相对于视口底部 */
     /* left 和 right 需要考虑 #document-preview 的居中和缩放 */
    /* width: calc(595px * 0.9); */ /* A4 width * scale */
    /* left: 50%; */
    /* transform: translateX(-50%); */
    /* ... 其他固定页脚的属性 ... */
    /* 同上，此处的特定规则可能不需要 */
}

/* 文档各部分内边距统一设置 */
/* 这些内边距现在由 #document-container 的 padding-top/bottom 以及各部分的 margin 控制 */
.document-header, /* 这个类可能需要调整，因为它现在是固定页眉的容器 */
.document-title-section,
.client-section,
.items-section,
.payment-notes-section,
.document-footer { /* 这个类可能需要调整，因为它现在是固定页脚的容器 */
    padding: 15px 25px; /* 统一设置内边距，左右更大以增加可读性 */
    /* margin-left: auto; */ /* 如果 #document-container 有固定宽度，这些可以居中 */
    /* margin-right: auto; */
    /* max-width: 100%; */ /* 确保内容不超过 #document-container 的可用宽度 */
}

/* 司机协议文档头部区域样式 */
/* 由于 .document-header-image-container 现在是固定的，这里的 .document-header 可能指代其内部结构或不再直接相关 */

/* 文档标题样式优化 */
.document-title-section h1 {
    font-size: 18pt; /* 增大标题字体 */
    margin-bottom: 10px; /* 调整下边距 */
    color: #1e3a8a; /* 深蓝色标题 */
}

/* 司机协议文档标题区域样式 */
.template-driver-agreement .document-title-section {
    padding-top: 0 !important; /* 移除顶部内边距 */
    padding-bottom: 0 !important; /* 移除底部内边距 */
    margin-bottom: 0 !important; /* 移除底部外边距 */
}

/* 买家信息区域字体缩小 */
.customer-info, .customer-info p, .customer-info span, .customer-info div {
    font-size: 10pt !important; /* 减小字体大小 */
}
.customer-info h3 {
    font-size: 11pt !important; /* 标题字体也相应调整 */
    margin-bottom: 4px !important;
}

/* 服务项目等内容字体缩小 */
.items-section table, .items-section th, .items-section td,
.description-cell, .quantity-cell, .unit-price-cell, .amount-cell,
.total-section table, .total-section th, .total-section td {
    font-size: 10pt !important; /* 减小字体大小 */
}
.items-section th {
    padding: 6px 4px !important;
}
.items-section td {
    padding: 4px !important;
}

/* 备注信息字体缩小 */
.notes-section p, #notes-preview {
    font-size: 9pt !important;
}

/* 付款方式字体缩小 */
.payment-method p, #payment-method-preview {
     font-size: 9pt !important; /* 从10pt减小到9pt */
}
.payment-method h3 {
    font-size: 10pt !important; /* 从11pt减小到10pt */
     margin-bottom: 2px !important; /* 从4px减小到2px */
}


/* 打印样式 */
@media print {
    body {
        background-color: white; /* 打印时背景强制为白色 */
        -webkit-print-color-adjust: exact; /* 强制打印背景和颜色 */
        print-color-adjust: exact;
    }

    #document-preview {
        transform: scale(1); /* 打印时恢复原始大小 */
        margin: 0;
        padding: 0;
        box-shadow: none;
        border: none;
        width: 100%;
        height: 100%;
        max-width: none;
        min-height: unset; /* 移除最小高度限制 */
        aspect-ratio: unset; /* 移除A4比例，让内容自然流动或分页 */
        overflow: visible; /* 打印时显示所有内容 */
    }

    #document-container {
        padding-top: var(--fixed-header-height); /* 确保打印时内容区域有足够空间 */
        padding-bottom: 0; /* 移除底部填充，与预览保持一致 */
        box-shadow: none;
        border: none;
        height: auto; /* 自动高度 */
        min-height: unset;
        display: flex; /* 使用弹性布局 */
        flex-direction: column; /* 垂直方向排列 */
        page-break-inside: avoid; /* 尝试避免容器内部分页 */
    }

    .document-header-image-container,
    .company-footer-image-container {
        position: fixed !important; /* 打印时也固定 */
        left: 0 !important;
        right: 0 !important;
        width: 100% !important; /* 打印时铺满页面宽度 */
        transform: none !important; /* 移除运行时的transform */
        box-shadow: none !important; /* 移除阴影 */
        background-color: white !important; /* 确保背景是白色 */
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    .document-header-image-container {
        top: 0 !important;
        padding: 5px !important; /* 保持内边距 */
        height: var(--fixed-header-height) !important; /* 确保高度固定 */
        display: flex !important; /* 使用弹性布局 */
        align-items: center !important; /* 垂直居中 */
        justify-content: center !important; /* 水平居中 */
        page-break-after: avoid !important; /* 避免页眉后分页 */
        page-break-inside: avoid !important; /* 避免页眉内部分页 */
        margin: 0 !important; /* 移除外边距 */
    }

    .company-footer-image-container {
        bottom: 0 !important;
        margin-bottom: 0 !important;
        padding-bottom: 0 !important;
        page-break-after: avoid !important; /* 避免页脚后分页 */
        page-break-inside: avoid !important; /* 避免页脚内部分页 */
    }

    /* 打印时的页眉图片样式 */
    .document-header-image-container img {
        max-height: 100% !important; /* 在容器内自适应 */
        max-width: 100% !important;
        width: auto !important; /* 自动宽度，保持纵横比 */
        height: auto !important; /* 自动高度，保持纵横比 */
        object-fit: contain !important; /* 保持纵横比例，完整显示图片 */
        display: block !important; /* 块级元素 */
        margin: 0 auto !important; /* 水平居中 */
        padding: 0 !important; /* 移除内边距 */
    }
    
    .company-footer-image-container img {
        max-height: 100%; /* 在容器内自适应 */
        max-width: 100%;
        padding: 5px 5px 0 5px; /* 顶部和两侧保持边距，底部无边距 */
        margin-bottom: 0; /* 确保没有底部边距 */
    }
    
    /* 打印时的页脚容器样式 */
    .company-footer {
        margin-top: auto !important; /* 将页脚推到容器底部 */
        height: var(--fixed-footer-height) !important; /* 使用固定高度 */
        width: 100% !important; /* 占据全宽 */
        display: flex !important; /* 使用弹性布局 */
        align-items: flex-end !important; /* 内容对齐到底部 */
        justify-content: center !important; /* 水平居中 */
        padding: 0 !important; /* 移除内边距 */
        border-top: 1px solid #e9ecef !important; /* 保持顶部边框 */
        box-sizing: border-box !important; /* 确保内边距不增加总大小 */
        page-break-inside: avoid !important; /* 避免页脚内部分页 */
        page-break-after: avoid !important; /* 避免页脚后分页 */
    }
    
    /* 打印时的页脚图片样式 */
    .template-footer-image {
        max-height: var(--fixed-footer-height) !important; /* 图片最大高度 */
        max-width: 100% !important; /* 最大宽度 */
        width: 100% !important; /* 占据全宽 */
        object-fit: contain !important; /* 保持纵横比例，完整显示图片 */
        display: block !important; /* 块级元素 */
        margin: 0 !important; /* 移除外边距 */
    }

    /* 隐藏非打印元素 */
    header, /* 页面主导航栏 */
    main > .flex > .w-full.lg\:w-1\/4, /* 左侧配置栏 */
    main > .flex > .w-full.lg\:w-1\/3, /* 中间输入栏 */
    .preview-header, /* 预览控制按钮等 */
    .fab, /* 浮动操作按钮 */
    .template-accent-bar, /* 模板顶部彩色条在打印时可以移除，除非特别需要 */
    .fixed.inset-0.bg-blue-500.h-1\.5 /* 顶部固定蓝色进度条 */ {
        display: none !important;
    }

    .w-full.lg\:w-1\/2 { /* 右侧预览列在打印时占满 */
        width: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    /* 确保内容区域正确分页 */
    .items-section table {
        page-break-inside: auto; /* 表格内容可以分页 */
    }
    .items-section tr {
        page-break-inside: avoid; /* 尽量避免行内分页 */
        page-break-after: auto;
    }

    /* 确保各主要内容块尽可能完整显示在一页，但允许在块之间分页 */
    .document-title-section,
    .client-section,
    .items-section,
    .total-section,
    .payment-notes-section,
    .signature-section, /* 如果有的话 */
    .quotation-terms-section {
        page-break-before: auto;
        page-break-after: auto;
        page-break-inside: avoid; /* 尝试避免在这些块内部分页 */
    }
}

/* 文档编号和日期样式 */
.document-number-container {
    font-size: 10pt; /* 设置字体大小 */
    color: #4b5563; /* 设置颜色 */
    margin-top: 5px; /* 添加上边距 */
    display: flex; /* 使用弹性布局 */
    justify-content: center; /* 水平居中 */
    flex-wrap: wrap; /* 允许换行 */
}

.document-number-container p {
    margin: 0 10px; /* 设置外边距 */
}

/* 客户和买方信息区域样式 */
.client-section {
    background-color: #f9fafb; /* 浅灰色背景 */
    border-top: 1px solid #e5e7eb; /* 添加顶部边框 */
    border-bottom: 1px solid #e5e7eb; /* 添加底部边框 */
}

.client-section h3 {
    font-size: 10pt; /* 减小标题字体 */
    text-transform: uppercase; /* 大写字母 */
    letter-spacing: 0.05em; /* 增加字母间距 */
    color: #6b7280; /* 灰色 */
}

.client-section .p-4 {
    padding: 12px; /* 调整内边距 */
    background-color: white; /* 白色背景 */
    border: 1px solid #e5e7eb; /* 添加边框 */
    border-radius: 4px; /* 圆角 */
    font-size: 10pt; /* 设置字体大小 */
}

/* 表格样式优化 */
.items-section table {
    border-collapse: collapse; /* 确保边框合并 */
    width: 100%; /* 确保表格宽度100% */
    font-size: 10pt; /* 设置字体大小 */
}

.items-section th {
    padding: 8px 12px; /* 调整单元格内边距 */
    border: 1px solid #d1d5db; /* 添加边框 */
    background-color: #f3f4f6; /* 浅灰色背景 */
    font-weight: 600; /* 设置字体粗细 */
    text-align: left; /* 左对齐 */
}

/* 金额列宽度 */
.items-section th.amount-column {
    width: 30%; /* 设置金额列宽度为30% */
    text-align: right; /* 右对齐 */
}

.items-section td {
    padding: 8px 12px; /* 调整单元格内边距 */
    border: 1px solid #e5e7eb; /* 添加边框 */
    vertical-align: top; /* 顶部对齐 */
}

/* 金额单元格样式 */
.items-section td.amount-cell {
    text-align: right; /* 右对齐 */
}

/* 表格底部总计行样式 */
.items-section tfoot td {
    font-weight: 600; /* 设置字体粗细 */
    background-color: #f9fafb; /* 浅灰色背景 */
}

/* 页脚感谢文本样式 */
.receipt-footer-text {
    margin-top: 10px !important; /* 增加顶部外边距，与公司信息分隔 */
    font-weight: 500; /* 稍微加粗 */
    color: #4b5563; /* 深灰色 */
}

/* 打印样式 - 用于导出PDF和图片 */
@media print {
    body {
        margin: 0;
        padding: 0;
        background: white;
    }

    #document-preview {
        width: 210mm; /* A4宽度 */
        height: 297mm; /* A4高度 */
        margin: 0;
        padding: 0;
        box-shadow: none;
        transform: none;
        overflow: visible;
        page-break-after: always;
    }

    /* 确保内容不会被截断 */
    .document-header,
    .document-title-section,
    .client-section,
    .items-section,
    .payment-notes-section,
    .document-footer {
        page-break-inside: avoid;
    }

    /* 隐藏不需要打印的元素 */
    .no-print {
        display: none !important;
    }

    /* 调整表格在打印时的样式 */
    .items-section table {
        page-break-inside: auto;
    }

    .items-section tr {
        page-break-inside: avoid;
        page-break-after: auto;
    }

    /* 确保图片在打印时正确显示 */
    img {
        max-width: 100%;
        page-break-inside: avoid;
    }
}

/* Mobile Optimizations */ /* 注释：说明下方是针对移动设备的优化样式 */
@media (max-width: 768px) { /* 定义屏幕宽度小于等于 768px 时的媒体查询规则 */
    #document-preview { /* 选择文档预览区域 */
        width: 100%; /* 在小屏幕上设置宽度为 100% */
        min-height: 500px; /* 设置最小高度 */
        transform: scale(1); /* 移动设备不缩放 */
        box-shadow: none; /* 移除阴影 */
    }

    /* 文档各部分内边距调整 */
    .document-header,
    .document-title-section,
    .client-section,
    .items-section,
    .payment-notes-section,
    .document-footer {
        padding: 10px 15px; /* 减小内边距 */
    }

    .company-logo {
        max-height: 60px;
    }

    .stamp-image {
        max-width: 70px;
        max-height: 70px;
    }
}

/* History Item Style */
.history-item {
    transition: all 0.2s ease;
}

.history-item:hover {
    background-color: #f3f4f6;
}

/* Tooltip Styles */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-thumb {
    background-color: #cbd5e0;
    border-radius: 3px;
}

::-webkit-scrollbar-track {
    background-color: #f1f1f1;
}

/* Collapsible panel styles */
.collapsible-panel {
    overflow: hidden;
    max-height: 0;
    transition: max-height 0.3s ease-out;
}

.collapsible-panel.active {
    max-height: 2000px;
    transition: max-height 0.5s ease-in;
}

/* Floating Action Button for Mobile */
.fab {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2), 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12);
    z-index: 1000;
}
/* #endregion 从 index.html 提取的样式 */

/* 新的公司页脚样式 */
.company-footer-image-container {
    width: 100%;       /* 容器宽度为100% */
    padding: 10px 0;   /* 上下内边距10px，左右为0 */
    margin-top: auto;  /* 尝试将页脚推到底部（如果父容器是flex布局） */
    text-align: center;/* 图片居中 */
    background-color: #f8f9fa; /* 添加一个浅灰色背景以便调试时看到容器 */
    border-top: 1px solid #e9ecef; /* 顶部边框 */
    box-sizing: border-box; /* 边框和内边距包含在宽度内 */
    position: relative; /* 为绝对定位的子元素提供参考点 */
    z-index: 1000; /* 确保容器在较高层级 */
}

.company-footer-image-style {
    width: 100%;        /* 图片宽度填充容器 */
    max-width: 100%;    /* 最大宽度不超过容器 */
    height: auto;       /* 高度自动，保持宽高比 */
    object-fit: contain; /* 图片内容保持比例缩放，完整显示 */
    display: block;     /* 块级元素 */
    margin-left: 0;     /* 移除左外边距 */
    margin-right: 0;    /* 移除右外边距 */
    position: relative; /* 相对定位，使 z-index 生效 */
    z-index: 1001;      /* 确保图片在容器之上 */
}

/* Styles moved from index.html */
/* 隐藏空值字段 */
.hidden-field {
    display: none !important;
}

/* 隐藏面板 */
.hidden-panel {
    display: none;
}

/* 表单验证样式 */
.border-red-500 {
    border-color: #f56565 !important;
}

.validation-error {
    color: #f56565;
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

/* 司机协议模板样式 */
.template-driver-agreement {
    font-size: 14px;
    line-height: 1.5;
}

.template-driver-agreement h1 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.template-driver-agreement h2 {
    font-size: 1.2rem;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
}

.template-driver-agreement p {
    margin-bottom: 0.5rem;
}

/* 司机协议模板中隐藏日期 */
.template-driver-agreement .document-date {
    display: none;
}

/* 司机协议模板中隐藏"感谢您的惠顾"文本 */
.template-driver-agreement .receipt-footer-text {
    display: none;
}

/* 司机协议模板中隐藏签名和日期区域 */
.template-driver-agreement .signature-text,
.template-driver-agreement .signature-date {
    display: none;
}
/* 页眉占位符样式 */
.header-image-placeholder {
    height: var(--fixed-header-height);
    visibility: hidden;
}

/* 页脚占位符样式 */
.footer-image-placeholder {
    height: var(--fixed-footer-height);
    visibility: hidden;
}

/* 页脚容器样式 - 贴合底部 */
.company-footer {
    margin-top: auto; /* 将页脚推到容器底部 */
    height: var(--fixed-footer-height); /* 使用固定高度 */
    width: 100%; /* 占据全宽 */
    display: flex; /* 使用弹性布局 */
    align-items: flex-end; /* 内容对齐到底部 */
    justify-content: center; /* 水平居中 */
    box-sizing: border-box; /* 确保内边距不增加总大小 */
    padding: 0; /* 移除内边距 */
}

/* 页脚图片样式 */
.template-footer-image {
    max-height: var(--fixed-footer-height); /* 图片最大高度 */
    max-width: 100%; /* 最大宽度 */
    width: 100%; /* 占据全宽 */
    object-fit: contain; /* 保持纵横比例，完整显示图片 */
    display: block; /* 块级元素 */
    margin: 0; /* 移除外边距 */
}

/* End of styles moved from index.html */

/* @region 司机协议A4排版关键样式 */

/* 抬头图片样式 */
#company-header-image {
  width: 100%;
  max-height: 60px;
  display: block;
  margin: 0 auto 10px auto;
  object-fit: contain;
}

/* 页脚图片样式 */
#company-footer-image {
  width: 100%;
  max-height: 60px;
  display: block;
  margin: 10px auto 0 auto;
  object-fit: contain;
}
.company-footer-image-container {
  width: 100%;
  text-align: center;
  position: relative;
  z-index: 1000;
  margin: 0;
  padding: 0;
}

/* 电子生成提示样式 */
.electronic-generated-notice {
  width: 100%;
  margin: 15px 0 15px 0;
  padding: 5px 0;
  font-size: 0.6rem;
  color: #666;
  text-align: center;
  border-top: 1px solid #ddd;
  background: #fff;
}

/* @endregion */

/* 服务项目表格列控制 */
.quantity-column, .unit-price-column {
    transition: width 0.3s ease, opacity 0.3s ease;
}

.quantity-column.hidden, .unit-price-column.hidden {
    width: 0;
    padding: 0;
    border: none;
    opacity: 0;
}

.quantity-cell, .unit-price-cell {
    transition: width 0.3s ease, padding 0.3s ease, opacity 0.3s ease;
}

.quantity-cell.hidden, .unit-price-cell.hidden {
    width: 0;
    padding: 0;
    border: none;
    opacity: 0;
}

/* 文档类型特定元素控制 */
.receipt-only-element {
    transition: opacity 0.3s ease, height 0.3s ease;
}

.receipt-only-element.hidden {
    opacity: 0;
    height: 0;
    overflow: hidden;
    margin: 0;
    padding: 0;
}

/* 确保表格布局在切换时保持稳定 */
.items-section table {
    table-layout: fixed;
    width: 100%;
}

.items-section th {
    overflow: hidden;
}

.items-section th:first-child {
    width: 50%;
}

.quantity-column {
    width: 15%;
}

.unit-price-column {
    width: 15%;
}

.amount-column {
    width: 20%;
}

/* 结语文本专用样式，确保正确处理换行 */
.conclusion-text {
    white-space: pre-wrap !important; /* 保留空白符和换行符，允许文本自动换行 */
    word-break: break-word; /* 允许在单词内部换行 */
    word-wrap: break-word; /* 兼容旧浏览器 */
    text-align: center;
    width: 100%;
    display: block;
    line-height: 0.2; /* 从1.6减小到1.4，减少行间距 */
    margin: 4px auto; /* 从15px减小到8px，减少上下外边距 */
    padding: 0 1px; /* 从15px减小到10px，减少左右内边距 */
    font-size: 6pt !important; /* 添加更小的字体大小 */
}

/* 调整结语区域容器样式 */
.conclusion-section {
    margin-bottom: 1px !important; /* 从6px减小到4px */
    padding-top: 0 !important; /* 减少顶部内边距 */
}

/* 调整结语区域中的行间距 */
.conclusion-section .flex-col {
    gap: 0 !important; /* 从2减小到0，减少行间距 */
}

/* 调整付款方式区域样式 */
.payment-method-section {
    margin-bottom: 1px !important; /* 减少底部外边距 */
}

/* 调整备注区域样式，与付款方式区域保持一致 */
.notes-section h4 {
    font-size: 10pt !important; /* 与付款方式标题大小一致 */
    margin-bottom: 2px !important; /* 与付款方式标题外边距一致 */
}

/* 详细信息区域减小内边距 */
.details-section {
    padding: 15px 25px !important; /* 从p-6(24px)减小到4px顶部底部，6px左右 */
}

/* 报价信息区域紧凑样式 */
.quotation-additional-info {
    padding: 2px 25px !important;
    margin-top: 1px !important;
    border-top: 1px solid #e5e7eb;
}

.quotation-additional-info h3 {
    font-size: 0.7rem !important;
    font-weight: 600;
    text-transform: uppercase;
    color: #6b7280;
    margin-bottom: 1px !important;
    line-height: 1.2;
}

.quotation-additional-info table {
    width: 100%;
    margin-top: 1px !important;
}

.quotation-additional-info td {
    padding-top: 1px !important;
    padding-bottom: 1px !important;
    font-size: 0.7rem !important;
    line-height: 1.2 !important;
}

/* 确保打印时也保持紧凑样式 */
@media print {
    .quotation-additional-info {
        padding: 2px 6px !important;
        margin-top: 1px !important;
    }
    
    .quotation-additional-info h3 {
        font-size: 0.7rem !important;
        margin-bottom: 1px !important;
    }
    
    .quotation-additional-info td {
        padding-top: 1px !important;
        padding-bottom: 1px !important;
        font-size: 0.7rem !important;
        line-height: 1.2 !important;
    }
}