/**
 * @file 布局组件 - SmartOffice 布局系统
 * <AUTHOR> Team
 * @description 
 * 提供完整的布局功能，包括：
 * - Layout 基础布局组件
 * - Container 容器组件
 * - Grid 栅格系统
 * - Panel 面板组件
 * - 响应式布局支持
 * - 弹性布局和网格布局
 * - 完整的事件系统和生命周期管理
 */

import { BaseComponent } from './base-component.js';
import { UI_EVENTS } from '../../core/events/event-types.js';

// #region Layout基础布局组件
/**
 * @class Layout - 基础布局组件
 * @extends BaseComponent
 * @description 提供基础的页面布局结构和响应式支持
 */
export class Layout extends BaseComponent {
    /**
     * 构造函数 - 初始化布局组件
     * @param {Object} config - 布局配置
     * @param {string} config.direction - 布局方向
     * @param {string} config.align - 对齐方式
     * @param {string} config.justify - 内容分布
     */
    constructor(config = {}) {
        super(config);
        
        // 布局属性
        this.layoutProps = {
            direction: config.direction || 'vertical', // vertical, horizontal
            align: config.align || 'stretch', // start, center, end, stretch
            justify: config.justify || 'start', // start, center, end, space-between, space-around, space-evenly
            wrap: config.wrap !== false, // 是否换行
            gap: config.gap || '0px', // 间距
            padding: config.padding || '0px', // 内边距
            margin: config.margin || '0px', // 外边距
            minHeight: config.minHeight || 'auto', // 最小高度
            className: config.className || '',
            responsive: config.responsive || false, // 是否响应式
            breakpoints: config.breakpoints || {
                xs: 576,
                sm: 768,
                md: 992,
                lg: 1200,
                xl: 1600
            }
        };
        
        // 布局状态
        this.layoutState = {
            currentBreakpoint: null, // 当前断点
            collapsed: false, // 是否折叠
            regions: new Map() // 布局区域
        };
        
        // DOM元素引用
        this.elements = {
            layout: null,
            header: null,
            main: null,
            aside: null,
            footer: null
        };
        
        // 事件处理器
        this.handlers = {
            resize: this._handleResize.bind(this)
        };
        
        // 初始化组件
        this._initializeLayout();
    }

    /**
     * 初始化布局 - 创建DOM结构和设置事件
     * @private
     */
    _initializeLayout() {
        this._createLayoutStructure();
        this._applyLayoutStyles();
        this._bindLayoutEvents();
        this._updateResponsive();
        
        // 发射初始化完成事件
        this.emit(UI_EVENTS.COMPONENT_INITIALIZED, {
            component: this,
            type: 'layout'
        });
    }

    /**
     * 创建布局DOM结构 - 构建完整的布局HTML
     * @private
     */
    _createLayoutStructure() {
        // 创建布局容器
        this.elements.layout = document.createElement('div');
        this.elements.layout.className = `smartoffice-layout smartoffice-layout-${this.layoutProps.direction} ${this.layoutProps.className}`;
        
        this.container = this.elements.layout;
    }

    /**
     * 应用布局样式 - 设置CSS样式
     * @private
     */
    _applyLayoutStyles() {
        // 基础样式
        Object.assign(this.elements.layout.style, {
            display: 'flex',
            flexDirection: this.layoutProps.direction === 'vertical' ? 'column' : 'row',
            alignItems: this._mapAlignValue(this.layoutProps.align),
            justifyContent: this._mapJustifyValue(this.layoutProps.justify),
            flexWrap: this.layoutProps.wrap ? 'wrap' : 'nowrap',
            gap: this.layoutProps.gap,
            padding: this.layoutProps.padding,
            margin: this.layoutProps.margin,
            minHeight: this.layoutProps.minHeight,
            width: '100%',
            boxSizing: 'border-box'
        });
    }

    /**
     * 绑定布局事件 - 设置所有事件监听器
     * @private
     */
    _bindLayoutEvents() {
        if (this.layoutProps.responsive) {
            window.addEventListener('resize', this.handlers.resize);
        }
    }

    /**
     * 映射对齐值 - 将对齐配置映射为CSS值
     * @param {string} align - 对齐配置
     * @returns {string} CSS对齐值
     * @private
     */
    _mapAlignValue(align) {
        const alignMap = {
            start: 'flex-start',
            center: 'center',
            end: 'flex-end',
            stretch: 'stretch'
        };
        return alignMap[align] || align;
    }

    /**
     * 映射分布值 - 将分布配置映射为CSS值
     * @param {string} justify - 分布配置
     * @returns {string} CSS分布值
     * @private
     */
    _mapJustifyValue(justify) {
        const justifyMap = {
            start: 'flex-start',
            center: 'center',
            end: 'flex-end',
            'space-between': 'space-between',
            'space-around': 'space-around',
            'space-evenly': 'space-evenly'
        };
        return justifyMap[justify] || justify;
    }

    // #region 公开方法
    /**
     * 添加区域 - 添加布局区域
     * @param {string} name - 区域名称
     * @param {HTMLElement|BaseComponent} content - 区域内容
     * @param {Object} options - 区域选项
     */
    addRegion(name, content, options = {}) {
        const region = document.createElement('div');
        region.className = `layout-region layout-region-${name}`;
        region.dataset.region = name;
        
        // 应用区域样式
        if (options.flex) {
            region.style.flex = options.flex;
        }
        if (options.width) {
            region.style.width = options.width;
        }
        if (options.height) {
            region.style.height = options.height;
        }
        if (options.className) {
            region.classList.add(options.className);
        }
        
        // 添加内容
        if (content instanceof BaseComponent) {
            region.appendChild(content.container);
        } else if (content instanceof HTMLElement) {
            region.appendChild(content);
        } else {
            region.innerHTML = String(content);
        }
        
        // 保存区域引用
        this.layoutState.regions.set(name, region);
        this.elements[name] = region;
        
        // 添加到布局
        this.elements.layout.appendChild(region);
        
        this.emit(UI_EVENTS.COMPONENT_UPDATED, {
            component: this,
            type: 'addRegion',
            region: name,
            options: options
        });
    }

    /**
     * 移除区域 - 移除布局区域
     * @param {string} name - 区域名称
     */
    removeRegion(name) {
        const region = this.layoutState.regions.get(name);
        if (region) {
            region.remove();
            this.layoutState.regions.delete(name);
            delete this.elements[name];
            
            this.emit(UI_EVENTS.COMPONENT_UPDATED, {
                component: this,
                type: 'removeRegion',
                region: name
            });
        }
    }

    /**
     * 更新区域 - 更新区域内容
     * @param {string} name - 区域名称
     * @param {HTMLElement|BaseComponent} content - 新内容
     */
    updateRegion(name, content) {
        const region = this.layoutState.regions.get(name);
        if (region) {
            region.innerHTML = '';
            
            if (content instanceof BaseComponent) {
                region.appendChild(content.container);
            } else if (content instanceof HTMLElement) {
                region.appendChild(content);
            } else {
                region.innerHTML = String(content);
            }
            
            this.emit(UI_EVENTS.COMPONENT_UPDATED, {
                component: this,
                type: 'updateRegion',
                region: name
            });
        }
    }

    /**
     * 设置布局方向 - 改变布局方向
     * @param {string} direction - 新方向
     */
    setDirection(direction) {
        this.layoutProps.direction = direction;
        this.elements.layout.style.flexDirection = direction === 'vertical' ? 'column' : 'row';
        this.elements.layout.className = this.elements.layout.className.replace(
            /smartoffice-layout-(vertical|horizontal)/,
            `smartoffice-layout-${direction}`
        );
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            type: 'direction',
            direction: direction
        });
    }

    /**
     * 设置间距 - 改变布局间距
     * @param {string} gap - 新间距
     */
    setGap(gap) {
        this.layoutProps.gap = gap;
        this.elements.layout.style.gap = gap;
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            type: 'gap',
            gap: gap
        });
    }
    // #endregion

    // #region 事件处理方法
    /**
     * 处理窗口大小变化 - 响应式布局处理
     * @param {ResizeEvent} e - 大小变化事件
     * @private
     */
    _handleResize(e) {
        if (this.layoutProps.responsive) {
            this._updateResponsive();
        }
    }
    // #endregion

    // #region 辅助方法
    /**
     * 更新响应式 - 更新响应式断点状态
     * @private
     */
    _updateResponsive() {
        if (!this.layoutProps.responsive) return;
        
        const width = window.innerWidth;
        let currentBreakpoint = 'xs';
        
        for (const [bp, minWidth] of Object.entries(this.layoutProps.breakpoints)) {
            if (width >= minWidth) {
                currentBreakpoint = bp;
            }
        }
        
        if (currentBreakpoint !== this.layoutState.currentBreakpoint) {
            this.layoutState.currentBreakpoint = currentBreakpoint;
            this.elements.layout.setAttribute('data-breakpoint', currentBreakpoint);
            
            this.emit(UI_EVENTS.COMPONENT_CHANGED, {
                component: this,
                type: 'breakpoint',
                breakpoint: currentBreakpoint,
                width: width
            });
        }
    }
    // #endregion

    // #region 生命周期方法
    /**
     * 组件销毁 - 清理资源
     */
    destroy() {
        if (this.layoutProps.responsive) {
            window.removeEventListener('resize', this.handlers.resize);
        }
        
        super.destroy();
        
        this.emit(UI_EVENTS.COMPONENT_DESTROYED, { component: this });
    }
    // #endregion
}

/**
 * @class Container - 容器组件
 * @extends BaseComponent
 * @description 提供内容容器和约束布局
 */
export class Container extends BaseComponent {
    /**
     * 构造函数 - 初始化容器组件
     * @param {Object} config - 容器配置
     */
    constructor(config = {}) {
        super(config);
        
        this.containerProps = {
            fluid: config.fluid || false, // 是否流式布局
            maxWidth: config.maxWidth || null, // 最大宽度
            padding: config.padding || '20px', // 内边距
            centered: config.centered !== false, // 是否居中
            className: config.className || ''
        };
        
        this._initializeContainer();
    }

    /**
     * 初始化容器 - 创建DOM结构
     * @private
     */
    _initializeContainer() {
        this._createContainerStructure();
        this._applyContainerStyles();
        
        this.emit(UI_EVENTS.COMPONENT_INITIALIZED, {
            component: this,
            type: 'container'
        });
    }

    /**
     * 创建容器结构 - 构建容器HTML
     * @private
     */
    _createContainerStructure() {
        this.container = document.createElement('div');
        this.container.className = `smartoffice-container ${this.containerProps.fluid ? 'container-fluid' : 'container-fixed'} ${this.containerProps.className}`;
    }

    /**
     * 应用容器样式 - 设置容器样式
     * @private
     */
    _applyContainerStyles() {
        Object.assign(this.container.style, {
            width: this.containerProps.fluid ? '100%' : 'auto',
            maxWidth: this.containerProps.maxWidth || (this.containerProps.fluid ? 'none' : '1200px'),
            margin: this.containerProps.centered ? '0 auto' : '0',
            padding: this.containerProps.padding,
            boxSizing: 'border-box'
        });
    }

    /**
     * 添加内容 - 添加子内容
     * @param {HTMLElement|BaseComponent} content - 内容
     */
    addContent(content) {
        if (content instanceof BaseComponent) {
            this.container.appendChild(content.container);
        } else if (content instanceof HTMLElement) {
            this.container.appendChild(content);
        } else {
            const div = document.createElement('div');
            div.innerHTML = String(content);
            this.container.appendChild(div);
        }
        
        this.emit(UI_EVENTS.COMPONENT_UPDATED, {
            component: this,
            type: 'addContent'
        });
    }
}

/**
 * @class Grid - 栅格系统组件
 * @extends BaseComponent
 * @description 提供响应式栅格布局系统
 */
export class Grid extends BaseComponent {
    /**
     * 构造函数 - 初始化栅格组件
     * @param {Object} config - 栅格配置
     */
    constructor(config = {}) {
        super(config);
        
        this.gridProps = {
            columns: config.columns || 12, // 栅格列数
            gutter: config.gutter || '16px', // 栅格间距
            responsive: config.responsive !== false, // 响应式
            className: config.className || ''
        };
        
        this.gridState = {
            rows: [] // 栅格行
        };
        
        this._initializeGrid();
    }

    /**
     * 初始化栅格 - 创建DOM结构
     * @private
     */
    _initializeGrid() {
        this._createGridStructure();
        this._applyGridStyles();
        
        this.emit(UI_EVENTS.COMPONENT_INITIALIZED, {
            component: this,
            type: 'grid'
        });
    }

    /**
     * 创建栅格结构 - 构建栅格HTML
     * @private
     */
    _createGridStructure() {
        this.container = document.createElement('div');
        this.container.className = `smartoffice-grid ${this.gridProps.className}`;
    }

    /**
     * 应用栅格样式 - 设置栅格样式
     * @private
     */
    _applyGridStyles() {
        Object.assign(this.container.style, {
            display: 'flex',
            flexDirection: 'column',
            gap: this.gridProps.gutter,
            width: '100%'
        });
    }

    /**
     * 添加行 - 添加栅格行
     * @param {Array} columns - 列配置数组
     * @returns {HTMLElement} 行元素
     */
    addRow(columns = []) {
        const row = document.createElement('div');
        row.className = 'grid-row';
        
        Object.assign(row.style, {
            display: 'flex',
            flexWrap: 'wrap',
            gap: this.gridProps.gutter,
            width: '100%'
        });
        
        // 添加列
        columns.forEach(colConfig => {
            const col = this._createColumn(colConfig);
            row.appendChild(col);
        });
        
        this.container.appendChild(row);
        this.gridState.rows.push(row);
        
        this.emit(UI_EVENTS.COMPONENT_UPDATED, {
            component: this,
            type: 'addRow',
            columns: columns.length
        });
        
        return row;
    }

    /**
     * 创建列 - 创建栅格列
     * @param {Object} config - 列配置
     * @returns {HTMLElement} 列元素
     * @private
     */
    _createColumn(config) {
        const col = document.createElement('div');
        col.className = 'grid-col';
        
        // 计算列宽
        const span = config.span || 1;
        const width = `calc(${(span / this.gridProps.columns) * 100}% - ${this.gridProps.gutter})`;
        
        Object.assign(col.style, {
            flex: `0 0 ${width}`,
            minWidth: '0',
            boxSizing: 'border-box'
        });
        
        // 响应式配置
        if (this.gridProps.responsive && config.responsive) {
            this._applyResponsiveColumn(col, config.responsive);
        }
        
        // 添加内容
        if (config.content) {
            if (config.content instanceof BaseComponent) {
                col.appendChild(config.content.container);
            } else if (config.content instanceof HTMLElement) {
                col.appendChild(config.content);
            } else {
                col.innerHTML = String(config.content);
            }
        }
        
        return col;
    }

    /**
     * 应用响应式列 - 应用响应式列配置
     * @param {HTMLElement} col - 列元素
     * @param {Object} responsive - 响应式配置
     * @private
     */
    _applyResponsiveColumn(col, responsive) {
        // 添加响应式类名和数据属性
        Object.entries(responsive).forEach(([breakpoint, span]) => {
            col.setAttribute(`data-${breakpoint}`, span);
            col.classList.add(`col-${breakpoint}-${span}`);
        });
    }

    /**
     * 清除所有行 - 清空栅格内容
     */
    clearRows() {
        this.container.innerHTML = '';
        this.gridState.rows = [];
        
        this.emit(UI_EVENTS.COMPONENT_UPDATED, {
            component: this,
            type: 'clearRows'
        });
    }
}

/**
 * @class Panel - 面板组件
 * @extends BaseComponent
 * @description 提供内容面板和卡片布局
 */
export class Panel extends BaseComponent {
    /**
     * 构造函数 - 初始化面板组件
     * @param {Object} config - 面板配置
     */
    constructor(config = {}) {
        super(config);
        
        this.panelProps = {
            title: config.title || '',
            collapsible: config.collapsible || false, // 是否可折叠
            collapsed: config.collapsed || false, // 初始折叠状态
            bordered: config.bordered !== false, // 边框
            shadow: config.shadow !== false, // 阴影
            padding: config.padding || '16px', // 内边距
            headerActions: config.headerActions || [], // 头部操作按钮
            className: config.className || ''
        };
        
        this.panelState = {
            collapsed: this.panelProps.collapsed
        };
        
        this.elements = {
            panel: null,
            header: null,
            title: null,
            actions: null,
            body: null,
            collapseButton: null
        };
        
        this._initializePanel();
    }

    /**
     * 初始化面板 - 创建DOM结构
     * @private
     */
    _initializePanel() {
        this._createPanelStructure();
        this._applyPanelStyles();
        this._bindPanelEvents();
        
        this.emit(UI_EVENTS.COMPONENT_INITIALIZED, {
            component: this,
            type: 'panel'
        });
    }

    /**
     * 创建面板结构 - 构建面板HTML
     * @private
     */
    _createPanelStructure() {
        // 面板容器
        this.elements.panel = document.createElement('div');
        this.elements.panel.className = `smartoffice-panel ${this.panelProps.className}`;
        
        // 面板头部
        if (this.panelProps.title || this.panelProps.collapsible || this.panelProps.headerActions.length > 0) {
            this._createPanelHeader();
        }
        
        // 面板主体
        this._createPanelBody();
        
        this.container = this.elements.panel;
    }

    /**
     * 创建面板头部 - 创建头部区域
     * @private
     */
    _createPanelHeader() {
        this.elements.header = document.createElement('div');
        this.elements.header.className = 'panel-header';
        
        // 标题区域
        if (this.panelProps.title) {
            this.elements.title = document.createElement('h3');
            this.elements.title.className = 'panel-title';
            this.elements.title.textContent = this.panelProps.title;
            this.elements.header.appendChild(this.elements.title);
        }
        
        // 操作区域
        this.elements.actions = document.createElement('div');
        this.elements.actions.className = 'panel-actions';
        
        // 折叠按钮
        if (this.panelProps.collapsible) {
            this.elements.collapseButton = document.createElement('button');
            this.elements.collapseButton.className = 'panel-collapse-button';
            this.elements.collapseButton.innerHTML = this.panelState.collapsed ? '▶' : '▼';
            this.elements.collapseButton.setAttribute('aria-label', '折叠/展开面板');
            this.elements.actions.appendChild(this.elements.collapseButton);
        }
        
        // 自定义操作按钮
        this.panelProps.headerActions.forEach(action => {
            const button = document.createElement('button');
            button.className = 'panel-action-button';
            button.textContent = action.text || '';
            button.innerHTML = action.icon || button.textContent;
            if (action.onClick) {
                button.addEventListener('click', action.onClick);
            }
            this.elements.actions.appendChild(button);
        });
        
        this.elements.header.appendChild(this.elements.actions);
        this.elements.panel.appendChild(this.elements.header);
    }

    /**
     * 创建面板主体 - 创建内容区域
     * @private
     */
    _createPanelBody() {
        this.elements.body = document.createElement('div');
        this.elements.body.className = 'panel-body';
        
        if (this.panelState.collapsed) {
            this.elements.body.style.display = 'none';
        }
        
        this.elements.panel.appendChild(this.elements.body);
    }

    /**
     * 应用面板样式 - 设置面板样式
     * @private
     */
    _applyPanelStyles() {
        // 面板容器样式
        Object.assign(this.elements.panel.style, {
            backgroundColor: 'white',
            borderRadius: '6px',
            overflow: 'hidden',
            border: this.panelProps.bordered ? '1px solid #e0e0e0' : 'none',
            boxShadow: this.panelProps.shadow ? '0 2px 8px rgba(0, 0, 0, 0.1)' : 'none'
        });
        
        // 头部样式
        if (this.elements.header) {
            Object.assign(this.elements.header.style, {
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '12px 16px',
                borderBottom: '1px solid #f0f0f0',
                backgroundColor: '#fafafa'
            });
        }
        
        // 主体样式
        Object.assign(this.elements.body.style, {
            padding: this.panelProps.padding
        });
        
        // 标题样式
        if (this.elements.title) {
            Object.assign(this.elements.title.style, {
                margin: '0',
                fontSize: '16px',
                fontWeight: '500',
                color: '#333'
            });
        }
        
        // 操作区域样式
        if (this.elements.actions) {
            Object.assign(this.elements.actions.style, {
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
            });
        }
    }

    /**
     * 绑定面板事件 - 设置事件监听器
     * @private
     */
    _bindPanelEvents() {
        if (this.elements.collapseButton) {
            this.elements.collapseButton.addEventListener('click', () => {
                this.toggle();
            });
        }
    }

    // #region 公开方法
    /**
     * 添加内容 - 添加面板内容
     * @param {HTMLElement|BaseComponent} content - 内容
     */
    addContent(content) {
        if (content instanceof BaseComponent) {
            this.elements.body.appendChild(content.container);
        } else if (content instanceof HTMLElement) {
            this.elements.body.appendChild(content);
        } else {
            const div = document.createElement('div');
            div.innerHTML = String(content);
            this.elements.body.appendChild(div);
        }
        
        this.emit(UI_EVENTS.COMPONENT_UPDATED, {
            component: this,
            type: 'addContent'
        });
    }

    /**
     * 设置内容 - 设置面板内容
     * @param {HTMLElement|BaseComponent} content - 内容
     */
    setContent(content) {
        this.elements.body.innerHTML = '';
        this.addContent(content);
    }

    /**
     * 折叠面板 - 折叠面板内容
     */
    collapse() {
        this.panelState.collapsed = true;
        this.elements.body.style.display = 'none';
        
        if (this.elements.collapseButton) {
            this.elements.collapseButton.innerHTML = '▶';
        }
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            type: 'collapse',
            collapsed: true
        });
    }

    /**
     * 展开面板 - 展开面板内容
     */
    expand() {
        this.panelState.collapsed = false;
        this.elements.body.style.display = 'block';
        
        if (this.elements.collapseButton) {
            this.elements.collapseButton.innerHTML = '▼';
        }
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            type: 'expand',
            collapsed: false
        });
    }

    /**
     * 切换面板 - 切换面板折叠状态
     */
    toggle() {
        if (this.panelState.collapsed) {
            this.expand();
        } else {
            this.collapse();
        }
    }

    /**
     * 设置标题 - 更新面板标题
     * @param {string} title - 新标题
     */
    setTitle(title) {
        this.panelProps.title = title;
        if (this.elements.title) {
            this.elements.title.textContent = title;
        }
        
        this.emit(UI_EVENTS.COMPONENT_UPDATED, {
            component: this,
            type: 'setTitle',
            title: title
        });
    }
    // #endregion
}
// #endregion

// #region 工厂函数
/**
 * 创建布局 - 便捷函数创建布局实例
 * @param {Object} config - 布局配置
 * @returns {Layout} 布局实例
 */
export function createLayout(config = {}) {
    return new Layout(config);
}

/**
 * 创建垂直布局 - 便捷函数创建垂直布局
 * @param {Object} config - 布局配置
 * @returns {Layout} 布局实例
 */
export function createVerticalLayout(config = {}) {
    return new Layout({
        ...config,
        direction: 'vertical'
    });
}

/**
 * 创建水平布局 - 便捷函数创建水平布局
 * @param {Object} config - 布局配置
 * @returns {Layout} 布局实例
 */
export function createHorizontalLayout(config = {}) {
    return new Layout({
        ...config,
        direction: 'horizontal'
    });
}

/**
 * 创建容器 - 便捷函数创建容器实例
 * @param {Object} config - 容器配置
 * @returns {Container} 容器实例
 */
export function createContainer(config = {}) {
    return new Container(config);
}

/**
 * 创建流式容器 - 便捷函数创建流式容器
 * @param {Object} config - 容器配置
 * @returns {Container} 容器实例
 */
export function createFluidContainer(config = {}) {
    return new Container({
        ...config,
        fluid: true
    });
}

/**
 * 创建栅格 - 便捷函数创建栅格实例
 * @param {Object} config - 栅格配置
 * @returns {Grid} 栅格实例
 */
export function createGrid(config = {}) {
    return new Grid(config);
}

/**
 * 创建面板 - 便捷函数创建面板实例
 * @param {Object} config - 面板配置
 * @returns {Panel} 面板实例
 */
export function createPanel(config = {}) {
    return new Panel(config);
}

/**
 * 创建可折叠面板 - 便捷函数创建可折叠面板
 * @param {Object} config - 面板配置
 * @returns {Panel} 面板实例
 */
export function createCollapsiblePanel(config = {}) {
    return new Panel({
        ...config,
        collapsible: true
    });
}
// #endregion 