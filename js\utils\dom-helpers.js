/**
 * @file DOM辅助工具模块
 * <AUTHOR> Team
 * @description 
 * 从index.html中提取的DOM操作功能
 * 提供统一的DOM查询、操作、事件处理等功能
 */

import { getLogger } from './logger.js';

// #region DOM辅助工具类
/**
 * @class DOMHelpers - DOM辅助工具
 * @description 提供统一的DOM操作功能
 */
export class DOMHelpers {
    /**
     * 构造函数
     */
    constructor() {
        this.logger = getLogger();
    }

    /**
     * 安全获取元素
     * @param {string} selector - CSS选择器
     * @param {Element} context - 查找上下文，默认为document
     * @returns {Element|null} 找到的元素或null
     */
    getElement(selector, context = document) {
        try {
            return context.querySelector(selector);
        } catch (error) {
            this.logger.error('DOMHelpers', 'getElement', `获取元素失败: ${selector}`, error);
            return null;
        }
    }

    /**
     * 安全获取多个元素
     * @param {string} selector - CSS选择器
     * @param {Element} context - 查找上下文，默认为document
     * @returns {NodeList} 找到的元素列表
     */
    getElements(selector, context = document) {
        try {
            return context.querySelectorAll(selector);
        } catch (error) {
            this.logger.error('DOMHelpers', 'getElements', `获取元素列表失败: ${selector}`, error);
            return [];
        }
    }

    /**
     * 安全获取元素的值
     * @param {string} selector - CSS选择器
     * @param {string} defaultValue - 默认值
     * @returns {string} 元素的值或默认值
     */
    getValue(selector, defaultValue = '') {
        const element = this.getElement(selector);
        if (!element) {
            this.logger.warn('DOMHelpers', 'getValue', `元素未找到: ${selector}`);
            return defaultValue;
        }
        
        return element.value || element.textContent || defaultValue;
    }

    /**
     * 安全设置元素的值
     * @param {string} selector - CSS选择器
     * @param {string} value - 要设置的值
     * @returns {boolean} 是否设置成功
     */
    setValue(selector, value) {
        const element = this.getElement(selector);
        if (!element) {
            this.logger.warn('DOMHelpers', 'setValue', `元素未找到: ${selector}`);
            return false;
        }
        
        try {
            if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA' || element.tagName === 'SELECT') {
                element.value = value;
            } else {
                element.textContent = value;
            }
            return true;
        } catch (error) {
            this.logger.error('DOMHelpers', 'setValue', `设置值失败: ${selector}`, error);
            return false;
        }
    }

    /**
     * 安全添加事件监听器
     * @param {string} selector - CSS选择器
     * @param {string} event - 事件类型
     * @param {Function} handler - 事件处理函数
     * @param {Object} options - 事件选项
     * @returns {boolean} 是否添加成功
     */
    addEventListener(selector, event, handler, options = {}) {
        const element = this.getElement(selector);
        if (!element) {
            this.logger.warn('DOMHelpers', 'addEventListener', `元素未找到: ${selector}`);
            return false;
        }
        
        try {
            element.addEventListener(event, handler, options);
            this.logger.trace('DOMHelpers', 'addEventListener', `事件监听器已添加: ${selector} -> ${event}`);
            return true;
        } catch (error) {
            this.logger.error('DOMHelpers', 'addEventListener', `添加事件监听器失败: ${selector}`, error);
            return false;
        }
    }

    /**
     * 安全移除事件监听器
     * @param {string} selector - CSS选择器
     * @param {string} event - 事件类型
     * @param {Function} handler - 事件处理函数
     * @returns {boolean} 是否移除成功
     */
    removeEventListener(selector, event, handler) {
        const element = this.getElement(selector);
        if (!element) {
            this.logger.warn('DOMHelpers', 'removeEventListener', `元素未找到: ${selector}`);
            return false;
        }
        
        try {
            element.removeEventListener(event, handler);
            this.logger.trace('DOMHelpers', 'removeEventListener', `事件监听器已移除: ${selector} -> ${event}`);
            return true;
        } catch (error) {
            this.logger.error('DOMHelpers', 'removeEventListener', `移除事件监听器失败: ${selector}`, error);
            return false;
        }
    }

    /**
     * 切换元素的CSS类
     * @param {string} selector - CSS选择器
     * @param {string} className - CSS类名
     * @param {boolean} force - 强制添加或移除
     * @returns {boolean} 是否操作成功
     */
    toggleClass(selector, className, force = undefined) {
        const element = this.getElement(selector);
        if (!element) {
            this.logger.warn('DOMHelpers', 'toggleClass', `元素未找到: ${selector}`);
            return false;
        }
        
        try {
            element.classList.toggle(className, force);
            return true;
        } catch (error) {
            this.logger.error('DOMHelpers', 'toggleClass', `切换CSS类失败: ${selector}`, error);
            return false;
        }
    }

    /**
     * 添加CSS类
     * @param {string} selector - CSS选择器
     * @param {string} className - CSS类名
     * @returns {boolean} 是否添加成功
     */
    addClass(selector, className) {
        const element = this.getElement(selector);
        if (!element) {
            this.logger.warn('DOMHelpers', 'addClass', `元素未找到: ${selector}`);
            return false;
        }
        
        try {
            element.classList.add(className);
            return true;
        } catch (error) {
            this.logger.error('DOMHelpers', 'addClass', `添加CSS类失败: ${selector}`, error);
            return false;
        }
    }

    /**
     * 移除CSS类
     * @param {string} selector - CSS选择器
     * @param {string} className - CSS类名
     * @returns {boolean} 是否移除成功
     */
    removeClass(selector, className) {
        const element = this.getElement(selector);
        if (!element) {
            this.logger.warn('DOMHelpers', 'removeClass', `元素未找到: ${selector}`);
            return false;
        }
        
        try {
            element.classList.remove(className);
            return true;
        } catch (error) {
            this.logger.error('DOMHelpers', 'removeClass', `移除CSS类失败: ${selector}`, error);
            return false;
        }
    }

    /**
     * 检查元素是否包含CSS类
     * @param {string} selector - CSS选择器
     * @param {string} className - CSS类名
     * @returns {boolean} 是否包含该类
     */
    hasClass(selector, className) {
        const element = this.getElement(selector);
        if (!element) {
            this.logger.warn('DOMHelpers', 'hasClass', `元素未找到: ${selector}`);
            return false;
        }
        
        return element.classList.contains(className);
    }

    /**
     * 设置元素属性
     * @param {string} selector - CSS选择器
     * @param {string} attribute - 属性名
     * @param {string} value - 属性值
     * @returns {boolean} 是否设置成功
     */
    setAttribute(selector, attribute, value) {
        const element = this.getElement(selector);
        if (!element) {
            this.logger.warn('DOMHelpers', 'setAttribute', `元素未找到: ${selector}`);
            return false;
        }
        
        try {
            element.setAttribute(attribute, value);
            return true;
        } catch (error) {
            this.logger.error('DOMHelpers', 'setAttribute', `设置属性失败: ${selector}`, error);
            return false;
        }
    }

    /**
     * 获取元素属性
     * @param {string} selector - CSS选择器
     * @param {string} attribute - 属性名
     * @param {string} defaultValue - 默认值
     * @returns {string} 属性值或默认值
     */
    getAttribute(selector, attribute, defaultValue = null) {
        const element = this.getElement(selector);
        if (!element) {
            this.logger.warn('DOMHelpers', 'getAttribute', `元素未找到: ${selector}`);
            return defaultValue;
        }
        
        return element.getAttribute(attribute) || defaultValue;
    }

    /**
     * 显示元素
     * @param {string} selector - CSS选择器
     * @returns {boolean} 是否操作成功
     */
    show(selector) {
        return this.removeClass(selector, 'hidden');
    }

    /**
     * 隐藏元素
     * @param {string} selector - CSS选择器
     * @returns {boolean} 是否操作成功
     */
    hide(selector) {
        return this.addClass(selector, 'hidden');
    }

    /**
     * 切换元素显示状态
     * @param {string} selector - CSS选择器
     * @returns {boolean} 是否操作成功
     */
    toggle(selector) {
        return this.toggleClass(selector, 'hidden');
    }

    /**
     * 禁用元素
     * @param {string} selector - CSS选择器
     * @returns {boolean} 是否操作成功
     */
    disable(selector) {
        return this.setAttribute(selector, 'disabled', 'true');
    }

    /**
     * 启用元素
     * @param {string} selector - CSS选择器
     * @returns {boolean} 是否操作成功
     */
    enable(selector) {
        const element = this.getElement(selector);
        if (!element) {
            this.logger.warn('DOMHelpers', 'enable', `元素未找到: ${selector}`);
            return false;
        }
        
        try {
            element.removeAttribute('disabled');
            return true;
        } catch (error) {
            this.logger.error('DOMHelpers', 'enable', `启用元素失败: ${selector}`, error);
            return false;
        }
    }

    /**
     * 等待DOM就绪
     * @returns {Promise} DOM就绪的Promise
     */
    ready() {
        return new Promise((resolve) => {
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', resolve);
            } else {
                resolve();
            }
        });
    }

    /**
     * 等待元素出现
     * @param {string} selector - CSS选择器
     * @param {number} timeout - 超时时间（毫秒）
     * @returns {Promise<Element>} 元素的Promise
     */
    waitForElement(selector, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const element = this.getElement(selector);
            if (element) {
                resolve(element);
                return;
            }
            
            const observer = new MutationObserver(() => {
                const element = this.getElement(selector);
                if (element) {
                    observer.disconnect();
                    resolve(element);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`元素未在${timeout}ms内出现: ${selector}`));
            }, timeout);
        });
    }
}
// #endregion

// #region 全局DOM辅助实例
let globalDOMHelpers = null;

/**
 * 获取全局DOM辅助工具实例
 * @returns {DOMHelpers} DOM辅助工具实例
 */
export function getDOMHelpers() {
    if (!globalDOMHelpers) {
        globalDOMHelpers = new DOMHelpers();
    }
    return globalDOMHelpers;
}
// #endregion
