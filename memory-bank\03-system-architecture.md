# SmartOffice 2.0 系统架构模式

## 🏗️ 核心架构模式

### 事件驱动架构 (Event-Driven Architecture)
```javascript
// 中央事件系统
class EventSystem {
    constructor() {
        this.listeners = new Map();
    }
    
    emit(eventType, data) {
        // 触发事件，通知所有监听器
    }
    
    on(eventType, callback) {
        // 注册事件监听器
    }
}

// 使用示例
eventSystem.emit('document:changed', { type: 'receipt', data: formData });
```

### 组件化架构 (Component-Based Architecture)
```javascript
// 基础组件类
class BaseComponent {
    constructor(config = {}) {
        this.id = generateUUID();
        this.config = config;
        this.element = null;
        this.state = {};
        this._initialize();
    }
    
    render() { /* 渲染逻辑 */ }
    destroy() { /* 清理逻辑 */ }
}

// 具体组件实现
class DocumentPreview extends BaseComponent {
    // 文档预览组件
}
```

### 模板引擎模式 (Template Engine Pattern)
```javascript
// 模板基类
class BaseTemplate {
    async render(data = {}, options = {}) {
        await this._validateData(data);
        const preprocessedData = await this._preprocessData(data);
        const content = await this._doRender(preprocessedData, options);
        return await this._postprocessContent(content, options);
    }
}

// 具体模板实现
class ReceiptTemplate extends BaseTemplate {
    // 收据模板实现
}
```

## 🔧 关键技术决策

### 决策1：纯前端实现
**背景**：需要支持完全离线使用
**决策**：采用纯前端架构，无后端依赖
**影响**：
- ✅ 优势：零配置启动，完全离线
- ⚠️ 限制：无法使用服务端功能
- 🔧 缓解：通过浏览器API实现核心功能

### 决策2：双模式运行
**背景**：平衡易用性和功能完整性
**决策**：支持file://和HTTP两种运行模式
**影响**：
- ✅ file://模式：双击启动，核心功能可用
- ✅ HTTP模式：完整功能，无安全限制
- 🔧 实现：功能降级和兼容性处理

### 决策3：模块化设计
**背景**：代码可维护性和扩展性需求
**决策**：采用ES6模块化架构
**影响**：
- ✅ 代码组织清晰，职责分离
- ✅ 易于测试和维护
- ✅ 支持按需加载

### 决策4：无构建工具
**背景**：简化开发和部署流程
**决策**：不使用Webpack、Vite等构建工具
**影响**：
- ✅ 开发环境简单，无需配置
- ✅ 部署简单，直接运行
- ⚠️ 无法使用高级优化功能

## 🧩 组件关系图

### 核心组件层次
```
Application Layer (应用层)
├── DocumentGenerator     # 文档生成器
├── TemplateManager      # 模板管理器
└── ExportController     # 导出控制器

Service Layer (服务层)
├── EventSystem          # 事件系统
├── StateManager         # 状态管理
├── ValidationService    # 验证服务
└── UtilityService       # 工具服务

UI Layer (界面层)
├── FormComponents       # 表单组件
├── PreviewComponents    # 预览组件
├── ControlComponents    # 控制组件
└── LayoutComponents     # 布局组件

Data Layer (数据层)
├── DocumentModels       # 文档模型
├── TemplateModels       # 模板模型
└── ConfigModels         # 配置模型
```

### 数据流向
```
用户输入 → 表单组件 → 数据验证 → 状态管理 → 模板渲染 → 预览显示 → 导出处理
    ↓         ↓         ↓         ↓         ↓         ↓         ↓
事件触发 → 组件更新 → 错误处理 → 状态同步 → 内容生成 → 实时更新 → 文件生成
```

## 📦 模块设计模式

### 单例模式 (Singleton Pattern)
```javascript
// 全局状态管理器
class StateManager {
    constructor() {
        if (StateManager.instance) {
            return StateManager.instance;
        }
        StateManager.instance = this;
        this.state = {};
    }
    
    static getInstance() {
        return new StateManager();
    }
}
```

### 工厂模式 (Factory Pattern)
```javascript
// 文档工厂
class DocumentFactory {
    static create(type, data) {
        switch (type) {
            case 'receipt':
                return new ReceiptDocument(data);
            case 'invoice':
                return new InvoiceDocument(data);
            case 'quotation':
                return new QuotationDocument(data);
            case 'driver-agreement':
                return new DriverAgreementDocument(data);
            default:
                throw new Error(`Unknown document type: ${type}`);
        }
    }
}
```

### 观察者模式 (Observer Pattern)
```javascript
// 状态变化通知
class StateObserver {
    constructor() {
        this.observers = [];
    }
    
    subscribe(callback) {
        this.observers.push(callback);
    }
    
    notify(state) {
        this.observers.forEach(callback => callback(state));
    }
}
```

### 策略模式 (Strategy Pattern)
```javascript
// 导出策略
class ExportStrategy {
    export(content, options) {
        throw new Error('Export method must be implemented');
    }
}

class PDFExportStrategy extends ExportStrategy {
    export(content, options) {
        // PDF导出实现
    }
}

class ImageExportStrategy extends ExportStrategy {
    export(content, options) {
        // 图片导出实现
    }
}
```

## 🔄 生命周期管理

### 应用生命周期
```javascript
// 应用启动流程
1. DOM加载完成
2. 初始化核心系统
3. 注册事件监听器
4. 加载默认配置
5. 渲染初始界面
6. 准备接收用户输入

// 应用关闭流程
1. 保存当前状态
2. 清理事件监听器
3. 销毁组件实例
4. 释放内存资源
```

### 组件生命周期
```javascript
class ComponentLifecycle {
    // 创建阶段
    constructor() { /* 初始化 */ }
    
    // 挂载阶段
    mount() { /* 挂载到DOM */ }
    
    // 更新阶段
    update(newProps) { /* 更新属性 */ }
    
    // 销毁阶段
    unmount() { /* 从DOM移除 */ }
    destroy() { /* 清理资源 */ }
}
```

## 🎯 性能优化模式

### 懒加载模式 (Lazy Loading)
```javascript
// 按需加载组件
class LazyLoader {
    static async loadComponent(componentName) {
        const module = await import(`./components/${componentName}.js`);
        return module.default;
    }
}
```

### 缓存模式 (Caching Pattern)
```javascript
// 模板缓存
class TemplateCache {
    constructor() {
        this.cache = new Map();
    }
    
    get(key) {
        return this.cache.get(key);
    }
    
    set(key, template) {
        this.cache.set(key, template);
    }
}
```

### 防抖节流模式 (Debounce/Throttle)
```javascript
// 输入防抖
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
```

## 🔐 安全设计模式

### 输入验证模式
```javascript
// 数据验证器
class DataValidator {
    static validate(data, schema) {
        // 验证数据格式和内容
        // 防止XSS攻击
        // 确保数据完整性
    }
}
```

### 安全渲染模式
```javascript
// 安全HTML渲染
class SafeRenderer {
    static render(template, data) {
        // 转义用户输入
        // 防止脚本注入
        // 安全的DOM操作
    }
}
```

## 📊 错误处理模式

### 全局错误处理
```javascript
// 错误边界
class ErrorBoundary {
    constructor() {
        window.addEventListener('error', this.handleError.bind(this));
        window.addEventListener('unhandledrejection', this.handlePromiseRejection.bind(this));
    }
    
    handleError(event) {
        // 记录错误
        // 用户友好提示
        // 错误恢复
    }
}
```

### 优雅降级模式
```javascript
// 功能降级
class FeatureDetector {
    static detect(feature) {
        // 检测浏览器功能支持
        // 提供降级方案
        // 确保核心功能可用
    }
}
```
