/**
 * @file 主题管理器 - SmartOffice 主题系统
 * <AUTHOR> Team
 * @description 
 * 这个文件实现了主题管理系统，提供：
 * - 主题加载和切换
 * - 自定义主题创建
 * - 主题变量管理
 * - 动态样式应用
 * - 主题持久化存储
 */

// #region 导入依赖模块
import { EventEmitter } from '../../core/events/event-emitter.js';
import { UIEvents } from '../../core/events/event-types.js';
import { getLogger } from '../../core/utils/logger.js';
// #endregion

// #region ThemeManager 主题管理器类
/**
 * @class ThemeManager - 主题管理器
 * @description 管理应用程序的主题系统
 */
export class ThemeManager extends EventEmitter {
    /**
     * 构造函数 - 初始化主题管理器
     * @param {Object} config - 主题配置
     */
    constructor(config = {}) {
        super();
        
        this.logger = getLogger();
        this.logger.debug('ThemeManager', 'constructor', '初始化主题管理器');
        
        // 配置
        this.config = {
            defaultTheme: 'light',
            enableCustomThemes: true,
            enableDarkMode: true,
            enableSystemTheme: true,
            storageKey: 'smartoffice_theme',
            cssVariablePrefix: '--so-',
            transitionDuration: '0.3s',
            ...config
        };
        
        // 主题存储
        this.themes = new Map();
        this.customThemes = new Map();
        
        // 当前主题
        this.currentTheme = null;
        this.currentThemeId = null;
        
        // 主题变量
        this.themeVariables = new Map();
        
        // 样式元素
        this.styleElement = null;
        this.customStyleElement = null;
        
        // 系统主题监听
        this.systemThemeMediaQuery = null;
        
        this._initializeBuiltinThemes();
        this._initializeStyleElements();
        this._initializeSystemThemeDetection();
        this._loadSavedTheme();
        
        this.logger.info('ThemeManager', 'constructor', '主题管理器初始化完成', {
            config: this.config,
            currentTheme: this.currentThemeId
        });
    }

    /**
     * 初始化内置主题
     * @private
     */
    _initializeBuiltinThemes() {
        // 浅色主题
        this.registerTheme('light', {
            id: 'light',
            name: '浅色主题',
            type: 'builtin',
            variables: {
                // 主要颜色
                'primary-color': '#1890ff',
                'primary-color-hover': '#40a9ff',
                'primary-color-active': '#096dd9',
                'primary-color-light': '#e6f7ff',
                
                // 成功颜色
                'success-color': '#52c41a',
                'success-color-hover': '#73d13d',
                'success-color-active': '#389e0d',
                'success-color-light': '#f6ffed',
                
                // 警告颜色
                'warning-color': '#faad14',
                'warning-color-hover': '#ffc53d',
                'warning-color-active': '#d48806',
                'warning-color-light': '#fffbe6',
                
                // 错误颜色
                'error-color': '#ff4d4f',
                'error-color-hover': '#ff7875',
                'error-color-active': '#d9363e',
                'error-color-light': '#fff2f0',
                
                // 背景颜色
                'background-color': '#ffffff',
                'background-color-secondary': '#fafafa',
                'background-color-tertiary': '#f5f5f5',
                
                // 文本颜色
                'text-color': '#000000d9',
                'text-color-secondary': '#00000073',
                'text-color-tertiary': '#00000040',
                'text-color-disabled': '#00000025',
                
                // 边框颜色
                'border-color': '#d9d9d9',
                'border-color-light': '#f0f0f0',
                'border-color-dark': '#bfbfbf',
                
                // 阴影
                'box-shadow': '0 2px 8px rgba(0, 0, 0, 0.15)',
                'box-shadow-light': '0 1px 3px rgba(0, 0, 0, 0.12)',
                'box-shadow-heavy': '0 4px 16px rgba(0, 0, 0, 0.15)',
                
                // 字体
                'font-family': '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                'font-size-base': '14px',
                'font-size-small': '12px',
                'font-size-large': '16px',
                'line-height-base': '1.5715',
                
                // 间距
                'padding-xs': '4px',
                'padding-sm': '8px',
                'padding-md': '16px',
                'padding-lg': '24px',
                'padding-xl': '32px',
                
                // 圆角
                'border-radius-base': '6px',
                'border-radius-sm': '4px',
                'border-radius-lg': '8px',
                
                // 动画
                'transition-duration': this.config.transitionDuration,
                'transition-timing': 'cubic-bezier(0.4, 0, 0.2, 1)'
            }
        });
        
        // 深色主题
        this.registerTheme('dark', {
            id: 'dark',
            name: '深色主题',
            type: 'builtin',
            variables: {
                // 主要颜色
                'primary-color': '#1890ff',
                'primary-color-hover': '#40a9ff',
                'primary-color-active': '#096dd9',
                'primary-color-light': '#111b26',
                
                // 成功颜色
                'success-color': '#52c41a',
                'success-color-hover': '#73d13d',
                'success-color-active': '#389e0d',
                'success-color-light': '#162312',
                
                // 警告颜色
                'warning-color': '#faad14',
                'warning-color-hover': '#ffc53d',
                'warning-color-active': '#d48806',
                'warning-color-light': '#2b2111',
                
                // 错误颜色
                'error-color': '#ff4d4f',
                'error-color-hover': '#ff7875',
                'error-color-active': '#d9363e',
                'error-color-light': '#2a1215',
                
                // 背景颜色
                'background-color': '#141414',
                'background-color-secondary': '#1f1f1f',
                'background-color-tertiary': '#262626',
                
                // 文本颜色
                'text-color': '#ffffffd9',
                'text-color-secondary': '#ffffff73',
                'text-color-tertiary': '#ffffff40',
                'text-color-disabled': '#ffffff25',
                
                // 边框颜色
                'border-color': '#434343',
                'border-color-light': '#303030',
                'border-color-dark': '#595959',
                
                // 阴影
                'box-shadow': '0 2px 8px rgba(0, 0, 0, 0.45)',
                'box-shadow-light': '0 1px 3px rgba(0, 0, 0, 0.32)',
                'box-shadow-heavy': '0 4px 16px rgba(0, 0, 0, 0.45)',
                
                // 字体（继承浅色主题）
                'font-family': '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                'font-size-base': '14px',
                'font-size-small': '12px',
                'font-size-large': '16px',
                'line-height-base': '1.5715',
                
                // 间距（继承浅色主题）
                'padding-xs': '4px',
                'padding-sm': '8px',
                'padding-md': '16px',
                'padding-lg': '24px',
                'padding-xl': '32px',
                
                // 圆角（继承浅色主题）
                'border-radius-base': '6px',
                'border-radius-sm': '4px',
                'border-radius-lg': '8px',
                
                // 动画（继承浅色主题）
                'transition-duration': this.config.transitionDuration,
                'transition-timing': 'cubic-bezier(0.4, 0, 0.2, 1)'
            }
        });
        
        // 高对比度主题
        this.registerTheme('high-contrast', {
            id: 'high-contrast',
            name: '高对比度主题',
            type: 'builtin',
            variables: {
                // 主要颜色
                'primary-color': '#0066cc',
                'primary-color-hover': '#0080ff',
                'primary-color-active': '#004499',
                'primary-color-light': '#e6f3ff',
                
                // 成功颜色
                'success-color': '#008000',
                'success-color-hover': '#00b300',
                'success-color-active': '#006600',
                'success-color-light': '#e6ffe6',
                
                // 警告颜色
                'warning-color': '#ff8000',
                'warning-color-hover': '#ffb366',
                'warning-color-active': '#cc6600',
                'warning-color-light': '#fff2e6',
                
                // 错误颜色
                'error-color': '#cc0000',
                'error-color-hover': '#ff3333',
                'error-color-active': '#990000',
                'error-color-light': '#ffe6e6',
                
                // 背景颜色
                'background-color': '#ffffff',
                'background-color-secondary': '#f8f8f8',
                'background-color-tertiary': '#f0f0f0',
                
                // 文本颜色
                'text-color': '#000000',
                'text-color-secondary': '#333333',
                'text-color-tertiary': '#666666',
                'text-color-disabled': '#999999',
                
                // 边框颜色
                'border-color': '#000000',
                'border-color-light': '#666666',
                'border-color-dark': '#333333',
                
                // 阴影
                'box-shadow': '0 2px 8px rgba(0, 0, 0, 0.3)',
                'box-shadow-light': '0 1px 3px rgba(0, 0, 0, 0.2)',
                'box-shadow-heavy': '0 4px 16px rgba(0, 0, 0, 0.3)',
                
                // 字体
                'font-family': '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                'font-size-base': '16px', // 更大的字体
                'font-size-small': '14px',
                'font-size-large': '18px',
                'line-height-base': '1.6', // 更大的行高
                
                // 间距
                'padding-xs': '6px',
                'padding-sm': '12px',
                'padding-md': '20px',
                'padding-lg': '28px',
                'padding-xl': '36px',
                
                // 圆角
                'border-radius-base': '4px', // 更小的圆角
                'border-radius-sm': '2px',
                'border-radius-lg': '6px',
                
                // 动画
                'transition-duration': '0.1s', // 更快的动画
                'transition-timing': 'linear'
            }
        });
    }

    /**
     * 初始化样式元素
     * @private
     */
    _initializeStyleElements() {
        // 创建主题样式元素
        this.styleElement = document.createElement('style');
        this.styleElement.id = 'smartoffice-theme-styles';
        document.head.appendChild(this.styleElement);
        
        // 创建自定义样式元素
        this.customStyleElement = document.createElement('style');
        this.customStyleElement.id = 'smartoffice-custom-styles';
        document.head.appendChild(this.customStyleElement);
    }

    /**
     * 初始化系统主题检测
     * @private
     */
    _initializeSystemThemeDetection() {
        if (!this.config.enableSystemTheme) return;
        
        // 检测系统深色模式
        if (window.matchMedia) {
            this.systemThemeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            
            // 监听系统主题变化
            this.systemThemeMediaQuery.addEventListener('change', (e) => {
                if (this.currentThemeId === 'system') {
                    this._applySystemTheme();
                }
            });
        }
    }

    /**
     * 加载保存的主题
     * @private
     */
    _loadSavedTheme() {
        try {
            const savedTheme = localStorage.getItem(this.config.storageKey);
            if (savedTheme) {
                const themeData = JSON.parse(savedTheme);
                this.applyTheme(themeData.id, { skipSave: true });
            } else {
                this.applyTheme(this.config.defaultTheme);
            }
        } catch (error) {
            this.logger.error('ThemeManager', '_loadSavedTheme', '加载保存的主题失败', {
                error: error.message
            });
            this.applyTheme(this.config.defaultTheme);
        }
    }

    /**
     * 注册主题
     * @param {string} id - 主题ID
     * @param {Object} theme - 主题对象
     */
    registerTheme(id, theme) {
        if (!theme.id) {
            theme.id = id;
        }
        
        this.themes.set(id, theme);
        
        this.logger.debug('ThemeManager', 'registerTheme', '注册主题', {
            id,
            name: theme.name,
            type: theme.type
        });
        
        // 触发主题注册事件
        this.emit(UIEvents.THEME_LOADED, {
            id,
            theme,
            timestamp: Date.now()
        });
    }

    /**
     * 应用主题
     * @param {string} themeId - 主题ID
     * @param {Object} options - 选项
     */
    applyTheme(themeId, options = {}) {
        const {
            skipSave = false,
            transition = true
        } = options;
        
        let theme;
        
        if (themeId === 'system') {
            theme = this._getSystemTheme();
        } else {
            theme = this.themes.get(themeId) || this.customThemes.get(themeId);
        }
        
        if (!theme) {
            this.logger.error('ThemeManager', 'applyTheme', '主题不存在', { themeId });
            return false;
        }
        
        // 应用过渡效果
        if (transition && this.currentTheme) {
            this._applyTransition();
        }
        
        // 应用主题变量
        this._applyThemeVariables(theme.variables);
        
        // 更新当前主题
        this.currentTheme = theme;
        this.currentThemeId = themeId;
        
        // 保存主题设置
        if (!skipSave) {
            this._saveTheme(themeId);
        }
        
        // 触发主题变化事件
        this.emit(UIEvents.THEME_CHANGED, {
            themeId,
            theme,
            timestamp: Date.now()
        });
        
        this.logger.info('ThemeManager', 'applyTheme', '应用主题', {
            themeId,
            name: theme.name
        });
        
        return true;
    }

    /**
     * 获取系统主题
     * @returns {Object} 系统主题
     * @private
     */
    _getSystemTheme() {
        if (this.systemThemeMediaQuery && this.systemThemeMediaQuery.matches) {
            return this.themes.get('dark');
        } else {
            return this.themes.get('light');
        }
    }

    /**
     * 应用系统主题
     * @private
     */
    _applySystemTheme() {
        const systemTheme = this._getSystemTheme();
        this._applyThemeVariables(systemTheme.variables);
        this.currentTheme = systemTheme;
        
        // 触发主题变化事件
        this.emit(UIEvents.THEME_CHANGED, {
            themeId: 'system',
            theme: systemTheme,
            timestamp: Date.now()
        });
    }

    /**
     * 应用主题变量
     * @param {Object} variables - 主题变量
     * @private
     */
    _applyThemeVariables(variables) {
        const root = document.documentElement;
        
        // 清除现有变量
        this.themeVariables.forEach((value, key) => {
            root.style.removeProperty(`${this.config.cssVariablePrefix}${key}`);
        });
        
        // 应用新变量
        this.themeVariables.clear();
        
        for (const [key, value] of Object.entries(variables)) {
            const cssVar = `${this.config.cssVariablePrefix}${key}`;
            root.style.setProperty(cssVar, value);
            this.themeVariables.set(key, value);
        }
        
        // 更新样式表
        this._updateStyleSheet();
    }

    /**
     * 应用过渡效果
     * @private
     */
    _applyTransition() {
        const root = document.documentElement;
        
        // 添加过渡样式
        root.style.transition = `all ${this.config.transitionDuration} ease-in-out`;
        
        // 移除过渡样式
        setTimeout(() => {
            root.style.transition = '';
        }, parseFloat(this.config.transitionDuration) * 1000);
    }

    /**
     * 更新样式表
     * @private
     */
    _updateStyleSheet() {
        // 生成基础样式
        const baseStyles = this._generateBaseStyles();
        this.styleElement.textContent = baseStyles;
    }

    /**
     * 生成基础样式
     * @returns {string} CSS样式
     * @private
     */
    _generateBaseStyles() {
        return `
            :root {
                color-scheme: ${this.currentThemeId === 'dark' ? 'dark' : 'light'};
            }
            
            * {
                transition: color ${this.config.transitionDuration} ease,
                           background-color ${this.config.transitionDuration} ease,
                           border-color ${this.config.transitionDuration} ease,
                           box-shadow ${this.config.transitionDuration} ease;
            }
            
            body {
                background-color: var(${this.config.cssVariablePrefix}background-color);
                color: var(${this.config.cssVariablePrefix}text-color);
                font-family: var(${this.config.cssVariablePrefix}font-family);
                font-size: var(${this.config.cssVariablePrefix}font-size-base);
                line-height: var(${this.config.cssVariablePrefix}line-height-base);
            }
            
            .theme-transition-disabled * {
                transition: none !important;
            }
        `;
    }

    /**
     * 创建自定义主题
     * @param {string} id - 主题ID
     * @param {Object} config - 主题配置
     * @returns {Object} 创建的主题
     */
    createCustomTheme(id, config) {
        if (!this.config.enableCustomThemes) {
            this.logger.error('ThemeManager', 'createCustomTheme', '自定义主题功能已禁用');
            return null;
        }
        
        const {
            name,
            baseTheme = 'light',
            variables = {},
            description = ''
        } = config;
        
        // 获取基础主题
        const base = this.themes.get(baseTheme);
        if (!base) {
            this.logger.error('ThemeManager', 'createCustomTheme', '基础主题不存在', { baseTheme });
            return null;
        }
        
        // 合并变量
        const mergedVariables = {
            ...base.variables,
            ...variables
        };
        
        // 创建自定义主题
        const customTheme = {
            id,
            name: name || `自定义主题 ${id}`,
            type: 'custom',
            baseTheme,
            description,
            variables: mergedVariables,
            created: Date.now(),
            modified: Date.now()
        };
        
        // 保存自定义主题
        this.customThemes.set(id, customTheme);
        this._saveCustomTheme(id, customTheme);
        
        this.logger.info('ThemeManager', 'createCustomTheme', '创建自定义主题', {
            id,
            name: customTheme.name,
            baseTheme
        });
        
        return customTheme;
    }

    /**
     * 更新自定义主题
     * @param {string} id - 主题ID
     * @param {Object} updates - 更新内容
     * @returns {boolean} 是否更新成功
     */
    updateCustomTheme(id, updates) {
        const theme = this.customThemes.get(id);
        if (!theme) {
            this.logger.error('ThemeManager', 'updateCustomTheme', '自定义主题不存在', { id });
            return false;
        }
        
        // 更新主题
        const updatedTheme = {
            ...theme,
            ...updates,
            modified: Date.now()
        };
        
        // 如果更新了变量，需要合并
        if (updates.variables) {
            updatedTheme.variables = {
                ...theme.variables,
                ...updates.variables
            };
        }
        
        this.customThemes.set(id, updatedTheme);
        this._saveCustomTheme(id, updatedTheme);
        
        // 如果是当前主题，重新应用
        if (this.currentThemeId === id) {
            this.applyTheme(id, { skipSave: true });
        }
        
        this.logger.info('ThemeManager', 'updateCustomTheme', '更新自定义主题', { id });
        
        return true;
    }

    /**
     * 删除自定义主题
     * @param {string} id - 主题ID
     * @returns {boolean} 是否删除成功
     */
    deleteCustomTheme(id) {
        if (!this.customThemes.has(id)) {
            return false;
        }
        
        // 如果是当前主题，切换到默认主题
        if (this.currentThemeId === id) {
            this.applyTheme(this.config.defaultTheme);
        }
        
        // 删除主题
        this.customThemes.delete(id);
        
        // 从存储中删除
        try {
            localStorage.removeItem(`${this.config.storageKey}_custom_${id}`);
        } catch (error) {
            this.logger.error('ThemeManager', 'deleteCustomTheme', '删除存储失败', {
                id,
                error: error.message
            });
        }
        
        this.logger.info('ThemeManager', 'deleteCustomTheme', '删除自定义主题', { id });
        
        return true;
    }

    /**
     * 获取主题变量
     * @param {string} key - 变量键
     * @returns {string} 变量值
     */
    getThemeVariable(key) {
        return this.themeVariables.get(key);
    }

    /**
     * 设置主题变量
     * @param {string} key - 变量键
     * @param {string} value - 变量值
     */
    setThemeVariable(key, value) {
        const cssVar = `${this.config.cssVariablePrefix}${key}`;
        document.documentElement.style.setProperty(cssVar, value);
        this.themeVariables.set(key, value);
        
        // 如果是自定义主题，保存更改
        if (this.currentTheme && this.currentTheme.type === 'custom') {
            this.updateCustomTheme(this.currentThemeId, {
                variables: {
                    [key]: value
                }
            });
        }
    }

    /**
     * 获取所有主题
     * @returns {Array} 主题列表
     */
    getAllThemes() {
        const themes = [];
        
        // 内置主题
        for (const theme of this.themes.values()) {
            themes.push({
                ...theme,
                isCurrent: this.currentThemeId === theme.id
            });
        }
        
        // 自定义主题
        for (const theme of this.customThemes.values()) {
            themes.push({
                ...theme,
                isCurrent: this.currentThemeId === theme.id
            });
        }
        
        // 系统主题
        if (this.config.enableSystemTheme) {
            themes.push({
                id: 'system',
                name: '跟随系统',
                type: 'system',
                description: '根据系统设置自动切换浅色/深色主题',
                isCurrent: this.currentThemeId === 'system'
            });
        }
        
        return themes;
    }

    /**
     * 获取当前主题
     * @returns {Object} 当前主题
     */
    getCurrentTheme() {
        return {
            id: this.currentThemeId,
            theme: this.currentTheme
        };
    }

    /**
     * 导出主题
     * @param {string} themeId - 主题ID
     * @returns {Object} 主题数据
     */
    exportTheme(themeId) {
        const theme = this.customThemes.get(themeId);
        if (!theme) {
            this.logger.error('ThemeManager', 'exportTheme', '主题不存在', { themeId });
            return null;
        }
        
        return {
            ...theme,
            exportedAt: Date.now(),
            version: '1.0.0'
        };
    }

    /**
     * 导入主题
     * @param {Object} themeData - 主题数据
     * @returns {boolean} 是否导入成功
     */
    importTheme(themeData) {
        try {
            const { id, name, variables, baseTheme, description } = themeData;
            
            if (!id || !name || !variables) {
                throw new Error('主题数据不完整');
            }
            
            // 检查ID冲突
            if (this.themes.has(id) || this.customThemes.has(id)) {
                throw new Error(`主题ID已存在: ${id}`);
            }
            
            // 创建主题
            const importedTheme = this.createCustomTheme(id, {
                name,
                baseTheme: baseTheme || 'light',
                variables,
                description: description || '导入的主题'
            });
            
            this.logger.info('ThemeManager', 'importTheme', '导入主题成功', {
                id,
                name
            });
            
            return true;
            
        } catch (error) {
            this.logger.error('ThemeManager', 'importTheme', '导入主题失败', {
                error: error.message
            });
            return false;
        }
    }

    /**
     * 保存主题设置
     * @param {string} themeId - 主题ID
     * @private
     */
    _saveTheme(themeId) {
        try {
            const themeData = {
                id: themeId,
                timestamp: Date.now()
            };
            
            localStorage.setItem(this.config.storageKey, JSON.stringify(themeData));
        } catch (error) {
            this.logger.error('ThemeManager', '_saveTheme', '保存主题设置失败', {
                themeId,
                error: error.message
            });
        }
    }

    /**
     * 保存自定义主题
     * @param {string} id - 主题ID
     * @param {Object} theme - 主题对象
     * @private
     */
    _saveCustomTheme(id, theme) {
        try {
            const key = `${this.config.storageKey}_custom_${id}`;
            localStorage.setItem(key, JSON.stringify(theme));
        } catch (error) {
            this.logger.error('ThemeManager', '_saveCustomTheme', '保存自定义主题失败', {
                id,
                error: error.message
            });
        }
    }

    /**
     * 加载自定义主题
     * @private
     */
    _loadCustomThemes() {
        try {
            const prefix = `${this.config.storageKey}_custom_`;
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(prefix)) {
                    const themeId = key.substring(prefix.length);
                    const themeData = localStorage.getItem(key);
                    
                    if (themeData) {
                        const theme = JSON.parse(themeData);
                        this.customThemes.set(themeId, theme);
                    }
                }
            }
            
            this.logger.debug('ThemeManager', '_loadCustomThemes', 
                `加载了 ${this.customThemes.size} 个自定义主题`);
                
        } catch (error) {
            this.logger.error('ThemeManager', '_loadCustomThemes', '加载自定义主题失败', {
                error: error.message
            });
        }
    }

    /**
     * 切换深色模式
     */
    toggleDarkMode() {
        if (!this.config.enableDarkMode) {
            return;
        }
        
        const isDark = this.currentThemeId === 'dark' || 
                      (this.currentThemeId === 'system' && this.systemThemeMediaQuery?.matches);
        
        this.applyTheme(isDark ? 'light' : 'dark');
    }

    /**
     * 销毁主题管理器
     */
    destroy() {
        // 移除样式元素
        if (this.styleElement) {
            this.styleElement.remove();
        }
        
        if (this.customStyleElement) {
            this.customStyleElement.remove();
        }
        
        // 移除系统主题监听
        if (this.systemThemeMediaQuery) {
            this.systemThemeMediaQuery.removeEventListener('change', this._applySystemTheme);
        }
        
        // 清理数据
        this.themes.clear();
        this.customThemes.clear();
        this.themeVariables.clear();
        
        // 移除事件监听器
        this.removeAllListeners();
        
        this.logger.info('ThemeManager', 'destroy', '主题管理器已销毁');
    }
}
// #endregion

// #region 导出
export default ThemeManager;
// #endregion