/**
 * @file 应用启动器 - 整合后的应用初始化模块
 * <AUTHOR> Team
 * @description 
 * 这是重构后的统一应用启动器，整合了js/app-initializer.js、js/services/app-initializer.js和js/main.js的功能
 * 提供完整的应用启动和初始化流程
 * 
 * 重构说明：
 * - 整合了多个应用初始化器的功能
 * - 消除了重复的初始化代码
 * - 建立了统一的启动流程
 * - 支持模块化加载和依赖管理
 */

// #region 导入依赖
import { getLogger } from './logger.js';
import { getConfigManager } from './config.js';
import { defaultEventBus } from './events.js';
import { defaultRendererManager } from '../renderers/index.js';
// #endregion

// #region 应用启动器类
/**
 * @class AppBootstrap - 应用启动器
 * @description 负责应用的完整启动和初始化流程
 */
export class AppBootstrap {
    /**
     * 构造函数 - 初始化应用启动器
     * @param {Object} config - 启动配置
     */
    constructor(config = {}) {
        this.config = {
            enablePerformanceMonitoring: true,
            enableErrorHandling: true,
            enableCompatibilityCheck: true,
            enableModulePreloading: true,
            startupTimeout: 30000,
            ...config
        };
        
        this.logger = getLogger();
        this.configManager = getConfigManager();
        this.eventBus = defaultEventBus;
        this.rendererManager = defaultRendererManager;
        
        this.isInitialized = false;
        this.startupTime = null;
        this.modules = new Map();
        this.dependencies = new Map();
        this.initializationSteps = [];
        
        this.logger.info('AppBootstrap', 'constructor', '应用启动器创建完成');
    }
    
    /**
     * 启动应用
     * @returns {Promise<boolean>} 启动是否成功
     */
    async start() {
        const startTime = performance.now();
        this.startupTime = new Date();
        
        try {
            this.logger.info('AppBootstrap', 'start', '🚀 开始启动SmartOffice 2.0应用');
            
            // 触发启动开始事件
            this.eventBus.emit('app:startup:start', {
                timestamp: this.startupTime
            });
            
            // 执行启动步骤
            await this._executeStartupSteps();
            
            // 标记为已初始化
            this.isInitialized = true;
            
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            // 触发启动完成事件
            this.eventBus.emit('app:startup:complete', {
                duration,
                timestamp: new Date()
            });
            
            this.logger.info('AppBootstrap', 'start', `✅ SmartOffice 2.0应用启动完成，耗时: ${duration.toFixed(2)}ms`);
            
            return true;
            
        } catch (error) {
            this.logger.error('AppBootstrap', 'start', '❌ 应用启动失败', error);
            
            // 触发启动失败事件
            this.eventBus.emit('app:startup:error', {
                error: error.message,
                timestamp: new Date()
            });
            
            throw error;
        }
    }
    
    /**
     * 执行启动步骤
     * @private
     */
    async _executeStartupSteps() {
        const steps = [
            { name: '环境检查', fn: this._checkEnvironment.bind(this) },
            { name: '兼容性检查', fn: this._checkCompatibility.bind(this) },
            { name: '配置初始化', fn: this._initializeConfig.bind(this) },
            { name: '核心模块加载', fn: this._loadCoreModules.bind(this) },
            { name: '服务初始化', fn: this._initializeServices.bind(this) },
            { name: 'UI组件初始化', fn: this._initializeUI.bind(this) },
            { name: '事件系统设置', fn: this._setupEventSystem.bind(this) },
            { name: '性能监控启动', fn: this._startPerformanceMonitoring.bind(this) },
            { name: '应用就绪检查', fn: this._checkAppReady.bind(this) }
        ];
        
        for (let i = 0; i < steps.length; i++) {
            const step = steps[i];
            const stepStartTime = performance.now();
            
            try {
                this.logger.info('AppBootstrap', '_executeStartupSteps', `执行启动步骤 ${i + 1}/${steps.length}: ${step.name}`);
                
                // 触发步骤开始事件
                this.eventBus.emit('app:startup:step:start', {
                    stepIndex: i,
                    stepName: step.name,
                    timestamp: new Date()
                });
                
                await step.fn();
                
                const stepDuration = performance.now() - stepStartTime;
                
                // 记录步骤信息
                this.initializationSteps.push({
                    name: step.name,
                    duration: stepDuration,
                    success: true,
                    timestamp: new Date()
                });
                
                // 触发步骤完成事件
                this.eventBus.emit('app:startup:step:complete', {
                    stepIndex: i,
                    stepName: step.name,
                    duration: stepDuration,
                    timestamp: new Date()
                });
                
                this.logger.debug('AppBootstrap', '_executeStartupSteps', `步骤完成: ${step.name} (${stepDuration.toFixed(2)}ms)`);
                
            } catch (error) {
                // 记录失败的步骤
                this.initializationSteps.push({
                    name: step.name,
                    duration: performance.now() - stepStartTime,
                    success: false,
                    error: error.message,
                    timestamp: new Date()
                });
                
                // 触发步骤失败事件
                this.eventBus.emit('app:startup:step:error', {
                    stepIndex: i,
                    stepName: step.name,
                    error: error.message,
                    timestamp: new Date()
                });
                
                this.logger.error('AppBootstrap', '_executeStartupSteps', `步骤失败: ${step.name}`, error);
                throw error;
            }
        }
    }
    
    /**
     * 检查运行环境
     * @private
     */
    async _checkEnvironment() {
        // 检查浏览器环境
        if (typeof window === 'undefined') {
            throw new Error('应用必须在浏览器环境中运行');
        }
        
        // 检查必需的API
        const requiredAPIs = [
            'localStorage',
            'sessionStorage',
            'fetch',
            'Promise',
            'Map',
            'Set'
        ];
        
        for (const api of requiredAPIs) {
            if (!(api in window)) {
                throw new Error(`浏览器不支持必需的API: ${api}`);
            }
        }
        
        // 检查文件协议兼容性
        if (location.protocol === 'file:') {
            this.logger.warn('AppBootstrap', '_checkEnvironment', '检测到file://协议，某些功能可能受限');
        }
        
        this.logger.info('AppBootstrap', '_checkEnvironment', '环境检查通过');
    }
    
    /**
     * 检查浏览器兼容性
     * @private
     */
    async _checkCompatibility() {
        if (!this.config.enableCompatibilityCheck) {
            return;
        }
        
        const userAgent = navigator.userAgent;
        const isModernBrowser = (
            'fetch' in window &&
            'Promise' in window &&
            'Map' in window &&
            'Set' in window
        );
        
        if (!isModernBrowser) {
            this.logger.warn('AppBootstrap', '_checkCompatibility', '检测到旧版浏览器，建议升级');
        }
        
        // 检查特定功能支持
        const features = {
            localStorage: 'localStorage' in window,
            canvas: 'HTMLCanvasElement' in window,
            webgl: this._checkWebGLSupport(),
            fileAPI: 'FileReader' in window
        };
        
        this.logger.info('AppBootstrap', '_checkCompatibility', '浏览器功能支持', features);
    }
    
    /**
     * 检查WebGL支持
     * @returns {boolean} 是否支持WebGL
     * @private
     */
    _checkWebGLSupport() {
        try {
            const canvas = document.createElement('canvas');
            return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
        } catch (e) {
            return false;
        }
    }
    
    /**
     * 初始化配置
     * @private
     */
    async _initializeConfig() {
        // 配置管理器已在构造函数中初始化
        // 这里可以加载特定的应用配置
        
        // 设置应用级配置
        this.configManager.set('app.startupTime', this.startupTime.toISOString(), false);
        this.configManager.set('app.environment', this._detectEnvironment(), false);
        
        this.logger.info('AppBootstrap', '_initializeConfig', '配置初始化完成');
    }
    
    /**
     * 检测运行环境
     * @returns {string} 环境类型
     * @private
     */
    _detectEnvironment() {
        if (location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
            return 'development';
        } else if (location.protocol === 'file:') {
            return 'local';
        } else {
            return 'production';
        }
    }
    
    /**
     * 加载核心模块
     * @private
     */
    async _loadCoreModules() {
        const coreModules = [
            'logger',
            'config',
            'events',
            'renderers'
        ];
        
        for (const moduleName of coreModules) {
            try {
                // 模块已通过import加载，这里主要是验证和注册
                this.modules.set(moduleName, {
                    name: moduleName,
                    loaded: true,
                    timestamp: new Date()
                });
                
                this.logger.debug('AppBootstrap', '_loadCoreModules', `核心模块已加载: ${moduleName}`);
                
            } catch (error) {
                this.logger.error('AppBootstrap', '_loadCoreModules', `核心模块加载失败: ${moduleName}`, error);
                throw error;
            }
        }
        
        this.logger.info('AppBootstrap', '_loadCoreModules', '核心模块加载完成');
    }
    
    /**
     * 初始化服务
     * @private
     */
    async _initializeServices() {
        // 初始化渲染器管理器
        // 渲染器管理器已在构造函数中初始化
        
        // 注册全局错误处理
        this._setupGlobalErrorHandling();
        
        this.logger.info('AppBootstrap', '_initializeServices', '服务初始化完成');
    }
    
    /**
     * 设置全局错误处理
     * @private
     */
    _setupGlobalErrorHandling() {
        if (!this.config.enableErrorHandling) {
            return;
        }
        
        // 全局错误处理已在Logger中设置
        // 这里可以添加额外的错误处理逻辑
        
        this.eventBus.on('app:error', (error) => {
            this.logger.error('AppBootstrap', 'globalErrorHandler', '应用错误', error);
        });
    }
    
    /**
     * 初始化UI组件
     * @private
     */
    async _initializeUI() {
        // 检查DOM是否就绪
        if (document.readyState === 'loading') {
            await new Promise(resolve => {
                document.addEventListener('DOMContentLoaded', resolve);
            });
        }
        
        // 设置基础UI事件
        this._setupBasicUIEvents();
        
        this.logger.info('AppBootstrap', '_initializeUI', 'UI组件初始化完成');
    }
    
    /**
     * 设置基础UI事件
     * @private
     */
    _setupBasicUIEvents() {
        // 页面可见性变化处理
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.eventBus.emit('app:visibility:hidden');
            } else {
                this.eventBus.emit('app:visibility:visible');
            }
        });
        
        // 窗口大小变化处理
        window.addEventListener('resize', () => {
            this.eventBus.emit('app:window:resize', {
                width: window.innerWidth,
                height: window.innerHeight
            });
        });
    }
    
    /**
     * 设置事件系统
     * @private
     */
    async _setupEventSystem() {
        // 事件总线已在构造函数中初始化
        // 这里可以设置应用级事件监听器
        
        this.eventBus.on('app:shutdown', () => {
            this.shutdown();
        });
        
        this.logger.info('AppBootstrap', '_setupEventSystem', '事件系统设置完成');
    }
    
    /**
     * 启动性能监控
     * @private
     */
    async _startPerformanceMonitoring() {
        if (!this.config.enablePerformanceMonitoring) {
            return;
        }
        
        // 监控内存使用
        if ('memory' in performance) {
            setInterval(() => {
                const memInfo = performance.memory;
                this.eventBus.emit('app:performance:memory', {
                    used: memInfo.usedJSHeapSize,
                    total: memInfo.totalJSHeapSize,
                    limit: memInfo.jsHeapSizeLimit
                });
            }, 30000); // 每30秒检查一次
        }
        
        this.logger.info('AppBootstrap', '_startPerformanceMonitoring', '性能监控启动完成');
    }
    
    /**
     * 检查应用就绪状态
     * @private
     */
    async _checkAppReady() {
        // 检查所有必需的模块是否已加载
        const requiredModules = ['logger', 'config', 'events', 'renderers'];
        for (const moduleName of requiredModules) {
            if (!this.modules.has(moduleName)) {
                throw new Error(`必需的模块未加载: ${moduleName}`);
            }
        }
        
        // 检查DOM是否就绪
        if (document.readyState !== 'complete' && document.readyState !== 'interactive') {
            throw new Error('DOM未就绪');
        }
        
        this.logger.info('AppBootstrap', '_checkAppReady', '应用就绪检查通过');
    }
    
    /**
     * 获取启动信息
     * @returns {Object} 启动信息
     */
    getStartupInfo() {
        return {
            isInitialized: this.isInitialized,
            startupTime: this.startupTime,
            modules: Object.fromEntries(this.modules),
            initializationSteps: this.initializationSteps,
            config: this.config
        };
    }
    
    /**
     * 关闭应用
     */
    async shutdown() {
        this.logger.info('AppBootstrap', 'shutdown', '开始关闭应用');
        
        try {
            // 触发关闭开始事件
            this.eventBus.emit('app:shutdown:start');
            
            // 清理渲染器
            this.rendererManager.cleanup();
            
            // 清理配置管理器
            this.configManager.destroy();
            
            // 清理模块
            this.modules.clear();
            
            // 重置状态
            this.isInitialized = false;
            
            // 触发关闭完成事件
            this.eventBus.emit('app:shutdown:complete');
            
            this.logger.info('AppBootstrap', 'shutdown', '应用关闭完成');
            
        } catch (error) {
            this.logger.error('AppBootstrap', 'shutdown', '应用关闭失败', error);
            throw error;
        }
    }
}
// #endregion

// #region 全局启动器实例
let globalBootstrap = null;

/**
 * 创建应用启动器
 * @param {Object} config - 启动配置
 * @returns {AppBootstrap} 应用启动器实例
 */
export function createAppBootstrap(config = {}) {
    return new AppBootstrap(config);
}

/**
 * 获取全局应用启动器
 * @returns {AppBootstrap} 全局应用启动器实例
 */
export function getAppBootstrap() {
    if (!globalBootstrap) {
        globalBootstrap = new AppBootstrap();
    }
    return globalBootstrap;
}

/**
 * 快速启动应用
 * @param {Object} config - 启动配置
 * @returns {Promise<AppBootstrap>} 启动器实例
 */
export async function startApp(config = {}) {
    const bootstrap = createAppBootstrap(config);
    await bootstrap.start();
    return bootstrap;
}
// #endregion

// #region 自动注册到全局
if (typeof window !== 'undefined') {
    window.SmartOfficeBootstrap = {
        AppBootstrap,
        createAppBootstrap,
        getAppBootstrap,
        startApp
    };
}
// #endregion
