/**
 * @file 工作流引擎
 * @description 自动化文档处理流程，提供智能化的工作流管理和执行
 */

// #region 导入依赖模块
import { EventEmitter } from '../core/events/event-emitter.js';
import { validateExportConfig } from '../core/utils/validation.js';
import { deepClone } from '../core/utils/index.js';
import { getLogger } from '../core/utils/logger.js';
// #endregion

// #region WorkflowStep 工作流步骤类
/**
 * @class WorkflowStep - 工作流步骤
 * @description 定义工作流中的单个步骤，包含执行逻辑和状态管理
 */
export class WorkflowStep {
    /**
     * 构造函数 - 创建工作流步骤
     * @param {Object} config - 步骤配置
     */
    constructor(config = {}) {
        // 初始化日志记录器
        this.logger = getLogger();
        this.logger.startPerformanceMark('workflow_step_construction', 'WorkflowStep', 'constructor');
        this.logger.debug('WorkflowStep', 'constructor', '开始构造工作流步骤', {
            configKeys: Object.keys(config)
        });
        
        this.id = config.id || `step_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        this.name = config.name || 'Unnamed Step';
        this.description = config.description || '';
        this.type = config.type || 'custom';
        
        // 步骤状态
        this.status = 'pending'; // pending, running, completed, failed, skipped
        this.result = null;
        this.error = null;
        this.startTime = null;
        this.endTime = null;
        
        // 步骤配置
        this.config = config;
        this.dependencies = config.dependencies || [];
        this.conditions = config.conditions || [];
        this.retryCount = config.retryCount || 0;
        this.maxRetries = config.maxRetries || 3;
        this.timeout = config.timeout || 30000; // 30秒
        
        // 执行函数
        this.executor = config.executor || this._defaultExecutor;
        this.validator = config.validator || this._defaultValidator;
        this.rollback = config.rollback || this._defaultRollback;
        
        const constructionDuration = this.logger.endPerformanceMark('workflow_step_construction', 'WorkflowStep', 'constructor');
        this.logger.info('WorkflowStep', 'constructor', '✅ 工作流步骤构造完成', {
            stepId: this.id,
            stepName: this.name,
            stepType: this.type,
            duration: `${constructionDuration?.toFixed(2)}ms`,
            dependenciesCount: this.dependencies.length,
            conditionsCount: this.conditions.length,
            hasCustomExecutor: config.executor !== undefined
        });
        
        // 记录构造统计
        this.logger.incrementCounter('workflow_step_instances', 'WorkflowStep');
    }

    /**
     * 执行步骤
     * @param {Object} context - 执行上下文
     * @returns {Promise<Object>} 执行结果
     */
    async execute(context = {}) {
        try {
            // 检查前置条件
            if (!this._checkConditions(context)) {
                this.status = 'skipped';
                return { success: true, skipped: true, reason: 'Conditions not met' };
            }
            
            this.status = 'running';
            this.startTime = Date.now();
            
            // 执行步骤逻辑
            const result = await this._executeWithTimeout(context);
            
            // 验证结果
            if (this.validator && !await this.validator(result, context)) {
                throw new Error('Step validation failed');
            }
            
            this.status = 'completed';
            this.result = result;
            this.endTime = Date.now();
            
            return result;
            
        } catch (error) {
            this.status = 'failed';
            this.error = error;
            this.endTime = Date.now();
            
            // 重试逻辑
            if (this.retryCount < this.maxRetries) {
                this.retryCount++;
                console.warn(`步骤 ${this.name} 执行失败，正在重试 (${this.retryCount}/${this.maxRetries})`);
                return await this.execute(context);
            }
            
            throw error;
        }
    }

    /**
     * 带超时的执行
     * @param {Object} context - 执行上下文
     * @returns {Promise<Object>} 执行结果
     * @private
     */
    async _executeWithTimeout(context) {
        return Promise.race([
            this.executor(context, this.config),
            new Promise((_, reject) => {
                setTimeout(() => {
                    reject(new Error(`Step ${this.name} timeout after ${this.timeout}ms`));
                }, this.timeout);
            })
        ]);
    }

    /**
     * 检查前置条件
     * @param {Object} context - 执行上下文
     * @returns {boolean} 是否满足条件
     * @private
     */
    _checkConditions(context) {
        return this.conditions.every(condition => {
            if (typeof condition === 'function') {
                return condition(context);
            } else if (typeof condition === 'object') {
                return this._evaluateCondition(condition, context);
            }
            return true;
        });
    }

    /**
     * 评估条件对象
     * @param {Object} condition - 条件对象
     * @param {Object} context - 执行上下文
     * @returns {boolean} 条件结果
     * @private
     */
    _evaluateCondition(condition, context) {
        const { field, operator, value } = condition;
        const contextValue = this._getNestedValue(context, field);
        
        switch (operator) {
            case 'equals': return contextValue === value;
            case 'not_equals': return contextValue !== value;
            case 'greater_than': return contextValue > value;
            case 'less_than': return contextValue < value;
            case 'contains': return Array.isArray(contextValue) && contextValue.includes(value);
            case 'exists': return contextValue !== undefined && contextValue !== null;
            default: return true;
        }
    }

    /**
     * 获取嵌套值
     * @param {Object} obj - 对象
     * @param {string} path - 路径
     * @returns {*} 值
     * @private
     */
    _getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current && current[key], obj);
    }

    /**
     * 默认执行器
     * @param {Object} context - 执行上下文
     * @returns {Promise<Object>} 执行结果
     * @private
     */
    async _defaultExecutor(context) {
        console.log(`执行步骤: ${this.name}`);
        return { success: true, message: 'Default execution completed' };
    }

    /**
     * 默认验证器
     * @param {Object} result - 执行结果
     * @param {Object} context - 执行上下文
     * @returns {Promise<boolean>} 验证结果
     * @private
     */
    async _defaultValidator(result, context) {
        return result && result.success !== false;
    }

    /**
     * 默认回滚函数
     * @param {Object} context - 执行上下文
     * @returns {Promise<Object>} 回滚结果
     * @private
     */
    async _defaultRollback(context) {
        console.log(`回滚步骤: ${this.name}`);
        return { success: true, message: 'Default rollback completed' };
    }

    /**
     * 重置步骤状态
     */
    reset() {
        this.status = 'pending';
        this.result = null;
        this.error = null;
        this.startTime = null;
        this.endTime = null;
        this.retryCount = 0;
    }

    /**
     * 获取步骤信息
     * @returns {Object} 步骤信息
     */
    getInfo() {
        return {
            id: this.id,
            name: this.name,
            description: this.description,
            type: this.type,
            status: this.status,
            duration: this.endTime ? this.endTime - this.startTime : null,
            retryCount: this.retryCount,
            hasError: !!this.error,
            errorMessage: this.error ? this.error.message : null
        };
    }
}
// #endregion

// #region Workflow 工作流类
/**
 * @class Workflow - 工作流
 * @description 管理一系列工作流步骤的执行顺序和依赖关系
 */
export class Workflow extends EventEmitter {
    /**
     * 构造函数 - 创建工作流
     * @param {Object} config - 工作流配置
     */
    constructor(config = {}) {
        super();
        
        this.id = config.id || `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        this.name = config.name || 'Unnamed Workflow';
        this.description = config.description || '';
        this.version = config.version || '1.0.0';
        
        // 工作流状态
        this.status = 'idle'; // idle, running, completed, failed, cancelled
        this.steps = new Map();
        this.stepOrder = [];
        this.currentStepIndex = -1;
        this.context = {};
        this.result = null;
        this.error = null;
        
        // 执行配置
        this.config = {
            parallel: config.parallel || false,
            stopOnError: config.stopOnError !== false,
            enableRollback: config.enableRollback || false,
            maxExecutionTime: config.maxExecutionTime || 300000, // 5分钟
            ...config
        };
        
        // 统计信息
        this.stats = {
            totalExecutions: 0,
            successfulExecutions: 0,
            failedExecutions: 0,
            averageExecutionTime: 0,
            totalExecutionTime: 0
        };
        
        // 添加初始步骤
        if (config.steps) {
            config.steps.forEach(stepConfig => this.addStep(stepConfig));
        }
    }

    /**
     * 添加步骤
     * @param {Object|WorkflowStep} stepConfig - 步骤配置或步骤实例
     * @returns {WorkflowStep} 步骤实例
     */
    addStep(stepConfig) {
        const step = stepConfig instanceof WorkflowStep ? stepConfig : new WorkflowStep(stepConfig);
        
        this.steps.set(step.id, step);
        this.stepOrder.push(step.id);
        
        this.emit('step:added', { workflow: this.id, step: step.id });
        
        return step;
    }

    /**
     * 移除步骤
     * @param {string} stepId - 步骤ID
     * @returns {boolean} 是否成功移除
     */
    removeStep(stepId) {
        if (this.steps.has(stepId)) {
            this.steps.delete(stepId);
            this.stepOrder = this.stepOrder.filter(id => id !== stepId);
            
            this.emit('step:removed', { workflow: this.id, step: stepId });
            return true;
        }
        return false;
    }

    /**
     * 执行工作流
     * @param {Object} initialContext - 初始上下文
     * @returns {Promise<Object>} 执行结果
     */
    async execute(initialContext = {}) {
        try {
            this.status = 'running';
            this.context = { ...initialContext };
            this.currentStepIndex = -1;
            this.error = null;
            
            const startTime = Date.now();
            
            this.emit('workflow:started', {
                workflow: this.id,
                name: this.name,
                stepCount: this.stepOrder.length
            });
            
            // 执行步骤
            if (this.config.parallel) {
                await this._executeParallel();
            } else {
                await this._executeSequential();
            }
            
            const executionTime = Date.now() - startTime;
            
            this.status = 'completed';
            this.result = {
                success: true,
                context: this.context,
                executionTime,
                completedSteps: this._getCompletedSteps()
            };
            
            // 更新统计信息
            this._updateStats(executionTime, true);
            
            this.emit('workflow:completed', {
                workflow: this.id,
                name: this.name,
                result: this.result
            });
            
            return this.result;
            
        } catch (error) {
            this.status = 'failed';
            this.error = error;
            
            // 更新统计信息
            this._updateStats(Date.now() - startTime, false);
            
            this.emit('workflow:failed', {
                workflow: this.id,
                name: this.name,
                error: error.message,
                failedStep: this._getCurrentStep()?.id
            });
            
            // 如果启用回滚，执行回滚
            if (this.config.enableRollback) {
                await this._rollback();
            }
            
            throw error;
        }
    }

    /**
     * 顺序执行步骤
     * @private
     */
    async _executeSequential() {
        for (let i = 0; i < this.stepOrder.length; i++) {
            this.currentStepIndex = i;
            const stepId = this.stepOrder[i];
            const step = this.steps.get(stepId);
            
            if (!step) {
                throw new Error(`Step ${stepId} not found`);
            }
            
            // 检查依赖
            if (!this._checkDependencies(step)) {
                throw new Error(`Dependencies not met for step ${step.name}`);
            }
            
            this.emit('step:started', {
                workflow: this.id,
                step: step.id,
                name: step.name,
                index: i
            });
            
            try {
                const result = await step.execute(this.context);
                
                // 更新上下文
                if (result && typeof result === 'object') {
                    this.context = { ...this.context, ...result };
                }
                
                this.emit('step:completed', {
                    workflow: this.id,
                    step: step.id,
                    name: step.name,
                    result
                });
                
            } catch (error) {
                this.emit('step:failed', {
                    workflow: this.id,
                    step: step.id,
                    name: step.name,
                    error: error.message
                });
                
                if (this.config.stopOnError) {
                    throw error;
                }
            }
        }
    }

    /**
     * 并行执行步骤
     * @private
     */
    async _executeParallel() {
        const promises = this.stepOrder.map(async (stepId, index) => {
            const step = this.steps.get(stepId);
            
            if (!step) {
                throw new Error(`Step ${stepId} not found`);
            }
            
            this.emit('step:started', {
                workflow: this.id,
                step: step.id,
                name: step.name,
                index
            });
            
            try {
                const result = await step.execute(this.context);
                
                this.emit('step:completed', {
                    workflow: this.id,
                    step: step.id,
                    name: step.name,
                    result
                });
                
                return { stepId, result };
                
            } catch (error) {
                this.emit('step:failed', {
                    workflow: this.id,
                    step: step.id,
                    name: step.name,
                    error: error.message
                });
                
                if (this.config.stopOnError) {
                    throw error;
                }
                
                return { stepId, error };
            }
        });
        
        const results = await Promise.all(promises);
        
        // 合并结果到上下文
        results.forEach(({ stepId, result, error }) => {
            if (result && typeof result === 'object') {
                this.context = { ...this.context, ...result };
            }
        });
    }

    /**
     * 检查步骤依赖
     * @param {WorkflowStep} step - 步骤
     * @returns {boolean} 依赖是否满足
     * @private
     */
    _checkDependencies(step) {
        return step.dependencies.every(depId => {
            const depStep = this.steps.get(depId);
            return depStep && depStep.status === 'completed';
        });
    }

    /**
     * 回滚工作流
     * @private
     */
    async _rollback() {
        console.log(`正在回滚工作流: ${this.name}`);
        
        // 按相反顺序回滚已完成的步骤
        const completedSteps = this._getCompletedSteps().reverse();
        
        for (const step of completedSteps) {
            try {
                await step.rollback(this.context);
                console.log(`步骤 ${step.name} 回滚完成`);
            } catch (error) {
                console.error(`步骤 ${step.name} 回滚失败:`, error);
            }
        }
        
        this.emit('workflow:rollback', {
            workflow: this.id,
            name: this.name,
            rolledBackSteps: completedSteps.length
        });
    }

    /**
     * 获取已完成的步骤
     * @returns {Array<WorkflowStep>} 已完成的步骤数组
     * @private
     */
    _getCompletedSteps() {
        return Array.from(this.steps.values()).filter(step => step.status === 'completed');
    }

    /**
     * 获取当前步骤
     * @returns {WorkflowStep|null} 当前步骤
     * @private
     */
    _getCurrentStep() {
        if (this.currentStepIndex >= 0 && this.currentStepIndex < this.stepOrder.length) {
            const stepId = this.stepOrder[this.currentStepIndex];
            return this.steps.get(stepId);
        }
        return null;
    }

    /**
     * 更新统计信息
     * @param {number} executionTime - 执行时间
     * @param {boolean} success - 是否成功
     * @private
     */
    _updateStats(executionTime, success) {
        this.stats.totalExecutions++;
        this.stats.totalExecutionTime += executionTime;
        this.stats.averageExecutionTime = this.stats.totalExecutionTime / this.stats.totalExecutions;
        
        if (success) {
            this.stats.successfulExecutions++;
        } else {
            this.stats.failedExecutions++;
        }
    }

    /**
     * 取消工作流执行
     */
    cancel() {
        if (this.status === 'running') {
            this.status = 'cancelled';
            
            this.emit('workflow:cancelled', {
                workflow: this.id,
                name: this.name,
                currentStep: this._getCurrentStep()?.id
            });
        }
    }

    /**
     * 重置工作流
     */
    reset() {
        this.status = 'idle';
        this.currentStepIndex = -1;
        this.context = {};
        this.result = null;
        this.error = null;
        
        // 重置所有步骤
        this.steps.forEach(step => step.reset());
        
        this.emit('workflow:reset', { workflow: this.id, name: this.name });
    }

    /**
     * 获取工作流信息
     * @returns {Object} 工作流信息
     */
    getInfo() {
        return {
            id: this.id,
            name: this.name,
            description: this.description,
            version: this.version,
            status: this.status,
            stepCount: this.steps.size,
            completedSteps: this._getCompletedSteps().length,
            currentStep: this._getCurrentStep()?.name,
            stats: { ...this.stats }
        };
    }
}
// #endregion

// #region WorkflowEngine 工作流引擎
/**
 * @class WorkflowEngine - 工作流引擎
 * @description 管理和执行多个工作流，提供预定义的文档处理工作流
 */
export class WorkflowEngine extends EventEmitter {
    /**
     * 构造函数 - 创建工作流引擎
     * @param {Object} config - 引擎配置
     */
    constructor(config = {}) {
        super();
        
        // 初始化日志记录器
        this.logger = getLogger();
        this.logger.startPerformanceMark('workflow_engine_construction', 'WorkflowEngine', 'constructor');
        this.logger.info('WorkflowEngine', 'constructor', '开始构造工作流引擎', {
            configKeys: Object.keys(config)
        });
        
        this.name = 'WorkflowEngine';
        this.version = '1.0.0';
        this.isInitialized = false;
        
        // 引擎配置
        this.config = {
            maxConcurrentWorkflows: config.maxConcurrentWorkflows || 5,
            defaultTimeout: config.defaultTimeout || 300000, // 5分钟
            enableAutoRetry: config.enableAutoRetry !== false,
            maxRetries: config.maxRetries || 3,
            enableMetrics: config.enableMetrics !== false,
            enableHistory: config.enableHistory !== false,
            historyLimit: config.historyLimit || 1000,
            ...config
        };
        
        this.logger.debug('WorkflowEngine', 'constructor', '引擎配置初始化完成', {
            config: this.config
        });
        
        // 工作流管理
        this.workflows = new Map(); // 运行中的工作流
        this.templates = new Map(); // 工作流模板
        this.queue = []; // 等待执行的工作流
        this.history = []; // 执行历史
        
        // 统计信息
        this.stats = {
            totalExecutions: 0,
            successfulExecutions: 0,
            failedExecutions: 0,
            averageExecutionTime: 0,
            totalExecutionTime: 0,
            concurrentPeak: 0,
            lastExecution: null
        };
        
        this.logger.trace('WorkflowEngine', 'constructor', '存储结构和统计信息初始化完成');
        
        const constructionDuration = this.logger.endPerformanceMark('workflow_engine_construction', 'WorkflowEngine', 'constructor');
        this.logger.info('WorkflowEngine', 'constructor', '✅ 工作流引擎构造完成', {
            duration: `${constructionDuration?.toFixed(2)}ms`,
            maxConcurrentWorkflows: this.config.maxConcurrentWorkflows,
            enabledFeatures: {
                autoRetry: this.config.enableAutoRetry,
                metrics: this.config.enableMetrics,
                history: this.config.enableHistory
            }
        });
        
        // 记录构造统计
        this.logger.incrementCounter('workflow_engine_instances', 'WorkflowEngine');
    }

    /**
     * 初始化工作流引擎
     */
    async initialize() {
        try {
            console.log('🔧 正在初始化工作流引擎...');
            
            // 注册预定义工作流模板
            this._registerBuiltinWorkflows();
            
            // 设置事件监听器
            this._setupEventListeners();
            
            this.isInitialized = true;
            
            this.emit('engine:initialized', {
                engine: this.name,
                workflowCount: this.workflows.size,
                templateCount: this.templates.size
            });
            
            console.log('✅ 工作流引擎初始化完成');
            
        } catch (error) {
            console.error('❌ 工作流引擎初始化失败:', error);
            throw error;
        }
    }

    /**
     * 注册内置工作流
     * @private
     */
    _registerBuiltinWorkflows() {
        // 文档创建工作流
        this.registerWorkflowTemplate('create-document', {
            name: '文档创建工作流',
            description: '自动化文档创建流程',
            steps: [
                {
                    id: 'validate-data',
                    name: '验证数据',
                    type: 'validation',
                    executor: this._validateDocumentData.bind(this)
                },
                {
                    id: 'select-template',
                    name: '选择模板',
                    type: 'template-selection',
                    executor: this._selectTemplate.bind(this),
                    dependencies: ['validate-data']
                },
                {
                    id: 'render-document',
                    name: '渲染文档',
                    type: 'rendering',
                    executor: this._renderDocument.bind(this),
                    dependencies: ['select-template']
                },
                {
                    id: 'generate-preview',
                    name: '生成预览',
                    type: 'preview',
                    executor: this._generatePreview.bind(this),
                    dependencies: ['render-document'],
                    conditions: [
                        { field: 'options.autoPreview', operator: 'equals', value: true }
                    ]
                }
            ]
        });
        
        // 文档导出工作流
        this.registerWorkflowTemplate('export-document', {
            name: '文档导出工作流',
            description: '自动化文档导出流程',
            steps: [
                {
                    id: 'prepare-content',
                    name: '准备内容',
                    type: 'preparation',
                    executor: this._prepareExportContent.bind(this)
                },
                {
                    id: 'validate-export-config',
                    name: '验证导出配置',
                    type: 'validation',
                    executor: this._validateExportConfig.bind(this),
                    dependencies: ['prepare-content']
                },
                {
                    id: 'export-document',
                    name: '导出文档',
                    type: 'export',
                    executor: this._exportDocument.bind(this),
                    dependencies: ['validate-export-config']
                },
                {
                    id: 'post-process',
                    name: '后处理',
                    type: 'post-processing',
                    executor: this._postProcessExport.bind(this),
                    dependencies: ['export-document']
                }
            ]
        });
        
        // 批量处理工作流
        this.registerWorkflowTemplate('batch-process', {
            name: '批量处理工作流',
            description: '批量处理多个文档',
            parallel: true,
            steps: [
                {
                    id: 'prepare-batch',
                    name: '准备批量数据',
                    type: 'preparation',
                    executor: this._prepareBatchData.bind(this)
                },
                {
                    id: 'process-documents',
                    name: '处理文档',
                    type: 'batch-processing',
                    executor: this._processBatchDocuments.bind(this),
                    dependencies: ['prepare-batch']
                },
                {
                    id: 'collect-results',
                    name: '收集结果',
                    type: 'collection',
                    executor: this._collectBatchResults.bind(this),
                    dependencies: ['process-documents']
                }
            ]
        });
        
        console.log('📋 内置工作流模板注册完成');
    }

    /**
     * 设置事件监听器
     * @private
     */
    _setupEventListeners() {
        // 监听工作流事件
        this.on('workflow:completed', (data) => {
            this._handleWorkflowCompleted(data);
        });
        
        this.on('workflow:failed', (data) => {
            this._handleWorkflowFailed(data);
        });
    }

    /**
     * 注册工作流模板
     * @param {string} templateId - 模板ID
     * @param {Object} templateConfig - 模板配置
     */
    registerWorkflowTemplate(templateId, templateConfig) {
        this.templates.set(templateId, templateConfig);
        
        this.emit('template:registered', {
            templateId,
            name: templateConfig.name
        });
    }

    /**
     * 执行工作流
     * @param {string} templateId - 模板ID
     * @param {Object} context - 执行上下文
     * @returns {Promise<Object>} 执行结果
     */
    async executeWorkflow(templateId, context = {}) {
        try {
            // 获取模板
            const template = this.templates.get(templateId);
            if (!template) {
                throw new Error(`Workflow template ${templateId} not found`);
            }
            
            // 创建工作流实例
            const workflow = new Workflow({
                ...template,
                id: `${templateId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
            });
            
            // 注册工作流
            this.workflows.set(workflow.id, workflow);
            
            // 检查并发限制
            await this._waitForExecutionSlot();
            
            // 执行工作流
            this.activeExecutions.set(workflow.id, workflow);
            
            const result = await workflow.execute(context);
            
            // 记录执行历史
            if (this.config.enableHistory) {
                this._recordExecution(workflow, result, null);
            }
            
            return result;
            
        } catch (error) {
            // 记录失败历史
            if (this.config.enableHistory) {
                this._recordExecution(workflow, null, error);
            }
            
            throw error;
            
        } finally {
            // 清理活跃执行
            if (workflow) {
                this.activeExecutions.delete(workflow.id);
                this._processExecutionQueue();
            }
        }
    }

    /**
     * 批量执行工作流
     * @param {string} templateId - 模板ID
     * @param {Object} batchContext - 批量上下文
     * @returns {Promise<Array>} 执行结果数组
     */
    async executeBatchWorkflow(templateId, batchContext = {}) {
        const { documents, options } = batchContext;
        
        if (!Array.isArray(documents)) {
            throw new Error('Batch context must contain documents array');
        }
        
        const results = [];
        const promises = documents.map(async (doc, index) => {
            try {
                const context = {
                    ...doc,
                    batchIndex: index,
                    batchTotal: documents.length,
                    options
                };
                
                const result = await this.executeWorkflow(templateId, context);
                results[index] = result;
                
            } catch (error) {
                results[index] = { error: error.message, index };
            }
        });
        
        await Promise.all(promises);
        return results;
    }

    /**
     * 等待执行槽位
     * @private
     */
    async _waitForExecutionSlot() {
        if (this.activeExecutions.size < this.config.maxConcurrentWorkflows) {
            return; // 直接执行
        }
        
        // 加入队列等待
        return new Promise((resolve) => {
            this.executionQueue.push(resolve);
        });
    }

    /**
     * 处理执行队列
     * @private
     */
    _processExecutionQueue() {
        if (this.executionQueue.length > 0 && 
            this.activeExecutions.size < this.config.maxConcurrentWorkflows) {
            const resolve = this.executionQueue.shift();
            resolve();
        }
    }

    /**
     * 记录执行历史
     * @param {Workflow} workflow - 工作流
     * @param {Object} result - 执行结果
     * @param {Error} error - 错误信息
     * @private
     */
    _recordExecution(workflow, result, error) {
        const record = {
            workflowId: workflow.id,
            workflowName: workflow.name,
            templateId: workflow.templateId,
            timestamp: Date.now(),
            success: !error,
            result,
            error: error ? error.message : null,
            executionTime: result ? result.executionTime : null,
            stepCount: workflow.steps.size
        };
        
        this.executionHistory.push(record);
        
        // 限制历史记录大小
        if (this.executionHistory.length > this.config.maxHistorySize) {
            this.executionHistory.shift();
        }
        
        // 更新统计信息
        this._updateStats(record);
    }

    /**
     * 更新统计信息
     * @param {Object} record - 执行记录
     * @private
     */
    _updateStats(record) {
        this.stats.totalExecutions++;
        
        if (record.success) {
            this.stats.successfulExecutions++;
        } else {
            this.stats.failedExecutions++;
        }
        
        if (record.executionTime) {
            const totalTime = this.stats.averageExecutionTime * (this.stats.totalExecutions - 1) + record.executionTime;
            this.stats.averageExecutionTime = totalTime / this.stats.totalExecutions;
        }
    }

    /**
     * 处理工作流完成
     * @param {Object} data - 完成数据
     * @private
     */
    _handleWorkflowCompleted(data) {
        console.log(`✅ 工作流完成: ${data.name}`);
        
        this.emit('workflow:completed', {
            workflowName: data.name,
            result: data.result
        });
    }

    /**
     * 处理工作流失败
     * @param {Object} data - 失败数据
     * @private
     */
    _handleWorkflowFailed(data) {
        console.error(`❌ 工作流失败: ${data.name} - ${data.error}`);
        
        this.emit('workflow:failed', {
            workflowName: data.name,
            error: data.error
        });
    }

    // #region 内置工作流步骤执行器

    /**
     * 验证文档数据
     * @param {Object} context - 执行上下文
     * @returns {Promise<Object>} 验证结果
     * @private
     */
    async _validateDocumentData(context) {
        const { documentType, data } = context;
        
        if (!documentType) {
            throw new Error('Document type is required');
        }
        
        if (!data || typeof data !== 'object') {
            throw new Error('Document data is required');
        }
        
        // 使用验证工具验证数据
        // 这里可以调用具体的验证逻辑
        
        return {
            validatedData: data,
            documentType,
            validationPassed: true
        };
    }

    /**
     * 选择模板
     * @param {Object} context - 执行上下文
     * @returns {Promise<Object>} 模板选择结果
     * @private
     */
    async _selectTemplate(context) {
        const { documentType, options } = context;
        
        if (!this.managers.templates) {
            throw new Error('Template manager not available');
        }
        
        // 智能模板选择逻辑
        let templateName = documentType;
        if (options && options.template) {
            templateName = options.template;
        }
        
        const template = await this.managers.templates.getTemplate(templateName);
        
        return {
            selectedTemplate: template,
            templateName
        };
    }

    /**
     * 渲染文档
     * @param {Object} context - 执行上下文
     * @returns {Promise<Object>} 渲染结果
     * @private
     */
    async _renderDocument(context) {
        const { selectedTemplate, validatedData, options } = context;
        
        if (!this.managers.renderers) {
            throw new Error('Renderer manager not available');
        }
        
        const renderedContent = await this.managers.renderers.render(
            selectedTemplate,
            validatedData,
            options
        );
        
        return {
            renderedContent,
            renderTime: Date.now()
        };
    }

    /**
     * 生成预览
     * @param {Object} context - 执行上下文
     * @returns {Promise<Object>} 预览结果
     * @private
     */
    async _generatePreview(context) {
        const { renderedContent } = context;
        
        // 这里可以生成预览或触发预览更新
        
        return {
            previewGenerated: true,
            previewContent: renderedContent
        };
    }

    /**
     * 准备导出内容
     * @param {Object} context - 执行上下文
     * @returns {Promise<Object>} 准备结果
     * @private
     */
    async _prepareExportContent(context) {
        const { content, format, options } = context;
        
        if (!content) {
            throw new Error('Export content is required');
        }
        
        return {
            exportContent: content,
            exportFormat: format || 'pdf',
            exportOptions: options || {}
        };
    }

    /**
     * 验证导出配置
     * @param {Object} context - 执行上下文
     * @returns {Promise<Object>} 验证结果
     * @private
     */
    async _validateExportConfig(context) {
        const { exportFormat, exportOptions } = context;
        
        try {
            validateExportConfig(exportOptions, exportFormat);
            
            return {
                configValid: true,
                validatedOptions: exportOptions
            };
            
        } catch (error) {
            throw new Error(`Export config validation failed: ${error.message}`);
        }
    }

    /**
     * 导出文档
     * @param {Object} context - 执行上下文
     * @returns {Promise<Object>} 导出结果
     * @private
     */
    async _exportDocument(context) {
        const { exportContent, exportFormat, validatedOptions } = context;
        
        if (!this.managers.exporters) {
            throw new Error('Export manager not available');
        }
        
        const exportResult = await this.managers.exporters.export(exportContent, {
            format: exportFormat,
            ...validatedOptions
        });
        
        return {
            exportResult,
            exportCompleted: true
        };
    }

    /**
     * 后处理导出
     * @param {Object} context - 执行上下文
     * @returns {Promise<Object>} 后处理结果
     * @private
     */
    async _postProcessExport(context) {
        const { exportResult } = context;
        
        // 这里可以进行后处理，如文件压缩、上传等
        
        return {
            postProcessed: true,
            finalResult: exportResult
        };
    }

    /**
     * 准备批量数据
     * @param {Object} context - 执行上下文
     * @returns {Promise<Object>} 准备结果
     * @private
     */
    async _prepareBatchData(context) {
        const { documents } = context;
        
        if (!Array.isArray(documents)) {
            throw new Error('Documents must be an array');
        }
        
        return {
            batchDocuments: documents,
            batchSize: documents.length,
            batchPrepared: true
        };
    }

    /**
     * 处理批量文档
     * @param {Object} context - 执行上下文
     * @returns {Promise<Object>} 处理结果
     * @private
     */
    async _processBatchDocuments(context) {
        const { batchDocuments } = context;
        
        const results = [];
        
        for (const doc of batchDocuments) {
            try {
                const result = await this.executeWorkflow('create-document', doc);
                results.push(result);
            } catch (error) {
                results.push({ error: error.message });
            }
        }
        
        return {
            batchResults: results,
            processedCount: results.length
        };
    }

    /**
     * 收集批量结果
     * @param {Object} context - 执行上下文
     * @returns {Promise<Object>} 收集结果
     * @private
     */
    async _collectBatchResults(context) {
        const { batchResults } = context;
        
        const successful = batchResults.filter(r => !r.error);
        const failed = batchResults.filter(r => r.error);
        
        return {
            totalProcessed: batchResults.length,
            successful: successful.length,
            failed: failed.length,
            results: batchResults
        };
    }
    // #endregion

    /**
     * 获取引擎统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.stats,
            totalWorkflows: this.workflows.size,
            totalTemplates: this.templates.size,
            activeExecutions: this.activeExecutions.size,
            queuedExecutions: this.executionQueue.length,
            historySize: this.executionHistory.length
        };
    }

    /**
     * 获取执行历史
     * @param {number} limit - 限制数量
     * @returns {Array} 执行历史
     */
    getExecutionHistory(limit = 50) {
        return this.executionHistory.slice(-limit);
    }

    /**
     * 清理资源
     */
    async destroy() {
        // 取消所有活跃执行
        for (const workflow of this.activeExecutions.values()) {
            workflow.cancel();
        }
        
        // 清理数据
        this.workflows.clear();
        this.templates.clear();
        this.executionHistory = [];
        this.executionQueue = [];
        this.activeExecutions.clear();
        
        // 清理事件监听器
        this.removeAllListeners();
        
        this.isInitialized = false;
        
        console.log('🔄 工作流引擎已销毁');
    }
}
// #endregion

// #region 工厂函数和便捷方法
/**
 * 创建工作流引擎
 * @param {Object} config - 配置
 * @returns {WorkflowEngine} 工作流引擎实例
 */
export function createWorkflowEngine(config = {}) {
    return new WorkflowEngine(config);
}

/**
 * 创建工作流
 * @param {Object} config - 配置
 * @returns {Workflow} 工作流实例
 */
export function createWorkflow(config = {}) {
    return new Workflow(config);
}

/**
 * 创建工作流步骤
 * @param {Object} config - 配置
 * @returns {WorkflowStep} 工作流步骤实例
 */
export function createWorkflowStep(config = {}) {
    return new WorkflowStep(config);
}
// #endregion 