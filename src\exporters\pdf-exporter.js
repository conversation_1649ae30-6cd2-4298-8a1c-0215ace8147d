/**
 * @file PDF导出器 - 专门用于PDF格式的文档导出
 * <AUTHOR> Team
 * @description 
 * 这个文件实现了PDF导出器，继承自BaseExporter，提供：
 * - PDF格式的文档导出功能
 * - 集成现有的PDF渲染器系统
 * - 文件下载和预览功能
 * - 多种PDF生成引擎支持
 * - 质量控制和页面配置
 */

// #region 导入依赖模块
import { BaseExporter } from './base-exporter.js';
import { UnifiedRenderEngine } from '../core/rendering/unified-render-engine.js';
import { DocumentModel } from '../core/rendering/document-model.js';
import { StyleManager } from '../core/rendering/style-manager.js';
import { PositionManager } from '../core/rendering/position-manager.js';
import { PDFRenderer } from '../renderers/pdf-renderer.js';
import { createPDFRenderer, createPresetPDFRenderer } from '../renderers/pdf-renderer.js';
import { validateExportOptions } from '../core/utils/validation.js';
import { EXPORT_EVENTS } from '../core/events/event-types.js';
// #endregion

// #region PDFExporter 类定义
/**
 * @class PDFExporter - PDF导出器类
 * @description 专门用于PDF格式的文档导出，提供完整的PDF生成和下载功能
 */
export class PDFExporter extends BaseExporter {
    /**
     * 构造函数 - 初始化PDF导出器
     * @param {Object} config - PDF导出器配置
     * @param {string} config.engine - PDF生成引擎 ('html2pdf', 'jspdf', 'puppeteer')
     * @param {string} config.quality - PDF质量 ('low', 'medium', 'high')
     * @param {string} config.pageFormat - 页面格式 ('A4', 'A3', 'A5', 'Letter', 'Legal')
     * @param {string} config.orientation - 页面方向 ('portrait', 'landscape')
     */
    constructor(config = {}) {
        // 调用父类构造函数
        super({
            name: config.name || 'pdf-exporter',
            type: 'pdf',
            version: config.version || '1.0.0',
            description: config.description || 'PDF格式文档导出器',
            ...config
        });
        
        // PDF特有配置
        this.pdfConfig = {
            engine: config.engine || 'html2pdf',
            quality: config.quality || 'medium',
            pageFormat: config.pageFormat || 'A4',
            orientation: config.orientation || 'portrait',
            enableCompression: config.enableCompression !== false,
            enableEncryption: config.enableEncryption || false,
            ...config.pdfConfig
        };
        
        // 统一渲染架构组件
        this.renderEngine = new UnifiedRenderEngine();
        this.styleManager = new StyleManager();
        this.positionManager = new PositionManager();
        this.pdfRenderer = new PDFRenderer({
            styleManager: this.styleManager,
            positionManager: this.positionManager,
            renderConfig: this.pdfConfig
        });
        
        // 下载管理
        this.downloadQueue = [];
        this.activeDownloads = new Map();
        
        // PDF生成统计
        this.pdfStats = {
            totalPDFsGenerated: 0,
            totalFileSizeMB: 0,
            averageGenerationTime: 0,
            byQuality: {
                low: 0,
                medium: 0,
                high: 0
            }
        };
    }

    /**
     * 验证特定配置 - 验证PDF导出器特有的配置
     * @protected
     */
    _validateSpecificConfig() {
        // 验证PDF引擎
        const validEngines = ['html2pdf', 'jspdf', 'puppeteer'];
        if (!validEngines.includes(this.pdfConfig.engine)) {
            throw new Error(`不支持的PDF引擎: ${this.pdfConfig.engine}`);
        }
        
        // 验证质量设置
        const validQualities = ['low', 'medium', 'high'];
        if (!validQualities.includes(this.pdfConfig.quality)) {
            throw new Error(`不支持的PDF质量: ${this.pdfConfig.quality}`);
        }
        
        // 验证页面格式
        const validFormats = ['A4', 'A3', 'A5', 'Letter', 'Legal'];
        if (!validFormats.includes(this.pdfConfig.pageFormat)) {
            throw new Error(`不支持的页面格式: ${this.pdfConfig.pageFormat}`);
        }
        
        // 验证页面方向
        const validOrientations = ['portrait', 'landscape'];
        if (!validOrientations.includes(this.pdfConfig.orientation)) {
            throw new Error(`不支持的页面方向: ${this.pdfConfig.orientation}`);
        }
    }

    /**
     * 初始化PDF导出器 - 创建PDF渲染器实例
     * @private
     */
    async _initializePDFExporter() {
        try {
            // 创建PDF渲染器
            this.pdfRenderer = createPDFRenderer({
                name: `${this.name}-renderer`,
                renderConfig: this.pdfConfig
            });
            
            // 监听渲染器事件
            this.pdfRenderer.on('render:completed', (data) => {
                this._updatePDFStats(data);
            });
            
            this.pdfRenderer.on('render:failed', (data) => {
                this.emit(EXPORT_EVENTS.EXPORT_FAILED, {
                    exporter: this.name,
                    error: data.error,
                    phase: 'pdf-rendering'
                });
            });
            
        } catch (error) {
            throw new Error(`PDF渲染器初始化失败: ${error.message}`);
        }
    }

    /**
     * 验证特定导出选项 - 验证PDF导出特有的选项
     * @param {Object} options - 导出选项
     * @protected
     */
    _validateSpecificOptions(options) {
        try {
            validateExportOptions(options, 'pdf');
        } catch (error) {
            throw new Error(`PDF导出选项验证失败: ${error.message}`);
        }
    }

    /**
     * 执行实际导出 - 生成PDF文档并处理下载
     * @param {Object} content - 要导出的内容
     * @param {Object} options - 导出选项
     * @returns {Promise<Object>} 导出结果
     * @protected
     */
    async _performExport(content, options) {
        // 确保PDF渲染器已初始化
        if (!this.pdfRenderer) {
            await this._initializePDFExporter();
        }
        
        // 准备渲染选项
        const renderOptions = this._prepareRenderOptions(options);
        
        // 触发PDF生成开始事件
        this.emit(EXPORT_EVENTS.EXPORT_STARTED, {
            exporter: this.name,
            type: 'pdf',
            options: renderOptions
        });
        
        try {
            // 使用PDF渲染器生成PDF
            const renderResult = await this.pdfRenderer.render(content, renderOptions);
            
            // 处理PDF结果
            const exportResult = await this._processPDFResult(renderResult, options);
            
            // 如果启用自动下载，触发下载
            if (options.autoDownload !== false) {
                await this._downloadPDF(exportResult, options);
            }
            
            return exportResult;
            
        } catch (error) {
            throw new Error(`PDF生成失败: ${error.message}`);
        }
    }

    /**
     * 准备渲染选项 - 合并和转换导出选项为渲染选项
     * @param {Object} options - 导出选项
     * @returns {Object} 渲染选项
     * @private
     */
    _prepareRenderOptions(options) {
        return {
            // 基础配置
            engine: options.engine || this.pdfConfig.engine,
            quality: options.quality || this.pdfConfig.quality,
            
            // 页面配置
            pageFormat: options.pageFormat || this.pdfConfig.pageFormat,
            orientation: options.orientation || this.pdfConfig.orientation,
            
            // PDF特有选项
            enableCompression: options.enableCompression !== undefined ? 
                options.enableCompression : this.pdfConfig.enableCompression,
            enableEncryption: options.enableEncryption !== undefined ? 
                options.enableEncryption : this.pdfConfig.enableEncryption,
            
            // 文件信息
            fileName: options.fileName || 'document.pdf',
            
            // 其他选项
            ...options.renderOptions
        };
    }

    /**
     * 处理PDF渲染结果 - 将渲染结果转换为导出结果
     * @param {Object} renderResult - PDF渲染结果
     * @param {Object} options - 导出选项
     * @returns {Promise<Object>} 处理后的导出结果
     * @private
     */
    async _processPDFResult(renderResult, options) {
        const fileName = options.fileName || 'document.pdf';
        
        // 确保文件名有正确的扩展名
        const finalFileName = fileName.endsWith('.pdf') ? fileName : `${fileName}.pdf`;
        
        // 计算文件大小（如果可能）
        const fileSize = this._calculateFileSize(renderResult);
        
        return {
            type: 'pdf',
            fileName: finalFileName,
            content: renderResult.content,
            blob: renderResult.blob,
            url: renderResult.url,
            metadata: {
                pageCount: renderResult.pageCount || 1,
                fileSize: fileSize,
                quality: renderResult.quality,
                engine: renderResult.engine,
                pageFormat: renderResult.pageFormat,
                orientation: renderResult.orientation,
                generatedAt: new Date().toISOString(),
                ...renderResult.metadata
            }
        };
    }

    /**
     * 计算文件大小 - 估算PDF文件大小
     * @param {Object} renderResult - 渲染结果
     * @returns {number} 文件大小（字节）
     * @private
     */
    _calculateFileSize(renderResult) {
        if (renderResult.blob && renderResult.blob.size) {
            return renderResult.blob.size;
        }
        
        if (renderResult.content && typeof renderResult.content === 'string') {
            return new Blob([renderResult.content]).size;
        }
        
        // 如果无法计算，返回估算值
        return 1024 * 100; // 100KB 估算
    }

    /**
     * 下载PDF文件 - 处理PDF文件的下载
     * @param {Object} exportResult - 导出结果
     * @param {Object} options - 导出选项
     * @returns {Promise<void>}
     * @private
     */
    async _downloadPDF(exportResult, options) {
        const downloadId = `download_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        try {
            // 创建下载URL
            let downloadUrl = exportResult.url;
            
            if (!downloadUrl && exportResult.blob) {
                downloadUrl = URL.createObjectURL(exportResult.blob);
            } else if (!downloadUrl && exportResult.content) {
                const blob = new Blob([exportResult.content], { type: 'application/pdf' });
                downloadUrl = URL.createObjectURL(blob);
            }
            
            if (!downloadUrl) {
                throw new Error('无法创建下载URL');
            }
            
            // 记录下载任务
            this.activeDownloads.set(downloadId, {
                fileName: exportResult.fileName,
                url: downloadUrl,
                startTime: Date.now()
            });
            
            // 执行下载
            await this._triggerDownload(downloadUrl, exportResult.fileName);
            
            // 触发下载完成事件
            this.emit(EXPORT_EVENTS.EXPORT_COMPLETED, {
                exporter: this.name,
                type: 'pdf',
                result: exportResult,
                downloadId
            });
            
        } catch (error) {
            this.emit(EXPORT_EVENTS.EXPORT_FAILED, {
                exporter: this.name,
                error: error.message,
                phase: 'download',
                downloadId
            });
            throw error;
            
        } finally {
            // 清理下载记录
            this.activeDownloads.delete(downloadId);
            
            // 清理临时URL
            const download = this.activeDownloads.get(downloadId);
            if (download && download.url.startsWith('blob:')) {
                setTimeout(() => URL.revokeObjectURL(download.url), 1000);
            }
        }
    }

    /**
     * 触发浏览器下载 - 使用浏览器API触发文件下载
     * @param {string} url - 下载URL
     * @param {string} fileName - 文件名
     * @returns {Promise<void>}
     * @private
     */
    async _triggerDownload(url, fileName) {
        return new Promise((resolve, reject) => {
            try {
                // 创建下载链接
                const link = document.createElement('a');
                link.href = url;
                link.download = fileName;
                link.style.display = 'none';
                
                // 添加到页面并触发点击
                document.body.appendChild(link);
                link.click();
                
                // 清理
                document.body.removeChild(link);
                
                // 延迟一点时间确保下载开始
                setTimeout(resolve, 100);
                
            } catch (error) {
                reject(new Error(`下载触发失败: ${error.message}`));
            }
        });
    }

    /**
     * 更新PDF统计信息 - 记录PDF生成的统计数据
     * @param {Object} data - 渲染完成数据
     * @private
     */
    _updatePDFStats(data) {
        this.pdfStats.totalPDFsGenerated++;
        
        if (data.result && data.result.metadata) {
            const metadata = data.result.metadata;
            
            // 更新文件大小统计
            if (metadata.fileSize) {
                this.pdfStats.totalFileSizeMB += metadata.fileSize / (1024 * 1024);
            }
            
            // 更新质量统计
            if (metadata.quality && this.pdfStats.byQuality[metadata.quality] !== undefined) {
                this.pdfStats.byQuality[metadata.quality]++;
            }
        }
        
        // 更新平均生成时间
        if (data.duration) {
            const totalTime = this.pdfStats.averageGenerationTime * (this.pdfStats.totalPDFsGenerated - 1) + data.duration;
            this.pdfStats.averageGenerationTime = totalTime / this.pdfStats.totalPDFsGenerated;
        }
    }

    /**
     * 批量PDF导出 - 同时导出多个PDF文档
     * @param {Array<Object>} contents - 要导出的内容数组
     * @param {Object} baseOptions - 基础导出选项
     * @returns {Promise<Array<Object>>} 导出结果数组
     */
    async batchExport(contents, baseOptions = {}) {
        const results = [];
        const exportPromises = [];
        
        for (let i = 0; i < contents.length; i++) {
            const content = contents[i];
            const options = {
                ...baseOptions,
                fileName: baseOptions.fileName ? 
                    `${baseOptions.fileName.replace('.pdf', '')}_${i + 1}.pdf` : 
                    `document_${i + 1}.pdf`,
                autoDownload: false // 批量导出时先不自动下载
            };
            
            exportPromises.push(
                this.export(content, options)
                    .then(result => {
                        results[i] = result;
                    })
                    .catch(error => {
                        results[i] = { error: error.message };
                    })
            );
        }
        
        await Promise.all(exportPromises);
        
        // 如果启用批量下载，创建压缩包下载
        if (baseOptions.batchDownload !== false) {
            await this._downloadBatch(results.filter(r => !r.error), baseOptions);
        }
        
        return results;
    }

    /**
     * 批量下载 - 将多个PDF打包下载
     * @param {Array<Object>} results - 导出结果数组
     * @param {Object} options - 下载选项
     * @returns {Promise<void>}
     * @private
     */
    async _downloadBatch(results, options) {
        // 这里可以实现ZIP打包功能，目前简化为逐个下载
        for (const result of results) {
            if (result && result.url) {
                await this._downloadPDF(result, { autoDownload: true });
                // 添加延迟避免浏览器阻止多个下载
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }
    }

    /**
     * 预览PDF - 在新窗口中预览PDF
     * @param {Object} content - 要预览的内容
     * @param {Object} options - 预览选项
     * @returns {Promise<Object>} 预览结果
     */
    async preview(content, options = {}) {
        const previewOptions = {
            ...options,
            autoDownload: false,
            quality: options.quality || 'medium'
        };
        
        const result = await this.export(content, previewOptions);
        
        // 在新窗口打开PDF
        if (result.url) {
            window.open(result.url, '_blank');
        }
        
        return result;
    }

    /**
     * 获取支持的格式 - 返回PDF导出器支持的格式
     * @returns {Array<string>} 支持的格式数组
     */
    getSupportedFormats() {
        return ['pdf'];
    }

    /**
     * 获取支持的引擎 - 返回支持的PDF生成引擎
     * @returns {Array<string>} 支持的引擎数组
     */
    getSupportedEngines() {
        return ['html2pdf', 'jspdf', 'puppeteer'];
    }

    /**
     * 获取PDF统计信息 - 返回PDF导出的详细统计
     * @returns {Object} PDF统计信息
     */
    getPDFStats() {
        return {
            ...this.pdfStats,
            averageFileSizeMB: this.pdfStats.totalPDFsGenerated > 0 ? 
                this.pdfStats.totalFileSizeMB / this.pdfStats.totalPDFsGenerated : 0,
            activeDownloads: this.activeDownloads.size
        };
    }

    /**
     * 清理资源 - 清理PDF导出器占用的资源
     */
    cleanup() {
        // 清理PDF渲染器
        if (this.pdfRenderer) {
            this.pdfRenderer.cleanup();
            this.pdfRenderer = null;
        }
        
        // 清理下载队列
        this.downloadQueue = [];
        
        // 清理活跃下载并释放URL
        for (const [downloadId, download] of this.activeDownloads.entries()) {
            if (download.url && download.url.startsWith('blob:')) {
                URL.revokeObjectURL(download.url);
            }
        }
        this.activeDownloads.clear();
        
        // 调用父类清理
        super.cleanup();
    }
}
// #endregion

// #region 工厂函数和预设
/**
 * 创建PDF导出器 - 工厂函数，创建配置好的PDF导出器实例
 * @param {Object} config - 导出器配置
 * @returns {PDFExporter} PDF导出器实例
 */
export function createPDFExporter(config = {}) {
    return new PDFExporter(config);
}

/**
 * 创建预设PDF导出器 - 使用预设配置创建PDF导出器
 * @param {string} preset - 预设名称 ('default', 'high-quality', 'print', 'web', 'archive')
 * @param {Object} customConfig - 自定义配置
 * @returns {PDFExporter} 配置好的PDF导出器实例
 */
export function createPresetPDFExporter(preset = 'default', customConfig = {}) {
    const presets = {
        'default': {
            engine: 'html2pdf',
            quality: 'medium',
            pageFormat: 'A4',
            orientation: 'portrait',
            enableCompression: true
        },
        'high-quality': {
            engine: 'puppeteer',
            quality: 'high',
            pageFormat: 'A4',
            orientation: 'portrait',
            enableCompression: false
        },
        'print': {
            engine: 'html2pdf',
            quality: 'high',
            pageFormat: 'A4',
            orientation: 'portrait',
            enableCompression: false
        },
        'web': {
            engine: 'html2pdf',
            quality: 'low',
            pageFormat: 'A4',
            orientation: 'portrait',
            enableCompression: true
        },
        'archive': {
            engine: 'puppeteer',
            quality: 'high',
            pageFormat: 'A4',
            orientation: 'portrait',
            enableCompression: false,
            enableEncryption: false
        }
    };
    
    const presetConfig = presets[preset];
    if (!presetConfig) {
        throw new Error(`不支持的PDF导出器预设: ${preset}`);
    }
    
    return createPDFExporter({
        name: `pdf-exporter-${preset}`,
        description: `${preset}预设PDF导出器`,
        ...presetConfig,
        ...customConfig
    });
}

/**
 * 便捷PDF导出函数 - 快速导出PDF文档
 * @param {Object} content - 要导出的内容
 * @param {Object} options - 导出选项
 * @returns {Promise<Object>} 导出结果
 */
export async function exportToPDF(content, options = {}) {
    const exporter = createPDFExporter(options.exporterConfig);
    
    try {
        return await exporter.export(content, options);
    } finally {
        exporter.cleanup();
    }
}
// #endregion