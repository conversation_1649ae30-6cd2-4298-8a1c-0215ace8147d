/**
 * @file 对话框组件 - SmartOffice 高级UI组件
 * <AUTHOR> Team
 * @description 
 * 这个文件实现了对话框组件，提供：
 * - 模态和非模态对话框
 * - 可拖拽和调整大小
 * - 多种对话框类型
 * - 动画效果
 * - 键盘导航支持
 */

// #region 导入依赖模块
import { BaseComponent } from '../base-component.js';
import { UIEvents } from '../../../core/events/event-types.js';
import { getLogger } from '../../../core/utils/logger.js';
// #endregion

// #region 常量定义
/**
 * 对话框类型
 */
const DIALOG_TYPES = {
    INFO: 'info',
    SUCCESS: 'success',
    WARNING: 'warning',
    ERROR: 'error',
    CONFIRM: 'confirm',
    CUSTOM: 'custom'
};

/**
 * 对话框大小
 */
const DIALOG_SIZES = {
    SMALL: 'small',
    MEDIUM: 'medium',
    LARGE: 'large',
    FULLSCREEN: 'fullscreen'
};

/**
 * 动画类型
 */
const ANIMATION_TYPES = {
    FADE: 'fade',
    SLIDE: 'slide',
    ZOOM: 'zoom',
    BOUNCE: 'bounce'
};
// #endregion

// #region Dialog 对话框组件类
/**
 * @class Dialog - 对话框组件
 * @description 提供各种类型的对话框功能
 */
export class Dialog extends BaseComponent {
    /**
     * 构造函数 - 初始化对话框组件
     * @param {Object} config - 对话框配置
     */
    constructor(config = {}) {
        super(config);
        
        this.logger = getLogger();
        this.logger.debug('Dialog', 'constructor', '初始化对话框组件');
        
        // 配置
        this.config = {
            type: DIALOG_TYPES.CUSTOM,
            size: DIALOG_SIZES.MEDIUM,
            title: '',
            content: '',
            modal: true,
            closable: true,
            draggable: false,
            resizable: false,
            animation: ANIMATION_TYPES.FADE,
            animationDuration: 300,
            backdrop: true,
            backdropClosable: true,
            keyboard: true,
            autoFocus: true,
            destroyOnClose: false,
            zIndex: 1000,
            width: null,
            height: null,
            maxWidth: '90vw',
            maxHeight: '90vh',
            position: 'center',
            buttons: [],
            className: '',
            ...config
        };
        
        // 状态
        this.isVisible = false;
        this.isAnimating = false;
        this.isDragging = false;
        this.isResizing = false;
        
        // DOM元素
        this.overlay = null;
        this.dialog = null;
        this.header = null;
        this.body = null;
        this.footer = null;
        this.closeButton = null;
        
        // 拖拽相关
        this.dragData = {
            startX: 0,
            startY: 0,
            startLeft: 0,
            startTop: 0
        };
        
        // 调整大小相关
        this.resizeData = {
            startX: 0,
            startY: 0,
            startWidth: 0,
            startHeight: 0
        };
        
        // 事件处理器
        this.keydownHandler = null;
        this.resizeHandler = null;
        
        // 焦点管理
        this.previousActiveElement = null;
        this.focusableElements = [];
        this.currentFocusIndex = 0;
        
        this._createDialog();
        this._bindEvents();
        
        this.logger.info('Dialog', 'constructor', '对话框组件初始化完成', {
            config: this.config
        });
    }

    /**
     * 创建对话框
     * @private
     */
    _createDialog() {
        // 创建遮罩层
        if (this.config.modal && this.config.backdrop) {
            this.overlay = document.createElement('div');
            this.overlay.className = 'dialog-overlay';
            this.overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: ${this.config.zIndex - 1};
                opacity: 0;
                transition: opacity ${this.config.animationDuration}ms ease;
            `;
        }
        
        // 创建对话框容器
        this.dialog = document.createElement('div');
        this.dialog.className = `dialog dialog-${this.config.type} dialog-${this.config.size} ${this.config.className}`;
        this.dialog.setAttribute('role', 'dialog');
        this.dialog.setAttribute('aria-modal', this.config.modal.toString());
        
        // 设置对话框样式
        this._applyDialogStyles();
        
        // 创建对话框内容
        this._createDialogContent();
        
        // 添加到DOM
        if (this.overlay) {
            document.body.appendChild(this.overlay);
        }
        document.body.appendChild(this.dialog);
        
        // 设置初始状态
        this.dialog.style.display = 'none';
    }

    /**
     * 应用对话框样式
     * @private
     */
    _applyDialogStyles() {
        const styles = {
            position: 'fixed',
            zIndex: this.config.zIndex,
            backgroundColor: 'var(--so-background-color, #ffffff)',
            border: '1px solid var(--so-border-color, #d9d9d9)',
            borderRadius: 'var(--so-border-radius-base, 6px)',
            boxShadow: 'var(--so-box-shadow, 0 2px 8px rgba(0, 0, 0, 0.15))',
            display: 'flex',
            flexDirection: 'column',
            outline: 'none'
        };
        
        // 设置大小
        if (this.config.width) {
            styles.width = typeof this.config.width === 'number' ? 
                `${this.config.width}px` : this.config.width;
        } else {
            // 根据size设置默认宽度
            switch (this.config.size) {
                case DIALOG_SIZES.SMALL:
                    styles.width = '400px';
                    break;
                case DIALOG_SIZES.MEDIUM:
                    styles.width = '600px';
                    break;
                case DIALOG_SIZES.LARGE:
                    styles.width = '800px';
                    break;
                case DIALOG_SIZES.FULLSCREEN:
                    styles.width = '100vw';
                    styles.height = '100vh';
                    styles.top = '0';
                    styles.left = '0';
                    styles.borderRadius = '0';
                    break;
            }
        }
        
        if (this.config.height) {
            styles.height = typeof this.config.height === 'number' ? 
                `${this.config.height}px` : this.config.height;
        }
        
        // 设置最大尺寸
        styles.maxWidth = this.config.maxWidth;
        styles.maxHeight = this.config.maxHeight;
        
        // 设置位置
        if (this.config.size !== DIALOG_SIZES.FULLSCREEN) {
            this._setDialogPosition(styles);
        }
        
        // 应用样式
        Object.assign(this.dialog.style, styles);
    }

    /**
     * 设置对话框位置
     * @param {Object} styles - 样式对象
     * @private
     */
    _setDialogPosition(styles) {
        switch (this.config.position) {
            case 'center':
                styles.top = '50%';
                styles.left = '50%';
                styles.transform = 'translate(-50%, -50%)';
                break;
            case 'top':
                styles.top = '20%';
                styles.left = '50%';
                styles.transform = 'translateX(-50%)';
                break;
            case 'bottom':
                styles.bottom = '20%';
                styles.left = '50%';
                styles.transform = 'translateX(-50%)';
                break;
            default:
                if (typeof this.config.position === 'object') {
                    const { top, left, right, bottom } = this.config.position;
                    if (top !== undefined) styles.top = top;
                    if (left !== undefined) styles.left = left;
                    if (right !== undefined) styles.right = right;
                    if (bottom !== undefined) styles.bottom = bottom;
                }
                break;
        }
    }

    /**
     * 创建对话框内容
     * @private
     */
    _createDialogContent() {
        // 创建头部
        if (this.config.title || this.config.closable) {
            this.header = document.createElement('div');
            this.header.className = 'dialog-header';
            this.header.style.cssText = `
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: var(--so-padding-md, 16px);
                border-bottom: 1px solid var(--so-border-color-light, #f0f0f0);
                font-size: var(--so-font-size-large, 16px);
                font-weight: 500;
                color: var(--so-text-color, #000000d9);
            `;
            
            if (this.config.title) {
                const title = document.createElement('div');
                title.className = 'dialog-title';
                title.textContent = this.config.title;
                this.header.appendChild(title);
            }
            
            if (this.config.closable) {
                this.closeButton = document.createElement('button');
                this.closeButton.className = 'dialog-close';
                this.closeButton.innerHTML = '×';
                this.closeButton.setAttribute('aria-label', '关闭对话框');
                this.closeButton.style.cssText = `
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    padding: 0;
                    width: 24px;
                    height: 24px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: var(--so-text-color-secondary, #00000073);
                    transition: color 0.2s ease;
                `;
                
                this.closeButton.addEventListener('click', () => this.close());
                this.closeButton.addEventListener('mouseenter', () => {
                    this.closeButton.style.color = 'var(--so-text-color, #000000d9)';
                });
                this.closeButton.addEventListener('mouseleave', () => {
                    this.closeButton.style.color = 'var(--so-text-color-secondary, #00000073)';
                });
                
                this.header.appendChild(this.closeButton);
            }
            
            // 添加拖拽功能
            if (this.config.draggable) {
                this.header.style.cursor = 'move';
                this._addDragHandlers(this.header);
            }
            
            this.dialog.appendChild(this.header);
        }
        
        // 创建主体
        this.body = document.createElement('div');
        this.body.className = 'dialog-body';
        this.body.style.cssText = `
            flex: 1;
            padding: var(--so-padding-md, 16px);
            overflow-y: auto;
            color: var(--so-text-color, #000000d9);
        `;
        
        if (this.config.content) {
            if (typeof this.config.content === 'string') {
                this.body.innerHTML = this.config.content;
            } else if (this.config.content instanceof HTMLElement) {
                this.body.appendChild(this.config.content);
            }
        }
        
        this.dialog.appendChild(this.body);
        
        // 创建底部
        if (this.config.buttons && this.config.buttons.length > 0) {
            this.footer = document.createElement('div');
            this.footer.className = 'dialog-footer';
            this.footer.style.cssText = `
                display: flex;
                justify-content: flex-end;
                gap: var(--so-padding-sm, 8px);
                padding: var(--so-padding-md, 16px);
                border-top: 1px solid var(--so-border-color-light, #f0f0f0);
            `;
            
            this._createButtons();
            this.dialog.appendChild(this.footer);
        }
        
        // 添加调整大小功能
        if (this.config.resizable) {
            this._addResizeHandlers();
        }
    }

    /**
     * 创建按钮
     * @private
     */
    _createButtons() {
        this.config.buttons.forEach((buttonConfig, index) => {
            const button = document.createElement('button');
            button.className = `dialog-button ${buttonConfig.type || 'default'}`;
            button.textContent = buttonConfig.text || '按钮';
            
            // 设置按钮样式
            const isPrimary = buttonConfig.type === 'primary';
            button.style.cssText = `
                padding: var(--so-padding-sm, 8px) var(--so-padding-md, 16px);
                border: 1px solid ${isPrimary ? 'var(--so-primary-color, #1890ff)' : 'var(--so-border-color, #d9d9d9)'};
                border-radius: var(--so-border-radius-base, 6px);
                background-color: ${isPrimary ? 'var(--so-primary-color, #1890ff)' : 'var(--so-background-color, #ffffff)'};
                color: ${isPrimary ? '#ffffff' : 'var(--so-text-color, #000000d9)'};
                cursor: pointer;
                font-size: var(--so-font-size-base, 14px);
                transition: all 0.2s ease;
                outline: none;
            `;
            
            // 添加悬停效果
            button.addEventListener('mouseenter', () => {
                if (isPrimary) {
                    button.style.backgroundColor = 'var(--so-primary-color-hover, #40a9ff)';
                } else {
                    button.style.borderColor = 'var(--so-primary-color, #1890ff)';
                    button.style.color = 'var(--so-primary-color, #1890ff)';
                }
            });
            
            button.addEventListener('mouseleave', () => {
                if (isPrimary) {
                    button.style.backgroundColor = 'var(--so-primary-color, #1890ff)';
                } else {
                    button.style.borderColor = 'var(--so-border-color, #d9d9d9)';
                    button.style.color = 'var(--so-text-color, #000000d9)';
                }
            });
            
            // 添加点击事件
            button.addEventListener('click', (e) => {
                if (buttonConfig.onClick) {
                    const result = buttonConfig.onClick(e, this);
                    if (result !== false && buttonConfig.autoClose !== false) {
                        this.close();
                    }
                } else if (buttonConfig.autoClose !== false) {
                    this.close();
                }
            });
            
            this.footer.appendChild(button);
        });
    }

    /**
     * 添加拖拽处理器
     * @param {HTMLElement} handle - 拖拽手柄
     * @private
     */
    _addDragHandlers(handle) {
        handle.addEventListener('mousedown', (e) => {
            if (e.button !== 0) return; // 只处理左键
            
            this.isDragging = true;
            this.dragData.startX = e.clientX;
            this.dragData.startY = e.clientY;
            
            const rect = this.dialog.getBoundingClientRect();
            this.dragData.startLeft = rect.left;
            this.dragData.startTop = rect.top;
            
            document.addEventListener('mousemove', this._handleDragMove);
            document.addEventListener('mouseup', this._handleDragEnd);
            
            e.preventDefault();
        });
    }

    /**
     * 处理拖拽移动
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleDragMove = (e) => {
        if (!this.isDragging) return;
        
        const deltaX = e.clientX - this.dragData.startX;
        const deltaY = e.clientY - this.dragData.startY;
        
        const newLeft = this.dragData.startLeft + deltaX;
        const newTop = this.dragData.startTop + deltaY;
        
        this.dialog.style.left = `${newLeft}px`;
        this.dialog.style.top = `${newTop}px`;
        this.dialog.style.transform = 'none';
    };

    /**
     * 处理拖拽结束
     * @private
     */
    _handleDragEnd = () => {
        this.isDragging = false;
        document.removeEventListener('mousemove', this._handleDragMove);
        document.removeEventListener('mouseup', this._handleDragEnd);
    };

    /**
     * 添加调整大小处理器
     * @private
     */
    _addResizeHandlers() {
        const resizeHandle = document.createElement('div');
        resizeHandle.className = 'dialog-resize-handle';
        resizeHandle.style.cssText = `
            position: absolute;
            bottom: 0;
            right: 0;
            width: 16px;
            height: 16px;
            cursor: se-resize;
            background: linear-gradient(-45deg, transparent 40%, var(--so-border-color, #d9d9d9) 40%, var(--so-border-color, #d9d9d9) 60%, transparent 60%);
        `;
        
        resizeHandle.addEventListener('mousedown', (e) => {
            if (e.button !== 0) return;
            
            this.isResizing = true;
            this.resizeData.startX = e.clientX;
            this.resizeData.startY = e.clientY;
            
            const rect = this.dialog.getBoundingClientRect();
            this.resizeData.startWidth = rect.width;
            this.resizeData.startHeight = rect.height;
            
            document.addEventListener('mousemove', this._handleResizeMove);
            document.addEventListener('mouseup', this._handleResizeEnd);
            
            e.preventDefault();
        });
        
        this.dialog.appendChild(resizeHandle);
    }

    /**
     * 处理调整大小移动
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleResizeMove = (e) => {
        if (!this.isResizing) return;
        
        const deltaX = e.clientX - this.resizeData.startX;
        const deltaY = e.clientY - this.resizeData.startY;
        
        const newWidth = Math.max(200, this.resizeData.startWidth + deltaX);
        const newHeight = Math.max(150, this.resizeData.startHeight + deltaY);
        
        this.dialog.style.width = `${newWidth}px`;
        this.dialog.style.height = `${newHeight}px`;
    };

    /**
     * 处理调整大小结束
     * @private
     */
    _handleResizeEnd = () => {
        this.isResizing = false;
        document.removeEventListener('mousemove', this._handleResizeMove);
        document.removeEventListener('mouseup', this._handleResizeEnd);
    };

    /**
     * 绑定事件
     * @private
     */
    _bindEvents() {
        // 键盘事件
        if (this.config.keyboard) {
            this.keydownHandler = (e) => {
                if (!this.isVisible) return;
                
                switch (e.key) {
                    case 'Escape':
                        if (this.config.closable) {
                            this.close();
                        }
                        break;
                    case 'Tab':
                        this._handleTabNavigation(e);
                        break;
                }
            };
            
            document.addEventListener('keydown', this.keydownHandler);
        }
        
        // 遮罩层点击
        if (this.overlay && this.config.backdropClosable) {
            this.overlay.addEventListener('click', () => {
                if (this.config.closable) {
                    this.close();
                }
            });
        }
        
        // 窗口大小变化
        this.resizeHandler = () => {
            if (this.isVisible && this.config.size !== DIALOG_SIZES.FULLSCREEN) {
                this._adjustPosition();
            }
        };
        
        window.addEventListener('resize', this.resizeHandler);
    }

    /**
     * 处理Tab导航
     * @param {KeyboardEvent} e - 键盘事件
     * @private
     */
    _handleTabNavigation(e) {
        this._updateFocusableElements();
        
        if (this.focusableElements.length === 0) return;
        
        const currentIndex = this.focusableElements.indexOf(document.activeElement);
        let nextIndex;
        
        if (e.shiftKey) {
            // Shift+Tab - 向前
            nextIndex = currentIndex <= 0 ? this.focusableElements.length - 1 : currentIndex - 1;
        } else {
            // Tab - 向后
            nextIndex = currentIndex >= this.focusableElements.length - 1 ? 0 : currentIndex + 1;
        }
        
        this.focusableElements[nextIndex].focus();
        e.preventDefault();
    }

    /**
     * 更新可聚焦元素列表
     * @private
     */
    _updateFocusableElements() {
        const focusableSelectors = [
            'button:not([disabled])',
            'input:not([disabled])',
            'select:not([disabled])',
            'textarea:not([disabled])',
            'a[href]',
            '[tabindex]:not([tabindex="-1"])'
        ];
        
        this.focusableElements = Array.from(
            this.dialog.querySelectorAll(focusableSelectors.join(', '))
        ).filter(el => {
            return el.offsetWidth > 0 && el.offsetHeight > 0;
        });
    }

    /**
     * 调整位置
     * @private
     */
    _adjustPosition() {
        if (this.config.position === 'center' && !this.isDragging) {
            this.dialog.style.top = '50%';
            this.dialog.style.left = '50%';
            this.dialog.style.transform = 'translate(-50%, -50%)';
        }
    }

    /**
     * 显示对话框
     * @param {Object} options - 显示选项
     * @returns {Promise} Promise对象
     */
    show(options = {}) {
        return new Promise((resolve, reject) => {
            if (this.isVisible || this.isAnimating) {
                reject(new Error('对话框已经显示或正在动画中'));
                return;
            }
            
            // 合并选项
            if (options.title) this.setTitle(options.title);
            if (options.content) this.setContent(options.content);
            
            this.isAnimating = true;
            
            // 保存当前焦点
            this.previousActiveElement = document.activeElement;
            
            // 显示对话框
            this.dialog.style.display = 'flex';
            
            // 显示遮罩层
            if (this.overlay) {
                this.overlay.style.display = 'block';
                requestAnimationFrame(() => {
                    this.overlay.style.opacity = '1';
                });
            }
            
            // 应用显示动画
            this._applyShowAnimation().then(() => {
                this.isVisible = true;
                this.isAnimating = false;
                
                // 设置焦点
                if (this.config.autoFocus) {
                    this._setInitialFocus();
                }
                
                // 触发显示事件
                this.emit(UIEvents.DIALOG_SHOWN, {
                    dialog: this,
                    timestamp: Date.now()
                });
                
                resolve(this);
            }).catch(reject);
        });
    }

    /**
     * 隐藏对话框
     * @param {*} result - 关闭结果
     * @returns {Promise} Promise对象
     */
    close(result = null) {
        return new Promise((resolve, reject) => {
            if (!this.isVisible || this.isAnimating) {
                reject(new Error('对话框未显示或正在动画中'));
                return;
            }
            
            this.isAnimating = true;
            
            // 应用隐藏动画
            this._applyHideAnimation().then(() => {
                this.isVisible = false;
                this.isAnimating = false;
                
                // 隐藏对话框
                this.dialog.style.display = 'none';
                
                // 隐藏遮罩层
                if (this.overlay) {
                    this.overlay.style.opacity = '0';
                    setTimeout(() => {
                        this.overlay.style.display = 'none';
                    }, this.config.animationDuration);
                }
                
                // 恢复焦点
                if (this.previousActiveElement) {
                    this.previousActiveElement.focus();
                    this.previousActiveElement = null;
                }
                
                // 触发关闭事件
                this.emit(UIEvents.DIALOG_CLOSED, {
                    dialog: this,
                    result,
                    timestamp: Date.now()
                });
                
                // 销毁对话框
                if (this.config.destroyOnClose) {
                    this.destroy();
                }
                
                resolve(result);
            }).catch(reject);
        });
    }

    /**
     * 应用显示动画
     * @returns {Promise} Promise对象
     * @private
     */
    _applyShowAnimation() {
        return new Promise((resolve) => {
            const dialog = this.dialog;
            
            switch (this.config.animation) {
                case ANIMATION_TYPES.FADE:
                    dialog.style.opacity = '0';
                    requestAnimationFrame(() => {
                        dialog.style.transition = `opacity ${this.config.animationDuration}ms ease`;
                        dialog.style.opacity = '1';
                        setTimeout(resolve, this.config.animationDuration);
                    });
                    break;
                    
                case ANIMATION_TYPES.SLIDE:
                    dialog.style.transform += ' translateY(-50px)';
                    dialog.style.opacity = '0';
                    requestAnimationFrame(() => {
                        dialog.style.transition = `all ${this.config.animationDuration}ms ease`;
                        dialog.style.transform = dialog.style.transform.replace(' translateY(-50px)', '');
                        dialog.style.opacity = '1';
                        setTimeout(resolve, this.config.animationDuration);
                    });
                    break;
                    
                case ANIMATION_TYPES.ZOOM:
                    dialog.style.transform += ' scale(0.8)';
                    dialog.style.opacity = '0';
                    requestAnimationFrame(() => {
                        dialog.style.transition = `all ${this.config.animationDuration}ms ease`;
                        dialog.style.transform = dialog.style.transform.replace(' scale(0.8)', '');
                        dialog.style.opacity = '1';
                        setTimeout(resolve, this.config.animationDuration);
                    });
                    break;
                    
                case ANIMATION_TYPES.BOUNCE:
                    dialog.style.transform += ' scale(0.3)';
                    dialog.style.opacity = '0';
                    requestAnimationFrame(() => {
                        dialog.style.transition = `all ${this.config.animationDuration}ms cubic-bezier(0.68, -0.55, 0.265, 1.55)`;
                        dialog.style.transform = dialog.style.transform.replace(' scale(0.3)', '');
                        dialog.style.opacity = '1';
                        setTimeout(resolve, this.config.animationDuration);
                    });
                    break;
                    
                default:
                    resolve();
                    break;
            }
        });
    }

    /**
     * 应用隐藏动画
     * @returns {Promise} Promise对象
     * @private
     */
    _applyHideAnimation() {
        return new Promise((resolve) => {
            const dialog = this.dialog;
            
            switch (this.config.animation) {
                case ANIMATION_TYPES.FADE:
                    dialog.style.transition = `opacity ${this.config.animationDuration}ms ease`;
                    dialog.style.opacity = '0';
                    setTimeout(resolve, this.config.animationDuration);
                    break;
                    
                case ANIMATION_TYPES.SLIDE:
                    dialog.style.transition = `all ${this.config.animationDuration}ms ease`;
                    dialog.style.transform += ' translateY(-50px)';
                    dialog.style.opacity = '0';
                    setTimeout(resolve, this.config.animationDuration);
                    break;
                    
                case ANIMATION_TYPES.ZOOM:
                    dialog.style.transition = `all ${this.config.animationDuration}ms ease`;
                    dialog.style.transform += ' scale(0.8)';
                    dialog.style.opacity = '0';
                    setTimeout(resolve, this.config.animationDuration);
                    break;
                    
                case ANIMATION_TYPES.BOUNCE:
                    dialog.style.transition = `all ${this.config.animationDuration}ms ease`;
                    dialog.style.transform += ' scale(0.3)';
                    dialog.style.opacity = '0';
                    setTimeout(resolve, this.config.animationDuration);
                    break;
                    
                default:
                    resolve();
                    break;
            }
        });
    }

    /**
     * 设置初始焦点
     * @private
     */
    _setInitialFocus() {
        this._updateFocusableElements();
        
        if (this.focusableElements.length > 0) {
            // 优先聚焦第一个主要按钮
            const primaryButton = this.dialog.querySelector('.dialog-button.primary');
            if (primaryButton) {
                primaryButton.focus();
            } else {
                this.focusableElements[0].focus();
            }
        } else {
            // 如果没有可聚焦元素，聚焦对话框本身
            this.dialog.focus();
        }
    }

    /**
     * 设置标题
     * @param {string} title - 标题
     */
    setTitle(title) {
        this.config.title = title;
        
        if (this.header) {
            const titleElement = this.header.querySelector('.dialog-title');
            if (titleElement) {
                titleElement.textContent = title;
            }
        }
    }

    /**
     * 设置内容
     * @param {string|HTMLElement} content - 内容
     */
    setContent(content) {
        this.config.content = content;
        
        if (this.body) {
            if (typeof content === 'string') {
                this.body.innerHTML = content;
            } else if (content instanceof HTMLElement) {
                this.body.innerHTML = '';
                this.body.appendChild(content);
            }
        }
    }

    /**
     * 获取对话框元素
     * @returns {HTMLElement} 对话框元素
     */
    getElement() {
        return this.dialog;
    }

    /**
     * 获取主体元素
     * @returns {HTMLElement} 主体元素
     */
    getBody() {
        return this.body;
    }

    /**
     * 检查是否可见
     * @returns {boolean} 是否可见
     */
    isShown() {
        return this.isVisible;
    }

    /**
     * 销毁对话框
     */
    destroy() {
        // 如果正在显示，先关闭
        if (this.isVisible) {
            this.close();
        }
        
        // 移除事件监听器
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler);
        }
        
        if (this.resizeHandler) {
            window.removeEventListener('resize', this.resizeHandler);
        }
        
        // 移除DOM元素
        if (this.overlay && this.overlay.parentNode) {
            this.overlay.parentNode.removeChild(this.overlay);
        }
        
        if (this.dialog && this.dialog.parentNode) {
            this.dialog.parentNode.removeChild(this.dialog);
        }
        
        // 清理引用
        this.overlay = null;
        this.dialog = null;
        this.header = null;
        this.body = null;
        this.footer = null;
        this.closeButton = null;
        
        // 调用父类销毁方法
        super.destroy();
        
        this.logger.info('Dialog', 'destroy', '对话框已销毁');
    }
}
// #endregion

// #region 静态方法
/**
 * 创建信息对话框
 * @param {Object} config - 配置
 * @returns {Dialog} 对话框实例
 */
Dialog.info = function(config) {
    return new Dialog({
        type: DIALOG_TYPES.INFO,
        title: '信息',
        buttons: [
            { text: '确定', type: 'primary' }
        ],
        ...config
    });
};

/**
 * 创建成功对话框
 * @param {Object} config - 配置
 * @returns {Dialog} 对话框实例
 */
Dialog.success = function(config) {
    return new Dialog({
        type: DIALOG_TYPES.SUCCESS,
        title: '成功',
        buttons: [
            { text: '确定', type: 'primary' }
        ],
        ...config
    });
};

/**
 * 创建警告对话框
 * @param {Object} config - 配置
 * @returns {Dialog} 对话框实例
 */
Dialog.warning = function(config) {
    return new Dialog({
        type: DIALOG_TYPES.WARNING,
        title: '警告',
        buttons: [
            { text: '确定', type: 'primary' }
        ],
        ...config
    });
};

/**
 * 创建错误对话框
 * @param {Object} config - 配置
 * @returns {Dialog} 对话框实例
 */
Dialog.error = function(config) {
    return new Dialog({
        type: DIALOG_TYPES.ERROR,
        title: '错误',
        buttons: [
            { text: '确定', type: 'primary' }
        ],
        ...config
    });
};

/**
 * 创建确认对话框
 * @param {Object} config - 配置
 * @returns {Dialog} 对话框实例
 */
Dialog.confirm = function(config) {
    return new Dialog({
        type: DIALOG_TYPES.CONFIRM,
        title: '确认',
        buttons: [
            { text: '取消', type: 'default' },
            { text: '确定', type: 'primary' }
        ],
        ...config
    });
};
// #endregion

// #region 导出常量
export { DIALOG_TYPES, DIALOG_SIZES, ANIMATION_TYPES };
// #endregion

// #region 导出
export default Dialog;
// #endregion