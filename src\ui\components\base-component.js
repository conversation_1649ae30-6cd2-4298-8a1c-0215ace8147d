/**
 * @file 基础UI组件 - 所有UI组件的基础类和架构
 * <AUTHOR> Team
 * @description 
 * 这个文件实现了UI组件系统的基础架构，提供：
 * - BaseComponent 基础组件类，所有组件的父类
 * - ComponentRegistry 组件注册表，管理所有已注册组件
 * - ComponentManager 组件管理器，统一管理组件生命周期
 * - 事件驱动架构和组件通信机制
 * - 统一的样式管理和主题系统
 */

// #region 导入依赖模块
import { EventEmitter } from '../../core/events/event-emitter.js';
import { UI_EVENTS } from '../../core/events/event-types.js';
import { validateNonEmpty } from '../../core/utils/validation.js';
import { getLogger } from '../../core/utils/logger.js';
import { ComponentRegistry, defaultComponentRegistry } from './component-registry.js';
import { ComponentManager, defaultComponentManager, createComponent, mountComponent } from './component-manager.js';
// #endregion

// #region BaseComponent 基础组件类
/**
 * @class BaseComponent - 基础UI组件类
 * @description 所有UI组件的基础类，提供统一的组件接口和生命周期管理
 */
export class BaseComponent extends EventEmitter {
    /**
     * 构造函数 - 初始化基础组件
     * @param {Object} config - 组件配置
     * @param {string} config.name - 组件名称
     * @param {string} config.type - 组件类型
     * @param {HTMLElement|string} config.container - 容器元素或选择器
     * @param {Object} config.props - 组件属性
     * @param {Object} config.styles - 组件样式配置
     * @param {Array<string>} config.events - 组件支持的事件列表
     */
    constructor(config = {}) {
        super();
        
        // 初始化日志记录器
        this.logger = getLogger();
        this.logger.startPerformanceMark('component_construction', 'BaseComponent', 'constructor');
        this.logger.debug('BaseComponent', 'constructor', '开始构造UI组件', {
            configKeys: Object.keys(config),
            componentClass: this.constructor.name
        });
        
        // 基础配置
        this.name = config.name || this._generateComponentName();
        this.type = config.type || 'component';
        this.version = config.version || '1.0.0';
        this.id = config.id || this._generateComponentId();
        
        this.logger.trace('BaseComponent', 'constructor', '基础配置设置完成', {
            name: this.name,
            type: this.type,
            version: this.version,
            id: this.id
        });
        
        // 容器和元素
        this.container = this._resolveContainer(config.container);
        this.element = null; // 组件主元素，由子类创建
        
        this.logger.debug('BaseComponent', 'constructor', '容器解析结果', {
            hasContainer: !!this.container,
            containerType: this.container?.tagName,
            containerClasses: this.container?.className
        });
        
        // 组件状态
        this.state = {
            mounted: false,
            visible: true,
            enabled: true,
            loading: false,
            ...config.initialState
        };
        
        // 组件属性
        this.props = {
            className: '',
            theme: 'default',
            size: 'medium',
            ...config.props
        };
        
        // 样式配置
        this.styles = {
            baseClass: `smartoffice-${this.type}`,
            themeClass: `theme-${this.props.theme}`,
            sizeClass: `size-${this.props.size}`,
            ...config.styles
        };
        
        this.logger.trace('BaseComponent', 'constructor', '组件状态和样式配置完成', {
            state: this.state,
            props: this.props,
            styles: this.styles
        });
        
        // 事件配置
        this.supportedEvents = config.events || [];
        this.eventListeners = new Map();
        
        // 子组件管理
        this.children = new Map();
        this.parent = null;
        
        // 组件统计
        this.stats = {
            createdAt: new Date(),
            mountedAt: null,
            renderCount: 0,
            updateCount: 0,
            eventCount: 0
        };
        
        this.logger.debug('BaseComponent', 'constructor', '组件事件和统计配置完成', {
            supportedEventsCount: this.supportedEvents.length,
            createdAt: this.stats.createdAt
        });
        
        // 初始化组件
        this._initializeComponent();
        
        const constructionDuration = this.logger.endPerformanceMark('component_construction', 'BaseComponent', 'constructor');
        this.logger.info('BaseComponent', 'constructor', `🎨 UI组件构造完成: ${this.name}`, {
            type: this.type,
            id: this.id,
            duration: `${constructionDuration?.toFixed(2)}ms`
        });
        
        // 记录组件创建统计
        this.logger.incrementCounter(`component_${this.type}_created`, 'BaseComponent');
    }

    /**
     * 初始化组件 - 执行组件初始化逻辑
     * @private
     */
    _initializeComponent() {
        this.logger.startPerformanceMark('component_initialization', 'BaseComponent', '_initializeComponent');
        this.logger.debug('BaseComponent', '_initializeComponent', '开始初始化组件', {
            componentName: this.name,
            componentType: this.type
        });
        
        try {
            // 验证配置
            this.logger.trace('BaseComponent', '_initializeComponent', '开始配置验证');
            this._validateConfig();
            this.logger.trace('BaseComponent', '_initializeComponent', '配置验证通过');
            
            // 设置组件ID
            if (this.container) {
                this.logger.trace('BaseComponent', '_initializeComponent', '设置容器属性');
                this.container.setAttribute('data-component-id', this.id);
                this.container.setAttribute('data-component-type', this.type);
                this.logger.trace('BaseComponent', '_initializeComponent', '容器属性设置完成');
            } else {
                this.logger.warn('BaseComponent', '_initializeComponent', '无容器可设置属性');
            }
            
            // 触发初始化事件
            this.logger.trace('BaseComponent', '_initializeComponent', '触发初始化事件');
            this.emit(UI_EVENTS.COMPONENT_INITIALIZED, {
                component: this,
                config: this._getComponentInfo()
            });
            
            const initDuration = this.logger.endPerformanceMark('component_initialization', 'BaseComponent', '_initializeComponent');
            this.logger.info('BaseComponent', '_initializeComponent', '组件初始化完成', {
                componentName: this.name,
                duration: `${initDuration?.toFixed(2)}ms`
            });
            
        } catch (error) {
            this.logger.error('BaseComponent', '_initializeComponent', '组件初始化失败', {
                componentName: this.name,
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }

    /**
     * 验证组件配置 - 验证必要的配置参数
     * @private
     */
    _validateConfig() {
        this.logger.debug('BaseComponent', '_validateConfig', '开始配置验证', {
            name: this.name,
            type: this.type,
            hasContainer: !!this.container
        });
        
        try {
            validateNonEmpty(this.name, '组件名称不能为空');
            this.logger.trace('BaseComponent', '_validateConfig', '组件名称验证通过');
            
            validateNonEmpty(this.type, '组件类型不能为空');
            this.logger.trace('BaseComponent', '_validateConfig', '组件类型验证通过');
            
            if (this.container) {
                validateElement(this.container, '容器必须是有效的HTML元素');
                this.logger.trace('BaseComponent', '_validateConfig', '容器元素验证通过');
            } else {
                this.logger.warn('BaseComponent', '_validateConfig', '组件无容器元素');
            }
            
            this.logger.debug('BaseComponent', '_validateConfig', '所有配置验证通过');
            
        } catch (error) {
            this.logger.error('BaseComponent', '_validateConfig', '配置验证失败', {
                error: error.message,
                componentName: this.name,
                componentType: this.type
            });
            throw error;
        }
    }

    /**
     * 解析容器元素 - 将字符串选择器转换为DOM元素
     * @param {HTMLElement|string} container - 容器元素或选择器
     * @returns {HTMLElement|null} 解析后的容器元素
     * @private
     */
    _resolveContainer(container) {
        this.logger.debug('BaseComponent', '_resolveContainer', '开始解析容器元素', {
            containerType: typeof container,
            containerValue: container?.toString?.() || container
        });
        
        if (!container) {
            this.logger.warn('BaseComponent', '_resolveContainer', '未提供容器参数');
            return null;
        }
        
        if (typeof container === 'string') {
            this.logger.trace('BaseComponent', '_resolveContainer', '解析字符串选择器', {
                selector: container
            });
            
            const element = document.querySelector(container);
            if (!element) {
                this.logger.warn('BaseComponent', '_resolveContainer', '未找到容器元素', {
                    selector: container
                });
                return null;
            }
            
            this.logger.trace('BaseComponent', '_resolveContainer', '选择器解析成功', {
                selector: container,
                elementTag: element.tagName,
                elementId: element.id,
                elementClasses: element.className
            });
            return element;
        }
        
        if (container instanceof HTMLElement) {
            this.logger.trace('BaseComponent', '_resolveContainer', 'HTML元素验证通过', {
                elementTag: container.tagName,
                elementId: container.id,
                elementClasses: container.className
            });
            return container;
        }
        
        this.logger.error('BaseComponent', '_resolveContainer', '无效的容器类型', {
            containerType: typeof container,
            containerConstructor: container?.constructor?.name
        });
        return null;
    }

    /**
     * 生成组件名称 - 生成默认的组件名称
     * @returns {string} 组件名称
     * @private
     */
    _generateComponentName() {
        const name = `${this.constructor.name}-${Date.now()}`;
        this.logger.trace('BaseComponent', '_generateComponentName', '生成组件名称', {
            generatedName: name,
            className: this.constructor.name
        });
        return name;
    }

    /**
     * 生成组件ID - 生成唯一的组件ID
     * @returns {string} 组件ID
     * @private
     */
    _generateComponentId() {
        const id = `component-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        this.logger.trace('BaseComponent', '_generateComponentId', '生成组件ID', {
            generatedId: id
        });
        return id;
    }

    /**
     * 渲染组件 - 渲染组件到DOM中（由子类实现）
     * @returns {Promise<HTMLElement>} 渲染后的元素
     * @abstract
     */
    async render() {
        throw new Error('render方法必须由子类实现');
    }

    /**
     * 挂载组件 - 将组件挂载到容器中
     * @param {HTMLElement} container - 目标容器（可选）
     * @returns {Promise<void>}
     */
    async mount(container = null) {
        this.logger.startPerformanceMark('component_mount', 'BaseComponent', 'mount');
        this.logger.info('BaseComponent', 'mount', `🔧 开始挂载组件: ${this.name}`, {
            componentType: this.type,
            hasContainer: !!this.container,
            hasNewContainer: !!container,
            currentlyMounted: this.state.mounted
        });
        
        if (this.state.mounted) {
            this.logger.warn('BaseComponent', 'mount', '组件已经挂载，跳过挂载操作', {
                componentName: this.name,
                mountedAt: this.stats.mountedAt
            });
            return;
        }
        
        try {
            // 更新容器
            if (container) {
                this.logger.debug('BaseComponent', 'mount', '更新组件容器', {
                    oldContainer: this.container?.tagName,
                    newContainer: container?.tagName || typeof container
                });
                this.container = this._resolveContainer(container);
            }
            
            if (!this.container) {
                const error = new Error('无法挂载组件：未找到容器元素');
                this.logger.error('BaseComponent', 'mount', '挂载失败：无容器', {
                    componentName: this.name,
                    providedContainer: !!container
                });
                throw error;
            }
            
            // 渲染组件元素
            this.logger.debug('BaseComponent', 'mount', '开始渲染组件元素');
            this.element = await this.render();
            
            if (!this.element) {
                const error = new Error('渲染方法未返回有效元素');
                this.logger.error('BaseComponent', 'mount', '挂载失败：渲染失败', {
                    componentName: this.name
                });
                throw error;
            }
            
            this.logger.trace('BaseComponent', 'mount', '组件元素渲染完成', {
                elementTag: this.element.tagName,
                elementId: this.element.id,
                elementClasses: this.element.className
            });
            
            // 应用样式
            this.logger.debug('BaseComponent', 'mount', '应用组件样式');
            this._applyStyles();
            
            // 绑定事件
            this.logger.debug('BaseComponent', 'mount', '绑定组件事件');
            this._bindEvents();
            
            // 将元素添加到容器
            this.logger.debug('BaseComponent', 'mount', '将元素添加到容器');
            this.container.appendChild(this.element);
            
            // 更新状态
            this.state.mounted = true;
            this.stats.mountedAt = new Date();
            this.stats.renderCount++;
            
            // 触发挂载事件
            this.logger.trace('BaseComponent', 'mount', '触发组件挂载事件');
            this.emit(UI_EVENTS.COMPONENT_MOUNTED, {
                component: this,
                element: this.element,
                container: this.container
            });
            
            const mountDuration = this.logger.endPerformanceMark('component_mount', 'BaseComponent', 'mount');
            this.logger.info('BaseComponent', 'mount', `✅ 组件挂载成功: ${this.name}`, {
                duration: `${mountDuration?.toFixed(2)}ms`,
                renderCount: this.stats.renderCount,
                elementTag: this.element.tagName
            });
            
            // 记录挂载统计
            this.logger.incrementCounter(`component_${this.type}_mounted`, 'BaseComponent');
            
        } catch (error) {
            this.logger.error('BaseComponent', 'mount', '组件挂载失败', {
                componentName: this.name,
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }

    /**
     * 卸载组件 - 从DOM中移除组件
     * @returns {Promise<void>}
     */
    async unmount() {
        this.logger.startPerformanceMark('component_unmount', 'BaseComponent', 'unmount');
        this.logger.info('BaseComponent', 'unmount', `🔧 开始卸载组件: ${this.name}`, {
            componentType: this.type,
            currentlyMounted: this.state.mounted,
            hasElement: !!this.element,
            childrenCount: this.children.size
        });
        
        if (!this.state.mounted) {
            this.logger.warn('BaseComponent', 'unmount', '组件未挂载，跳过卸载操作', {
                componentName: this.name
            });
            return;
        }
        
        try {
            // 卸载子组件
            if (this.children.size > 0) {
                this.logger.debug('BaseComponent', 'unmount', `卸载 ${this.children.size} 个子组件`);
                await this._unmountChildren();
            }
            
            // 解绑事件
            this.logger.debug('BaseComponent', 'unmount', '解绑组件事件');
            this._unbindEvents();
            
            // 从DOM中移除元素
            if (this.element && this.element.parentNode) {
                this.logger.debug('BaseComponent', 'unmount', '从DOM中移除组件元素');
                this.element.parentNode.removeChild(this.element);
            }
            
            // 清理引用
            this.element = null;
            this.state.mounted = false;
            
            // 触发卸载事件
            this.logger.trace('BaseComponent', 'unmount', '触发组件卸载事件');
            this.emit(UI_EVENTS.COMPONENT_UNMOUNTED, {
                component: this
            });
            
            const unmountDuration = this.logger.endPerformanceMark('component_unmount', 'BaseComponent', 'unmount');
            this.logger.info('BaseComponent', 'unmount', `✅ 组件卸载成功: ${this.name}`, {
                duration: `${unmountDuration?.toFixed(2)}ms`
            });
            
            // 记录卸载统计
            this.logger.incrementCounter(`component_${this.type}_unmounted`, 'BaseComponent');
            
        } catch (error) {
            this.logger.error('BaseComponent', 'unmount', '组件卸载失败', {
                componentName: this.name,
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }

    /**
     * 更新组件 - 更新组件的属性和状态
     * @param {Object} newProps - 新的属性
     * @param {Object} newState - 新的状态
     * @returns {Promise<void>}
     */
    async update(newProps = {}, newState = {}) {
        this.logger.startPerformanceMark('component_update', 'BaseComponent', 'update');
        this.logger.debug('BaseComponent', 'update', `开始更新组件: ${this.name}`, {
            newPropsKeys: Object.keys(newProps),
            newStateKeys: Object.keys(newState),
            currentUpdateCount: this.stats.updateCount
        });
        
        try {
            // 保存旧的属性和状态
            const oldProps = { ...this.props };
            const oldState = { ...this.state };
            
            this.logger.trace('BaseComponent', 'update', '保存当前属性和状态', {
                oldPropsKeys: Object.keys(oldProps),
                oldStateKeys: Object.keys(oldState)
            });
            
            // 更新属性和状态
            this.props = { ...this.props, ...newProps };
            this.state = { ...this.state, ...newState };
            
            this.logger.trace('BaseComponent', 'update', '属性和状态已更新', {
                updatedProps: newProps,
                updatedState: newState
            });
            
            // 检查是否需要重新渲染
            const shouldUpdate = this._shouldUpdate(oldProps, oldState);
            
            this.logger.debug('BaseComponent', 'update', '更新检查结果', {
                shouldUpdate,
                propsChanged: !this._deepEqual(oldProps, this.props),
                stateChanged: !this._deepEqual(oldState, this.state)
            });
            
            if (shouldUpdate && this.state.mounted) {
                this.logger.debug('BaseComponent', 'update', '开始重新渲染组件');
                await this._rerender();
            } else {
                this.logger.debug('BaseComponent', 'update', '跳过重新渲染', {
                    reason: !shouldUpdate ? '不需要更新' : '组件未挂载'
                });
            }
            
            // 更新统计
            this.stats.updateCount++;
            
            // 触发更新事件
            this.logger.trace('BaseComponent', 'update', '触发组件更新事件');
            this.emit(UI_EVENTS.COMPONENT_UPDATED, {
                component: this,
                oldProps,
                oldState,
                newProps: this.props,
                newState: this.state
            });
            
            const updateDuration = this.logger.endPerformanceMark('component_update', 'BaseComponent', 'update');
            this.logger.info('BaseComponent', 'update', `✅ 组件更新完成: ${this.name}`, {
                duration: `${updateDuration?.toFixed(2)}ms`,
                updateCount: this.stats.updateCount,
                wasRerendered: shouldUpdate && this.state.mounted
            });
            
            // 记录更新统计
            this.logger.incrementCounter(`component_${this.type}_updated`, 'BaseComponent');
            
        } catch (error) {
            this.logger.error('BaseComponent', 'update', '组件更新失败', {
                componentName: this.name,
                error: error.message,
                stack: error.stack,
                newProps,
                newState
            });
            throw error;
        }
    }

    /**
     * 检查是否需要更新 - 判断属性或状态变化是否需要重新渲染
     * @param {Object} oldProps - 旧属性
     * @param {Object} oldState - 旧状态
     * @returns {boolean} 是否需要更新
     * @protected
     */
    _shouldUpdate(oldProps, oldState) {
        // 默认实现：检查属性和状态是否有变化
        return !this._deepEqual(oldProps, this.props) || !this._deepEqual(oldState, this.state);
    }

    /**
     * 重新渲染组件 - 重新渲染组件内容
     * @returns {Promise<void>}
     * @private
     */
    async _rerender() {
        if (!this.state.mounted) {
            return;
        }
        
        // 保存旧元素
        const oldElement = this.element;
        
        // 重新渲染
        this.element = await this.render();
        
        // 应用样式
        this._applyStyles();
        
        // 解绑旧事件
        this._unbindEvents();
        
        // 绑定新事件
        this._bindEvents();
        
        // 替换DOM元素
        if (oldElement && oldElement.parentNode) {
            oldElement.parentNode.replaceChild(this.element, oldElement);
        }
        
        // 更新统计
        this.stats.renderCount++;
    }

    /**
     * 应用样式 - 应用组件样式
     * @private
     */
    _applyStyles() {
        if (!this.element) {
            return;
        }
        
        // 添加基础样式类
        this.element.className = [
            this.styles.baseClass,
            this.styles.themeClass,
            this.styles.sizeClass,
            this.props.className,
            this.state.enabled ? '' : 'disabled',
            this.state.visible ? '' : 'hidden',
            this.state.loading ? 'loading' : ''
        ].filter(Boolean).join(' ');
        
        // 设置自定义样式属性
        if (this.props.style) {
            Object.assign(this.element.style, this.props.style);
        }
    }

    /**
     * 绑定事件 - 绑定组件事件监听器
     * @private
     */
    _bindEvents() {
        if (!this.element) {
            return;
        }
        
        // 绑定组件特定事件（由子类实现）
        this._bindComponentEvents();
        
        // 绑定通用事件
        this._bindCommonEvents();
    }

    /**
     * 解绑事件 - 解绑组件事件监听器
     * @private
     */
    _unbindEvents() {
        // 解绑所有事件监听器
        for (const [element, listeners] of this.eventListeners.entries()) {
            for (const [event, handler] of listeners.entries()) {
                element.removeEventListener(event, handler);
            }
        }
        this.eventListeners.clear();
    }

    /**
     * 绑定组件特定事件 - 由子类实现具体的事件绑定
     * @protected
     */
    _bindComponentEvents() {
        // 由子类实现
    }

    /**
     * 绑定通用事件 - 绑定所有组件通用的事件
     * @private
     */
    _bindCommonEvents() {
        if (!this.element) {
            return;
        }
        
        // 绑定点击事件
        this._addEventListener(this.element, 'click', (event) => {
            if (!this.state.enabled) {
                event.preventDefault();
                return;
            }
            
            this.emit(UI_EVENTS.COMPONENT_CLICKED, {
                component: this,
                event,
                target: event.target
            });
        });
        
        // 绑定焦点事件
        this._addEventListener(this.element, 'focus', (event) => {
            this.emit(UI_EVENTS.COMPONENT_FOCUSED, {
                component: this,
                event
            });
        });
        
        this._addEventListener(this.element, 'blur', (event) => {
            this.emit(UI_EVENTS.COMPONENT_BLURRED, {
                component: this,
                event
            });
        });
    }

    /**
     * 添加事件监听器 - 统一的事件监听器添加方法
     * @param {HTMLElement} element - 目标元素
     * @param {string} event - 事件名称
     * @param {Function} handler - 事件处理函数
     * @protected
     */
    _addEventListener(element, event, handler) {
        if (!this.eventListeners.has(element)) {
            this.eventListeners.set(element, new Map());
        }
        
        const elementListeners = this.eventListeners.get(element);
        elementListeners.set(event, handler);
        element.addEventListener(event, handler);
        
        // 更新统计
        this.stats.eventCount++;
    }

    /**
     * 卸载子组件 - 卸载所有子组件
     * @returns {Promise<void>}
     * @private
     */
    async _unmountChildren() {
        const unmountPromises = [];
        
        for (const child of this.children.values()) {
            if (child.unmount) {
                unmountPromises.push(child.unmount());
            }
        }
        
        await Promise.all(unmountPromises);
        this.children.clear();
    }

    /**
     * 添加子组件 - 添加子组件到当前组件
     * @param {string} name - 子组件名称
     * @param {BaseComponent} component - 子组件实例
     */
    addChild(name, component) {
        if (this.children.has(name)) {
            console.warn(`子组件 ${name} 已存在，将被替换`);
        }
        
        this.children.set(name, component);
        component.parent = this;
        
        this.emit(UI_EVENTS.COMPONENT_CHILD_ADDED, {
            parent: this,
            child: component,
            name
        });
    }

    /**
     * 移除子组件 - 移除指定的子组件
     * @param {string} name - 子组件名称
     * @returns {Promise<void>}
     */
    async removeChild(name) {
        const child = this.children.get(name);
        if (!child) {
            console.warn(`子组件 ${name} 不存在`);
            return;
        }
        
        if (child.unmount) {
            await child.unmount();
        }
        
        child.parent = null;
        this.children.delete(name);
        
        this.emit(UI_EVENTS.COMPONENT_CHILD_REMOVED, {
            parent: this,
            child,
            name
        });
    }

    /**
     * 获取子组件 - 根据名称获取子组件
     * @param {string} name - 子组件名称
     * @returns {BaseComponent|null} 子组件实例
     */
    getChild(name) {
        return this.children.get(name) || null;
    }

    /**
     * 显示组件 - 显示组件
     */
    show() {
        this.update({}, { visible: true });
    }

    /**
     * 隐藏组件 - 隐藏组件
     */
    hide() {
        this.update({}, { visible: false });
    }

    /**
     * 启用组件 - 启用组件交互
     */
    enable() {
        this.update({}, { enabled: true });
    }

    /**
     * 禁用组件 - 禁用组件交互
     */
    disable() {
        this.update({}, { enabled: false });
    }

    /**
     * 设置加载状态 - 设置组件加载状态
     * @param {boolean} loading - 是否加载中
     */
    setLoading(loading) {
        this.update({}, { loading });
    }

    /**
     * 获取组件信息 - 获取组件的基本信息
     * @returns {Object} 组件信息
     */
    _getComponentInfo() {
        return {
            name: this.name,
            type: this.type,
            version: this.version,
            id: this.id,
            state: { ...this.state },
            props: { ...this.props },
            children: Array.from(this.children.keys()),
            stats: { ...this.stats }
        };
    }

    /**
     * 深度比较 - 比较两个对象是否相等
     * @param {*} obj1 - 对象1
     * @param {*} obj2 - 对象2
     * @returns {boolean} 是否相等
     * @private
     */
    _deepEqual(obj1, obj2) {
        if (obj1 === obj2) {
            return true;
        }
        
        if (obj1 == null || obj2 == null) {
            return obj1 === obj2;
        }
        
        if (typeof obj1 !== typeof obj2) {
            return false;
        }
        
        if (typeof obj1 !== 'object') {
            return obj1 === obj2;
        }
        
        const keys1 = Object.keys(obj1);
        const keys2 = Object.keys(obj2);
        
        if (keys1.length !== keys2.length) {
            return false;
        }
        
        for (const key of keys1) {
            if (!keys2.includes(key) || !this._deepEqual(obj1[key], obj2[key])) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 销毁组件 - 完全销毁组件和释放资源
     * @returns {Promise<void>}
     */
    async destroy() {
        try {
            // 卸载组件
            if (this.state.mounted) {
                await this.unmount();
            }
            
            // 清理事件监听器
            this.removeAllListeners();
            
            // 清理引用
            this.container = null;
            this.element = null;
            this.parent = null;
            
            // 触发销毁事件
            this.emit(UI_EVENTS.COMPONENT_DESTROYED, { component: this });
            
        } catch (error) {
            console.error(`销毁组件 ${this.name} 时出错:`, error);
        }
    }
}
// #endregion