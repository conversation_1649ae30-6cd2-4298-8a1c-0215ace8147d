/**
 * @file 统一配置管理器 - 整合后的配置管理模块
 * <AUTHOR> Team
 * @description 
 * 这是重构后的统一配置管理器，整合了js/config/config-manager.js和src/config/configuration-manager.js的功能
 * 提供完整的配置加载、验证、监听和存储功能
 * 
 * 重构说明：
 * - 整合了ConfigManager和ConfigurationManager的功能
 * - 消除了重复的配置处理代码
 * - 建立了统一的配置接口
 * - 支持配置热更新和验证
 */

// #region 导入依赖
import { EventEmitter } from './events.js';
import { getLogger } from './logger.js';
// #endregion

// #region 默认配置定义
/**
 * 默认应用配置
 */
const DEFAULT_CONFIG = {
    // 应用基础配置
    app: {
        name: 'SmartOffice 2.0',
        version: '2.0.0',
        environment: 'production',
        debug: false,
        language: 'zh-CN',
        theme: 'default'
    },
    
    // 日志配置
    logging: {
        level: 'INFO',
        enableConsole: true,
        enableStorage: true,
        enablePerformance: true,
        maxLogEntries: 1000
    },
    
    // 渲染器配置
    renderer: {
        defaultFormat: 'html',
        enableCache: true,
        cacheTimeout: 1800000, // 30分钟
        quality: 'high',
        timeout: 30000
    },
    
    // 文档配置
    document: {
        defaultType: 'receipt',
        autoSave: true,
        autoSaveInterval: 30000, // 30秒
        maxHistory: 50,
        enableValidation: true
    },
    
    // 导出配置
    export: {
        defaultFormat: 'pdf',
        quality: 'high',
        enableWatermark: false,
        compression: true,
        timeout: 60000
    },
    
    // UI配置
    ui: {
        theme: 'light',
        language: 'zh-CN',
        enableAnimations: true,
        enableTooltips: true,
        autoHideEmptyFields: true,
        responsiveBreakpoint: 768
    },
    
    // 性能配置
    performance: {
        enableLazyLoading: true,
        enableCaching: true,
        maxCacheSize: 100,
        enableCompression: true,
        enableMinification: false
    },
    
    // 安全配置
    security: {
        enableCSP: true,
        enableSanitization: true,
        maxFileSize: 10485760, // 10MB
        allowedFileTypes: ['jpg', 'jpeg', 'png', 'gif', 'pdf']
    },
    
    // 集成配置
    integrations: {
        gemini: {
            enabled: true,
            apiKey: '',
            model: 'gemini-pro',
            timeout: 30000,
            maxRetries: 3
        }
    }
};
// #endregion

// #region 配置管理器类
/**
 * @class ConfigManager - 统一配置管理器
 * @description 提供完整的配置管理功能
 */
export class ConfigManager extends EventEmitter {
    /**
     * 构造函数 - 初始化配置管理器
     * @param {Object} initialConfig - 初始配置
     */
    constructor(initialConfig = {}) {
        super();
        
        this.logger = getLogger();
        this.config = {};
        this.watchers = new Map();
        this.validators = new Map();
        this.isInitialized = false;
        this.configHistory = [];
        this.maxHistorySize = 10;
        
        // 合并默认配置和初始配置
        this.config = this._deepMerge(DEFAULT_CONFIG, initialConfig);
        
        this.logger.info('ConfigManager', 'constructor', '配置管理器创建完成');
        
        // 初始化配置管理器
        this._initialize();
    }
    
    /**
     * 初始化配置管理器
     * @private
     */
    _initialize() {
        try {
            this.logger.info('ConfigManager', '_initialize', '初始化配置管理器');
            
            // 加载存储的配置
            this._loadStoredConfig();
            
            // 验证配置
            this._validateConfig();
            
            // 设置默认验证器
            this._setupDefaultValidators();
            
            // 标记为已初始化
            this.isInitialized = true;
            
            // 触发初始化完成事件
            this.emit('config:initialized', {
                config: this.config,
                timestamp: new Date()
            });
            
            this.logger.info('ConfigManager', '_initialize', '配置管理器初始化完成');
            
        } catch (error) {
            this.logger.error('ConfigManager', '_initialize', '配置管理器初始化失败', error);
            throw error;
        }
    }
    
    /**
     * 加载存储的配置
     * @private
     */
    _loadStoredConfig() {
        try {
            if (typeof localStorage !== 'undefined') {
                const storedConfig = localStorage.getItem('smartoffice_config');
                if (storedConfig) {
                    const parsedConfig = JSON.parse(storedConfig);
                    this.config = this._deepMerge(this.config, parsedConfig);
                    this.logger.info('ConfigManager', '_loadStoredConfig', '已加载存储的配置');
                }
            }
        } catch (error) {
            this.logger.warn('ConfigManager', '_loadStoredConfig', '加载存储配置失败', error);
        }
    }
    
    /**
     * 验证配置
     * @private
     */
    _validateConfig() {
        const errors = [];
        
        // 验证必需的配置项
        const requiredPaths = [
            'app.name',
            'app.version',
            'logging.level',
            'renderer.defaultFormat'
        ];
        
        for (const path of requiredPaths) {
            if (!this.get(path)) {
                errors.push(`缺少必需的配置项: ${path}`);
            }
        }
        
        if (errors.length > 0) {
            throw new Error(`配置验证失败: ${errors.join(', ')}`);
        }
    }
    
    /**
     * 设置默认验证器
     * @private
     */
    _setupDefaultValidators() {
        // 日志级别验证器
        this.addValidator('logging.level', (value) => {
            const validLevels = ['TRACE', 'DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL'];
            return validLevels.includes(value);
        });
        
        // 渲染器格式验证器
        this.addValidator('renderer.defaultFormat', (value) => {
            const validFormats = ['html', 'pdf', 'image', 'print'];
            return validFormats.includes(value);
        });
        
        // 文档类型验证器
        this.addValidator('document.defaultType', (value) => {
            const validTypes = ['receipt', 'invoice', 'quotation', 'driver_agreement'];
            return validTypes.includes(value);
        });
    }
    
    /**
     * 获取配置值
     * @param {string} path - 配置路径（支持点号分隔）
     * @param {any} defaultValue - 默认值
     * @returns {any} 配置值
     */
    get(path, defaultValue = undefined) {
        const keys = path.split('.');
        let current = this.config;
        
        for (const key of keys) {
            if (current && typeof current === 'object' && key in current) {
                current = current[key];
            } else {
                return defaultValue;
            }
        }
        
        return current;
    }
    
    /**
     * 设置配置值
     * @param {string} path - 配置路径
     * @param {any} value - 配置值
     * @param {boolean} persist - 是否持久化存储
     * @returns {boolean} 是否设置成功
     */
    set(path, value, persist = true) {
        try {
            // 验证配置值
            if (!this._validateValue(path, value)) {
                this.logger.warn('ConfigManager', 'set', `配置值验证失败: ${path} = ${value}`);
                return false;
            }
            
            // 保存历史记录
            this._saveToHistory(path, this.get(path), value);
            
            // 设置配置值
            const keys = path.split('.');
            let current = this.config;
            
            for (let i = 0; i < keys.length - 1; i++) {
                const key = keys[i];
                if (!current[key] || typeof current[key] !== 'object') {
                    current[key] = {};
                }
                current = current[key];
            }
            
            const lastKey = keys[keys.length - 1];
            const oldValue = current[lastKey];
            current[lastKey] = value;
            
            // 触发配置变更事件
            this.emit('config:changed', {
                path,
                oldValue,
                newValue: value,
                timestamp: new Date()
            });
            
            // 触发特定路径的监听器
            this._notifyWatchers(path, value, oldValue);
            
            // 持久化存储
            if (persist) {
                this._persistConfig();
            }
            
            this.logger.debug('ConfigManager', 'set', `配置已更新: ${path} = ${value}`);
            
            return true;
            
        } catch (error) {
            this.logger.error('ConfigManager', 'set', `设置配置失败: ${path}`, error);
            return false;
        }
    }
    
    /**
     * 批量设置配置
     * @param {Object} configObject - 配置对象
     * @param {boolean} persist - 是否持久化存储
     * @returns {boolean} 是否设置成功
     */
    setMultiple(configObject, persist = true) {
        try {
            const oldConfig = this._deepClone(this.config);
            
            // 合并配置
            this.config = this._deepMerge(this.config, configObject);
            
            // 触发批量变更事件
            this.emit('config:batch-changed', {
                oldConfig,
                newConfig: this.config,
                changes: configObject,
                timestamp: new Date()
            });
            
            // 持久化存储
            if (persist) {
                this._persistConfig();
            }
            
            this.logger.info('ConfigManager', 'setMultiple', '批量配置更新完成');
            
            return true;
            
        } catch (error) {
            this.logger.error('ConfigManager', 'setMultiple', '批量设置配置失败', error);
            return false;
        }
    }
    
    /**
     * 添加配置监听器
     * @param {string} path - 配置路径
     * @param {Function} callback - 回调函数
     * @returns {Function} 取消监听的函数
     */
    watch(path, callback) {
        if (!this.watchers.has(path)) {
            this.watchers.set(path, new Set());
        }
        
        this.watchers.get(path).add(callback);
        
        this.logger.debug('ConfigManager', 'watch', `添加配置监听器: ${path}`);
        
        // 返回取消监听的函数
        return () => {
            this.unwatch(path, callback);
        };
    }
    
    /**
     * 移除配置监听器
     * @param {string} path - 配置路径
     * @param {Function} callback - 回调函数
     */
    unwatch(path, callback) {
        if (this.watchers.has(path)) {
            this.watchers.get(path).delete(callback);
            if (this.watchers.get(path).size === 0) {
                this.watchers.delete(path);
            }
        }
        
        this.logger.debug('ConfigManager', 'unwatch', `移除配置监听器: ${path}`);
    }
    
    /**
     * 添加配置验证器
     * @param {string} path - 配置路径
     * @param {Function} validator - 验证函数
     */
    addValidator(path, validator) {
        if (!this.validators.has(path)) {
            this.validators.set(path, new Set());
        }
        
        this.validators.get(path).add(validator);
        
        this.logger.debug('ConfigManager', 'addValidator', `添加配置验证器: ${path}`);
    }
    
    /**
     * 重置配置到默认值
     * @param {string} path - 配置路径（可选，不提供则重置全部）
     */
    reset(path = null) {
        if (path) {
            // 重置特定路径
            const defaultValue = this._getDefaultValue(path);
            if (defaultValue !== undefined) {
                this.set(path, defaultValue);
            }
        } else {
            // 重置全部配置
            this.config = this._deepClone(DEFAULT_CONFIG);
            this._persistConfig();
            
            this.emit('config:reset', {
                timestamp: new Date()
            });
        }
        
        this.logger.info('ConfigManager', 'reset', `配置已重置: ${path || 'all'}`);
    }
    
    /**
     * 获取配置历史记录
     * @returns {Array} 历史记录
     */
    getHistory() {
        return [...this.configHistory];
    }
    
    /**
     * 导出配置
     * @param {string} format - 导出格式 ('json', 'yaml')
     * @returns {string} 导出的配置数据
     */
    export(format = 'json') {
        switch (format.toLowerCase()) {
            case 'json':
                return JSON.stringify(this.config, null, 2);
            case 'yaml':
                // 简单的YAML导出（实际项目中可能需要专门的YAML库）
                return this._toYaml(this.config);
            default:
                throw new Error(`不支持的导出格式: ${format}`);
        }
    }
    
    /**
     * 导入配置
     * @param {string} configData - 配置数据
     * @param {string} format - 数据格式 ('json', 'yaml')
     * @returns {boolean} 是否导入成功
     */
    import(configData, format = 'json') {
        try {
            let importedConfig;
            
            switch (format.toLowerCase()) {
                case 'json':
                    importedConfig = JSON.parse(configData);
                    break;
                case 'yaml':
                    importedConfig = this._fromYaml(configData);
                    break;
                default:
                    throw new Error(`不支持的导入格式: ${format}`);
            }
            
            return this.setMultiple(importedConfig);
            
        } catch (error) {
            this.logger.error('ConfigManager', 'import', '导入配置失败', error);
            return false;
        }
    }
    
    /**
     * 验证配置值
     * @param {string} path - 配置路径
     * @param {any} value - 配置值
     * @returns {boolean} 是否有效
     * @private
     */
    _validateValue(path, value) {
        if (!this.validators.has(path)) {
            return true;
        }
        
        const validators = this.validators.get(path);
        for (const validator of validators) {
            if (!validator(value)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 通知监听器
     * @param {string} path - 配置路径
     * @param {any} newValue - 新值
     * @param {any} oldValue - 旧值
     * @private
     */
    _notifyWatchers(path, newValue, oldValue) {
        if (this.watchers.has(path)) {
            const callbacks = this.watchers.get(path);
            for (const callback of callbacks) {
                try {
                    callback(newValue, oldValue, path);
                } catch (error) {
                    this.logger.error('ConfigManager', '_notifyWatchers', '监听器执行错误', error);
                }
            }
        }
    }
    
    /**
     * 保存到历史记录
     * @param {string} path - 配置路径
     * @param {any} oldValue - 旧值
     * @param {any} newValue - 新值
     * @private
     */
    _saveToHistory(path, oldValue, newValue) {
        this.configHistory.push({
            path,
            oldValue,
            newValue,
            timestamp: new Date()
        });
        
        // 限制历史记录大小
        if (this.configHistory.length > this.maxHistorySize) {
            this.configHistory = this.configHistory.slice(-this.maxHistorySize);
        }
    }
    
    /**
     * 持久化配置
     * @private
     */
    _persistConfig() {
        try {
            if (typeof localStorage !== 'undefined') {
                localStorage.setItem('smartoffice_config', JSON.stringify(this.config));
            }
        } catch (error) {
            this.logger.warn('ConfigManager', '_persistConfig', '配置持久化失败', error);
        }
    }
    
    /**
     * 深度合并对象
     * @param {Object} target - 目标对象
     * @param {Object} source - 源对象
     * @returns {Object} 合并后的对象
     * @private
     */
    _deepMerge(target, source) {
        const result = { ...target };
        
        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                    result[key] = this._deepMerge(result[key] || {}, source[key]);
                } else {
                    result[key] = source[key];
                }
            }
        }
        
        return result;
    }
    
    /**
     * 深度克隆对象
     * @param {Object} obj - 要克隆的对象
     * @returns {Object} 克隆后的对象
     * @private
     */
    _deepClone(obj) {
        return JSON.parse(JSON.stringify(obj));
    }
    
    /**
     * 获取默认值
     * @param {string} path - 配置路径
     * @returns {any} 默认值
     * @private
     */
    _getDefaultValue(path) {
        const keys = path.split('.');
        let current = DEFAULT_CONFIG;
        
        for (const key of keys) {
            if (current && typeof current === 'object' && key in current) {
                current = current[key];
            } else {
                return undefined;
            }
        }
        
        return current;
    }
    
    /**
     * 转换为YAML格式（简单实现）
     * @param {Object} obj - 对象
     * @param {number} indent - 缩进级别
     * @returns {string} YAML字符串
     * @private
     */
    _toYaml(obj, indent = 0) {
        const spaces = '  '.repeat(indent);
        let yaml = '';
        
        for (const [key, value] of Object.entries(obj)) {
            if (value && typeof value === 'object' && !Array.isArray(value)) {
                yaml += `${spaces}${key}:\n${this._toYaml(value, indent + 1)}`;
            } else {
                yaml += `${spaces}${key}: ${JSON.stringify(value)}\n`;
            }
        }
        
        return yaml;
    }
    
    /**
     * 从YAML格式解析（简单实现）
     * @param {string} yaml - YAML字符串
     * @returns {Object} 解析后的对象
     * @private
     */
    _fromYaml(yaml) {
        // 这里应该使用专门的YAML解析库
        // 简单实现仅作示例
        throw new Error('YAML解析需要专门的库支持');
    }
    
    /**
     * 销毁配置管理器
     */
    destroy() {
        this.logger.info('ConfigManager', 'destroy', '销毁配置管理器');
        
        // 清理监听器
        this.watchers.clear();
        this.validators.clear();
        
        // 清理历史记录
        this.configHistory = [];
        
        // 移除所有事件监听器
        this.removeAllListeners();
        
        // 重置状态
        this.isInitialized = false;
    }
}
// #endregion

// #region 全局配置实例
let globalConfigManager = null;

/**
 * 创建配置管理器
 * @param {Object} config - 初始配置
 * @returns {ConfigManager} 配置管理器实例
 */
export function createConfigManager(config = {}) {
    return new ConfigManager(config);
}

/**
 * 获取全局配置管理器
 * @returns {ConfigManager} 全局配置管理器实例
 */
export function getConfigManager() {
    if (!globalConfigManager) {
        globalConfigManager = new ConfigManager();
    }
    return globalConfigManager;
}

// 便捷函数
export function getConfig(path, defaultValue) {
    return getConfigManager().get(path, defaultValue);
}

export function setConfig(path, value, persist = true) {
    return getConfigManager().set(path, value, persist);
}

export function watchConfig(path, callback) {
    return getConfigManager().watch(path, callback);
}
// #endregion

// #region 自动注册到全局
if (typeof window !== 'undefined') {
    window.SmartOfficeConfig = {
        ConfigManager,
        createConfigManager,
        getConfigManager,
        getConfig,
        setConfig,
        watchConfig,
        DEFAULT_CONFIG
    };
}
// #endregion
