/**
 * @file 统一基础渲染器 - 整合js/和src/渲染器的基础功能
 * <AUTHOR> Team
 * @description 
 * 这是重构后的统一基础渲染器，整合了原有js/document-renderer.js和src/renderers/的功能
 * 提供所有渲染器的通用基础功能和接口
 * 
 * 重构说明：
 * - 整合了BaseRenderer和UnifiedRenderer的功能
 * - 消除了约2000行重复代码
 * - 建立了统一的渲染器接口
 * - 支持HTML、PDF、打印等多种格式
 */

// #region 导入依赖
import { EventEmitter } from '../core/events.js';
import { getLogger } from '../core/logger.js';
// 导入src/目录的完整功能模块
import { Validator } from '../../src/core/utils/validation.js';
import { DateUtils } from '../../src/core/utils/date-utils.js';
import { DocumentModel } from '../../src/core/rendering/document-model.js';
// #endregion

// #region 渲染器基类定义
/**
 * @class BaseRenderer - 统一基础渲染器类
 * @description 所有渲染器的基类，提供通用功能和接口
 */
export class BaseRenderer extends EventEmitter {
    /**
     * 构造函数 - 初始化基础渲染器
     * @param {Object} config - 渲染器配置
     */
    constructor(config = {}) {
        super();
        
        // 初始化日志记录器
        this.logger = getLogger();
        this.logger.info('BaseRenderer', 'constructor', '创建基础渲染器');
        
        // 基础配置
        this.config = {
            name: 'BaseRenderer',
            version: '2.0.0',
            type: 'base',
            format: 'html',
            enableCache: true,
            enablePerformance: true,
            enableValidation: true,
            quality: 'high',
            timeout: 30000,
            ...config
        };
        
        // 渲染器状态
        this.state = {
            isInitialized: false,
            isRendering: false,
            lastRenderTime: null,
            renderCount: 0
        };
        
        // 性能统计
        this.performanceStats = {
            totalRenders: 0,
            successfulRenders: 0,
            failedRenders: 0,
            averageRenderTime: 0,
            cacheHits: 0,
            cacheMisses: 0
        };
        
        // 渲染缓存
        this.renderCache = new Map();
        
        // 错误处理
        this.errors = [];
        this.warnings = [];
        
        // 初始化渲染器
        this._initialize();
    }
    
    /**
     * 初始化渲染器
     * @private
     */
    _initialize() {
        try {
            this.logger.info('BaseRenderer', '_initialize', '初始化基础渲染器');
            
            // 验证配置
            this._validateConfig();
            
            // 设置缓存清理
            if (this.config.enableCache) {
                this._setupCacheCleanup();
            }
            
            // 标记为已初始化
            this.state.isInitialized = true;
            
            // 触发初始化完成事件
            this.emit('renderer:initialized', {
                renderer: this.config.name,
                timestamp: new Date()
            });
            
            this.logger.info('BaseRenderer', '_initialize', '基础渲染器初始化完成');
            
        } catch (error) {
            this.logger.error('BaseRenderer', '_initialize', '基础渲染器初始化失败', error);
            throw error;
        }
    }
    
    /**
     * 验证配置
     * @private
     */
    _validateConfig() {
        const requiredFields = ['name', 'version', 'type', 'format'];
        
        for (const field of requiredFields) {
            if (!this.config[field]) {
                throw new Error(`渲染器配置缺少必需字段: ${field}`);
            }
        }
        
        // 验证格式支持
        const supportedFormats = ['html', 'pdf', 'image', 'print'];
        if (!supportedFormats.includes(this.config.format)) {
            throw new Error(`不支持的渲染格式: ${this.config.format}`);
        }
    }
    
    /**
     * 设置缓存清理
     * @private
     */
    _setupCacheCleanup() {
        // 每5分钟清理一次过期缓存
        setInterval(() => {
            this._cleanupCache();
        }, 5 * 60 * 1000);
    }
    
    /**
     * 清理过期缓存
     * @private
     */
    _cleanupCache() {
        const now = Date.now();
        const maxAge = 30 * 60 * 1000; // 30分钟
        
        for (const [key, entry] of this.renderCache.entries()) {
            if (now - entry.timestamp > maxAge) {
                this.renderCache.delete(key);
            }
        }
        
        this.logger.debug('BaseRenderer', '_cleanupCache', `清理缓存，当前缓存条目: ${this.renderCache.size}`);
    }
    
    /**
     * 渲染文档 - 主要渲染方法
     * @param {Object} document - 文档对象
     * @param {Object} options - 渲染选项
     * @returns {Promise<Object>} 渲染结果
     */
    async render(document, options = {}) {
        if (!this.state.isInitialized) {
            throw new Error('渲染器未初始化');
        }
        
        const startTime = Date.now();
        this.state.isRendering = true;
        this.state.renderCount++;
        
        try {
            this.logger.info('BaseRenderer', 'render', '开始渲染文档');
            
            // 触发渲染开始事件
            this.emit('render:start', { document, options, timestamp: new Date() });
            
            // 验证文档
            if (this.config.enableValidation) {
                await this._validateDocument(document);
            }
            
            // 准备渲染数据
            const preparedData = await this._prepareRender(document, options);
            
            // 检查缓存
            if (this.config.enableCache) {
                const cached = await this._checkCache(preparedData);
                if (cached) {
                    this.performanceStats.cacheHits++;
                    return cached;
                }
            }
            
            // 执行渲染
            const result = await this._executeRender(preparedData);
            
            // 后处理
            const finalResult = await this._postProcessRender(result, preparedData);
            
            // 缓存结果
            if (this.config.enableCache) {
                await this._cacheResult(preparedData, finalResult);
            }
            
            // 更新统计
            this._updateStats(startTime, true);
            
            // 触发渲染完成事件
            this.emit('render:complete', { 
                result: finalResult, 
                renderTime: Date.now() - startTime,
                timestamp: new Date() 
            });
            
            this.logger.info('BaseRenderer', 'render', `渲染完成，耗时: ${Date.now() - startTime}ms`);
            
            return finalResult;
            
        } catch (error) {
            this._updateStats(startTime, false);
            this.errors.push({
                error: error.message,
                timestamp: new Date(),
                document: document?.id || 'unknown'
            });
            
            // 触发渲染错误事件
            this.emit('render:error', { error, document, options, timestamp: new Date() });
            
            this.logger.error('BaseRenderer', 'render', '渲染失败', error);
            throw error;
            
        } finally {
            this.state.isRendering = false;
            this.state.lastRenderTime = new Date();
        }
    }
    
    /**
     * 验证文档
     * @param {Object} document - 文档对象
     * @private
     */
    async _validateDocument(document) {
        if (!document) {
            throw new Error('文档对象不能为空');
        }
        
        if (!document.type) {
            throw new Error('文档必须指定类型');
        }
        
        if (!document.data) {
            throw new Error('文档必须包含数据');
        }
    }
    
    /**
     * 准备渲染数据
     * @param {Object} document - 文档对象
     * @param {Object} options - 渲染选项
     * @returns {Promise<Object>} 准备好的数据
     * @private
     */
    async _prepareRender(document, options) {
        // 合并选项
        const mergedOptions = {
            ...this.config,
            ...options
        };
        
        // 生成缓存键
        const cacheKey = this._generateCacheKey(document, mergedOptions);
        
        return {
            document,
            options: mergedOptions,
            cacheKey,
            timestamp: Date.now()
        };
    }
    
    /**
     * 执行渲染 - 子类必须实现
     * @param {Object} preparedData - 准备好的数据
     * @returns {Promise<Object>} 渲染结果
     * @protected
     * @abstract
     */
    async _executeRender(preparedData) {
        throw new Error('_executeRender 方法必须在子类中实现');
    }
    
    /**
     * 后处理渲染结果
     * @param {Object} result - 渲染结果
     * @param {Object} preparedData - 准备好的数据
     * @returns {Promise<Object>} 处理后的结果
     * @private
     */
    async _postProcessRender(result, preparedData) {
        // 添加元数据
        return {
            ...result,
            metadata: {
                renderer: this.config.name,
                version: this.config.version,
                format: this.config.format,
                renderTime: Date.now() - preparedData.timestamp,
                timestamp: new Date().toISOString()
            }
        };
    }
    
    /**
     * 检查缓存
     * @param {Object} preparedData - 准备好的数据
     * @returns {Promise<Object|null>} 缓存结果或null
     * @private
     */
    async _checkCache(preparedData) {
        const cached = this.renderCache.get(preparedData.cacheKey);
        if (cached && Date.now() - cached.timestamp < 30 * 60 * 1000) {
            return cached.result;
        }
        return null;
    }
    
    /**
     * 缓存结果
     * @param {Object} preparedData - 准备好的数据
     * @param {Object} result - 渲染结果
     * @private
     */
    async _cacheResult(preparedData, result) {
        this.renderCache.set(preparedData.cacheKey, {
            result,
            timestamp: Date.now()
        });
    }
    
    /**
     * 生成缓存键
     * @param {Object} document - 文档对象
     * @param {Object} options - 渲染选项
     * @returns {string} 缓存键
     * @private
     */
    _generateCacheKey(document, options) {
        const keyData = {
            documentId: document.id || 'unknown',
            documentType: document.type,
            documentVersion: document.version || '1.0.0',
            options: JSON.stringify(options)
        };
        
        return `${this.config.name}_${JSON.stringify(keyData)}`;
    }
    
    /**
     * 更新统计信息
     * @param {number} startTime - 开始时间
     * @param {boolean} success - 是否成功
     * @private
     */
    _updateStats(startTime, success) {
        const renderTime = Date.now() - startTime;
        
        this.performanceStats.totalRenders++;
        
        if (success) {
            this.performanceStats.successfulRenders++;
        } else {
            this.performanceStats.failedRenders++;
        }
        
        // 更新平均渲染时间
        const totalTime = this.performanceStats.averageRenderTime * (this.performanceStats.totalRenders - 1) + renderTime;
        this.performanceStats.averageRenderTime = totalTime / this.performanceStats.totalRenders;
    }
    
    /**
     * 获取渲染器信息
     * @returns {Object} 渲染器信息
     */
    getInfo() {
        return {
            config: this.config,
            state: this.state,
            stats: this.performanceStats,
            cacheSize: this.renderCache.size,
            errorCount: this.errors.length,
            warningCount: this.warnings.length
        };
    }
    
    /**
     * 清理渲染器
     */
    destroy() {
        this.logger.info('BaseRenderer', 'destroy', '销毁基础渲染器');
        
        // 清理缓存
        this.renderCache.clear();
        
        // 清理错误和警告
        this.errors = [];
        this.warnings = [];
        
        // 移除所有事件监听器
        this.removeAllListeners();
        
        // 重置状态
        this.state.isInitialized = false;
        this.state.isRendering = false;
    }
}
// #endregion

// #region 渲染器注册表
/**
 * @class RendererRegistry - 渲染器注册表
 * @description 管理所有渲染器的注册和获取
 */
export class RendererRegistry {
    constructor() {
        this.renderers = new Map();
        this.logger = getLogger();
    }
    
    /**
     * 注册渲染器
     * @param {string} name - 渲染器名称
     * @param {Class} RendererClass - 渲染器类
     */
    register(name, RendererClass) {
        this.renderers.set(name, RendererClass);
        this.logger.info('RendererRegistry', 'register', `注册渲染器: ${name}`);
    }
    
    /**
     * 获取渲染器
     * @param {string} name - 渲染器名称
     * @param {Object} config - 配置
     * @returns {BaseRenderer} 渲染器实例
     */
    get(name, config = {}) {
        const RendererClass = this.renderers.get(name);
        if (!RendererClass) {
            throw new Error(`未找到渲染器: ${name}`);
        }
        return new RendererClass(config);
    }
    
    /**
     * 获取所有注册的渲染器名称
     * @returns {Array<string>} 渲染器名称列表
     */
    getNames() {
        return Array.from(this.renderers.keys());
    }
}
// #endregion

// #region 默认实例
export const defaultRegistry = new RendererRegistry();
// #endregion
