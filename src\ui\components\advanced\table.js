/**
 * @file 表格组件 - SmartOffice 高级UI组件
 * <AUTHOR> Team
 * @description 
 * 这个文件实现了表格组件，提供：
 * - 数据展示和渲染
 * - 排序和筛选
 * - 分页功能
 * - 行选择
 * - 列配置
 * - 虚拟滚动
 * - 导出功能
 */

// #region 导入依赖模块
import { BaseComponent } from '../base-component.js';
import { UIEvents } from '../../../core/events/event-types.js';
import { getLogger } from '../../../core/utils/logger.js';
// #endregion

// #region 常量定义
/**
 * 排序方向
 */
const SORT_DIRECTIONS = {
    ASC: 'asc',
    DESC: 'desc'
};

/**
 * 列类型
 */
const COLUMN_TYPES = {
    TEXT: 'text',
    NUMBER: 'number',
    DATE: 'date',
    BOOLEAN: 'boolean',
    CUSTOM: 'custom'
};

/**
 * 选择模式
 */
const SELECTION_MODES = {
    NONE: 'none',
    SINGLE: 'single',
    MULTIPLE: 'multiple'
};

/**
 * 表格大小
 */
const TABLE_SIZES = {
    SMALL: 'small',
    MEDIUM: 'medium',
    LARGE: 'large'
};

/**
 * 默认配置
 */
const DEFAULT_CONFIG = {
    columns: [],
    data: [],
    size: TABLE_SIZES.MEDIUM,
    bordered: true,
    striped: false,
    hoverable: true,
    sortable: true,
    filterable: false,
    selectable: SELECTION_MODES.NONE,
    pagination: false,
    pageSize: 10,
    currentPage: 1,
    virtualScroll: false,
    rowHeight: 40,
    maxHeight: null,
    loading: false,
    emptyText: '暂无数据',
    showHeader: true,
    resizable: false
};
// #endregion

// #region Table 表格组件类
/**
 * @class Table - 表格组件
 * @description 数据表格展示组件
 */
export class Table extends BaseComponent {
    /**
     * 构造函数 - 初始化表格组件
     * @param {Object} config - 表格配置
     */
    constructor(config = {}) {
        super(config);
        
        this.logger = getLogger();
        this.logger.debug('Table', 'constructor', '初始化表格组件');
        
        // 配置
        this.config = {
            ...DEFAULT_CONFIG,
            ...config
        };
        
        // 状态
        this.sortColumn = null;
        this.sortDirection = null;
        this.selectedRows = new Set();
        this.filteredData = [];
        this.displayData = [];
        this.filters = new Map();
        
        // DOM元素
        this.element = null;
        this.tableElement = null;
        this.headerElement = null;
        this.bodyElement = null;
        this.footerElement = null;
        this.loadingElement = null;
        this.emptyElement = null;
        this.paginationElement = null;
        
        // 虚拟滚动相关
        this.virtualScrollData = {
            startIndex: 0,
            endIndex: 0,
            visibleCount: 0,
            scrollTop: 0
        };
        
        // 创建表格
        this._createTable();
        this._processData();
        this._render();
        
        this.logger.info('Table', 'constructor', '表格组件初始化完成', {
            config: this.config
        });
    }

    /**
     * 创建表格
     * @private
     */
    _createTable() {
        // 创建主容器
        this.element = document.createElement('div');
        this.element.className = `table-container table-${this.config.size}`;
        
        // 设置容器样式
        this.element.style.cssText = `
            position: relative;
            background-color: var(--so-background-color, #ffffff);
            border: ${this.config.bordered ? '1px solid var(--so-border-color, #d9d9d9)' : 'none'};
            border-radius: var(--so-border-radius-base, 6px);
            overflow: hidden;
        `;
        
        // 创建表格元素
        this.tableElement = document.createElement('table');
        this.tableElement.className = 'table';
        this.tableElement.style.cssText = `
            width: 100%;
            border-collapse: collapse;
            font-size: var(--so-font-size-base, 14px);
            color: var(--so-text-color, #000000d9);
        `;
        
        // 创建表头
        if (this.config.showHeader) {
            this._createHeader();
        }
        
        // 创建表体
        this._createBody();
        
        // 创建加载状态
        this._createLoading();
        
        // 创建空状态
        this._createEmpty();
        
        // 创建分页
        if (this.config.pagination) {
            this._createPagination();
        }
        
        this.element.appendChild(this.tableElement);
        
        // 绑定事件
        this._bindEvents();
    }

    /**
     * 创建表头
     * @private
     */
    _createHeader() {
        this.headerElement = document.createElement('thead');
        this.headerElement.className = 'table-header';
        
        const headerRow = document.createElement('tr');
        headerRow.className = 'table-header-row';
        
        // 选择列
        if (this.config.selectable !== SELECTION_MODES.NONE) {
            const selectCell = document.createElement('th');
            selectCell.className = 'table-cell table-cell-select';
            selectCell.style.cssText = this._getCellStyles(true);
            
            if (this.config.selectable === SELECTION_MODES.MULTIPLE) {
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.className = 'table-select-all';
                checkbox.addEventListener('change', (e) => {
                    this._handleSelectAll(e.target.checked);
                });
                selectCell.appendChild(checkbox);
            }
            
            headerRow.appendChild(selectCell);
        }
        
        // 数据列
        this.config.columns.forEach((column, index) => {
            const cell = document.createElement('th');
            cell.className = 'table-cell table-header-cell';
            cell.style.cssText = this._getCellStyles(true);
            cell.setAttribute('data-column', index);
            
            // 设置列宽
            if (column.width) {
                cell.style.width = typeof column.width === 'number' ? 
                    `${column.width}px` : column.width;
            }
            
            // 创建列内容
            const content = document.createElement('div');
            content.className = 'table-header-content';
            content.style.cssText = `
                display: flex;
                align-items: center;
                justify-content: ${column.align || 'left'};
            `;
            
            // 列标题
            const title = document.createElement('span');
            title.textContent = column.title || column.key;
            content.appendChild(title);
            
            // 排序图标
            if (this.config.sortable && column.sortable !== false) {
                const sortIcon = document.createElement('span');
                sortIcon.className = 'table-sort-icon';
                sortIcon.innerHTML = '↕';
                sortIcon.style.cssText = `
                    margin-left: var(--so-margin-xs, 4px);
                    opacity: 0.5;
                    cursor: pointer;
                    user-select: none;
                `;
                
                content.appendChild(sortIcon);
                cell.style.cursor = 'pointer';
                
                // 排序事件
                cell.addEventListener('click', () => {
                    this._handleSort(column.key, index);
                });
            }
            
            // 筛选图标
            if (this.config.filterable && column.filterable !== false) {
                const filterIcon = document.createElement('span');
                filterIcon.className = 'table-filter-icon';
                filterIcon.innerHTML = '🔍';
                filterIcon.style.cssText = `
                    margin-left: var(--so-margin-xs, 4px);
                    opacity: 0.5;
                    cursor: pointer;
                    user-select: none;
                `;
                
                content.appendChild(filterIcon);
                
                // 筛选事件
                filterIcon.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this._showFilterMenu(column, filterIcon);
                });
            }
            
            cell.appendChild(content);
            headerRow.appendChild(cell);
        });
        
        this.headerElement.appendChild(headerRow);
        this.tableElement.appendChild(this.headerElement);
    }

    /**
     * 创建表体
     * @private
     */
    _createBody() {
        this.bodyElement = document.createElement('tbody');
        this.bodyElement.className = 'table-body';
        
        // 虚拟滚动容器
        if (this.config.virtualScroll) {
            this.bodyElement.style.cssText = `
                display: block;
                height: ${this.config.maxHeight || '400px'};
                overflow-y: auto;
            `;
            
            this.bodyElement.addEventListener('scroll', () => {
                this._handleVirtualScroll();
            });
        }
        
        this.tableElement.appendChild(this.bodyElement);
    }

    /**
     * 创建加载状态
     * @private
     */
    _createLoading() {
        this.loadingElement = document.createElement('div');
        this.loadingElement.className = 'table-loading';
        this.loadingElement.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 10;
        `;
        
        const spinner = document.createElement('div');
        spinner.className = 'table-loading-spinner';
        spinner.textContent = '加载中...';
        spinner.style.cssText = `
            padding: var(--so-padding-md, 16px);
            background-color: var(--so-background-color, #ffffff);
            border-radius: var(--so-border-radius-base, 6px);
            box-shadow: var(--so-box-shadow, 0 2px 8px rgba(0, 0, 0, 0.15));
        `;
        
        this.loadingElement.appendChild(spinner);
        this.element.appendChild(this.loadingElement);
    }

    /**
     * 创建空状态
     * @private
     */
    _createEmpty() {
        this.emptyElement = document.createElement('div');
        this.emptyElement.className = 'table-empty';
        this.emptyElement.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: var(--so-text-color-secondary, #00000073);
            display: none;
        `;
        
        this.emptyElement.textContent = this.config.emptyText;
        this.element.appendChild(this.emptyElement);
    }

    /**
     * 创建分页
     * @private
     */
    _createPagination() {
        this.paginationElement = document.createElement('div');
        this.paginationElement.className = 'table-pagination';
        this.paginationElement.style.cssText = `
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--so-padding-md, 16px);
            border-top: 1px solid var(--so-border-color-light, #f0f0f0);
        `;
        
        this.element.appendChild(this.paginationElement);
    }

    /**
     * 获取单元格样式
     * @param {boolean} isHeader - 是否为表头
     * @returns {string} 样式字符串
     * @private
     */
    _getCellStyles(isHeader = false) {
        const sizeMap = {
            [TABLE_SIZES.SMALL]: '8px 12px',
            [TABLE_SIZES.MEDIUM]: '12px 16px',
            [TABLE_SIZES.LARGE]: '16px 20px'
        };
        
        const padding = sizeMap[this.config.size];
        
        return `
            padding: ${padding};
            border-bottom: ${this.config.bordered ? '1px solid var(--so-border-color-light, #f0f0f0)' : 'none'};
            text-align: left;
            font-weight: ${isHeader ? '500' : 'normal'};
            background-color: ${isHeader ? 'var(--so-background-color-light, #fafafa)' : 'transparent'};
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        `;
    }

    /**
     * 绑定事件
     * @private
     */
    _bindEvents() {
        // 行悬停效果
        if (this.config.hoverable) {
            this.bodyElement.addEventListener('mouseover', (e) => {
                const row = e.target.closest('tr');
                if (row) {
                    row.style.backgroundColor = 'var(--so-background-color-light, #fafafa)';
                }
            });
            
            this.bodyElement.addEventListener('mouseout', (e) => {
                const row = e.target.closest('tr');
                if (row && !row.classList.contains('table-row-selected')) {
                    row.style.backgroundColor = '';
                }
            });
        }
        
        // 行点击事件
        this.bodyElement.addEventListener('click', (e) => {
            const row = e.target.closest('tr');
            if (row) {
                const rowIndex = parseInt(row.getAttribute('data-row'));
                this._handleRowClick(rowIndex, e);
            }
        });
    }

    /**
     * 处理数据
     * @private
     */
    _processData() {
        // 应用筛选
        this.filteredData = this._applyFilters(this.config.data);
        
        // 应用排序
        if (this.sortColumn) {
            this.filteredData = this._applySorting(this.filteredData);
        }
        
        // 应用分页
        if (this.config.pagination) {
            this.displayData = this._applyPagination(this.filteredData);
        } else {
            this.displayData = this.filteredData;
        }
        
        // 虚拟滚动
        if (this.config.virtualScroll) {
            this._calculateVirtualScroll();
        }
    }

    /**
     * 应用筛选
     * @param {Array} data - 数据
     * @returns {Array} 筛选后的数据
     * @private
     */
    _applyFilters(data) {
        if (this.filters.size === 0) {
            return data;
        }
        
        return data.filter(row => {
            for (const [key, filterValue] of this.filters) {
                const cellValue = row[key];
                if (!this._matchFilter(cellValue, filterValue)) {
                    return false;
                }
            }
            return true;
        });
    }

    /**
     * 匹配筛选条件
     * @param {*} value - 值
     * @param {*} filter - 筛选条件
     * @returns {boolean} 是否匹配
     * @private
     */
    _matchFilter(value, filter) {
        if (filter === null || filter === undefined || filter === '') {
            return true;
        }
        
        const valueStr = String(value).toLowerCase();
        const filterStr = String(filter).toLowerCase();
        
        return valueStr.includes(filterStr);
    }

    /**
     * 应用排序
     * @param {Array} data - 数据
     * @returns {Array} 排序后的数据
     * @private
     */
    _applySorting(data) {
        const column = this.config.columns.find(col => col.key === this.sortColumn);
        if (!column) return data;
        
        return [...data].sort((a, b) => {
            let valueA = a[this.sortColumn];
            let valueB = b[this.sortColumn];
            
            // 自定义排序函数
            if (column.sorter && typeof column.sorter === 'function') {
                return column.sorter(a, b) * (this.sortDirection === SORT_DIRECTIONS.DESC ? -1 : 1);
            }
            
            // 默认排序
            if (column.type === COLUMN_TYPES.NUMBER) {
                valueA = Number(valueA) || 0;
                valueB = Number(valueB) || 0;
            } else if (column.type === COLUMN_TYPES.DATE) {
                valueA = new Date(valueA).getTime() || 0;
                valueB = new Date(valueB).getTime() || 0;
            } else {
                valueA = String(valueA).toLowerCase();
                valueB = String(valueB).toLowerCase();
            }
            
            let result = 0;
            if (valueA < valueB) result = -1;
            else if (valueA > valueB) result = 1;
            
            return result * (this.sortDirection === SORT_DIRECTIONS.DESC ? -1 : 1);
        });
    }

    /**
     * 应用分页
     * @param {Array} data - 数据
     * @returns {Array} 分页后的数据
     * @private
     */
    _applyPagination(data) {
        const start = (this.config.currentPage - 1) * this.config.pageSize;
        const end = start + this.config.pageSize;
        return data.slice(start, end);
    }

    /**
     * 计算虚拟滚动
     * @private
     */
    _calculateVirtualScroll() {
        const containerHeight = this.bodyElement.clientHeight;
        const visibleCount = Math.ceil(containerHeight / this.config.rowHeight) + 2;
        const startIndex = Math.floor(this.virtualScrollData.scrollTop / this.config.rowHeight);
        const endIndex = Math.min(startIndex + visibleCount, this.displayData.length);
        
        this.virtualScrollData = {
            ...this.virtualScrollData,
            startIndex,
            endIndex,
            visibleCount
        };
    }

    /**
     * 渲染表格
     * @private
     */
    _render() {
        // 显示/隐藏加载状态
        this.loadingElement.style.display = this.config.loading ? 'flex' : 'none';
        
        // 显示/隐藏空状态
        const hasData = this.displayData.length > 0;
        this.emptyElement.style.display = !hasData && !this.config.loading ? 'block' : 'none';
        
        // 渲染表体
        this._renderBody();
        
        // 渲染分页
        if (this.config.pagination) {
            this._renderPagination();
        }
        
        // 更新排序图标
        this._updateSortIcons();
    }

    /**
     * 渲染表体
     * @private
     */
    _renderBody() {
        // 清空表体
        this.bodyElement.innerHTML = '';
        
        if (this.displayData.length === 0) {
            return;
        }
        
        // 确定渲染范围
        let startIndex = 0;
        let endIndex = this.displayData.length;
        
        if (this.config.virtualScroll) {
            startIndex = this.virtualScrollData.startIndex;
            endIndex = this.virtualScrollData.endIndex;
            
            // 创建占位空间
            if (startIndex > 0) {
                const spacer = document.createElement('tr');
                spacer.style.height = `${startIndex * this.config.rowHeight}px`;
                this.bodyElement.appendChild(spacer);
            }
        }
        
        // 渲染行
        for (let i = startIndex; i < endIndex; i++) {
            const rowData = this.displayData[i];
            const row = this._createRow(rowData, i);
            this.bodyElement.appendChild(row);
        }
        
        // 虚拟滚动底部占位
        if (this.config.virtualScroll && endIndex < this.displayData.length) {
            const spacer = document.createElement('tr');
            spacer.style.height = `${(this.displayData.length - endIndex) * this.config.rowHeight}px`;
            this.bodyElement.appendChild(spacer);
        }
    }

    /**
     * 创建行
     * @param {Object} rowData - 行数据
     * @param {number} rowIndex - 行索引
     * @returns {HTMLElement} 行元素
     * @private
     */
    _createRow(rowData, rowIndex) {
        const row = document.createElement('tr');
        row.className = 'table-row';
        row.setAttribute('data-row', rowIndex);
        
        // 设置行样式
        let rowStyles = this._getCellStyles();
        
        if (this.config.striped && rowIndex % 2 === 1) {
            rowStyles += 'background-color: var(--so-background-color-light, #fafafa);';
        }
        
        if (this.selectedRows.has(rowIndex)) {
            row.classList.add('table-row-selected');
            rowStyles += 'background-color: var(--so-primary-color-light, #e6f7ff);';
        }
        
        row.style.cssText = rowStyles;
        
        // 选择列
        if (this.config.selectable !== SELECTION_MODES.NONE) {
            const selectCell = document.createElement('td');
            selectCell.className = 'table-cell table-cell-select';
            selectCell.style.cssText = this._getCellStyles();
            
            if (this.config.selectable === SELECTION_MODES.MULTIPLE) {
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.checked = this.selectedRows.has(rowIndex);
                checkbox.addEventListener('change', (e) => {
                    e.stopPropagation();
                    this._handleRowSelect(rowIndex, e.target.checked);
                });
                selectCell.appendChild(checkbox);
            } else {
                const radio = document.createElement('input');
                radio.type = 'radio';
                radio.name = 'table-select';
                radio.checked = this.selectedRows.has(rowIndex);
                radio.addEventListener('change', (e) => {
                    e.stopPropagation();
                    if (e.target.checked) {
                        this._handleRowSelect(rowIndex, true);
                    }
                });
                selectCell.appendChild(radio);
            }
            
            row.appendChild(selectCell);
        }
        
        // 数据列
        this.config.columns.forEach((column) => {
            const cell = document.createElement('td');
            cell.className = 'table-cell';
            cell.style.cssText = this._getCellStyles();
            
            // 设置对齐方式
            if (column.align) {
                cell.style.textAlign = column.align;
            }
            
            // 设置列宽
            if (column.width) {
                cell.style.width = typeof column.width === 'number' ? 
                    `${column.width}px` : column.width;
            }
            
            // 渲染单元格内容
            const content = this._renderCellContent(rowData, column, rowIndex);
            if (typeof content === 'string') {
                cell.textContent = content;
            } else if (content instanceof HTMLElement) {
                cell.appendChild(content);
            }
            
            row.appendChild(cell);
        });
        
        return row;
    }

    /**
     * 渲染单元格内容
     * @param {Object} rowData - 行数据
     * @param {Object} column - 列配置
     * @param {number} rowIndex - 行索引
     * @returns {string|HTMLElement} 内容
     * @private
     */
    _renderCellContent(rowData, column, rowIndex) {
        const value = rowData[column.key];
        
        // 自定义渲染函数
        if (column.render && typeof column.render === 'function') {
            return column.render(value, rowData, rowIndex);
        }
        
        // 默认渲染
        switch (column.type) {
            case COLUMN_TYPES.BOOLEAN:
                return value ? '是' : '否';
                
            case COLUMN_TYPES.DATE:
                if (value) {
                    const date = new Date(value);
                    return date.toLocaleDateString();
                }
                return '';
                
            case COLUMN_TYPES.NUMBER:
                if (typeof value === 'number') {
                    return column.precision !== undefined ? 
                        value.toFixed(column.precision) : value.toString();
                }
                return value || '';
                
            default:
                return value || '';
        }
    }

    /**
     * 渲染分页
     * @private
     */
    _renderPagination() {
        if (!this.paginationElement) return;
        
        const totalCount = this.filteredData.length;
        const totalPages = Math.ceil(totalCount / this.config.pageSize);
        const currentPage = this.config.currentPage;
        
        this.paginationElement.innerHTML = '';
        
        // 信息显示
        const info = document.createElement('div');
        info.className = 'pagination-info';
        info.textContent = `共 ${totalCount} 条记录，第 ${currentPage} / ${totalPages} 页`;
        this.paginationElement.appendChild(info);
        
        // 分页控件
        const controls = document.createElement('div');
        controls.className = 'pagination-controls';
        controls.style.cssText = `
            display: flex;
            align-items: center;
            gap: var(--so-margin-sm, 8px);
        `;
        
        // 上一页
        const prevButton = this._createPaginationButton('上一页', currentPage > 1, () => {
            this.setCurrentPage(currentPage - 1);
        });
        controls.appendChild(prevButton);
        
        // 页码
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            const pageButton = this._createPaginationButton(i.toString(), true, () => {
                this.setCurrentPage(i);
            }, i === currentPage);
            controls.appendChild(pageButton);
        }
        
        // 下一页
        const nextButton = this._createPaginationButton('下一页', currentPage < totalPages, () => {
            this.setCurrentPage(currentPage + 1);
        });
        controls.appendChild(nextButton);
        
        this.paginationElement.appendChild(controls);
    }

    /**
     * 创建分页按钮
     * @param {string} text - 按钮文本
     * @param {boolean} enabled - 是否启用
     * @param {Function} onClick - 点击事件
     * @param {boolean} active - 是否激活
     * @returns {HTMLElement} 按钮元素
     * @private
     */
    _createPaginationButton(text, enabled, onClick, active = false) {
        const button = document.createElement('button');
        button.textContent = text;
        button.disabled = !enabled;
        button.className = `pagination-button ${active ? 'active' : ''}`;
        
        button.style.cssText = `
            padding: var(--so-padding-sm, 8px) var(--so-padding-md, 16px);
            border: 1px solid var(--so-border-color, #d9d9d9);
            border-radius: var(--so-border-radius-base, 6px);
            background-color: ${active ? 'var(--so-primary-color, #1890ff)' : 'var(--so-background-color, #ffffff)'};
            color: ${active ? '#ffffff' : 'var(--so-text-color, #000000d9)'};
            cursor: ${enabled ? 'pointer' : 'not-allowed'};
            font-size: var(--so-font-size-base, 14px);
            transition: all 0.2s ease;
            opacity: ${enabled ? '1' : '0.5'};
        `;
        
        if (enabled) {
            button.addEventListener('click', onClick);
            
            if (!active) {
                button.addEventListener('mouseenter', () => {
                    button.style.borderColor = 'var(--so-primary-color, #1890ff)';
                    button.style.color = 'var(--so-primary-color, #1890ff)';
                });
                
                button.addEventListener('mouseleave', () => {
                    button.style.borderColor = 'var(--so-border-color, #d9d9d9)';
                    button.style.color = 'var(--so-text-color, #000000d9)';
                });
            }
        }
        
        return button;
    }

    /**
     * 更新排序图标
     * @private
     */
    _updateSortIcons() {
        if (!this.headerElement) return;
        
        const sortIcons = this.headerElement.querySelectorAll('.table-sort-icon');
        sortIcons.forEach((icon, index) => {
            const column = this.config.columns[index];
            if (column && column.key === this.sortColumn) {
                icon.innerHTML = this.sortDirection === SORT_DIRECTIONS.ASC ? '↑' : '↓';
                icon.style.opacity = '1';
            } else {
                icon.innerHTML = '↕';
                icon.style.opacity = '0.5';
            }
        });
    }

    /**
     * 处理排序
     * @param {string} columnKey - 列键
     * @param {number} columnIndex - 列索引
     * @private
     */
    _handleSort(columnKey, columnIndex) {
        if (this.sortColumn === columnKey) {
            // 切换排序方向
            this.sortDirection = this.sortDirection === SORT_DIRECTIONS.ASC ? 
                SORT_DIRECTIONS.DESC : SORT_DIRECTIONS.ASC;
        } else {
            // 新列排序
            this.sortColumn = columnKey;
            this.sortDirection = SORT_DIRECTIONS.ASC;
        }
        
        // 重新处理数据并渲染
        this._processData();
        this._render();
        
        // 触发排序事件
        this.emit(UIEvents.TABLE_SORTED, {
            column: columnKey,
            direction: this.sortDirection,
            data: this.displayData
        });
    }

    /**
     * 处理行选择
     * @param {number} rowIndex - 行索引
     * @param {boolean} selected - 是否选中
     * @private
     */
    _handleRowSelect(rowIndex, selected) {
        if (this.config.selectable === SELECTION_MODES.SINGLE) {
            this.selectedRows.clear();
        }
        
        if (selected) {
            this.selectedRows.add(rowIndex);
        } else {
            this.selectedRows.delete(rowIndex);
        }
        
        // 重新渲染
        this._render();
        
        // 触发选择事件
        this.emit(UIEvents.TABLE_SELECTION_CHANGED, {
            selectedRows: Array.from(this.selectedRows),
            selectedData: Array.from(this.selectedRows).map(index => this.displayData[index])
        });
    }

    /**
     * 处理全选
     * @param {boolean} selectAll - 是否全选
     * @private
     */
    _handleSelectAll(selectAll) {
        if (selectAll) {
            for (let i = 0; i < this.displayData.length; i++) {
                this.selectedRows.add(i);
            }
        } else {
            this.selectedRows.clear();
        }
        
        // 重新渲染
        this._render();
        
        // 触发选择事件
        this.emit(UIEvents.TABLE_SELECTION_CHANGED, {
            selectedRows: Array.from(this.selectedRows),
            selectedData: Array.from(this.selectedRows).map(index => this.displayData[index])
        });
    }

    /**
     * 处理行点击
     * @param {number} rowIndex - 行索引
     * @param {Event} event - 事件对象
     * @private
     */
    _handleRowClick(rowIndex, event) {
        const rowData = this.displayData[rowIndex];
        
        // 触发行点击事件
        this.emit(UIEvents.TABLE_ROW_CLICKED, {
            rowIndex,
            rowData,
            event
        });
        
        // 如果配置了行选择，处理选择
        if (this.config.selectable !== SELECTION_MODES.NONE && 
            !event.target.closest('input')) {
            const isSelected = this.selectedRows.has(rowIndex);
            this._handleRowSelect(rowIndex, !isSelected);
        }
    }

    /**
     * 处理虚拟滚动
     * @private
     */
    _handleVirtualScroll() {
        this.virtualScrollData.scrollTop = this.bodyElement.scrollTop;
        this._calculateVirtualScroll();
        this._renderBody();
    }

    /**
     * 显示筛选菜单
     * @param {Object} column - 列配置
     * @param {HTMLElement} trigger - 触发元素
     * @private
     */
    _showFilterMenu(column, trigger) {
        // 简单的筛选输入框实现
        const input = document.createElement('input');
        input.type = 'text';
        input.placeholder = `筛选 ${column.title}`;
        input.value = this.filters.get(column.key) || '';
        
        input.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            z-index: 1000;
            padding: var(--so-padding-sm, 8px);
            border: 1px solid var(--so-border-color, #d9d9d9);
            border-radius: var(--so-border-radius-base, 6px);
            background-color: var(--so-background-color, #ffffff);
            box-shadow: var(--so-box-shadow, 0 2px 8px rgba(0, 0, 0, 0.15));
        `;
        
        // 添加到触发元素的父容器
        const container = trigger.closest('th');
        container.style.position = 'relative';
        container.appendChild(input);
        
        input.focus();
        
        // 处理输入
        const handleInput = () => {
            const value = input.value.trim();
            if (value) {
                this.filters.set(column.key, value);
            } else {
                this.filters.delete(column.key);
            }
            
            this._processData();
            this._render();
        };
        
        input.addEventListener('input', handleInput);
        input.addEventListener('blur', () => {
            setTimeout(() => {
                if (input.parentNode) {
                    input.parentNode.removeChild(input);
                }
            }, 200);
        });
        
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                input.blur();
            }
        });
    }

    // #region 公共方法
    /**
     * 设置数据
     * @param {Array} data - 数据
     */
    setData(data) {
        this.config.data = data || [];
        this.selectedRows.clear();
        this._processData();
        this._render();
        
        this.emit(UIEvents.TABLE_DATA_CHANGED, {
            data: this.config.data
        });
    }

    /**
     * 获取数据
     * @returns {Array} 数据
     */
    getData() {
        return this.config.data;
    }

    /**
     * 获取筛选后的数据
     * @returns {Array} 筛选后的数据
     */
    getFilteredData() {
        return this.filteredData;
    }

    /**
     * 获取当前显示的数据
     * @returns {Array} 当前显示的数据
     */
    getDisplayData() {
        return this.displayData;
    }

    /**
     * 设置当前页
     * @param {number} page - 页码
     */
    setCurrentPage(page) {
        const totalPages = Math.ceil(this.filteredData.length / this.config.pageSize);
        this.config.currentPage = Math.max(1, Math.min(page, totalPages));
        
        this._processData();
        this._render();
        
        this.emit(UIEvents.TABLE_PAGE_CHANGED, {
            currentPage: this.config.currentPage,
            totalPages
        });
    }

    /**
     * 设置页面大小
     * @param {number} size - 页面大小
     */
    setPageSize(size) {
        this.config.pageSize = Math.max(1, size);
        this.config.currentPage = 1;
        
        this._processData();
        this._render();
    }

    /**
     * 获取选中的行
     * @returns {Array} 选中的行数据
     */
    getSelectedRows() {
        return Array.from(this.selectedRows).map(index => this.displayData[index]);
    }

    /**
     * 设置选中的行
     * @param {Array} indices - 行索引数组
     */
    setSelectedRows(indices) {
        this.selectedRows.clear();
        indices.forEach(index => {
            if (index >= 0 && index < this.displayData.length) {
                this.selectedRows.add(index);
            }
        });
        
        this._render();
    }

    /**
     * 清除选择
     */
    clearSelection() {
        this.selectedRows.clear();
        this._render();
    }

    /**
     * 设置加载状态
     * @param {boolean} loading - 是否加载中
     */
    setLoading(loading) {
        this.config.loading = loading;
        this._render();
    }

    /**
     * 刷新表格
     */
    refresh() {
        this._processData();
        this._render();
    }

    /**
     * 导出数据
     * @param {string} format - 导出格式 (csv, json)
     * @returns {string} 导出的数据
     */
    exportData(format = 'csv') {
        const data = this.getFilteredData();
        
        switch (format.toLowerCase()) {
            case 'csv':
                return this._exportToCsv(data);
            case 'json':
                return JSON.stringify(data, null, 2);
            default:
                throw new Error(`不支持的导出格式: ${format}`);
        }
    }

    /**
     * 导出为CSV
     * @param {Array} data - 数据
     * @returns {string} CSV字符串
     * @private
     */
    _exportToCsv(data) {
        if (data.length === 0) return '';
        
        // 表头
        const headers = this.config.columns.map(col => col.title || col.key);
        const csvContent = [headers.join(',')];
        
        // 数据行
        data.forEach(row => {
            const values = this.config.columns.map(col => {
                const value = row[col.key] || '';
                // 处理包含逗号或引号的值
                if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                    return `"${value.replace(/"/g, '""')}"`;
                }
                return value;
            });
            csvContent.push(values.join(','));
        });
        
        return csvContent.join('\n');
    }

    /**
     * 获取元素
     * @returns {HTMLElement} 表格元素
     */
    getElement() {
        return this.element;
    }

    /**
     * 销毁表格
     */
    destroy() {
        // 移除DOM元素
        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }
        
        // 清理引用
        this.element = null;
        this.tableElement = null;
        this.headerElement = null;
        this.bodyElement = null;
        this.footerElement = null;
        this.loadingElement = null;
        this.emptyElement = null;
        this.paginationElement = null;
        
        // 清理数据
        this.selectedRows.clear();
        this.filters.clear();
        
        // 调用父类销毁方法
        super.destroy();
        
        this.logger.info('Table', 'destroy', '表格已销毁');
    }
    // #endregion
}
// #endregion

// #region 导出常量
export { 
    SORT_DIRECTIONS, 
    COLUMN_TYPES, 
    SELECTION_MODES, 
    TABLE_SIZES 
};
// #endregion

// #region 导出
export default Table;
// #endregion