/**
 * @file 模板系统统一导出 - 提供所有模板类和工厂函数的统一入口
 * <AUTHOR> Team
 * @description 
 * 这个文件是模板系统的统一导出入口，包括：
 * - 所有模板类的导出（BaseTemplate、ReceiptTemplate、InvoiceTemplate、DriverAgreementTemplate）
 * - 模板注册表和渲染器的导出
 * - 工厂函数的便捷导出
 * - 预设模板和配置的导出
 */

// #region 导入依赖模块
import { getLogger } from '../core/utils/logger.js';
// #endregion

// #region 基础模板引擎导出
export {
    BaseTemplate,
    TemplateRegistry,
    TemplateRenderer,
    defaultRegistry,
    defaultRenderer
} from './base-template.js';
// #endregion

// #region 收据模板导出
export {
    ReceiptTemplate,
    createReceiptTemplate,
    createPresetReceiptTemplate
} from './receipt-template.js';
// #endregion

// #region 发票模板导出
export {
    InvoiceTemplate,
    createInvoiceTemplate,
    createPresetInvoiceTemplate
} from './invoice-template.js';
// #endregion

// #region 司机协议模板导出
export {
    DriverAgreementTemplate,
    createDriverAgreementTemplate,
    createPresetDriverAgreementTemplate
} from './driver-agreement-template.js';
// #endregion

// #region 模板工厂类定义
/**
 * @class TemplateFactory - 模板工厂类
 * @description 统一的模板创建工厂，支持按类型创建各种模板
 */
export class TemplateFactory {
    /**
     * 创建模板实例 - 根据类型创建对应的模板
     * @param {string} type - 模板类型 ('receipt', 'invoice', 'driver-agreement')
     * @param {Object} config - 模板配置
     * @returns {BaseTemplate} 模板实例
     * @static
     */
    static create(type, config = {}) {
        switch (type) {
            case 'receipt':
                return createReceiptTemplate(config);
            case 'invoice':
                return createInvoiceTemplate(config);
            case 'driver-agreement':
                return createDriverAgreementTemplate(config);
            default:
                throw new Error(`不支持的模板类型: ${type}`);
        }
    }

    /**
     * 创建预设模板 - 根据类型和主题创建预设模板
     * @param {string} type - 模板类型
     * @param {string} theme - 主题名称
     * @param {Object} config - 额外配置
     * @returns {BaseTemplate} 模板实例
     * @static
     */
    static createPreset(type, theme, config = {}) {
        switch (type) {
            case 'receipt':
                return createPresetReceiptTemplate(theme, config);
            case 'invoice':
                return createPresetInvoiceTemplate(theme, config);
            case 'driver-agreement':
                return createPresetDriverAgreementTemplate(theme, config);
            default:
                throw new Error(`不支持的模板类型: ${type}`);
        }
    }

    /**
     * 获取支持的模板类型 - 返回所有支持的模板类型列表
     * @returns {Array<Object>} 模板类型信息数组
     * @static
     */
    static getSupportedTypes() {
        return [
            {
                id: 'receipt',
                name: '收据',
                description: '用于生成收据文档',
                themes: ['classic', 'modern', 'elegant', 'compact']
            },
            {
                id: 'invoice',
                name: '发票',
                description: '用于生成发票文档',
                themes: ['classic', 'modern', 'elegant', 'formal']
            },
            {
                id: 'driver-agreement',
                name: '司机协议',
                description: '用于生成司机服务协议文档',
                themes: ['standard', 'formal', 'simple', 'detailed']
            }
        ];
    }

    /**
     * 验证模板类型 - 检查指定的模板类型是否受支持
     * @param {string} type - 模板类型
     * @returns {boolean} 是否支持
     * @static
     */
    static isSupported(type) {
        const supportedTypes = this.getSupportedTypes();
        return supportedTypes.some(t => t.id === type);
    }

    /**
     * 获取模板主题 - 获取指定模板类型支持的主题列表
     * @param {string} type - 模板类型
     * @returns {Array<string>} 主题名称数组
     * @static
     */
    static getThemes(type) {
        const typeInfo = this.getSupportedTypes().find(t => t.id === type);
        return typeInfo ? typeInfo.themes : [];
    }
}
// #endregion

// #region 模板管理器类定义
/**
 * @class TemplateManager - 模板管理器类
 * @description 高级模板管理功能，包括批量操作、模板组合等
 */
export class TemplateManager {
    /**
     * 构造函数 - 初始化模板管理器
     * @param {TemplateRegistry} registry - 模板注册表实例，默认使用全局注册表
     */
    constructor(registry = defaultRegistry) {
        // 初始化日志记录器
        this.logger = getLogger();
        this.logger.startPerformanceMark('template_manager_construction', 'TemplateManager', 'constructor');
        this.logger.info('TemplateManager', 'constructor', '开始构造模板管理器', {
            hasCustomRegistry: registry !== defaultRegistry
        });
        
        this.registry = registry;
        this.presets = new Map(); // 存储预设配置
        this.groups = new Map(); // 存储模板组
        
        this.logger.debug('TemplateManager', 'constructor', '基础存储结构初始化完成', {
            registryType: registry === defaultRegistry ? 'default' : 'custom'
        });
        
        // 初始化预设配置
        this.logger.debug('TemplateManager', 'constructor', '开始初始化预设配置');
        this._initializePresets();
        
        const constructionDuration = this.logger.endPerformanceMark('template_manager_construction', 'TemplateManager', 'constructor');
        this.logger.info('TemplateManager', 'constructor', '✅ 模板管理器构造完成', {
            duration: `${constructionDuration?.toFixed(2)}ms`,
            registrySize: this.registry.getAll().length,
            presetsCount: this.presets.size,
            groupsCount: this.groups.size
        });
        
        // 记录构造统计
        this.logger.incrementCounter('template_manager_instances', 'TemplateManager');
    }

    /**
     * 初始化预设配置 - 设置常用的模板预设
     * @private
     */
    _initializePresets() {
        // 收据预设
        this.presets.set('receipt-default', {
            type: 'receipt',
            theme: 'classic',
            config: {
                renderConfig: {
                    currency: 'RM',
                    language: 'zh-CN'
                }
            }
        });

        // 发票预设
        this.presets.set('invoice-default', {
            type: 'invoice',
            theme: 'classic',
            config: {
                renderConfig: {
                    currency: 'RM',
                    language: 'zh-CN',
                    showTax: true
                }
            }
        });

        // 司机协议预设
        this.presets.set('agreement-default', {
            type: 'driver-agreement',
            theme: 'standard',
            config: {
                renderConfig: {
                    language: 'zh-CN',
                    showSignatures: true,
                    showStamp: true
                }
            }
        });
    }

    /**
     * 批量注册模板 - 一次性注册多个模板
     * @param {Array<Object>} templates - 模板配置数组
     * @returns {Array<string>} 注册的模板名称数组
     */
    batchRegister(templates) {
        const registeredNames = [];
        
        for (const templateConfig of templates) {
            try {
                const template = TemplateFactory.create(templateConfig.type, templateConfig.config);
                this.registry.register(template);
                registeredNames.push(template.name);
            } catch (error) {
                console.error(`注册模板失败: ${templateConfig.type}`, error);
            }
        }
        
        return registeredNames;
    }

    /**
     * 创建模板组 - 将相关模板组织成组
     * @param {string} groupName - 组名称
     * @param {Array<string>} templateNames - 模板名称数组
     */
    createGroup(groupName, templateNames) {
        // 验证所有模板都存在
        const validTemplates = templateNames.filter(name => this.registry.has(name));
        
        if (validTemplates.length !== templateNames.length) {
            console.warn(`模板组 "${groupName}" 中有模板不存在`);
        }
        
        this.groups.set(groupName, validTemplates);
    }

    /**
     * 获取模板组 - 获取指定组的所有模板
     * @param {string} groupName - 组名称
     * @returns {Array<BaseTemplate>} 模板实例数组
     */
    getGroup(groupName) {
        const templateNames = this.groups.get(groupName) || [];
        return templateNames.map(name => this.registry.get(name)).filter(Boolean);
    }

    /**
     * 批量渲染 - 使用指定的模板组进行批量渲染
     * @param {string} groupName - 组名称
     * @param {Object} data - 渲染数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<Array>} 渲染结果数组
     */
    async batchRender(groupName, data, options = {}) {
        const templates = this.getGroup(groupName);
        const results = [];
        
        for (const template of templates) {
            try {
                const result = await template.render(data, options);
                results.push({
                    template: template.name,
                    success: true,
                    result
                });
            } catch (error) {
                results.push({
                    template: template.name,
                    success: false,
                    error: error.message
                });
            }
        }
        
        return results;
    }

    /**
     * 从预设创建模板 - 使用预设配置创建模板
     * @param {string} presetName - 预设名称
     * @param {Object} overrides - 覆盖配置
     * @returns {BaseTemplate} 模板实例
     */
    createFromPreset(presetName, overrides = {}) {
        const preset = this.presets.get(presetName);
        if (!preset) {
            throw new Error(`预设配置不存在: ${presetName}`);
        }
        
        const config = {
            ...preset.config,
            ...overrides
        };
        
        return TemplateFactory.createPreset(preset.type, preset.theme, config);
    }

    /**
     * 添加预设配置 - 添加新的预设配置
     * @param {string} name - 预设名称
     * @param {string} type - 模板类型
     * @param {string} theme - 主题名称
     * @param {Object} config - 配置对象
     */
    addPreset(name, type, theme, config = {}) {
        this.presets.set(name, {
            type,
            theme,
            config
        });
    }

    /**
     * 获取所有预设 - 返回所有可用的预设配置
     * @returns {Array<Object>} 预设信息数组
     */
    getPresets() {
        return Array.from(this.presets.entries()).map(([name, preset]) => ({
            name,
            ...preset
        }));
    }

    /**
     * 获取统计信息 - 返回模板管理器的统计数据
     * @returns {Object} 统计信息对象
     */
    getStats() {
        return {
            totalTemplates: this.registry.getNames().length,
            totalGroups: this.groups.size,
            totalPresets: this.presets.size,
            typeDistribution: this.registry.getStats().typeDistribution,
            groups: Array.from(this.groups.keys()),
            presets: Array.from(this.presets.keys())
        };
    }
}
// #endregion

// #region 默认实例导出
// 创建默认的模板管理器实例
export const defaultTemplateManager = new TemplateManager();

// 注册默认模板到全局注册表
const defaultTemplates = [
    TemplateFactory.createPreset('receipt', 'classic', { name: 'default-receipt' }),
    TemplateFactory.createPreset('invoice', 'classic', { name: 'default-invoice' }),
    TemplateFactory.createPreset('driver-agreement', 'standard', { name: 'default-agreement' })
];

defaultTemplates.forEach(template => {
    try {
        defaultRegistry.register(template);
    } catch (error) {
        console.warn(`默认模板注册失败: ${template.name}`, error);
    }
});

// 创建默认模板组
defaultTemplateManager.createGroup('all-documents', [
    'default-receipt',
    'default-invoice',
    'default-agreement'
]);
// #endregion 