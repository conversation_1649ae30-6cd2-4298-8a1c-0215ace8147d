/**
 * @file 组件注册表 - 管理所有UI组件的注册和查找
 * <AUTHOR> Team
 * @description 
 * 这个文件实现了组件注册表系统，提供：
 * - ComponentRegistry 组件注册表类
 * - 组件的注册、查找、管理功能
 * - 组件生命周期跟踪
 * - 组件统计和监控
 */

// #region 导入依赖模块
import { EventEmitter } from '../../core/events/event-emitter.js';
import { UI_EVENTS } from '../../core/events/event-types.js';
import { validateNonEmpty } from '../../core/utils/validation.js';
import { getLogger } from '../../core/utils/logger.js';
// #endregion

// #region ComponentRegistry 组件注册表类
/**
 * @class ComponentRegistry - 组件注册表
 * @description 管理所有UI组件的注册、查找和生命周期
 */
export class ComponentRegistry extends EventEmitter {
    /**
     * 构造函数 - 初始化组件注册表
     * @param {Object} config - 注册表配置
     */
    constructor(config = {}) {
        super();
        
        this.logger = getLogger();
        this.logger.debug('ComponentRegistry', 'constructor', '初始化组件注册表');
        
        // 组件存储
        this.components = new Map();
        this.componentsByType = new Map();
        this.componentsByName = new Map();
        
        // 配置
        this.config = {
            maxComponents: 1000,
            enableStats: true,
            enableEvents: true,
            ...config
        };
        
        // 统计信息
        this.stats = {
            totalRegistered: 0,
            totalUnregistered: 0,
            activeComponents: 0,
            componentTypes: new Set()
        };
        
        this.logger.info('ComponentRegistry', 'constructor', '组件注册表初始化完成', {
            maxComponents: this.config.maxComponents,
            enableStats: this.config.enableStats
        });
    }

    /**
     * 注册组件 - 将组件添加到注册表
     * @param {BaseComponent} component - 要注册的组件
     * @returns {boolean} 注册是否成功
     */
    register(component) {
        this.logger.debug('ComponentRegistry', 'register', '开始注册组件', {
            componentId: component.id,
            componentName: component.name,
            componentType: component.type
        });
        
        try {
            // 验证组件
            this._validateComponent(component);
            
            // 检查是否已注册
            if (this.components.has(component.id)) {
                this.logger.warn('ComponentRegistry', 'register', '组件已存在，跳过注册', {
                    componentId: component.id
                });
                return false;
            }
            
            // 检查数量限制
            if (this.components.size >= this.config.maxComponents) {
                this.logger.error('ComponentRegistry', 'register', '组件数量超出限制', {
                    currentCount: this.components.size,
                    maxComponents: this.config.maxComponents
                });
                throw new Error(`组件数量超出限制: ${this.config.maxComponents}`);
            }
            
            // 注册组件
            this.components.set(component.id, component);
            
            // 按类型分组
            if (!this.componentsByType.has(component.type)) {
                this.componentsByType.set(component.type, new Set());
            }
            this.componentsByType.get(component.type).add(component.id);
            
            // 按名称索引
            if (!this.componentsByName.has(component.name)) {
                this.componentsByName.set(component.name, new Set());
            }
            this.componentsByName.get(component.name).add(component.id);
            
            // 更新统计
            if (this.config.enableStats) {
                this.stats.totalRegistered++;
                this.stats.activeComponents++;
                this.stats.componentTypes.add(component.type);
            }
            
            // 触发事件
            if (this.config.enableEvents) {
                this.emit(UI_EVENTS.COMPONENT_REGISTERED, {
                    component,
                    registry: this
                });
            }
            
            this.logger.info('ComponentRegistry', 'register', '组件注册成功', {
                componentId: component.id,
                componentName: component.name,
                componentType: component.type,
                totalComponents: this.components.size
            });
            
            return true;
            
        } catch (error) {
            this.logger.error('ComponentRegistry', 'register', '组件注册失败', {
                componentId: component?.id,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * 注销组件 - 从注册表移除组件
     * @param {string} componentId - 组件ID
     * @returns {boolean} 注销是否成功
     */
    unregister(componentId) {
        this.logger.debug('ComponentRegistry', 'unregister', '开始注销组件', {
            componentId
        });
        
        const component = this.components.get(componentId);
        if (!component) {
            this.logger.warn('ComponentRegistry', 'unregister', '组件不存在', {
                componentId
            });
            return false;
        }
        
        try {
            // 从主存储移除
            this.components.delete(componentId);
            
            // 从类型分组移除
            const typeSet = this.componentsByType.get(component.type);
            if (typeSet) {
                typeSet.delete(componentId);
                if (typeSet.size === 0) {
                    this.componentsByType.delete(component.type);
                }
            }
            
            // 从名称索引移除
            const nameSet = this.componentsByName.get(component.name);
            if (nameSet) {
                nameSet.delete(componentId);
                if (nameSet.size === 0) {
                    this.componentsByName.delete(component.name);
                }
            }
            
            // 更新统计
            if (this.config.enableStats) {
                this.stats.totalUnregistered++;
                this.stats.activeComponents--;
                
                // 更新类型统计
                if (!this.componentsByType.has(component.type)) {
                    this.stats.componentTypes.delete(component.type);
                }
            }
            
            // 触发事件
            if (this.config.enableEvents) {
                this.emit(UI_EVENTS.COMPONENT_UNREGISTERED, {
                    component,
                    componentId,
                    registry: this
                });
            }
            
            this.logger.info('ComponentRegistry', 'unregister', '组件注销成功', {
                componentId,
                componentName: component.name,
                componentType: component.type,
                remainingComponents: this.components.size
            });
            
            return true;
            
        } catch (error) {
            this.logger.error('ComponentRegistry', 'unregister', '组件注销失败', {
                componentId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * 获取组件 - 根据ID获取组件
     * @param {string} componentId - 组件ID
     * @returns {BaseComponent|null} 组件实例
     */
    get(componentId) {
        return this.components.get(componentId) || null;
    }

    /**
     * 检查组件是否存在
     * @param {string} componentId - 组件ID
     * @returns {boolean} 是否存在
     */
    has(componentId) {
        return this.components.has(componentId);
    }

    /**
     * 根据类型获取组件
     * @param {string} type - 组件类型
     * @returns {Array<BaseComponent>} 组件数组
     */
    getByType(type) {
        const componentIds = this.componentsByType.get(type);
        if (!componentIds) {
            return [];
        }
        
        return Array.from(componentIds)
            .map(id => this.components.get(id))
            .filter(Boolean);
    }

    /**
     * 根据名称获取组件
     * @param {string} name - 组件名称
     * @returns {Array<BaseComponent>} 组件数组
     */
    getByName(name) {
        const componentIds = this.componentsByName.get(name);
        if (!componentIds) {
            return [];
        }
        
        return Array.from(componentIds)
            .map(id => this.components.get(id))
            .filter(Boolean);
    }

    /**
     * 查找组件 - 根据条件查找组件
     * @param {Function} predicate - 查找条件函数
     * @returns {Array<BaseComponent>} 匹配的组件数组
     */
    find(predicate) {
        const results = [];
        for (const component of this.components.values()) {
            if (predicate(component)) {
                results.push(component);
            }
        }
        return results;
    }

    /**
     * 获取所有组件
     * @returns {Array<BaseComponent>} 所有组件数组
     */
    getAll() {
        return Array.from(this.components.values());
    }

    /**
     * 获取所有组件ID
     * @returns {Array<string>} 所有组件ID数组
     */
    getAllIds() {
        return Array.from(this.components.keys());
    }

    /**
     * 获取组件类型列表
     * @returns {Array<string>} 组件类型数组
     */
    getTypes() {
        return Array.from(this.componentsByType.keys());
    }

    /**
     * 获取组件名称列表
     * @returns {Array<string>} 组件名称数组
     */
    getNames() {
        return Array.from(this.componentsByName.keys());
    }

    /**
     * 获取统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.stats,
            componentTypes: Array.from(this.stats.componentTypes),
            totalTypes: this.stats.componentTypes.size,
            totalNames: this.componentsByName.size
        };
    }

    /**
     * 清空注册表
     * @returns {Promise<void>}
     */
    async clear() {
        this.logger.info('ComponentRegistry', 'clear', '开始清空组件注册表', {
            componentCount: this.components.size
        });
        
        // 注销所有组件
        const componentIds = Array.from(this.components.keys());
        for (const componentId of componentIds) {
            try {
                const component = this.components.get(componentId);
                if (component && typeof component.destroy === 'function') {
                    await component.destroy();
                }
                this.unregister(componentId);
            } catch (error) {
                this.logger.error('ComponentRegistry', 'clear', '清理组件失败', {
                    componentId,
                    error: error.message
                });
            }
        }
        
        // 清空所有存储
        this.components.clear();
        this.componentsByType.clear();
        this.componentsByName.clear();
        
        // 重置统计
        this.stats.activeComponents = 0;
        this.stats.componentTypes.clear();
        
        // 触发事件
        if (this.config.enableEvents) {
            this.emit(UI_EVENTS.REGISTRY_CLEARED, {
                registry: this
            });
        }
        
        this.logger.info('ComponentRegistry', 'clear', '组件注册表清空完成');
    }

    /**
     * 验证组件 - 验证组件是否符合注册要求
     * @param {BaseComponent} component - 要验证的组件
     * @private
     */
    _validateComponent(component) {
        if (!component) {
            throw new Error('组件不能为空');
        }
        
        if (!component.id) {
            throw new Error('组件必须有ID');
        }
        
        if (!component.name) {
            throw new Error('组件必须有名称');
        }
        
        if (!component.type) {
            throw new Error('组件必须有类型');
        }
        
        validateNonEmpty(component.id, '组件ID不能为空');
        validateNonEmpty(component.name, '组件名称不能为空');
        validateNonEmpty(component.type, '组件类型不能为空');
    }
}
// #endregion

// #region 创建默认实例
/**
 * 创建默认组件注册表
 */
export const defaultComponentRegistry = new ComponentRegistry();
// #endregion