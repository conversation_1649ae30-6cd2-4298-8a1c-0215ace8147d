/**
 * @file 统一样式文件 - 重构后的CSS样式整合
 * <AUTHOR> Team
 * @description 
 * 这个文件整合了原本分散在多个CSS文件中的样式定义
 * 消除了重复的CSS规则，统一了变量命名，建立了清晰的样式层级
 * 
 * 重构说明：
 * - 整合了core-styles.css和header-footer-fixed.css中的重复定义
 * - 统一了CSS变量命名规范
 * - 建立了模块化的样式结构
 * - 优化了页眉页脚的定位逻辑
 */

/* #region CSS变量定义 - 统一的全局变量 */
:root {
    /* 布局变量 - 统一页眉页脚高度定义 */
    --header-height: 160px;
    --footer-height: 38px;
    --content-padding: 20px;
    --document-max-width: 595px; /* A4宽度 */
    --document-min-height: 842px; /* A4高度 */
    
    /* 颜色变量 - 主题色彩系统 */
    --primary-color: #1e40af;
    --secondary-color: #3b82f6;
    --accent-color: #f59e0b;
    --light-color: #f3f4f6;
    --dark-color: #1f2937;
    --background-color: #f9fafb;
    --text-color: #333333;
    --border-color: #e5e7eb;
    
    /* 字体变量 */
    --base-font-family: 'Roboto', 'Noto Sans SC', sans-serif;
    --classic-font-family: 'Times New Roman', 'SimSun', serif;
    --elegant-font-family: 'Georgia', 'STZhongsong', serif;
    --tourism-font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
    --base-font-size: 11pt;
    --title-font-size: 18pt;
    --small-font-size: 9pt;
    --line-height: 1.5;
    
    /* 阴影变量 */
    --box-shadow-light: 0 2px 4px rgba(0,0,0,0.1);
    --box-shadow-medium: 0 4px 15px -1px rgba(0, 0, 0, 0.1), 0 6px 8px -1px rgba(0, 0, 0, 0.05);
    --box-shadow-heavy: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    
    /* 过渡变量 */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Z-index层级 */
    --z-index-header: 100;
    --z-index-footer: 100;
    --z-index-modal: 1000;
    --z-index-tooltip: 1100;
}
/* #endregion */

/* #region 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--base-font-family);
    font-size: var(--base-font-size);
    line-height: var(--line-height);
    color: var(--text-color);
    background-color: var(--background-color);
    -ms-overflow-style: none;
    overflow: -moz-scrollbars-auto;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-color);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--dark-color);
}
/* #endregion */

/* #region 文档容器样式 - 统一的文档布局 */
#document-preview {
    width: 100%;
    max-width: var(--document-max-width);
    min-height: var(--document-min-height);
    height: auto;
    margin: 0 auto 30px;
    padding: 0;
    background-color: white;
    box-shadow: var(--box-shadow-medium);
    transform: scale(0.9);
    transform-origin: top center;
    transition: var(--transition-normal);
    position: relative;
    overflow: visible;
    aspect-ratio: 1 / 1.414; /* A4纸张比例 */
}

#document-container {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 100%;
    padding-top: var(--header-height);
    padding-bottom: var(--footer-height);
    padding-left: var(--content-padding);
    padding-right: var(--content-padding);
    display: flex;
    flex-direction: column;
    font-size: var(--base-font-size);
    line-height: var(--line-height);
    color: var(--text-color);
    overflow: visible;
}
/* #endregion */

/* #region 页眉页脚样式 - 统一的定位和样式 */
.document-header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: var(--header-height);
    z-index: var(--z-index-header);
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--box-shadow-light);
}

.document-header-image-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 5px;
}

.document-header-image-container img {
    max-height: calc(var(--header-height) - 10px);
    max-width: 100%;
    object-fit: contain;
    display: block;
}

.document-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: var(--footer-height);
    z-index: var(--z-index-footer);
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid var(--border-color);
}

.document-footer-content {
    width: 100%;
    text-align: center;
    font-size: 8pt;
    color: #666;
    line-height: 1.2;
    padding: 2px 5px;
}

/* 统一页脚样式 */
.unified-document-footer.company-footer-image-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: auto;
    min-height: var(--footer-height);
    background-color: white;
    z-index: var(--z-index-footer);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    padding: 5px 5px 2px 5px;
    box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
    margin-bottom: 0;
}

.unified-document-footer.company-footer-image-container img {
    max-height: calc(var(--footer-height) - 20px);
    max-width: 100%;
    object-fit: contain;
    margin-bottom: 2px;
}
/* #endregion */

/* #region 模板样式 - 不同文档模板的主题样式 */
.template-classic {
    font-family: var(--classic-font-family);
    color: #333;
}

.template-classic .header {
    border-bottom: 1px solid #ddd;
}

.template-classic .table th {
    background-color: #f5f5f5;
}

.template-classic .table td,
.template-classic .table th {
    border: 1px solid #ddd;
}

.template-modern {
    font-family: var(--base-font-family);
    color: #2d3748;
}

.template-modern .header {
    border-bottom: 2px solid var(--secondary-color);
}

.template-modern .table th {
    background-color: var(--secondary-color);
    color: white;
}

.template-modern .table td,
.template-modern .table th {
    border: none;
    border-bottom: 1px solid #e2e8f0;
}

.template-elegant {
    font-family: var(--elegant-font-family);
    color: #1a202c;
}

.template-elegant .header {
    border-bottom: double 3px #805ad5;
}

.template-elegant .table th {
    background-color: #faf5ff;
    color: #6b46c1;
}

.template-elegant .table td,
.template-elegant .table th {
    border: 1px solid #e9d8fd;
}

.template-tourism {
    font-family: var(--tourism-font-family);
    color: #2c5282;
}

.template-tourism .header {
    background: linear-gradient(to right, #90cdf4, #4299e1);
    color: white;
}

.template-tourism .table th {
    background-color: #4299e1;
    color: white;
}

.template-tourism .table td,
.template-tourism .table th {
    border: 1px solid #bee3f8;
}

/* 模板强调条样式 */
.template-accent-bar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(to right, var(--primary-color), var(--accent-color));
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
}
/* #endregion */

/* #region 文档内容样式 */
.document-title-section {
    padding: 15px 25px;
    text-align: center;
}

.document-title-section h1 {
    font-size: var(--title-font-size);
    margin-bottom: 10px;
    color: var(--primary-color);
    font-weight: bold;
}

.client-section,
.items-section,
.payment-notes-section {
    padding: 15px 25px;
}

.client-section {
    background-color: var(--light-color);
}

/* 表格样式 */
.items-section table {
    width: 100%;
    border-collapse: collapse;
    margin: 1em 0;
}

.items-section th,
.items-section td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.items-section th {
    background-color: var(--light-color);
    font-weight: bold;
    font-size: 10pt;
    padding: 6px 4px;
}

.items-section td {
    font-size: 10pt;
    padding: 4px;
}

/* 客户信息样式 */
.customer-info,
.customer-info p,
.customer-info span,
.customer-info div {
    font-size: 10pt;
}

.customer-info h3 {
    font-size: 11pt;
    margin-bottom: 4px;
}

/* 备注和付款方式样式 */
.notes-section p,
#notes-preview {
    font-size: var(--small-font-size);
}

.payment-method p,
#payment-method-preview {
    font-size: var(--small-font-size);
}

.payment-method h3 {
    font-size: 10pt;
    margin-bottom: 2px;
}
/* #endregion */

/* #region 响应式设计 */
@media (max-width: 768px) {
    #document-preview {
        transform: scale(0.8);
        margin: 0 auto 20px;
    }
    
    #document-container {
        padding-top: 200px;
        padding-bottom: 150px;
    }
    
    .document-header {
        height: 180px;
    }
    
    .document-footer {
        height: 100px;
    }
    
    .document-title-section,
    .client-section,
    .items-section,
    .payment-notes-section {
        padding: 10px 15px;
    }
}

@media (max-width: 480px) {
    #document-preview {
        transform: scale(0.7);
    }
    
    .document-title-section h1 {
        font-size: 16pt;
    }
    
    .items-section th,
    .items-section td {
        padding: 4px 2px;
        font-size: 9pt;
    }
}
/* #endregion */

/* #region 打印样式 */
@media print {
    body {
        background-color: white;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
    
    #document-preview {
        transform: scale(1);
        margin: 0;
        padding: 0;
        box-shadow: none;
        border: none;
        width: 100%;
        height: 100%;
        max-width: none;
        min-height: unset;
        aspect-ratio: unset;
        overflow: visible;
    }
    
    #document-container {
        padding-top: var(--header-height);
        padding-bottom: 0;
        box-shadow: none;
        border: none;
        height: auto;
        min-height: unset;
        display: flex;
        flex-direction: column;
        page-break-inside: avoid;
    }
    
    .document-header,
    .document-footer {
        position: fixed !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        transform: none !important;
        box-shadow: none !important;
        background-color: white !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
    
    .document-header {
        top: 0 !important;
        height: var(--header-height) !important;
        page-break-after: avoid !important;
        page-break-inside: avoid !important;
    }
    
    .document-footer {
        bottom: 0 !important;
        height: var(--footer-height) !important;
        page-break-before: avoid !important;
        page-break-inside: avoid !important;
    }
    
    .document-header-image-container img,
    .document-footer img {
        max-height: 100% !important;
        max-width: 100% !important;
        object-fit: contain !important;
        display: block !important;
        margin: 0 auto !important;
    }
    
    /* 隐藏非打印元素 */
    header,
    .preview-header,
    .fab,
    .fixed.inset-0.bg-blue-500.h-1\.5 {
        display: none !important;
    }
    
    /* 确保内容正确分页 */
    .items-section table {
        page-break-inside: auto;
    }
    
    .items-section tr {
        page-break-inside: avoid;
        page-break-after: auto;
    }
    
    .document-title-section,
    .client-section,
    .payment-notes-section {
        page-break-inside: avoid;
    }
}
/* #endregion */

/* #region 动画和过渡效果 */
.template-option {
    transition: var(--transition-normal);
}

.template-option:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-heavy);
}

.form-control:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
    transition: var(--transition-fast);
}

#document-preview:hover {
    transform: scale(0.92);
}
/* #endregion */

/* #region 工具类样式 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: bold; }
.font-normal { font-weight: normal; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }

.p-2 { padding: 0.5rem; }
.p-4 { padding: 1rem; }

.hidden { display: none; }
.block { display: block; }
.flex { display: flex; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.bg-white { background-color: white; }
.bg-gray-50 { background-color: #f9fafb; }

.border { border: 1px solid var(--border-color); }
.border-gray-300 { border-color: #d1d5db; }

.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }

.shadow { box-shadow: var(--box-shadow-light); }
.shadow-md { box-shadow: var(--box-shadow-medium); }
.shadow-lg { box-shadow: var(--box-shadow-heavy); }
/* #endregion */
