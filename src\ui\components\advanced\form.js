/**
 * @file 表单组件 - SmartOffice 高级UI组件
 * <AUTHOR> Team
 * @description 
 * 这个文件实现了表单组件，提供：
 * - 表单字段管理
 * - 数据验证
 * - 数据绑定
 * - 表单布局
 * - 提交处理
 * - 重置功能
 * - 动态字段
 */

// #region 导入依赖模块
import { BaseComponent } from '../base-component.js';
import { UIEvents } from '../../../core/events/event-types.js';
import { getLogger } from '../../../core/utils/logger.js';
// #endregion

// #region 常量定义
/**
 * 字段类型
 */
const FIELD_TYPES = {
    TEXT: 'text',
    EMAIL: 'email',
    PASSWORD: 'password',
    NUMBER: 'number',
    TEL: 'tel',
    URL: 'url',
    TEXTAREA: 'textarea',
    SELECT: 'select',
    CHECKBOX: 'checkbox',
    RADIO: 'radio',
    DATE: 'date',
    TIME: 'time',
    DATETIME: 'datetime-local',
    FILE: 'file',
    HIDDEN: 'hidden',
    CUSTOM: 'custom'
};

/**
 * 验证规则类型
 */
const VALIDATION_RULES = {
    REQUIRED: 'required',
    MIN_LENGTH: 'minLength',
    MAX_LENGTH: 'maxLength',
    MIN: 'min',
    MAX: 'max',
    PATTERN: 'pattern',
    EMAIL: 'email',
    URL: 'url',
    CUSTOM: 'custom'
};

/**
 * 表单布局
 */
const FORM_LAYOUTS = {
    VERTICAL: 'vertical',
    HORIZONTAL: 'horizontal',
    INLINE: 'inline'
};

/**
 * 表单大小
 */
const FORM_SIZES = {
    SMALL: 'small',
    MEDIUM: 'medium',
    LARGE: 'large'
};

/**
 * 默认配置
 */
const DEFAULT_CONFIG = {
    layout: FORM_LAYOUTS.VERTICAL,
    size: FORM_SIZES.MEDIUM,
    labelWidth: '100px',
    validateOnChange: true,
    validateOnBlur: true,
    showValidationIcon: true,
    disabled: false,
    readonly: false,
    colon: true,
    requiredMark: true,
    scrollToError: true,
    preserveWhitespace: false
};

/**
 * 内置验证规则
 */
const BUILT_IN_VALIDATORS = {
    email: {
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: '请输入有效的邮箱地址'
    },
    url: {
        pattern: /^https?:\/\/.+/,
        message: '请输入有效的URL地址'
    },
    phone: {
        pattern: /^1[3-9]\d{9}$/,
        message: '请输入有效的手机号码'
    },
    idCard: {
        pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
        message: '请输入有效的身份证号码'
    }
};
// #endregion

// #region Form 表单组件类
/**
 * @class Form - 表单组件
 * @description 表单数据收集和验证组件
 */
export class Form extends BaseComponent {
    /**
     * 构造函数 - 初始化表单组件
     * @param {Object} config - 表单配置
     */
    constructor(config = {}) {
        super(config);
        
        this.logger = getLogger();
        this.logger.debug('Form', 'constructor', '初始化表单组件');
        
        // 配置
        this.config = {
            ...DEFAULT_CONFIG,
            ...config
        };
        
        // 状态
        this.fields = new Map();
        this.values = {};
        this.errors = new Map();
        this.touched = new Set();
        this.validating = new Set();
        this.isSubmitting = false;
        this.isValid = true;
        
        // DOM元素
        this.element = null;
        this.formElement = null;
        
        // 创建表单
        this._createForm();
        
        // 如果有初始字段，添加它们
        if (this.config.fields) {
            this.config.fields.forEach(field => {
                this.addField(field);
            });
        }
        
        // 设置初始值
        if (this.config.initialValues) {
            this.setValues(this.config.initialValues);
        }
        
        this.logger.info('Form', 'constructor', '表单组件初始化完成', {
            config: this.config
        });
    }

    /**
     * 创建表单
     * @private
     */
    _createForm() {
        // 创建主容器
        this.element = document.createElement('div');
        this.element.className = `form-container form-${this.config.layout} form-${this.config.size}`;
        
        // 设置容器样式
        this.element.style.cssText = `
            font-family: var(--so-font-family, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            font-size: var(--so-font-size-base, 14px);
            line-height: var(--so-line-height-base, 1.5715);
            color: var(--so-text-color, #000000d9);
        `;
        
        // 创建表单元素
        this.formElement = document.createElement('form');
        this.formElement.className = 'form';
        this.formElement.noValidate = true; // 禁用浏览器默认验证
        
        // 设置表单样式
        this.formElement.style.cssText = `
            width: 100%;
        `;
        
        this.element.appendChild(this.formElement);
        
        // 绑定事件
        this._bindEvents();
    }

    /**
     * 绑定事件
     * @private
     */
    _bindEvents() {
        // 阻止默认提交
        this.formElement.addEventListener('submit', (e) => {
            e.preventDefault();
            this.submit();
        });
        
        // 表单重置
        this.formElement.addEventListener('reset', (e) => {
            e.preventDefault();
            this.reset();
        });
    }

    /**
     * 添加字段
     * @param {Object} fieldConfig - 字段配置
     */
    addField(fieldConfig) {
        const field = this._createField(fieldConfig);
        this.fields.set(fieldConfig.name, field);
        this.formElement.appendChild(field.element);
        
        // 设置初始值
        if (fieldConfig.defaultValue !== undefined) {
            this.setFieldValue(fieldConfig.name, fieldConfig.defaultValue);
        }
        
        this.emit(UIEvents.FORM_FIELD_ADDED, {
            fieldName: fieldConfig.name,
            field: field
        });
    }

    /**
     * 创建字段
     * @param {Object} config - 字段配置
     * @returns {Object} 字段对象
     * @private
     */
    _createField(config) {
        const field = {
            name: config.name,
            type: config.type || FIELD_TYPES.TEXT,
            label: config.label,
            placeholder: config.placeholder,
            required: config.required || false,
            disabled: config.disabled || this.config.disabled,
            readonly: config.readonly || this.config.readonly,
            rules: config.rules || [],
            options: config.options || [],
            element: null,
            labelElement: null,
            inputElement: null,
            errorElement: null,
            helpElement: null
        };
        
        // 创建字段容器
        field.element = document.createElement('div');
        field.element.className = `form-item form-item-${field.type}`;
        field.element.setAttribute('data-field', field.name);
        
        // 设置字段样式
        const itemStyles = this._getFieldItemStyles();
        field.element.style.cssText = itemStyles;
        
        // 创建标签
        if (field.label) {
            field.labelElement = this._createLabel(field);
            field.element.appendChild(field.labelElement);
        }
        
        // 创建输入控件容器
        const controlContainer = document.createElement('div');
        controlContainer.className = 'form-control-container';
        controlContainer.style.cssText = this._getControlContainerStyles();
        
        // 创建输入控件
        field.inputElement = this._createInput(field);
        controlContainer.appendChild(field.inputElement);
        
        // 创建验证图标
        if (this.config.showValidationIcon) {
            const iconElement = document.createElement('span');
            iconElement.className = 'form-validation-icon';
            iconElement.style.cssText = `
                position: absolute;
                right: var(--so-padding-sm, 8px);
                top: 50%;
                transform: translateY(-50%);
                color: var(--so-text-color-secondary, #00000073);
                pointer-events: none;
                display: none;
            `;
            controlContainer.appendChild(iconElement);
            field.iconElement = iconElement;
        }
        
        field.element.appendChild(controlContainer);
        
        // 创建帮助文本
        if (config.help) {
            field.helpElement = this._createHelp(config.help);
            field.element.appendChild(field.helpElement);
        }
        
        // 创建错误信息
        field.errorElement = this._createError();
        field.element.appendChild(field.errorElement);
        
        // 绑定字段事件
        this._bindFieldEvents(field);
        
        return field;
    }

    /**
     * 创建标签
     * @param {Object} field - 字段对象
     * @returns {HTMLElement} 标签元素
     * @private
     */
    _createLabel(field) {
        const label = document.createElement('label');
        label.className = 'form-label';
        label.setAttribute('for', field.name);
        
        // 设置标签样式
        label.style.cssText = this._getLabelStyles();
        
        // 标签文本
        const text = document.createElement('span');
        text.textContent = field.label;
        label.appendChild(text);
        
        // 必填标记
        if (field.required && this.config.requiredMark) {
            const mark = document.createElement('span');
            mark.className = 'form-required-mark';
            mark.textContent = '*';
            mark.style.cssText = `
                color: var(--so-error-color, #ff4d4f);
                margin-left: var(--so-margin-xs, 4px);
            `;
            label.appendChild(mark);
        }
        
        // 冒号
        if (this.config.colon && this.config.layout === FORM_LAYOUTS.HORIZONTAL) {
            const colon = document.createElement('span');
            colon.className = 'form-colon';
            colon.textContent = ':';
            colon.style.cssText = `
                margin-left: var(--so-margin-xs, 2px);
            `;
            label.appendChild(colon);
        }
        
        return label;
    }

    /**
     * 创建输入控件
     * @param {Object} field - 字段对象
     * @returns {HTMLElement} 输入元素
     * @private
     */
    _createInput(field) {
        let input;
        
        switch (field.type) {
            case FIELD_TYPES.TEXTAREA:
                input = this._createTextarea(field);
                break;
                
            case FIELD_TYPES.SELECT:
                input = this._createSelect(field);
                break;
                
            case FIELD_TYPES.CHECKBOX:
                input = this._createCheckbox(field);
                break;
                
            case FIELD_TYPES.RADIO:
                input = this._createRadio(field);
                break;
                
            case FIELD_TYPES.FILE:
                input = this._createFile(field);
                break;
                
            case FIELD_TYPES.CUSTOM:
                input = this._createCustom(field);
                break;
                
            default:
                input = this._createTextInput(field);
                break;
        }
        
        return input;
    }

    /**
     * 创建文本输入框
     * @param {Object} field - 字段对象
     * @returns {HTMLElement} 输入元素
     * @private
     */
    _createTextInput(field) {
        const input = document.createElement('input');
        input.type = field.type;
        input.id = field.name;
        input.name = field.name;
        input.className = 'form-input';
        
        if (field.placeholder) {
            input.placeholder = field.placeholder;
        }
        
        input.disabled = field.disabled;
        input.readOnly = field.readonly;
        
        // 设置输入框样式
        input.style.cssText = this._getInputStyles();
        
        return input;
    }

    /**
     * 创建文本域
     * @param {Object} field - 字段对象
     * @returns {HTMLElement} 文本域元素
     * @private
     */
    _createTextarea(field) {
        const textarea = document.createElement('textarea');
        textarea.id = field.name;
        textarea.name = field.name;
        textarea.className = 'form-textarea';
        
        if (field.placeholder) {
            textarea.placeholder = field.placeholder;
        }
        
        if (field.rows) {
            textarea.rows = field.rows;
        }
        
        textarea.disabled = field.disabled;
        textarea.readOnly = field.readonly;
        
        // 设置文本域样式
        textarea.style.cssText = this._getInputStyles() + `
            resize: vertical;
            min-height: 80px;
        `;
        
        return textarea;
    }

    /**
     * 创建选择框
     * @param {Object} field - 字段对象
     * @returns {HTMLElement} 选择框元素
     * @private
     */
    _createSelect(field) {
        const select = document.createElement('select');
        select.id = field.name;
        select.name = field.name;
        select.className = 'form-select';
        
        select.disabled = field.disabled;
        
        // 设置选择框样式
        select.style.cssText = this._getInputStyles();
        
        // 添加选项
        if (field.placeholder) {
            const placeholderOption = document.createElement('option');
            placeholderOption.value = '';
            placeholderOption.textContent = field.placeholder;
            placeholderOption.disabled = true;
            placeholderOption.selected = true;
            select.appendChild(placeholderOption);
        }
        
        field.options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.label || option.value;
            select.appendChild(optionElement);
        });
        
        return select;
    }

    /**
     * 创建复选框
     * @param {Object} field - 字段对象
     * @returns {HTMLElement} 复选框容器
     * @private
     */
    _createCheckbox(field) {
        const container = document.createElement('div');
        container.className = 'form-checkbox-group';
        
        if (field.options && field.options.length > 0) {
            // 多个复选框
            field.options.forEach(option => {
                const wrapper = document.createElement('label');
                wrapper.className = 'form-checkbox-wrapper';
                wrapper.style.cssText = `
                    display: inline-flex;
                    align-items: center;
                    margin-right: var(--so-margin-md, 16px);
                    margin-bottom: var(--so-margin-sm, 8px);
                    cursor: pointer;
                `;
                
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.name = field.name;
                checkbox.value = option.value;
                checkbox.disabled = field.disabled;
                checkbox.style.cssText = `
                    margin-right: var(--so-margin-sm, 8px);
                `;
                
                const label = document.createElement('span');
                label.textContent = option.label || option.value;
                
                wrapper.appendChild(checkbox);
                wrapper.appendChild(label);
                container.appendChild(wrapper);
            });
        } else {
            // 单个复选框
            const wrapper = document.createElement('label');
            wrapper.className = 'form-checkbox-wrapper';
            wrapper.style.cssText = `
                display: inline-flex;
                align-items: center;
                cursor: pointer;
            `;
            
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.id = field.name;
            checkbox.name = field.name;
            checkbox.disabled = field.disabled;
            checkbox.style.cssText = `
                margin-right: var(--so-margin-sm, 8px);
            `;
            
            const label = document.createElement('span');
            label.textContent = field.checkboxLabel || field.label || '';
            
            wrapper.appendChild(checkbox);
            wrapper.appendChild(label);
            container.appendChild(wrapper);
        }
        
        return container;
    }

    /**
     * 创建单选框
     * @param {Object} field - 字段对象
     * @returns {HTMLElement} 单选框容器
     * @private
     */
    _createRadio(field) {
        const container = document.createElement('div');
        container.className = 'form-radio-group';
        
        field.options.forEach(option => {
            const wrapper = document.createElement('label');
            wrapper.className = 'form-radio-wrapper';
            wrapper.style.cssText = `
                display: inline-flex;
                align-items: center;
                margin-right: var(--so-margin-md, 16px);
                margin-bottom: var(--so-margin-sm, 8px);
                cursor: pointer;
            `;
            
            const radio = document.createElement('input');
            radio.type = 'radio';
            radio.name = field.name;
            radio.value = option.value;
            radio.disabled = field.disabled;
            radio.style.cssText = `
                margin-right: var(--so-margin-sm, 8px);
            `;
            
            const label = document.createElement('span');
            label.textContent = option.label || option.value;
            
            wrapper.appendChild(radio);
            wrapper.appendChild(label);
            container.appendChild(wrapper);
        });
        
        return container;
    }

    /**
     * 创建文件输入框
     * @param {Object} field - 字段对象
     * @returns {HTMLElement} 文件输入元素
     * @private
     */
    _createFile(field) {
        const input = document.createElement('input');
        input.type = 'file';
        input.id = field.name;
        input.name = field.name;
        input.className = 'form-file';
        
        if (field.accept) {
            input.accept = field.accept;
        }
        
        if (field.multiple) {
            input.multiple = true;
        }
        
        input.disabled = field.disabled;
        
        // 设置文件输入框样式
        input.style.cssText = this._getInputStyles();
        
        return input;
    }

    /**
     * 创建自定义控件
     * @param {Object} field - 字段对象
     * @returns {HTMLElement} 自定义元素
     * @private
     */
    _createCustom(field) {
        const container = document.createElement('div');
        container.className = 'form-custom';
        container.id = field.name;
        
        if (field.render && typeof field.render === 'function') {
            const customElement = field.render(field, this);
            if (customElement instanceof HTMLElement) {
                container.appendChild(customElement);
            }
        }
        
        return container;
    }

    /**
     * 创建帮助文本
     * @param {string} helpText - 帮助文本
     * @returns {HTMLElement} 帮助元素
     * @private
     */
    _createHelp(helpText) {
        const help = document.createElement('div');
        help.className = 'form-help';
        help.textContent = helpText;
        
        help.style.cssText = `
            font-size: var(--so-font-size-sm, 12px);
            color: var(--so-text-color-secondary, #00000073);
            margin-top: var(--so-margin-xs, 4px);
            line-height: 1.5;
        `;
        
        return help;
    }

    /**
     * 创建错误信息
     * @returns {HTMLElement} 错误元素
     * @private
     */
    _createError() {
        const error = document.createElement('div');
        error.className = 'form-error';
        
        error.style.cssText = `
            font-size: var(--so-font-size-sm, 12px);
            color: var(--so-error-color, #ff4d4f);
            margin-top: var(--so-margin-xs, 4px);
            line-height: 1.5;
            display: none;
        `;
        
        return error;
    }

    /**
     * 绑定字段事件
     * @param {Object} field - 字段对象
     * @private
     */
    _bindFieldEvents(field) {
        const inputs = this._getFieldInputs(field);
        
        inputs.forEach(input => {
            // 值变化事件
            const changeHandler = () => {
                this._handleFieldChange(field.name);
            };
            
            // 失焦事件
            const blurHandler = () => {
                this._handleFieldBlur(field.name);
            };
            
            // 聚焦事件
            const focusHandler = () => {
                this._handleFieldFocus(field.name);
            };
            
            if (input.type === 'checkbox' || input.type === 'radio') {
                input.addEventListener('change', changeHandler);
            } else {
                input.addEventListener('input', changeHandler);
                input.addEventListener('change', changeHandler);
            }
            
            input.addEventListener('blur', blurHandler);
            input.addEventListener('focus', focusHandler);
        });
    }

    /**
     * 获取字段输入元素
     * @param {Object} field - 字段对象
     * @returns {Array} 输入元素数组
     * @private
     */
    _getFieldInputs(field) {
        const inputs = [];
        
        if (field.type === FIELD_TYPES.CHECKBOX && field.options) {
            // 多个复选框
            const checkboxes = field.inputElement.querySelectorAll('input[type="checkbox"]');
            inputs.push(...checkboxes);
        } else if (field.type === FIELD_TYPES.RADIO) {
            // 单选框组
            const radios = field.inputElement.querySelectorAll('input[type="radio"]');
            inputs.push(...radios);
        } else {
            // 单个输入控件
            const input = field.inputElement.querySelector('input, select, textarea');
            if (input) {
                inputs.push(input);
            }
        }
        
        return inputs;
    }

    /**
     * 处理字段变化
     * @param {string} fieldName - 字段名
     * @private
     */
    _handleFieldChange(fieldName) {
        const value = this.getFieldValue(fieldName);
        this.values[fieldName] = value;
        
        // 标记为已触摸
        this.touched.add(fieldName);
        
        // 实时验证
        if (this.config.validateOnChange) {
            this.validateField(fieldName);
        }
        
        // 触发变化事件
        this.emit(UIEvents.FORM_FIELD_CHANGED, {
            fieldName,
            value,
            values: { ...this.values }
        });
    }

    /**
     * 处理字段失焦
     * @param {string} fieldName - 字段名
     * @private
     */
    _handleFieldBlur(fieldName) {
        // 标记为已触摸
        this.touched.add(fieldName);
        
        // 失焦验证
        if (this.config.validateOnBlur) {
            this.validateField(fieldName);
        }
        
        // 触发失焦事件
        this.emit(UIEvents.FORM_FIELD_BLURRED, {
            fieldName,
            value: this.getFieldValue(fieldName)
        });
    }

    /**
     * 处理字段聚焦
     * @param {string} fieldName - 字段名
     * @private
     */
    _handleFieldFocus(fieldName) {
        // 清除错误状态
        this._clearFieldError(fieldName);
        
        // 触发聚焦事件
        this.emit(UIEvents.FORM_FIELD_FOCUSED, {
            fieldName,
            value: this.getFieldValue(fieldName)
        });
    }

    /**
     * 获取字段项样式
     * @returns {string} 样式字符串
     * @private
     */
    _getFieldItemStyles() {
        const sizeMap = {
            [FORM_SIZES.SMALL]: 'var(--so-margin-sm, 8px)',
            [FORM_SIZES.MEDIUM]: 'var(--so-margin-md, 16px)',
            [FORM_SIZES.LARGE]: 'var(--so-margin-lg, 24px)'
        };
        
        const marginBottom = sizeMap[this.config.size];
        
        let styles = `
            margin-bottom: ${marginBottom};
        `;
        
        if (this.config.layout === FORM_LAYOUTS.HORIZONTAL) {
            styles += `
                display: flex;
                align-items: flex-start;
            `;
        } else if (this.config.layout === FORM_LAYOUTS.INLINE) {
            styles += `
                display: inline-block;
                margin-right: var(--so-margin-md, 16px);
                vertical-align: top;
            `;
        }
        
        return styles;
    }

    /**
     * 获取标签样式
     * @returns {string} 样式字符串
     * @private
     */
    _getLabelStyles() {
        let styles = `
            font-weight: 500;
            color: var(--so-text-color, #000000d9);
            line-height: 1.5715;
        `;
        
        if (this.config.layout === FORM_LAYOUTS.HORIZONTAL) {
            styles += `
                width: ${this.config.labelWidth};
                flex-shrink: 0;
                text-align: right;
                padding-right: var(--so-padding-md, 16px);
                padding-top: var(--so-padding-xs, 4px);
            `;
        } else {
            styles += `
                display: block;
                margin-bottom: var(--so-margin-xs, 4px);
            `;
        }
        
        return styles;
    }

    /**
     * 获取控件容器样式
     * @returns {string} 样式字符串
     * @private
     */
    _getControlContainerStyles() {
        let styles = `
            position: relative;
        `;
        
        if (this.config.layout === FORM_LAYOUTS.HORIZONTAL) {
            styles += `
                flex: 1;
            `;
        }
        
        return styles;
    }

    /**
     * 获取输入框样式
     * @returns {string} 样式字符串
     * @private
     */
    _getInputStyles() {
        const sizeMap = {
            [FORM_SIZES.SMALL]: {
                height: '24px',
                padding: '0 var(--so-padding-sm, 8px)',
                fontSize: 'var(--so-font-size-sm, 12px)'
            },
            [FORM_SIZES.MEDIUM]: {
                height: '32px',
                padding: '0 var(--so-padding-md, 12px)',
                fontSize: 'var(--so-font-size-base, 14px)'
            },
            [FORM_SIZES.LARGE]: {
                height: '40px',
                padding: '0 var(--so-padding-lg, 16px)',
                fontSize: 'var(--so-font-size-lg, 16px)'
            }
        };
        
        const size = sizeMap[this.config.size];
        
        return `
            width: 100%;
            height: ${size.height};
            padding: ${size.padding};
            font-size: ${size.fontSize};
            line-height: 1.5715;
            color: var(--so-text-color, #000000d9);
            background-color: var(--so-background-color, #ffffff);
            border: 1px solid var(--so-border-color, #d9d9d9);
            border-radius: var(--so-border-radius-base, 6px);
            transition: all 0.2s ease;
            box-sizing: border-box;
        `;
    }

    /**
     * 验证字段
     * @param {string} fieldName - 字段名
     * @returns {Promise<boolean>} 验证结果
     */
    async validateField(fieldName) {
        const field = this.fields.get(fieldName);
        if (!field) {
            return true;
        }
        
        const value = this.getFieldValue(fieldName);
        const errors = [];
        
        // 标记为验证中
        this.validating.add(fieldName);
        
        try {
            // 必填验证
            if (field.required && this._isEmpty(value)) {
                errors.push(`${field.label || fieldName}是必填项`);
            }
            
            // 如果有值，进行其他验证
            if (!this._isEmpty(value)) {
                // 内置规则验证
                for (const rule of field.rules) {
                    const error = await this._validateRule(value, rule, field);
                    if (error) {
                        errors.push(error);
                        break; // 遇到第一个错误就停止
                    }
                }
            }
            
            // 更新错误状态
            if (errors.length > 0) {
                this.errors.set(fieldName, errors[0]);
                this._showFieldError(fieldName, errors[0]);
            } else {
                this.errors.delete(fieldName);
                this._clearFieldError(fieldName);
            }
            
            // 更新整体验证状态
            this._updateValidationState();
            
            return errors.length === 0;
            
        } finally {
            this.validating.delete(fieldName);
        }
    }

    /**
     * 验证规则
     * @param {*} value - 值
     * @param {Object} rule - 规则
     * @param {Object} field - 字段
     * @returns {Promise<string|null>} 错误信息
     * @private
     */
    async _validateRule(value, rule, field) {
        switch (rule.type) {
            case VALIDATION_RULES.MIN_LENGTH:
                if (String(value).length < rule.value) {
                    return rule.message || `${field.label || field.name}长度不能少于${rule.value}个字符`;
                }
                break;
                
            case VALIDATION_RULES.MAX_LENGTH:
                if (String(value).length > rule.value) {
                    return rule.message || `${field.label || field.name}长度不能超过${rule.value}个字符`;
                }
                break;
                
            case VALIDATION_RULES.MIN:
                if (Number(value) < rule.value) {
                    return rule.message || `${field.label || field.name}不能小于${rule.value}`;
                }
                break;
                
            case VALIDATION_RULES.MAX:
                if (Number(value) > rule.value) {
                    return rule.message || `${field.label || field.name}不能大于${rule.value}`;
                }
                break;
                
            case VALIDATION_RULES.PATTERN:
                if (!rule.value.test(String(value))) {
                    return rule.message || `${field.label || field.name}格式不正确`;
                }
                break;
                
            case VALIDATION_RULES.EMAIL:
                if (!BUILT_IN_VALIDATORS.email.pattern.test(String(value))) {
                    return rule.message || BUILT_IN_VALIDATORS.email.message;
                }
                break;
                
            case VALIDATION_RULES.URL:
                if (!BUILT_IN_VALIDATORS.url.pattern.test(String(value))) {
                    return rule.message || BUILT_IN_VALIDATORS.url.message;
                }
                break;
                
            case VALIDATION_RULES.CUSTOM:
                if (rule.validator && typeof rule.validator === 'function') {
                    try {
                        const result = await rule.validator(value, this.values, field);
                        if (result !== true) {
                            return typeof result === 'string' ? result : 
                                (rule.message || `${field.label || field.name}验证失败`);
                        }
                    } catch (error) {
                        return rule.message || error.message || `${field.label || field.name}验证出错`;
                    }
                }
                break;
        }
        
        return null;
    }

    /**
     * 检查值是否为空
     * @param {*} value - 值
     * @returns {boolean} 是否为空
     * @private
     */
    _isEmpty(value) {
        return value === null || value === undefined || 
               (typeof value === 'string' && value.trim() === '') ||
               (Array.isArray(value) && value.length === 0);
    }

    /**
     * 显示字段错误
     * @param {string} fieldName - 字段名
     * @param {string} message - 错误信息
     * @private
     */
    _showFieldError(fieldName, message) {
        const field = this.fields.get(fieldName);
        if (!field) return;
        
        // 显示错误信息
        if (field.errorElement) {
            field.errorElement.textContent = message;
            field.errorElement.style.display = 'block';
        }
        
        // 添加错误样式
        field.element.classList.add('form-item-error');
        
        // 更新输入框样式
        const inputs = this._getFieldInputs(field);
        inputs.forEach(input => {
            input.style.borderColor = 'var(--so-error-color, #ff4d4f)';
        });
        
        // 显示错误图标
        if (field.iconElement) {
            field.iconElement.innerHTML = '✕';
            field.iconElement.style.color = 'var(--so-error-color, #ff4d4f)';
            field.iconElement.style.display = 'block';
        }
    }

    /**
     * 清除字段错误
     * @param {string} fieldName - 字段名
     * @private
     */
    _clearFieldError(fieldName) {
        const field = this.fields.get(fieldName);
        if (!field) return;
        
        // 隐藏错误信息
        if (field.errorElement) {
            field.errorElement.style.display = 'none';
        }
        
        // 移除错误样式
        field.element.classList.remove('form-item-error');
        
        // 恢复输入框样式
        const inputs = this._getFieldInputs(field);
        inputs.forEach(input => {
            input.style.borderColor = 'var(--so-border-color, #d9d9d9)';
        });
        
        // 隐藏图标或显示成功图标
        if (field.iconElement) {
            if (this.touched.has(fieldName) && !this.errors.has(fieldName)) {
                field.iconElement.innerHTML = '✓';
                field.iconElement.style.color = 'var(--so-success-color, #52c41a)';
                field.iconElement.style.display = 'block';
            } else {
                field.iconElement.style.display = 'none';
            }
        }
    }

    /**
     * 更新验证状态
     * @private
     */
    _updateValidationState() {
        this.isValid = this.errors.size === 0;
    }

    // #region 公共方法
    /**
     * 获取字段值
     * @param {string} fieldName - 字段名
     * @returns {*} 字段值
     */
    getFieldValue(fieldName) {
        const field = this.fields.get(fieldName);
        if (!field) return undefined;
        
        switch (field.type) {
            case FIELD_TYPES.CHECKBOX:
                if (field.options && field.options.length > 0) {
                    // 多个复选框
                    const checkboxes = field.inputElement.querySelectorAll('input[type="checkbox"]:checked');
                    return Array.from(checkboxes).map(cb => cb.value);
                } else {
                    // 单个复选框
                    const checkbox = field.inputElement.querySelector('input[type="checkbox"]');
                    return checkbox ? checkbox.checked : false;
                }
                
            case FIELD_TYPES.RADIO:
                const radio = field.inputElement.querySelector('input[type="radio"]:checked');
                return radio ? radio.value : null;
                
            case FIELD_TYPES.FILE:
                const fileInput = field.inputElement.querySelector('input[type="file"]');
                return fileInput ? fileInput.files : null;
                
            default:
                const input = field.inputElement.querySelector('input, select, textarea');
                return input ? input.value : '';
        }
    }

    /**
     * 设置字段值
     * @param {string} fieldName - 字段名
     * @param {*} value - 值
     */
    setFieldValue(fieldName, value) {
        const field = this.fields.get(fieldName);
        if (!field) return;
        
        switch (field.type) {
            case FIELD_TYPES.CHECKBOX:
                if (field.options && field.options.length > 0) {
                    // 多个复选框
                    const checkboxes = field.inputElement.querySelectorAll('input[type="checkbox"]');
                    const values = Array.isArray(value) ? value : [value];
                    checkboxes.forEach(cb => {
                        cb.checked = values.includes(cb.value);
                    });
                } else {
                    // 单个复选框
                    const checkbox = field.inputElement.querySelector('input[type="checkbox"]');
                    if (checkbox) {
                        checkbox.checked = Boolean(value);
                    }
                }
                break;
                
            case FIELD_TYPES.RADIO:
                const radios = field.inputElement.querySelectorAll('input[type="radio"]');
                radios.forEach(radio => {
                    radio.checked = radio.value === value;
                });
                break;
                
            default:
                const input = field.inputElement.querySelector('input, select, textarea');
                if (input) {
                    input.value = value || '';
                }
                break;
        }
        
        // 更新内部值
        this.values[fieldName] = value;
    }

    /**
     * 获取所有值
     * @returns {Object} 所有字段值
     */
    getValues() {
        const values = {};
        for (const fieldName of this.fields.keys()) {
            values[fieldName] = this.getFieldValue(fieldName);
        }
        return values;
    }

    /**
     * 设置所有值
     * @param {Object} values - 值对象
     */
    setValues(values) {
        for (const [fieldName, value] of Object.entries(values)) {
            this.setFieldValue(fieldName, value);
        }
    }

    /**
     * 验证表单
     * @returns {Promise<boolean>} 验证结果
     */
    async validate() {
        const validationPromises = [];
        
        for (const fieldName of this.fields.keys()) {
            validationPromises.push(this.validateField(fieldName));
        }
        
        const results = await Promise.all(validationPromises);
        const isValid = results.every(result => result);
        
        // 滚动到第一个错误字段
        if (!isValid && this.config.scrollToError) {
            this._scrollToFirstError();
        }
        
        return isValid;
    }

    /**
     * 滚动到第一个错误字段
     * @private
     */
    _scrollToFirstError() {
        const firstErrorField = this.formElement.querySelector('.form-item-error');
        if (firstErrorField) {
            firstErrorField.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        }
    }

    /**
     * 提交表单
     * @returns {Promise<Object|null>} 表单数据或null
     */
    async submit() {
        if (this.isSubmitting) {
            return null;
        }
        
        this.isSubmitting = true;
        
        try {
            // 验证表单
            const isValid = await this.validate();
            
            if (!isValid) {
                this.emit(UIEvents.FORM_VALIDATION_FAILED, {
                    errors: Object.fromEntries(this.errors)
                });
                return null;
            }
            
            // 获取表单数据
            const values = this.getValues();
            
            // 触发提交前事件
            this.emit(UIEvents.FORM_BEFORE_SUBMIT, { values });
            
            // 触发提交事件
            this.emit(UIEvents.FORM_SUBMITTED, { values });
            
            return values;
            
        } finally {
            this.isSubmitting = false;
        }
    }

    /**
     * 重置表单
     */
    reset() {
        // 清除所有值
        for (const fieldName of this.fields.keys()) {
            const field = this.fields.get(fieldName);
            if (field.defaultValue !== undefined) {
                this.setFieldValue(fieldName, field.defaultValue);
            } else {
                this.setFieldValue(fieldName, '');
            }
        }
        
        // 清除状态
        this.values = {};
        this.errors.clear();
        this.touched.clear();
        this.validating.clear();
        
        // 清除所有错误显示
        for (const fieldName of this.fields.keys()) {
            this._clearFieldError(fieldName);
        }
        
        this._updateValidationState();
        
        // 触发重置事件
        this.emit(UIEvents.FORM_RESET, {});
    }

    /**
     * 移除字段
     * @param {string} fieldName - 字段名
     */
    removeField(fieldName) {
        const field = this.fields.get(fieldName);
        if (!field) return;
        
        // 移除DOM元素
        if (field.element && field.element.parentNode) {
            field.element.parentNode.removeChild(field.element);
        }
        
        // 清除数据
        this.fields.delete(fieldName);
        delete this.values[fieldName];
        this.errors.delete(fieldName);
        this.touched.delete(fieldName);
        this.validating.delete(fieldName);
        
        this._updateValidationState();
        
        this.emit(UIEvents.FORM_FIELD_REMOVED, {
            fieldName
        });
    }

    /**
     * 设置字段属性
     * @param {string} fieldName - 字段名
     * @param {Object} props - 属性对象
     */
    setFieldProps(fieldName, props) {
        const field = this.fields.get(fieldName);
        if (!field) return;
        
        // 更新字段配置
        Object.assign(field, props);
        
        // 更新DOM属性
        const inputs = this._getFieldInputs(field);
        inputs.forEach(input => {
            if (props.disabled !== undefined) {
                input.disabled = props.disabled;
            }
            if (props.readonly !== undefined) {
                input.readOnly = props.readonly;
            }
            if (props.placeholder !== undefined) {
                input.placeholder = props.placeholder;
            }
        });
        
        // 更新标签
        if (props.label !== undefined && field.labelElement) {
            const textSpan = field.labelElement.querySelector('span');
            if (textSpan) {
                textSpan.textContent = props.label;
            }
        }
    }

    /**
     * 获取表单状态
     * @returns {Object} 表单状态
     */
    getFormState() {
        return {
            values: this.getValues(),
            errors: Object.fromEntries(this.errors),
            touched: Array.from(this.touched),
            validating: Array.from(this.validating),
            isSubmitting: this.isSubmitting,
            isValid: this.isValid
        };
    }

    /**
     * 获取元素
     * @returns {HTMLElement} 表单元素
     */
    getElement() {
        return this.element;
    }

    /**
     * 销毁表单
     */
    destroy() {
        // 移除DOM元素
        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }
        
        // 清理引用
        this.element = null;
        this.formElement = null;
        
        // 清理数据
        this.fields.clear();
        this.values = {};
        this.errors.clear();
        this.touched.clear();
        this.validating.clear();
        
        // 调用父类销毁方法
        super.destroy();
        
        this.logger.info('Form', 'destroy', '表单已销毁');
    }
    // #endregion
}
// #endregion

// #region 导出常量
export { 
    FIELD_TYPES, 
    VALIDATION_RULES, 
    FORM_LAYOUTS, 
    FORM_SIZES 
};
// #endregion

// #region 导出
export default Form;
// #endregion