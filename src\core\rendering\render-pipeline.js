/**
 * SmartOffice 2.0 渲染管道
 * 统一渲染流程的核心执行引擎，协调各个组件完成渲染任务
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-12-19
 */

import { DocumentModel } from './document-model.js';
import { StyleManager } from './style-manager.js';
import { PositionManager } from './position-manager.js';

/**
 * 渲染管道类
 * 负责协调整个渲染流程，确保各个阶段的正确执行
 */
export class RenderPipeline {
    /**
     * 构造函数
     * @param {Object} config - 渲染配置
     */
    constructor(config = {}) {
        this.config = {
            // 渲染模式
            mode: 'production', // 'development', 'production'
            
            // 性能配置
            enableCache: true,
            enableOptimization: true,
            enableParallel: false,
            
            // 调试配置
            enableDebug: false,
            enableProfiling: false,
            
            // 错误处理
            errorHandling: 'throw', // 'throw', 'log', 'ignore'
            
            // 渲染阶段配置
            stages: {
                validation: { enabled: true, timeout: 5000 },
                preparation: { enabled: true, timeout: 10000 },
                content: { enabled: true, timeout: 30000 },
                styling: { enabled: true, timeout: 15000 },
                layout: { enabled: true, timeout: 20000 },
                positioning: { enabled: true, timeout: 15000 },
                output: { enabled: true, timeout: 25000 }
            },
            
            ...config
        };
        
        // 管理器实例
        this.styleManager = null;
        this.positionManager = null;
        
        // 渲染状态
        this.isRendering = false;
        this.currentStage = null;
        this.renderContext = null;
        
        // 性能统计
        this.performanceStats = {
            totalRenders: 0,
            successfulRenders: 0,
            failedRenders: 0,
            averageRenderTime: 0,
            stageStats: {}
        };
        
        // 错误收集
        this.errors = [];
        this.warnings = [];
        
        // 事件监听器
        this.eventListeners = new Map();
        
        // 渲染缓存
        this.renderCache = new Map();
        
        // 中间件
        this.middlewares = [];
        
        console.log('[RenderPipeline] 渲染管道初始化完成');
    }
    
    /**
     * 设置样式管理器
     * @param {StyleManager} styleManager - 样式管理器实例
     */
    setStyleManager(styleManager) {
        this.styleManager = styleManager;
        console.log('[RenderPipeline] 样式管理器已设置');
    }
    
    /**
     * 设置位置管理器
     * @param {PositionManager} positionManager - 位置管理器实例
     */
    setPositionManager(positionManager) {
        this.positionManager = positionManager;
        console.log('[RenderPipeline] 位置管理器已设置');
    }
    
    /**
     * 添加中间件
     * @param {Function} middleware - 中间件函数
     */
    use(middleware) {
        if (typeof middleware !== 'function') {
            throw new Error('中间件必须是函数');
        }
        
        this.middlewares.push(middleware);
        console.log('[RenderPipeline] 中间件已添加');
    }
    
    /**
     * 执行渲染
     * @param {DocumentModel|Object} document - 文档模型或文档数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<Object>} 渲染结果
     */
    async render(document, options = {}) {
        if (this.isRendering) {
            throw new Error('渲染管道正在执行中，请等待当前渲染完成');
        }
        
        const startTime = Date.now();
        this.isRendering = true;
        this.performanceStats.totalRenders++;
        
        try {
            console.log('[RenderPipeline] 开始渲染流程');
            
            // 创建渲染上下文
            this.renderContext = this._createRenderContext(document, options);
            
            // 执行中间件（前置）
            await this._executeMiddlewares('before', this.renderContext);
            
            // 执行渲染阶段
            const result = await this._executeRenderStages(this.renderContext);
            
            // 执行中间件（后置）
            await this._executeMiddlewares('after', this.renderContext, result);
            
            // 更新性能统计
            const renderTime = Date.now() - startTime;
            this._updatePerformanceStats(renderTime, true);
            
            // 触发完成事件
            this._emitEvent('render:complete', { result, renderTime, context: this.renderContext });
            
            console.log(`[RenderPipeline] 渲染完成，耗时: ${renderTime}ms`);
            
            return {
                success: true,
                result,
                renderTime,
                context: this.renderContext,
                stats: this._getStageStats()
            };
            
        } catch (error) {
            // 更新性能统计
            const renderTime = Date.now() - startTime;
            this._updatePerformanceStats(renderTime, false);
            
            // 错误处理
            this._handleError(error);
            
            // 触发错误事件
            this._emitEvent('render:error', { error, renderTime, context: this.renderContext });
            
            console.error('[RenderPipeline] 渲染失败:', error);
            
            return {
                success: false,
                error: error.message,
                renderTime,
                context: this.renderContext,
                stats: this._getStageStats()
            };
            
        } finally {
            this.isRendering = false;
            this.currentStage = null;
            this.renderContext = null;
        }
    }
    
    /**
     * 添加事件监听器
     * @param {string} event - 事件名称
     * @param {Function} listener - 监听器函数
     */
    on(event, listener) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        
        this.eventListeners.get(event).push(listener);
        console.log(`[RenderPipeline] 事件监听器已添加: ${event}`);
    }
    
    /**
     * 移除事件监听器
     * @param {string} event - 事件名称
     * @param {Function} listener - 监听器函数
     */
    off(event, listener) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(listener);
            if (index > -1) {
                listeners.splice(index, 1);
                console.log(`[RenderPipeline] 事件监听器已移除: ${event}`);
            }
        }
    }
    
    /**
     * 获取性能统计
     * @returns {Object} 性能统计信息
     */
    getPerformanceStats() {
        return {
            ...this.performanceStats,
            cacheSize: this.renderCache.size,
            isRendering: this.isRendering,
            currentStage: this.currentStage
        };
    }
    
    /**
     * 清除缓存
     */
    clearCache() {
        this.renderCache.clear();
        console.log('[RenderPipeline] 渲染缓存已清除');
    }
    
    /**
     * 重置统计信息
     */
    resetStats() {
        this.performanceStats = {
            totalRenders: 0,
            successfulRenders: 0,
            failedRenders: 0,
            averageRenderTime: 0,
            stageStats: {}
        };
        
        this.errors = [];
        this.warnings = [];
        
        console.log('[RenderPipeline] 统计信息已重置');
    }
    
    /**
     * 更新配置
     * @param {Object} newConfig - 新配置
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        console.log('[RenderPipeline] 配置已更新');
    }
    
    /**
     * 销毁渲染管道
     */
    destroy() {
        this.renderCache.clear();
        this.eventListeners.clear();
        this.middlewares = [];
        this.errors = [];
        this.warnings = [];
        
        console.log('[RenderPipeline] 渲染管道已销毁');
    }
    
    // ==================== 私有方法 ====================
    
    /**
     * 创建渲染上下文
     * @param {DocumentModel|Object} document - 文档
     * @param {Object} options - 选项
     * @returns {Object} 渲染上下文
     * @private
     */
    _createRenderContext(document, options) {
        // 确保文档是DocumentModel实例
        const documentModel = document instanceof DocumentModel 
            ? document 
            : new DocumentModel(document);
        
        return {
            document: documentModel,
            options: {
                format: 'html',
                theme: 'default',
                quality: 'high',
                enableOptimization: this.config.enableOptimization,
                ...options
            },
            startTime: Date.now(),
            stageResults: {},
            metadata: {
                pipelineVersion: '1.0.0',
                renderMode: this.config.mode,
                timestamp: new Date().toISOString()
            }
        };
    }
    
    /**
     * 执行渲染阶段
     * @param {Object} context - 渲染上下文
     * @returns {Promise<Object>} 渲染结果
     * @private
     */
    async _executeRenderStages(context) {
        const stages = [
            { name: 'validation', handler: this._validateStage },
            { name: 'preparation', handler: this._preparationStage },
            { name: 'content', handler: this._contentStage },
            { name: 'styling', handler: this._stylingStage },
            { name: 'layout', handler: this._layoutStage },
            { name: 'positioning', handler: this._positioningStage },
            { name: 'output', handler: this._outputStage }
        ];
        
        for (const stage of stages) {
            if (!this.config.stages[stage.name]?.enabled) {
                console.log(`[RenderPipeline] 跳过阶段: ${stage.name}`);
                continue;
            }
            
            await this._executeStage(stage.name, stage.handler, context);
        }
        
        return context.stageResults.output;
    }
    
    /**
     * 执行单个阶段
     * @param {string} stageName - 阶段名称
     * @param {Function} handler - 处理函数
     * @param {Object} context - 渲染上下文
     * @private
     */
    async _executeStage(stageName, handler, context) {
        const stageConfig = this.config.stages[stageName];
        const startTime = Date.now();
        
        this.currentStage = stageName;
        
        try {
            console.log(`[RenderPipeline] 执行阶段: ${stageName}`);
            
            // 触发阶段开始事件
            this._emitEvent(`stage:${stageName}:start`, { context });
            
            // 执行阶段处理
            const result = await this._executeWithTimeout(
                handler.bind(this),
                [context],
                stageConfig.timeout
            );
            
            // 保存阶段结果
            context.stageResults[stageName] = result;
            
            // 更新阶段统计
            const stageTime = Date.now() - startTime;
            this._updateStageStats(stageName, stageTime, true);
            
            // 触发阶段完成事件
            this._emitEvent(`stage:${stageName}:complete`, { context, result, stageTime });
            
            console.log(`[RenderPipeline] 阶段完成: ${stageName}, 耗时: ${stageTime}ms`);
            
        } catch (error) {
            const stageTime = Date.now() - startTime;
            this._updateStageStats(stageName, stageTime, false);
            
            // 触发阶段错误事件
            this._emitEvent(`stage:${stageName}:error`, { context, error, stageTime });
            
            throw new Error(`阶段 '${stageName}' 执行失败: ${error.message}`);
        }
    }
    
    /**
     * 带超时的执行
     * @param {Function} fn - 执行函数
     * @param {Array} args - 参数
     * @param {number} timeout - 超时时间
     * @returns {Promise} 执行结果
     * @private
     */
    async _executeWithTimeout(fn, args, timeout) {
        return new Promise((resolve, reject) => {
            const timer = setTimeout(() => {
                reject(new Error(`执行超时: ${timeout}ms`));
            }, timeout);
            
            Promise.resolve(fn(...args))
                .then(result => {
                    clearTimeout(timer);
                    resolve(result);
                })
                .catch(error => {
                    clearTimeout(timer);
                    reject(error);
                });
        });
    }
    
    /**
     * 验证阶段
     * @param {Object} context - 渲染上下文
     * @returns {Promise<Object>} 验证结果
     * @private
     */
    async _validateStage(context) {
        const { document, options } = context;
        
        // 验证文档模型
        const documentValidation = document.validate();
        if (!documentValidation.isValid) {
            throw new Error(`文档验证失败: ${documentValidation.errors.join(', ')}`);
        }
        
        // 验证渲染选项
        const optionsValidation = this._validateRenderOptions(options);
        if (!optionsValidation.isValid) {
            throw new Error(`选项验证失败: ${optionsValidation.errors.join(', ')}`);
        }
        
        // 验证依赖
        const dependencyValidation = this._validateDependencies();
        if (!dependencyValidation.isValid) {
            throw new Error(`依赖验证失败: ${dependencyValidation.errors.join(', ')}`);
        }
        
        return {
            documentValid: true,
            optionsValid: true,
            dependenciesValid: true,
            warnings: [
                ...documentValidation.warnings,
                ...optionsValidation.warnings,
                ...dependencyValidation.warnings
            ]
        };
    }
    
    /**
     * 准备阶段
     * @param {Object} context - 渲染上下文
     * @returns {Promise<Object>} 准备结果
     * @private
     */
    async _preparationStage(context) {
        const { document, options } = context;
        
        // 解析模板
        await document.parseTemplate();
        
        // 绑定数据
        await document.bindData();
        
        // 预处理组件
        const components = this._extractComponents(document);
        
        // 准备渲染环境
        const environment = this._prepareRenderEnvironment(options);
        
        return {
            templateParsed: true,
            dataBound: true,
            components,
            environment
        };
    }
    
    /**
     * 内容阶段
     * @param {Object} context - 渲染上下文
     * @returns {Promise<Object>} 内容结果
     * @private
     */
    async _contentStage(context) {
        const { document } = context;
        const { components } = context.stageResults.preparation;
        
        // 渲染内容
        const content = await document.renderContent();
        
        // 处理组件
        const processedComponents = await this._processComponents(components, context);
        
        // 生成DOM结构
        const domStructure = this._generateDOMStructure(content, processedComponents);
        
        return {
            content,
            components: processedComponents,
            domStructure
        };
    }
    
    /**
     * 样式阶段
     * @param {Object} context - 渲染上下文
     * @returns {Promise<Object>} 样式结果
     * @private
     */
    async _stylingStage(context) {
        const { options } = context;
        const { components } = context.stageResults.content;
        
        if (!this.styleManager) {
            throw new Error('样式管理器未设置');
        }
        
        // 编译样式
        const compiledStyles = await this.styleManager.compileStyles(
            options.theme,
            components,
            options.format
        );
        
        // 应用样式
        const appliedStyles = await document.applyStyles(compiledStyles);
        
        return {
            compiledStyles,
            appliedStyles
        };
    }
    
    /**
     * 布局阶段
     * @param {Object} context - 渲染上下文
     * @returns {Promise<Object>} 布局结果
     * @private
     */
    async _layoutStage(context) {
        const { document } = context;
        const { domStructure } = context.stageResults.content;
        
        // 计算布局
        const layout = await document.calculateLayout(domStructure);
        
        // 处理分页
        const pagination = this._handlePagination(layout, context);
        
        return {
            layout,
            pagination
        };
    }
    
    /**
     * 定位阶段
     * @param {Object} context - 渲染上下文
     * @returns {Promise<Object>} 定位结果
     * @private
     */
    async _positioningStage(context) {
        const { document, options } = context;
        const { layout } = context.stageResults.layout;
        
        if (!this.positionManager) {
            throw new Error('位置管理器未设置');
        }
        
        // 计算位置
        const positions = await document.calculatePositions(layout);
        
        // 注册元素到位置管理器
        for (const element of positions.elements) {
            this.positionManager.registerElement(element.id, element);
        }
        
        // 检测碰撞
        const collisions = this._detectCollisions(positions.elements);
        
        return {
            positions,
            collisions
        };
    }
    
    /**
     * 输出阶段
     * @param {Object} context - 渲染上下文
     * @returns {Promise<Object>} 输出结果
     * @private
     */
    async _outputStage(context) {
        const { options } = context;
        const { appliedStyles } = context.stageResults.styling;
        const { positions } = context.stageResults.positioning;
        
        // 生成最终输出
        const output = await this._generateOutput({
            styles: appliedStyles,
            positions,
            format: options.format,
            quality: options.quality
        });
        
        // 优化输出
        const optimizedOutput = options.enableOptimization 
            ? this._optimizeOutput(output, options)
            : output;
        
        return {
            output: optimizedOutput,
            metadata: {
                format: options.format,
                size: this._calculateOutputSize(optimizedOutput),
                generatedAt: new Date().toISOString()
            }
        };
    }
    
    /**
     * 执行中间件
     * @param {string} phase - 阶段 ('before', 'after')
     * @param {Object} context - 渲染上下文
     * @param {Object} result - 渲染结果（仅after阶段）
     * @private
     */
    async _executeMiddlewares(phase, context, result = null) {
        for (const middleware of this.middlewares) {
            try {
                await middleware(phase, context, result);
            } catch (error) {
                console.warn(`[RenderPipeline] 中间件执行失败 (${phase}):`, error);
            }
        }
    }
    
    /**
     * 验证渲染选项
     * @param {Object} options - 渲染选项
     * @returns {Object} 验证结果
     * @private
     */
    _validateRenderOptions(options) {
        const errors = [];
        const warnings = [];
        
        // 验证格式
        const supportedFormats = ['html', 'pdf', 'image', 'print'];
        if (!supportedFormats.includes(options.format)) {
            errors.push(`不支持的输出格式: ${options.format}`);
        }
        
        // 验证主题
        if (options.theme && typeof options.theme !== 'string') {
            errors.push('主题必须是字符串类型');
        }
        
        // 验证质量
        const supportedQualities = ['low', 'medium', 'high'];
        if (options.quality && !supportedQualities.includes(options.quality)) {
            warnings.push(`不支持的质量设置: ${options.quality}，将使用默认值`);
        }
        
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
    
    /**
     * 验证依赖
     * @returns {Object} 验证结果
     * @private
     */
    _validateDependencies() {
        const errors = [];
        const warnings = [];
        
        if (!this.styleManager) {
            warnings.push('样式管理器未设置，样式功能将受限');
        }
        
        if (!this.positionManager) {
            warnings.push('位置管理器未设置，位置功能将受限');
        }
        
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
    
    /**
     * 提取组件
     * @param {DocumentModel} document - 文档模型
     * @returns {Array} 组件列表
     * @private
     */
    _extractComponents(document) {
        // 从文档中提取组件信息
        return document.getComponents ? document.getComponents() : [];
    }
    
    /**
     * 准备渲染环境
     * @param {Object} options - 渲染选项
     * @returns {Object} 渲染环境
     * @private
     */
    _prepareRenderEnvironment(options) {
        return {
            format: options.format,
            theme: options.theme,
            quality: options.quality,
            timestamp: Date.now()
        };
    }
    
    /**
     * 处理组件
     * @param {Array} components - 组件列表
     * @param {Object} context - 渲染上下文
     * @returns {Promise<Array>} 处理后的组件
     * @private
     */
    async _processComponents(components, context) {
        const processedComponents = [];
        
        for (const component of components) {
            try {
                const processed = await this._processComponent(component, context);
                processedComponents.push(processed);
            } catch (error) {
                console.warn(`[RenderPipeline] 组件处理失败: ${component.id}`, error);
                // 继续处理其他组件
            }
        }
        
        return processedComponents;
    }
    
    /**
     * 处理单个组件
     * @param {Object} component - 组件
     * @param {Object} context - 渲染上下文
     * @returns {Promise<Object>} 处理后的组件
     * @private
     */
    async _processComponent(component, context) {
        // 组件处理逻辑
        return {
            ...component,
            processed: true,
            processedAt: Date.now()
        };
    }
    
    /**
     * 生成DOM结构
     * @param {Object} content - 内容
     * @param {Array} components - 组件列表
     * @returns {Object} DOM结构
     * @private
     */
    _generateDOMStructure(content, components) {
        return {
            content,
            components,
            structure: 'dom-tree',
            generatedAt: Date.now()
        };
    }
    
    /**
     * 处理分页
     * @param {Object} layout - 布局
     * @param {Object} context - 渲染上下文
     * @returns {Object} 分页信息
     * @private
     */
    _handlePagination(layout, context) {
        return {
            pages: 1,
            pageBreaks: [],
            layout
        };
    }
    
    /**
     * 检测碰撞
     * @param {Array} elements - 元素列表
     * @returns {Array} 碰撞列表
     * @private
     */
    _detectCollisions(elements) {
        const collisions = [];
        
        if (this.positionManager) {
            for (const element of elements) {
                const elementCollisions = this.positionManager.detectCollisions(element);
                collisions.push(...elementCollisions);
            }
        }
        
        return collisions;
    }
    
    /**
     * 生成输出
     * @param {Object} data - 输出数据
     * @returns {Promise<Object>} 输出结果
     * @private
     */
    async _generateOutput(data) {
        const { styles, positions, format, quality } = data;
        
        // 根据格式生成不同的输出
        switch (format) {
            case 'html':
                return this._generateHTMLOutput(styles, positions, quality);
            case 'pdf':
                return this._generatePDFOutput(styles, positions, quality);
            case 'image':
                return this._generateImageOutput(styles, positions, quality);
            case 'print':
                return this._generatePrintOutput(styles, positions, quality);
            default:
                throw new Error(`不支持的输出格式: ${format}`);
        }
    }
    
    /**
     * 生成HTML输出
     * @param {Object} styles - 样式
     * @param {Object} positions - 位置
     * @param {string} quality - 质量
     * @returns {Object} HTML输出
     * @private
     */
    _generateHTMLOutput(styles, positions, quality) {
        return {
            type: 'html',
            content: '<div>HTML输出内容</div>',
            styles: styles.css,
            metadata: {
                quality,
                generatedAt: Date.now()
            }
        };
    }
    
    /**
     * 生成PDF输出
     * @param {Object} styles - 样式
     * @param {Object} positions - 位置
     * @param {string} quality - 质量
     * @returns {Object} PDF输出
     * @private
     */
    _generatePDFOutput(styles, positions, quality) {
        return {
            type: 'pdf',
            content: 'PDF二进制数据',
            metadata: {
                quality,
                generatedAt: Date.now()
            }
        };
    }
    
    /**
     * 生成图片输出
     * @param {Object} styles - 样式
     * @param {Object} positions - 位置
     * @param {string} quality - 质量
     * @returns {Object} 图片输出
     * @private
     */
    _generateImageOutput(styles, positions, quality) {
        return {
            type: 'image',
            content: '图片二进制数据',
            metadata: {
                quality,
                generatedAt: Date.now()
            }
        };
    }
    
    /**
     * 生成打印输出
     * @param {Object} styles - 样式
     * @param {Object} positions - 位置
     * @param {string} quality - 质量
     * @returns {Object} 打印输出
     * @private
     */
    _generatePrintOutput(styles, positions, quality) {
        return {
            type: 'print',
            content: '<div>打印输出内容</div>',
            styles: styles.css,
            metadata: {
                quality,
                generatedAt: Date.now()
            }
        };
    }
    
    /**
     * 优化输出
     * @param {Object} output - 输出
     * @param {Object} options - 选项
     * @returns {Object} 优化后的输出
     * @private
     */
    _optimizeOutput(output, options) {
        // 输出优化逻辑
        return {
            ...output,
            optimized: true,
            optimizedAt: Date.now()
        };
    }
    
    /**
     * 计算输出大小
     * @param {Object} output - 输出
     * @returns {number} 输出大小（字节）
     * @private
     */
    _calculateOutputSize(output) {
        return JSON.stringify(output).length;
    }
    
    /**
     * 更新性能统计
     * @param {number} renderTime - 渲染时间
     * @param {boolean} success - 是否成功
     * @private
     */
    _updatePerformanceStats(renderTime, success) {
        if (success) {
            this.performanceStats.successfulRenders++;
        } else {
            this.performanceStats.failedRenders++;
        }
        
        // 更新平均渲染时间
        const totalSuccessful = this.performanceStats.successfulRenders;
        if (totalSuccessful > 0) {
            this.performanceStats.averageRenderTime = 
                (this.performanceStats.averageRenderTime * (totalSuccessful - 1) + renderTime) / totalSuccessful;
        }
    }
    
    /**
     * 更新阶段统计
     * @param {string} stageName - 阶段名称
     * @param {number} stageTime - 阶段时间
     * @param {boolean} success - 是否成功
     * @private
     */
    _updateStageStats(stageName, stageTime, success) {
        if (!this.performanceStats.stageStats[stageName]) {
            this.performanceStats.stageStats[stageName] = {
                totalExecutions: 0,
                successfulExecutions: 0,
                failedExecutions: 0,
                averageTime: 0
            };
        }
        
        const stats = this.performanceStats.stageStats[stageName];
        stats.totalExecutions++;
        
        if (success) {
            stats.successfulExecutions++;
            stats.averageTime = (stats.averageTime * (stats.successfulExecutions - 1) + stageTime) / stats.successfulExecutions;
        } else {
            stats.failedExecutions++;
        }
    }
    
    /**
     * 获取阶段统计
     * @returns {Object} 阶段统计
     * @private
     */
    _getStageStats() {
        return { ...this.performanceStats.stageStats };
    }
    
    /**
     * 处理错误
     * @param {Error} error - 错误对象
     * @private
     */
    _handleError(error) {
        this.errors.push({
            message: error.message,
            stack: error.stack,
            timestamp: Date.now(),
            stage: this.currentStage
        });
        
        if (this.config.errorHandling === 'throw') {
            throw error;
        } else if (this.config.errorHandling === 'log') {
            console.error('[RenderPipeline] 错误:', error);
        }
        // 'ignore' 模式不做任何处理
    }
    
    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {Object} data - 事件数据
     * @private
     */
    _emitEvent(event, data) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            for (const listener of listeners) {
                try {
                    listener(data);
                } catch (error) {
                    console.warn(`[RenderPipeline] 事件监听器执行失败 (${event}):`, error);
                }
            }
        }
    }
}