/**
 * @file 配置管理器
 * @description 统一管理SmartOffice系统的所有配置，支持动态配置更新和配置验证
 */

// #region 导入依赖模块
import { EventEmitter } from '../core/events/event-emitter.js';
import { deepClone } from '../core/utils/index.js';
import { getLogger } from '../core/utils/logger.js';
// #endregion

// #region ConfigurationManager 配置管理器类
/**
 * @class ConfigurationManager - 配置管理器
 * @description 统一管理系统配置，支持配置验证、动态更新和持久化
 */
export class ConfigurationManager extends EventEmitter {
    /**
     * 构造函数 - 创建配置管理器
     * @param {Object} initialConfig - 初始配置
     */
    constructor(initialConfig = {}) {
        super();
        
        // 初始化日志记录器
        this.logger = getLogger();
        this.logger.startPerformanceMark('config_manager_construction', 'ConfigurationManager', 'constructor');
        this.logger.info('ConfigurationManager', 'constructor', '开始构造配置管理器', {
            hasInitialConfig: !!initialConfig,
            initialConfigKeys: Object.keys(initialConfig || {})
        });
        
        this.name = 'ConfigurationManager';
        this.version = '1.0.0';
        this.isInitialized = false;
        
        // 配置存储
        this.config = {};
        this.defaultConfig = {};
        this.configSchema = {};
        this.configHistory = [];
        
        this.logger.trace('ConfigurationManager', 'constructor', '基础存储初始化完成');
        
        // 配置管理选项
        this.options = {
            enablePersistence: true,
            enableHistory: true,
            enableValidation: true,
            maxHistorySize: 20,
            autoSave: true,
            storageKey: 'smartoffice_config',
            ...initialConfig.options
        };
        
        this.logger.debug('ConfigurationManager', 'constructor', '配置选项设置完成', {
            options: this.options
        });
        
        // 配置监听器
        this.watchers = new Map();
        this.validators = new Map();
        
        // 设置默认配置
        this.logger.trace('ConfigurationManager', 'constructor', '开始设置默认配置');
        this._setupDefaultConfig();
        
        // 合并初始配置
        if (initialConfig) {
            this.logger.debug('ConfigurationManager', 'constructor', '合并初始配置', {
                defaultConfigKeys: Object.keys(this.defaultConfig),
                initialConfigKeys: Object.keys(initialConfig)
            });
            this.config = this._mergeConfig(this.defaultConfig, initialConfig);
        } else {
            this.logger.debug('ConfigurationManager', 'constructor', '使用默认配置');
            this.config = deepClone(this.defaultConfig);
        }
        
        const constructionDuration = this.logger.endPerformanceMark('config_manager_construction', 'ConfigurationManager', 'constructor');
        this.logger.info('ConfigurationManager', 'constructor', '✅ 配置管理器构造完成', {
            duration: `${constructionDuration?.toFixed(2)}ms`,
            configSectionsCount: Object.keys(this.config).length,
            hasWatchers: this.watchers.size > 0,
            hasValidators: this.validators.size > 0
        });
        
        // 记录构造统计
        this.logger.incrementCounter('config_manager_instances', 'ConfigurationManager');
    }

    /**
     * 初始化配置管理器
     */
    async initialize() {
        this.logger.startPerformanceMark('config_manager_initialize', 'ConfigurationManager', 'initialize');
        this.logger.info('ConfigurationManager', 'initialize', '🔧 正在初始化配置管理器...');
        
        try {
            // 设置配置模式
            this.logger.debug('ConfigurationManager', 'initialize', '步骤 1/5: 设置配置模式');
            this._setupConfigSchema();
            this.logger.trace('ConfigurationManager', 'initialize', '配置模式设置完成');
            
            // 加载持久化配置
            if (this.options.enablePersistence) {
                this.logger.debug('ConfigurationManager', 'initialize', '步骤 2/5: 加载持久化配置');
                await this._loadPersistedConfig();
                this.logger.trace('ConfigurationManager', 'initialize', '持久化配置加载完成');
            } else {
                this.logger.debug('ConfigurationManager', 'initialize', '步骤 2/5: 跳过持久化配置（已禁用）');
            }
            
            // 验证配置
            if (this.options.enableValidation) {
                this.logger.debug('ConfigurationManager', 'initialize', '步骤 3/5: 验证配置');
                this._validateConfig();
                this.logger.trace('ConfigurationManager', 'initialize', '配置验证完成');
            } else {
                this.logger.debug('ConfigurationManager', 'initialize', '步骤 3/5: 跳过配置验证（已禁用）');
            }
            
            // 设置自动保存
            if (this.options.autoSave) {
                this.logger.debug('ConfigurationManager', 'initialize', '步骤 4/5: 设置自动保存');
                this._setupAutoSave();
                this.logger.trace('ConfigurationManager', 'initialize', '自动保存设置完成');
            } else {
                this.logger.debug('ConfigurationManager', 'initialize', '步骤 4/5: 跳过自动保存（已禁用）');
            }
            
            // 标记为已初始化
            this.logger.debug('ConfigurationManager', 'initialize', '步骤 5/5: 完成初始化');
            this.isInitialized = true;
            
            // 触发初始化事件
            this.emit('config:initialized', {
                manager: this.name,
                configKeys: Object.keys(this.config),
                timestamp: new Date().toISOString()
            });
            
            const initDuration = this.logger.endPerformanceMark('config_manager_initialize', 'ConfigurationManager', 'initialize');
            this.logger.info('ConfigurationManager', 'initialize', '✅ 配置管理器初始化完成', {
                duration: `${initDuration?.toFixed(2)}ms`,
                configSections: Object.keys(this.config),
                watchersCount: this.watchers.size,
                validatorsCount: this.validators.size,
                enabledFeatures: {
                    persistence: this.options.enablePersistence,
                    history: this.options.enableHistory,
                    validation: this.options.enableValidation,
                    autoSave: this.options.autoSave
                }
            });
            
            // 记录初始化统计
            this.logger.incrementCounter('config_manager_initializations', 'ConfigurationManager');
            
        } catch (error) {
            this.logger.error('ConfigurationManager', 'initialize', '❌ 配置管理器初始化失败', {
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }

    /**
     * 设置默认配置
     * @private
     */
    _setupDefaultConfig() {
        this.defaultConfig = {
            // 应用基础配置
            app: {
                name: 'SmartOffice',
                version: '2.0.0',
                language: 'zh-CN',
                theme: 'default',
                debug: false
            },
            
            // UI配置
            ui: {
                showToolbar: true,
                showSidebar: true,
                showStatusBar: true,
                enableKeyboardShortcuts: true,
                enableAnimations: true,
                compactMode: false,
                autoHideMenus: false
            },
            
            // 文档配置
            document: {
                defaultType: 'receipt',
                autoSave: true,
                autoSaveInterval: 30000, // 30秒
                enableVersioning: true,
                maxVersions: 10
            },
            
            // 模板配置
            template: {
                defaultTemplate: 'default',
                enableCustomTemplates: true,
                templateCacheSize: 50,
                autoReload: true
            },
            
            // 渲染配置
            rendering: {
                enableCache: true,
                cacheSize: 100,
                enableOptimization: true,
                renderTimeout: 10000, // 10秒
                enableProgressTracking: true
            },
            
            // 导出配置
            export: {
                defaultFormat: 'pdf',
                enableBatch: true,
                maxBatchSize: 100,
                enableProgress: true,
                autoDownload: true,
                compressionLevel: 'medium',
                quality: 'high'
            },
            
            // 工作流配置
            workflow: {
                enableWorkflow: true,
                autoPreview: true,
                autoSave: true,
                smartTemplateSelection: true,
                batchProcessing: false,
                maxConcurrentWorkflows: 5
            },
            
            // 性能配置
            performance: {
                enableLazyLoading: true,
                enableVirtualScrolling: true,
                chunkSize: 50,
                debounceDelay: 300,
                throttleDelay: 100,
                enableMemoryOptimization: true
            },
            
            // 安全配置
            security: {
                enableCSP: true,
                enableSanitization: true,
                maxFileSize: 10485760, // 10MB
                allowedFileTypes: ['pdf', 'png', 'jpg', 'jpeg'],
                enableEncryption: false
            },
            
            // 网络配置
            network: {
                timeout: 30000, // 30秒
                retryCount: 3,
                retryDelay: 1000,
                enableOfflineMode: true,
                enableCaching: true
            },
            
            // 日志配置
            logging: {
                level: 'info', // debug, info, warn, error
                enableConsole: true,
                enableFile: false,
                maxLogSize: 1048576, // 1MB
                enableRemoteLogging: false
            },
            
            // 插件配置
            plugins: {
                enablePlugins: true,
                autoLoad: true,
                enableSandbox: true,
                maxPlugins: 20,
                pluginTimeout: 5000
            }
        };
    }

    /**
     * 设置配置模式
     * @private
     */
    _setupConfigSchema() {
        this.configSchema = {
            app: {
                name: { type: 'string', required: true },
                version: { type: 'string', required: true },
                language: { type: 'string', enum: ['zh-CN', 'en-US'] },
                theme: { type: 'string', enum: ['default', 'dark', 'light'] },
                debug: { type: 'boolean' }
            },
            
            ui: {
                showToolbar: { type: 'boolean' },
                showSidebar: { type: 'boolean' },
                showStatusBar: { type: 'boolean' },
                enableKeyboardShortcuts: { type: 'boolean' },
                enableAnimations: { type: 'boolean' },
                compactMode: { type: 'boolean' },
                autoHideMenus: { type: 'boolean' }
            },
            
            document: {
                defaultType: { type: 'string', enum: ['receipt', 'invoice', 'driver-agreement', 'quotation'] },
                autoSave: { type: 'boolean' },
                autoSaveInterval: { type: 'number', min: 5000, max: 300000 },
                enableVersioning: { type: 'boolean' },
                maxVersions: { type: 'number', min: 1, max: 50 }
            },
            
            export: {
                defaultFormat: { type: 'string', enum: ['pdf', 'png', 'jpg', 'print'] },
                enableBatch: { type: 'boolean' },
                maxBatchSize: { type: 'number', min: 1, max: 1000 },
                enableProgress: { type: 'boolean' },
                autoDownload: { type: 'boolean' },
                compressionLevel: { type: 'string', enum: ['low', 'medium', 'high'] },
                quality: { type: 'string', enum: ['low', 'medium', 'high'] }
            },
            
            performance: {
                enableLazyLoading: { type: 'boolean' },
                enableVirtualScrolling: { type: 'boolean' },
                chunkSize: { type: 'number', min: 10, max: 1000 },
                debounceDelay: { type: 'number', min: 0, max: 2000 },
                throttleDelay: { type: 'number', min: 0, max: 1000 },
                enableMemoryOptimization: { type: 'boolean' }
            },
            
            security: {
                enableCSP: { type: 'boolean' },
                enableSanitization: { type: 'boolean' },
                maxFileSize: { type: 'number', min: 1048576, max: 104857600 }, // 1MB - 100MB
                allowedFileTypes: { type: 'array', items: { type: 'string' } },
                enableEncryption: { type: 'boolean' }
            },
            
            logging: {
                level: { type: 'string', enum: ['debug', 'info', 'warn', 'error'] },
                enableConsole: { type: 'boolean' },
                enableFile: { type: 'boolean' },
                maxLogSize: { type: 'number', min: 102400, max: 10485760 }, // 100KB - 10MB
                enableRemoteLogging: { type: 'boolean' }
            }
        };
    }

    /**
     * 获取配置值
     * @param {string} key - 配置键，支持点号分隔的嵌套路径
     * @param {*} defaultValue - 默认值
     * @returns {*} 配置值
     */
    get(key, defaultValue = undefined) {
        this.logger.trace('ConfigurationManager', 'get', '获取配置值', {
            key,
            hasDefaultValue: defaultValue !== undefined
        });
        
        if (!key) {
            return this.config;
        }
        
        const value = this._getNestedValue(this.config, key);
        const result = value !== undefined ? value : defaultValue;
        
        this.logger.trace('ConfigurationManager', 'get', '配置值获取成功', {
            key,
            hasValue: value !== undefined,
            valueType: typeof result
        });
        
        // 记录配置访问统计
        this.logger.incrementCounter(`config_get_${key.replace(/\./g, '_')}`, 'ConfigurationManager');
        
        return result;
    }

    /**
     * 设置配置值
     * @param {string|Object} key - 配置键或配置对象
     * @param {*} value - 配置值
     * @param {Object} options - 设置选项
     * @returns {boolean} 是否设置成功
     */
    set(key, value = undefined, options = {}) {
        this.logger.startPerformanceMark('config_set', 'ConfigurationManager', 'set');
        this.logger.debug('ConfigurationManager', 'set', '开始设置配置值', {
            key,
            valueType: typeof value,
            options
        });
        
        try {
            let changes = {};
            
            if (typeof key === 'object') {
                // 批量设置
                changes = key;
                options = value || {};
            } else {
                // 单个设置
                changes[key] = value;
            }
            
            // 获取旧值
            const oldValue = this._getNestedValue(this.config, key);
            
            this.logger.trace('ConfigurationManager', 'set', '获取旧配置值', {
                key,
                oldValueType: typeof oldValue,
                hasOldValue: oldValue !== undefined
            });
            
            // 如果值没有变化，直接返回
            if (oldValue === value && !options.force) {
                this.logger.debug('ConfigurationManager', 'set', '配置值未变化，跳过设置', {
                    key,
                    value
                });
                return true;
            }
            
            // 验证新值
            if (this.options.enableValidation && !options.skipValidation) {
                this.logger.trace('ConfigurationManager', 'set', '开始配置值验证', {
                    key,
                    value
                });
                
                const isValid = this._validateValue(key, value, this.configSchema);
                if (!isValid) {
                    this.logger.error('ConfigurationManager', 'set', '配置值验证失败', {
                        key,
                        value,
                        valueType: typeof value
                    });
                    return false;
                }
                
                this.logger.trace('ConfigurationManager', 'set', '配置值验证通过');
            }
            
            // 记录历史
            if (this.options.enableHistory && !options.skipHistory) {
                this.logger.trace('ConfigurationManager', 'set', '记录配置历史');
                this._recordConfigHistory();
            }
            
            // 应用更改
            const oldConfig = deepClone(this.config);
            
            for (const [changeKey, changeValue] of Object.entries(changes)) {
                this._setNestedValue(this.config, changeKey, changeValue);
            }
            
            this.logger.trace('ConfigurationManager', 'set', '配置值设置完成', {
                key,
                newValue: value,
                oldValue
            });
            
            // 通知监听器
            this._notifyWatchers(key, value, oldValue);
            
            // 触发变更事件
            this.emit('config:changed', {
                key,
                newValue: value,
                oldValue,
                timestamp: new Date().toISOString()
            });
            
            // 自动保存
            if (this.options.autoSave && !options.skipSave) {
                this.logger.trace('ConfigurationManager', 'set', '触发自动保存');
                this._saveConfig();
            }
            
            const setDuration = this.logger.endPerformanceMark('config_set', 'ConfigurationManager', 'set');
            this.logger.info('ConfigurationManager', 'set', '✅ 配置值设置成功', {
                key,
                duration: `${setDuration?.toFixed(2)}ms`,
                valueChanged: oldValue !== value
            });
            
            // 记录设置统计
            this.logger.incrementCounter(`config_set_${key.replace(/\./g, '_')}`, 'ConfigurationManager');
            
            return true;
            
        } catch (error) {
            this.logger.error('ConfigurationManager', 'set', '设置配置值失败', {
                key,
                value,
                error: error.message,
                stack: error.stack
            });
            this.emit('config:error', {
                operation: 'set',
                key,
                value,
                error: error.message
            });
            return false;
        }
    }

    /**
     * 重置配置
     * @param {string} key - 配置键，如果为空则重置所有配置
     * @param {Object} options - 重置选项
     */
    reset(key = null, options = {}) {
        try {
            if (key) {
                // 重置特定配置
                const defaultValue = this._getNestedValue(this.defaultConfig, key);
                this.set(key, defaultValue, options);
            } else {
                // 重置所有配置
                const oldConfig = deepClone(this.config);
                this.config = deepClone(this.defaultConfig);
                
                this.emit('config:reset', {
                    oldConfig,
                    newConfig: this.config
                });
                
                if (this.options.autoSave && !options.skipSave) {
                    this._saveConfig();
                }
            }
            
        } catch (error) {
            console.error('配置重置失败:', error);
            this.emit('config:error', {
                operation: 'reset',
                key,
                error: error.message
            });
        }
    }

    /**
     * 监听配置变更
     * @param {string} key - 配置键
     * @param {Function} callback - 回调函数
     * @param {Object} options - 监听选项
     * @returns {string} 监听器ID
     */
    watch(key, callback, options = {}) {
        const watcherId = `watcher_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        this.watchers.set(watcherId, {
            key,
            callback,
            options,
            created: Date.now()
        });
        
        this.emit('config:watcher:added', {
            watcherId,
            key,
            watcherCount: this.watchers.size
        });
        
        return watcherId;
    }

    /**
     * 取消监听配置变更
     * @param {string} watcherId - 监听器ID
     * @returns {boolean} 是否成功取消
     */
    unwatch(watcherId) {
        const removed = this.watchers.delete(watcherId);
        
        if (removed) {
            this.emit('config:watcher:removed', {
                watcherId,
                watcherCount: this.watchers.size
            });
        }
        
        return removed;
    }

    /**
     * 注册配置验证器
     * @param {string} key - 配置键
     * @param {Function} validator - 验证函数
     */
    addValidator(key, validator) {
        if (!this.validators.has(key)) {
            this.validators.set(key, []);
        }
        
        this.validators.get(key).push(validator);
        
        this.emit('config:validator:added', {
            key,
            validatorCount: this.validators.get(key).length
        });
    }

    /**
     * 移除配置验证器
     * @param {string} key - 配置键
     * @param {Function} validator - 验证函数
     * @returns {boolean} 是否成功移除
     */
    removeValidator(key, validator) {
        if (!this.validators.has(key)) {
            return false;
        }
        
        const validators = this.validators.get(key);
        const index = validators.indexOf(validator);
        
        if (index !== -1) {
            validators.splice(index, 1);
            
            if (validators.length === 0) {
                this.validators.delete(key);
            }
            
            this.emit('config:validator:removed', {
                key,
                validatorCount: validators.length
            });
            
            return true;
        }
        
        return false;
    }

    /**
     * 导出配置
     * @param {Array<string>} keys - 要导出的配置键
     * @returns {Object} 导出的配置
     */
    export(keys = null) {
        if (!keys) {
            return deepClone(this.config);
        }
        
        const exported = {};
        
        for (const key of keys) {
            const value = this.get(key);
            if (value !== undefined) {
                this._setNestedValue(exported, key, value);
            }
        }
        
        return exported;
    }

    /**
     * 导入配置
     * @param {Object} config - 要导入的配置
     * @param {Object} options - 导入选项
     * @returns {boolean} 是否导入成功
     */
    import(config, options = {}) {
        try {
            if (!config || typeof config !== 'object') {
                throw new Error('Invalid config object');
            }
            
            // 验证导入的配置
            if (this.options.enableValidation && !options.skipValidation) {
                this._validateConfig(config);
            }
            
            // 合并配置
            if (options.merge !== false) {
                this.config = this._mergeConfig(this.config, config);
            } else {
                this.config = deepClone(config);
            }
            
            this.emit('config:imported', {
                importedKeys: Object.keys(config),
                merged: options.merge !== false
            });
            
            // 自动保存
            if (this.options.autoSave && !options.skipSave) {
                this._saveConfig();
            }
            
            return true;
            
        } catch (error) {
            console.error('配置导入失败:', error);
            this.emit('config:error', {
                operation: 'import',
                error: error.message
            });
            return false;
        }
    }

    /**
     * 获取配置历史
     * @param {number} limit - 限制数量
     * @returns {Array} 配置历史
     */
    getHistory(limit = 10) {
        return this.configHistory.slice(-limit);
    }

    /**
     * 回滚到历史配置
     * @param {number} index - 历史索引
     * @returns {boolean} 是否回滚成功
     */
    rollback(index) {
        try {
            if (index < 0 || index >= this.configHistory.length) {
                throw new Error('Invalid history index');
            }
            
            const historyConfig = this.configHistory[index];
            const oldConfig = deepClone(this.config);
            
            this.config = deepClone(historyConfig.config);
            
            this.emit('config:rollback', {
                fromTimestamp: Date.now(),
                toTimestamp: historyConfig.timestamp,
                oldConfig,
                newConfig: this.config
            });
            
            if (this.options.autoSave) {
                this._saveConfig();
            }
            
            return true;
            
        } catch (error) {
            console.error('配置回滚失败:', error);
            this.emit('config:error', {
                operation: 'rollback',
                index,
                error: error.message
            });
            return false;
        }
    }

    // #region 私有方法

    /**
     * 获取嵌套值
     * @param {Object} obj - 对象
     * @param {string} path - 路径
     * @returns {*} 值
     * @private
     */
    _getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : undefined;
        }, obj);
    }

    /**
     * 设置嵌套值
     * @param {Object} obj - 对象
     * @param {string} path - 路径
     * @param {*} value - 值
     * @private
     */
    _setNestedValue(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        
        const target = keys.reduce((current, key) => {
            if (!current[key] || typeof current[key] !== 'object') {
                current[key] = {};
            }
            return current[key];
        }, obj);
        
        target[lastKey] = value;
    }

    /**
     * 合并配置
     * @param {Object} target - 目标配置
     * @param {Object} source - 源配置
     * @returns {Object} 合并后的配置
     * @private
     */
    _mergeConfig(target, source) {
        const result = deepClone(target);
        
        for (const [key, value] of Object.entries(source)) {
            if (value && typeof value === 'object' && !Array.isArray(value)) {
                result[key] = this._mergeConfig(result[key] || {}, value);
            } else {
                result[key] = value;
            }
        }
        
        return result;
    }

    /**
     * 验证配置
     * @param {Object} config - 要验证的配置
     * @private
     */
    _validateConfig(config = this.config) {
        for (const [section, sectionConfig] of Object.entries(config)) {
            if (this.configSchema[section]) {
                this._validateSection(section, sectionConfig, this.configSchema[section]);
            }
        }
    }

    /**
     * 验证配置段
     * @param {string} sectionName - 段名称
     * @param {Object} sectionConfig - 段配置
     * @param {Object} sectionSchema - 段模式
     * @private
     */
    _validateSection(sectionName, sectionConfig, sectionSchema) {
        for (const [key, value] of Object.entries(sectionConfig)) {
            if (sectionSchema[key]) {
                this._validateValue(`${sectionName}.${key}`, value, sectionSchema[key]);
            }
        }
    }

    /**
     * 验证配置值
     * @param {string} path - 配置路径
     * @param {*} value - 配置值
     * @param {Object} schema - 值模式
     * @private
     */
    _validateValue(path, value, schema) {
        // 类型验证
        if (schema.type && typeof value !== schema.type) {
            throw new Error(`Config ${path}: expected ${schema.type}, got ${typeof value}`);
        }
        
        // 必需验证
        if (schema.required && (value === undefined || value === null)) {
            throw new Error(`Config ${path}: required value is missing`);
        }
        
        // 枚举验证
        if (schema.enum && !schema.enum.includes(value)) {
            throw new Error(`Config ${path}: value must be one of ${schema.enum.join(', ')}`);
        }
        
        // 数值范围验证
        if (schema.type === 'number') {
            if (schema.min !== undefined && value < schema.min) {
                throw new Error(`Config ${path}: value must be >= ${schema.min}`);
            }
            if (schema.max !== undefined && value > schema.max) {
                throw new Error(`Config ${path}: value must be <= ${schema.max}`);
            }
        }
        
        // 数组验证
        if (schema.type === 'array' && Array.isArray(value)) {
            if (schema.items) {
                value.forEach((item, index) => {
                    this._validateValue(`${path}[${index}]`, item, schema.items);
                });
            }
        }
        
        // 自定义验证器
        if (this.validators.has(path)) {
            const validators = this.validators.get(path);
            for (const validator of validators) {
                if (!validator(value)) {
                    throw new Error(`Config ${path}: custom validation failed`);
                }
            }
        }
    }

    /**
     * 验证配置变更
     * @param {Object} changes - 配置变更
     * @private
     */
    _validateChanges(changes) {
        for (const [key, value] of Object.entries(changes)) {
            // 创建临时配置进行验证
            const tempConfig = deepClone(this.config);
            this._setNestedValue(tempConfig, key, value);
            
            // 验证变更后的配置
            this._validateConfig(tempConfig);
        }
    }

    /**
     * 记录配置历史
     * @private
     */
    _recordConfigHistory() {
        if (!this.options.enableHistory) {
            return;
        }
        
        this.configHistory.push({
            timestamp: Date.now(),
            config: deepClone(this.config)
        });
        
        // 限制历史记录大小
        if (this.configHistory.length > this.options.maxHistorySize) {
            this.configHistory.shift();
        }
    }

    /**
     * 触发配置变更事件
     * @param {Object} changes - 配置变更
     * @param {Object} oldConfig - 旧配置
     * @private
     */
    _emitConfigChanges(changes, oldConfig) {
        // 触发全局变更事件
        this.emit('config:changed', {
            changes,
            oldConfig,
            newConfig: this.config,
            timestamp: Date.now()
        });
        
        // 触发特定键的变更事件
        for (const [key, value] of Object.entries(changes)) {
            const oldValue = this._getNestedValue(oldConfig, key);
            
            this.emit(`config:changed:${key}`, {
                key,
                oldValue,
                newValue: value,
                timestamp: Date.now()
            });
            
            // 通知监听器
            this._notifyWatchers(key, value, oldValue);
        }
    }

    /**
     * 通知配置监听器
     * @param {string} key - 配置键
     * @param {*} newValue - 新值
     * @param {*} oldValue - 旧值
     * @private
     */
    _notifyWatchers(key, newValue, oldValue) {
        for (const [watcherId, watcher] of this.watchers) {
            if (this._keyMatches(key, watcher.key)) {
                try {
                    watcher.callback({
                        key,
                        newValue,
                        oldValue,
                        timestamp: Date.now()
                    });
                } catch (error) {
                    console.error(`配置监听器 ${watcherId} 执行失败:`, error);
                }
            }
        }
    }

    /**
     * 检查键是否匹配
     * @param {string} key - 实际键
     * @param {string} pattern - 模式键
     * @returns {boolean} 是否匹配
     * @private
     */
    _keyMatches(key, pattern) {
        if (key === pattern) {
            return true;
        }
        
        // 支持通配符匹配
        if (pattern.includes('*')) {
            const regex = new RegExp(pattern.replace(/\*/g, '.*'));
            return regex.test(key);
        }
        
        // 支持前缀匹配
        return key.startsWith(pattern + '.');
    }

    /**
     * 加载持久化配置
     * @private
     */
    async _loadPersistedConfig() {
        try {
            if (typeof localStorage !== 'undefined') {
                const stored = localStorage.getItem(this.options.storageKey);
                if (stored) {
                    const persistedConfig = JSON.parse(stored);
                    this.config = this._mergeConfig(this.config, persistedConfig);
                    
                    this.emit('config:loaded', {
                        source: 'localStorage',
                        keys: Object.keys(persistedConfig)
                    });
                }
            }
        } catch (error) {
            console.warn('加载持久化配置失败:', error);
        }
    }

    /**
     * 保存配置
     * @private
     */
    _saveConfig() {
        try {
            if (typeof localStorage !== 'undefined') {
                localStorage.setItem(this.options.storageKey, JSON.stringify(this.config));
                
                this.emit('config:saved', {
                    destination: 'localStorage',
                    timestamp: Date.now()
                });
            }
        } catch (error) {
            console.warn('保存配置失败:', error);
        }
    }

    /**
     * 设置自动保存
     * @private
     */
    _setupAutoSave() {
        this.on('config:changed', () => {
            // 防抖保存
            if (this._saveTimeout) {
                clearTimeout(this._saveTimeout);
            }
            
            this._saveTimeout = setTimeout(() => {
                this._saveConfig();
            }, 1000); // 1秒后保存
        });
    }
    // #endregion

    /**
     * 获取配置管理器信息
     * @returns {Object} 管理器信息
     */
    getInfo() {
        return {
            name: this.name,
            version: this.version,
            isInitialized: this.isInitialized,
            configSections: Object.keys(this.config),
            watcherCount: this.watchers.size,
            validatorCount: this.validators.size,
            historySize: this.configHistory.length,
            options: { ...this.options }
        };
    }

    /**
     * 销毁配置管理器
     */
    async destroy() {
        // 清理定时器
        if (this._saveTimeout) {
            clearTimeout(this._saveTimeout);
        }
        
        // 清理监听器和验证器
        this.watchers.clear();
        this.validators.clear();
        
        // 清理事件监听器
        this.removeAllListeners();
        
        // 最后保存配置
        if (this.options.autoSave) {
            this._saveConfig();
        }
        
        this.isInitialized = false;
        
        console.log('🔄 配置管理器已销毁');
    }
}
// #endregion

// #region 工厂函数和便捷方法
/**
 * 创建配置管理器
 * @param {Object} config - 初始配置
 * @returns {ConfigurationManager} 配置管理器实例
 */
export function createConfigurationManager(config = {}) {
    return new ConfigurationManager(config);
}

/**
 * 获取默认配置
 * @returns {Object} 默认配置
 */
export function getDefaultConfig() {
    const manager = new ConfigurationManager();
    return manager.defaultConfig;
}
// #endregion 