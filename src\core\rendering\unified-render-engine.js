/**
 * SmartOffice 2.0 统一渲染引擎
 * 负责预览和导出的统一渲染逻辑，确保输出一致性
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-12-19
 */

import { DocumentModel } from './document-model.js';
import { StyleManager } from './style-manager.js';
import { PositionManager } from './position-manager.js';
import { RenderPipeline } from './render-pipeline.js';
import { ConfigManager } from './config-manager.js';

/**
 * 统一渲染引擎类
 * 所有预览和导出功能的核心入口点
 */
export class UnifiedRenderEngine {
    /**
     * 构造函数
     * @param {Object} config - 渲染引擎配置
     * @param {string} config.theme - 主题名称
     * @param {Object} config.layout - 布局配置
     * @param {Object} config.styles - 样式配置
     * @param {Object} config.positions - 位置配置
     */
    constructor(config = {}) {
        this.config = new ConfigManager(config);
        this.documentModel = null;
        this.styleManager = new StyleManager(this.config.getStyleConfig());
        this.positionManager = new PositionManager(this.config.getPositionConfig());
        this.renderPipeline = new RenderPipeline(this.config.getPipelineConfig());
        
        // 渲染状态
        this.isRendering = false;
        this.lastRenderTime = null;
        this.renderCache = new Map();
        
        // 性能统计
        this.stats = {
            totalRenders: 0,
            averageRenderTime: 0,
            cacheHits: 0,
            cacheMisses: 0
        };
        
        this._initializeEngine();
    }
    
    /**
     * 初始化渲染引擎
     * @private
     */
    _initializeEngine() {
        // 注册渲染管道阶段
        this.renderPipeline.registerStage('content', this._renderContent.bind(this));
        this.renderPipeline.registerStage('styles', this._applyStyles.bind(this));
        this.renderPipeline.registerStage('layout', this._calculateLayout.bind(this));
        this.renderPipeline.registerStage('positions', this._calculatePositions.bind(this));
        this.renderPipeline.registerStage('output', this._generateOutput.bind(this));
        
        // 设置错误处理
        this.renderPipeline.onError(this._handleRenderError.bind(this));
        
        console.log('[UnifiedRenderEngine] 渲染引擎初始化完成');
    }
    
    /**
     * 主要渲染方法
     * @param {Object} template - 模板对象
     * @param {Object} data - 数据对象
     * @param {Object} options - 渲染选项
     * @param {string} options.format - 输出格式 ('html', 'pdf', 'image')
     * @param {string} options.target - 渲染目标 ('preview', 'export')
     * @param {boolean} options.useCache - 是否使用缓存
     * @returns {Promise<Object>} 渲染结果
     */
    async render(template, data, options = {}) {
        const startTime = performance.now();
        
        try {
            // 验证输入参数
            this._validateRenderParams(template, data, options);
            
            // 设置渲染状态
            this.isRendering = true;
            
            // 生成缓存键
            const cacheKey = this._generateCacheKey(template, data, options);
            
            // 检查缓存
            if (options.useCache !== false && this.renderCache.has(cacheKey)) {
                this.stats.cacheHits++;
                console.log('[UnifiedRenderEngine] 使用缓存结果');
                return this.renderCache.get(cacheKey);
            }
            
            this.stats.cacheMisses++;
            
            // 创建文档模型
            this.documentModel = await this.createDocumentModel(template, data, options);
            
            // 执行渲染管道
            const renderResult = await this.renderPipeline.execute(
                this.documentModel, 
                options
            );
            
            // 缓存结果
            if (options.useCache !== false) {
                this.renderCache.set(cacheKey, renderResult);
            }
            
            // 更新统计信息
            this._updateStats(startTime);
            
            return renderResult;
            
        } catch (error) {
            console.error('[UnifiedRenderEngine] 渲染失败:', error);
            throw new Error(`渲染失败: ${error.message}`);
        } finally {
            this.isRendering = false;
            this.lastRenderTime = Date.now();
        }
    }
    
    /**
     * 创建标准化文档模型
     * @param {Object} template - 模板对象
     * @param {Object} data - 数据对象
     * @param {Object} options - 选项
     * @returns {Promise<DocumentModel>} 文档模型
     */
    async createDocumentModel(template, data, options) {
        const documentModel = new DocumentModel();
        
        // 设置基础信息
        documentModel.setTemplate(template);
        documentModel.setData(data);
        documentModel.setOptions(options);
        
        // 解析模板内容
        await documentModel.parseTemplate();
        
        // 绑定数据
        await documentModel.bindData();
        
        // 验证文档模型
        documentModel.validate();
        
        return documentModel;
    }
    
    /**
     * 渲染内容阶段
     * @param {DocumentModel} documentModel - 文档模型
     * @param {Object} options - 选项
     * @returns {Promise<DocumentModel>} 处理后的文档模型
     * @private
     */
    async _renderContent(documentModel, options) {
        console.log('[UnifiedRenderEngine] 执行内容渲染阶段');
        
        // 渲染主要内容
        await documentModel.renderContent();
        
        // 渲染页眉页脚
        await documentModel.renderHeaderFooter();
        
        // 渲染印章和签名
        await documentModel.renderStampsAndSignatures();
        
        return documentModel;
    }
    
    /**
     * 应用样式阶段
     * @param {DocumentModel} documentModel - 文档模型
     * @param {Object} options - 选项
     * @returns {Promise<DocumentModel>} 处理后的文档模型
     * @private
     */
    async _applyStyles(documentModel, options) {
        console.log('[UnifiedRenderEngine] 执行样式应用阶段');
        
        // 编译样式
        const compiledStyles = await this.styleManager.compileStyles(
            options.theme || 'default',
            documentModel.getComponents(),
            options.format
        );
        
        // 应用样式到文档模型
        documentModel.applyStyles(compiledStyles);
        
        return documentModel;
    }
    
    /**
     * 计算布局阶段
     * @param {DocumentModel} documentModel - 文档模型
     * @param {Object} options - 选项
     * @returns {Promise<DocumentModel>} 处理后的文档模型
     * @private
     */
    async _calculateLayout(documentModel, options) {
        console.log('[UnifiedRenderEngine] 执行布局计算阶段');
        
        // 计算页面布局
        const layoutInfo = await this.positionManager.calculatePageLayout(
            documentModel.getPageConfig(),
            options.format
        );
        
        // 应用布局信息
        documentModel.applyLayout(layoutInfo);
        
        return documentModel;
    }
    
    /**
     * 计算位置阶段
     * @param {DocumentModel} documentModel - 文档模型
     * @param {Object} options - 选项
     * @returns {Promise<DocumentModel>} 处理后的文档模型
     * @private
     */
    async _calculatePositions(documentModel, options) {
        console.log('[UnifiedRenderEngine] 执行位置计算阶段');
        
        // 计算所有元素的绝对位置
        const positionInfo = await this.positionManager.calculateAllPositions(
            documentModel.getElements(),
            documentModel.getLayout(),
            options.format
        );
        
        // 应用位置信息
        documentModel.applyPositions(positionInfo);
        
        // 验证位置约束
        this.positionManager.validateConstraints(documentModel);
        
        return documentModel;
    }
    
    /**
     * 生成输出阶段
     * @param {DocumentModel} documentModel - 文档模型
     * @param {Object} options - 选项
     * @returns {Promise<Object>} 渲染结果
     * @private
     */
    async _generateOutput(documentModel, options) {
        console.log('[UnifiedRenderEngine] 执行输出生成阶段');
        
        // 根据格式生成输出
        const outputGenerator = this._getOutputGenerator(options.format);
        const result = await outputGenerator.generate(documentModel, options);
        
        return {
            content: result.content,
            metadata: result.metadata,
            format: options.format,
            target: options.target,
            timestamp: Date.now(),
            renderTime: result.renderTime
        };
    }
    
    /**
     * 获取输出生成器
     * @param {string} format - 输出格式
     * @returns {Object} 输出生成器
     * @private
     */
    _getOutputGenerator(format) {
        const generators = {
            html: () => import('./generators/html-generator.js').then(m => new m.HTMLGenerator()),
            pdf: () => import('./generators/pdf-generator.js').then(m => new m.PDFGenerator()),
            image: () => import('./generators/image-generator.js').then(m => new m.ImageGenerator())
        };
        
        if (!generators[format]) {
            throw new Error(`不支持的输出格式: ${format}`);
        }
        
        return generators[format]();
    }
    
    /**
     * 验证渲染参数
     * @param {Object} template - 模板
     * @param {Object} data - 数据
     * @param {Object} options - 选项
     * @private
     */
    _validateRenderParams(template, data, options) {
        if (!template) {
            throw new Error('模板参数不能为空');
        }
        
        if (!data) {
            throw new Error('数据参数不能为空');
        }
        
        const validFormats = ['html', 'pdf', 'image'];
        if (options.format && !validFormats.includes(options.format)) {
            throw new Error(`无效的输出格式: ${options.format}`);
        }
        
        const validTargets = ['preview', 'export'];
        if (options.target && !validTargets.includes(options.target)) {
            throw new Error(`无效的渲染目标: ${options.target}`);
        }
    }
    
    /**
     * 生成缓存键
     * @param {Object} template - 模板
     * @param {Object} data - 数据
     * @param {Object} options - 选项
     * @returns {string} 缓存键
     * @private
     */
    _generateCacheKey(template, data, options) {
        const keyData = {
            templateId: template.id || 'unknown',
            dataHash: this._hashObject(data),
            format: options.format || 'html',
            target: options.target || 'preview',
            theme: options.theme || 'default'
        };
        
        return btoa(JSON.stringify(keyData));
    }
    
    /**
     * 计算对象哈希值
     * @param {Object} obj - 对象
     * @returns {string} 哈希值
     * @private
     */
    _hashObject(obj) {
        const str = JSON.stringify(obj, Object.keys(obj).sort());
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return hash.toString();
    }
    
    /**
     * 处理渲染错误
     * @param {Error} error - 错误对象
     * @param {string} stage - 出错阶段
     * @private
     */
    _handleRenderError(error, stage) {
        console.error(`[UnifiedRenderEngine] 渲染阶段 '${stage}' 出错:`, error);
        
        // 记录错误统计
        this.stats.errors = (this.stats.errors || 0) + 1;
        
        // 可以在这里添加错误上报逻辑
        this._reportError(error, stage);
    }
    
    /**
     * 上报错误
     * @param {Error} error - 错误对象
     * @param {string} stage - 出错阶段
     * @private
     */
    _reportError(error, stage) {
        // 错误上报逻辑
        // 可以发送到监控系统或日志服务
    }
    
    /**
     * 更新性能统计
     * @param {number} startTime - 开始时间
     * @private
     */
    _updateStats(startTime) {
        const renderTime = performance.now() - startTime;
        this.stats.totalRenders++;
        this.stats.averageRenderTime = (
            (this.stats.averageRenderTime * (this.stats.totalRenders - 1) + renderTime) / 
            this.stats.totalRenders
        );
    }
    
    /**
     * 清除渲染缓存
     * @param {string} pattern - 缓存键模式（可选）
     */
    clearCache(pattern = null) {
        if (pattern) {
            const regex = new RegExp(pattern);
            for (const [key] of this.renderCache) {
                if (regex.test(key)) {
                    this.renderCache.delete(key);
                }
            }
        } else {
            this.renderCache.clear();
        }
        
        console.log('[UnifiedRenderEngine] 缓存已清除');
    }
    
    /**
     * 获取渲染统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.stats,
            cacheSize: this.renderCache.size,
            isRendering: this.isRendering,
            lastRenderTime: this.lastRenderTime
        };
    }
    
    /**
     * 更新配置
     * @param {Object} newConfig - 新配置
     */
    updateConfig(newConfig) {
        this.config.update(newConfig);
        
        // 更新子组件配置
        this.styleManager.updateConfig(this.config.getStyleConfig());
        this.positionManager.updateConfig(this.config.getPositionConfig());
        this.renderPipeline.updateConfig(this.config.getPipelineConfig());
        
        // 清除缓存，因为配置变更可能影响渲染结果
        this.clearCache();
        
        console.log('[UnifiedRenderEngine] 配置已更新');
    }
    
    /**
     * 销毁渲染引擎
     */
    destroy() {
        this.clearCache();
        this.renderPipeline.destroy();
        this.styleManager.destroy();
        this.positionManager.destroy();
        
        console.log('[UnifiedRenderEngine] 渲染引擎已销毁');
    }
}