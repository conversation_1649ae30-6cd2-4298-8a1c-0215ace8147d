/**
 * @file Gemini 2.5 Flash 自然语言处理器
 * @description 使用Google Gemini 2.5 Flash API进行智能文字和图片分析
 * - 支持文字内容的智能解析和信息提取
 * - 支持图片内容的OCR识别和信息提取
 * - 支持多模态输入（文字+图片）
 * - 提供高精度的结构化数据提取
 * <AUTHOR> Team
 */

import { getLogger } from './logger.js';

/**
 * @class GeminiNLPProcessor
 * @description Gemini 2.5 Flash 自然语言处理器类
 * 提供基于AI的文本解析、图片识别、信息提取等功能
 */
export class GeminiNLPProcessor {
    constructor(options = {}) {
        this.logger = getLogger();
        this.logger.info('GeminiNLPProcessor', 'constructor', '🤖 初始化Gemini 2.5 Flash处理器');
        
        // API配置 - 硬植入API密钥（自用）
        this.apiKey = options.apiKey || 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s'; // 请替换为您的实际API密钥
        this.apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent';
        this.model = 'gemini-2.0-flash-exp';
        
        // 处理配置
        this.config = {
            maxRetries: 3,
            timeout: 30000,
            temperature: 0.1, // 低温度确保一致性
            topP: 0.8,
            topK: 40,
            maxOutputTokens: 2048,
            ...options
        };
        
        // 性能统计
        this.stats = {
            processedTexts: 0,
            processedImages: 0,
            extractedFields: 0,
            processingTime: 0,
            apiCalls: 0,
            errors: 0
        };
        
        // 初始化提示模板
        this._initializePromptTemplates();
        
        this.logger.info('GeminiNLPProcessor', 'constructor', '✅ Gemini处理器初始化完成');
    }

    /**
     * @function _initializePromptTemplates
     * @description 初始化各种文档类型的提示模板
     */
    _initializePromptTemplates() {
        this.promptTemplates = {
            // 通用信息提取提示
            general: `你是一个专业的文档信息提取助手。请从提供的内容中提取结构化信息。

请严格按照以下JSON格式返回结果，不要添加任何其他文字：

{
  "documentType": "文档类型(receipt/invoice/quotation/driver_agreement)",
  "extractedData": {
    "customerName": "客户姓名",
    "amounts": ["金额数组"],
    "totalAmount": "总金额",
    "currency": "货币类型(RM/RMB)",
    "dates": ["日期数组"],
    "quantities": ["数量数组"],
    "services": ["服务项目数组"],
    "phones": ["电话号码数组"],
    "addresses": ["地址数组"],
    "paymentMethod": "支付方式",
    "channel": "销售渠道",
    "notes": "备注信息"
  },
  "confidence": 0.95,
  "metadata": {
    "processedAt": "处理时间",
    "language": "检测到的语言",
    "hasImage": false
  }
}

提取规则：
1. 金额：识别所有数字金额，包括单价、总价等
2. 货币：识别RM(令吉)、RMB(人民币)等货币类型
3. 姓名：识别客户、司机、联系人等姓名
4. 日期：识别各种日期格式
5. 服务：识别酒店、接机、包车等服务项目
6. 置信度：根据信息完整性和准确性评估(0-1)

请分析以下内容：`,

            // 收据专用提示
            receipt: `你是一个专业的收据信息提取助手。请从收据内容中提取以下信息：

返回JSON格式：
{
  "documentType": "receipt",
  "extractedData": {
    "receiptNumber": "收据编号",
    "customerName": "客户姓名",
    "totalAmount": "总金额",
    "currency": "货币类型",
    "issueDate": "开具日期",
    "services": ["服务项目"],
    "paymentMethod": "支付方式",
    "channel": "销售渠道"
  },
  "confidence": 0.95
}`,

            // 发票专用提示
            invoice: `你是一个专业的发票信息提取助手。请从发票内容中提取以下信息：

返回JSON格式：
{
  "documentType": "invoice",
  "extractedData": {
    "invoiceNumber": "发票号码",
    "buyerName": "购买方名称",
    "totalAmount": "总金额",
    "taxAmount": "税额",
    "currency": "货币类型",
    "issueDate": "开票日期",
    "items": ["项目明细"],
    "taxRate": "税率"
  },
  "confidence": 0.95
}`,

            // 报价单专用提示
            quotation: `你是一个专业的报价单信息提取助手。请从报价单内容中提取以下信息：

返回JSON格式：
{
  "documentType": "quotation",
  "extractedData": {
    "quotationNumber": "报价单号",
    "clientName": "客户名称",
    "estimatedAmount": "预估金额",
    "currency": "货币类型",
    "quotationDate": "报价日期",
    "validUntil": "有效期至",
    "services": ["服务项目"],
    "notes": "备注说明"
  },
  "confidence": 0.95
}`,

            // 司机协议专用提示
            driver_agreement: `你是一个专业的司机协议信息提取助手。请从协议内容中提取以下信息：

返回JSON格式：
{
  "documentType": "driver_agreement",
  "extractedData": {
    "agreementNumber": "协议编号",
    "driverName": "司机姓名",
    "driverPhone": "司机电话",
    "vehiclePlate": "车牌号码",
    "idNumber": "身份证号",
    "signingDate": "签约日期",
    "route": "行程路线",
    "amount": "协议金额",
    "currency": "货币类型"
  },
  "confidence": 0.95
}`
        };
    }

    /**
     * @function processText
     * @description 处理纯文本内容
     * @param {string} text - 输入文本
     * @param {string} documentType - 文档类型
     * @returns {Object} 提取的结构化数据
     */
    async processText(text, documentType = 'receipt') {
        const startTime = performance.now();
        this.logger.startPerformanceMark('gemini_process_text', 'GeminiNLPProcessor', 'processText');
        
        try {
            this.logger.info('GeminiNLPProcessor', 'processText', '🔍 开始处理文本', {
                textLength: text.length,
                documentType
            });

            if (!this.apiKey) {
                throw new Error('Gemini API密钥未配置');
            }

            // 构建请求
            const prompt = this._buildPrompt(text, documentType);
            const requestBody = {
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: this.config.temperature,
                    topP: this.config.topP,
                    topK: this.config.topK,
                    maxOutputTokens: this.config.maxOutputTokens,
                    responseMimeType: "application/json"
                }
            };

            // 调用API
            const result = await this._callGeminiAPI(requestBody);
            
            // 解析结果
            const extractedData = this._parseGeminiResponse(result, documentType);
            
            // 更新统计
            this.stats.processedTexts++;
            this.stats.extractedFields += Object.keys(extractedData.extractedData || {}).length;
            this.stats.apiCalls++;
            
            const duration = this.logger.endPerformanceMark('gemini_process_text', 'GeminiNLPProcessor', 'processText');
            this.stats.processingTime += duration || 0;
            
            this.logger.info('GeminiNLPProcessor', 'processText', '✅ 文本处理完成', {
                extractedFields: Object.keys(extractedData.extractedData || {}).length,
                confidence: extractedData.confidence,
                duration: `${duration?.toFixed(2)}ms`
            });
            
            return extractedData;
            
        } catch (error) {
            this.stats.errors++;
            this.logger.error('GeminiNLPProcessor', 'processText', '❌ 文本处理失败', { error });
            
            // 返回错误结果
            return {
                documentType,
                extractedData: {},
                confidence: 0,
                error: error.message,
                metadata: {
                    processedAt: new Date().toISOString(),
                    hasError: true
                }
            };
        }
    }

    /**
     * @function processImage
     * @description 处理图片内容（OCR + 信息提取）或多模态内容（文字+图片）
     * @param {File|string} image - 图片文件或base64字符串
     * @param {string} text - 可选的文本内容（用于多模态处理）
     * @param {string} documentType - 文档类型
     * @returns {Object} 提取的结构化数据
     */
    async processImage(image, text = '', documentType = 'receipt') {
        const startTime = performance.now();
        this.logger.startPerformanceMark('gemini_process_image', 'GeminiNLPProcessor', 'processImage');
        
        try {
            this.logger.info('GeminiNLPProcessor', 'processImage', '🖼️ 开始处理图片', {
                documentType,
                imageType: typeof image,
                hasText: !!text,
                isMultiModal: !!text
            });

            if (!this.apiKey) {
                throw new Error('Gemini API密钥未配置');
            }

            // 转换图片为base64
            const imageData = await this._prepareImageData(image);
            
            // 构建多模态请求
            const parts = [];
            
            // 添加文本提示
            if (text) {
                // 如果有文本，使用多模态提示
                const prompt = this._buildMultiModalPrompt(text, documentType);
                parts.push({ text: prompt });
            } else {
                // 纯图片处理
                const prompt = this._buildImagePrompt(documentType);
                parts.push({ text: prompt });
            }
            
            // 添加图片
            parts.push({
                inline_data: {
                    mime_type: imageData.mimeType,
                    data: imageData.base64
                }
            });
            
            const requestBody = {
                contents: [{ parts }],
                generationConfig: {
                    temperature: this.config.temperature,
                    topP: this.config.topP,
                    topK: this.config.topK,
                    maxOutputTokens: this.config.maxOutputTokens,
                    responseMimeType: "application/json"
                }
            };

            // 调用API
            const result = await this._callGeminiAPI(requestBody);
            
            // 解析结果
            const extractedData = this._parseGeminiResponse(result, documentType);
            extractedData.metadata = extractedData.metadata || {};
            extractedData.metadata.hasImage = true;
            extractedData.metadata.hasText = !!text;
            extractedData.metadata.isMultiModal = !!text;
            
            // 更新统计
            this.stats.processedImages++;
            this.stats.extractedFields += Object.keys(extractedData.extractedData || {}).length;
            this.stats.apiCalls++;
            
            const duration = this.logger.endPerformanceMark('gemini_process_image', 'GeminiNLPProcessor', 'processImage');
            this.stats.processingTime += duration || 0;
            
            this.logger.info('GeminiNLPProcessor', 'processImage', '✅ 图片处理完成', {
                extractedFields: Object.keys(extractedData.extractedData || {}).length,
                confidence: extractedData.confidence,
                duration: `${duration?.toFixed(2)}ms`
            });
            
            return extractedData;
            
        } catch (error) {
            this.stats.errors++;
            this.logger.error('GeminiNLPProcessor', 'processImage', '❌ 图片处理失败', { error });
            
            // 返回错误结果
            return {
                documentType,
                extractedData: {},
                confidence: 0,
                error: error.message,
                metadata: {
                    processedAt: new Date().toISOString(),
                    hasImage: true,
                    hasError: true
                }
            };
        }
    }

    /**
     * @function processMultiModal
     * @description 处理多模态内容（文字+图片）
     * @param {string} text - 文本内容
     * @param {File|string} image - 图片文件或base64字符串
     * @param {string} documentType - 文档类型
     * @returns {Object} 提取的结构化数据
     */
    async processMultiModal(text, image, documentType = 'receipt') {
        const startTime = performance.now();
        this.logger.startPerformanceMark('gemini_process_multimodal', 'GeminiNLPProcessor', 'processMultiModal');
        
        try {
            this.logger.info('GeminiNLPProcessor', 'processMultiModal', '🔄 开始处理多模态内容', {
                textLength: text.length,
                documentType,
                hasImage: !!image
            });

            if (!this.apiKey) {
                throw new Error('Gemini API密钥未配置');
            }

            // 准备内容部分
            const parts = [];
            
            // 添加文本提示
            const prompt = this._buildMultiModalPrompt(text, documentType);
            parts.push({ text: prompt });
            
            // 添加图片（如果有）
            if (image) {
                const imageData = await this._prepareImageData(image);
                parts.push({
                    inline_data: {
                        mime_type: imageData.mimeType,
                        data: imageData.base64
                    }
                });
            }

            // 构建请求
            const requestBody = {
                contents: [{ parts }],
                generationConfig: {
                    temperature: this.config.temperature,
                    topP: this.config.topP,
                    topK: this.config.topK,
                    maxOutputTokens: this.config.maxOutputTokens,
                    responseMimeType: "application/json"
                }
            };

            // 调用API
            const result = await this._callGeminiAPI(requestBody);
            
            // 解析结果
            const extractedData = this._parseGeminiResponse(result, documentType);
            extractedData.metadata = extractedData.metadata || {};
            extractedData.metadata.hasImage = !!image;
            extractedData.metadata.hasText = !!text;
            
            // 更新统计
            this.stats.processedTexts++;
            if (image) this.stats.processedImages++;
            this.stats.extractedFields += Object.keys(extractedData.extractedData || {}).length;
            this.stats.apiCalls++;
            
            const duration = this.logger.endPerformanceMark('gemini_process_multimodal', 'GeminiNLPProcessor', 'processMultiModal');
            this.stats.processingTime += duration || 0;
            
            this.logger.info('GeminiNLPProcessor', 'processMultiModal', '✅ 多模态处理完成', {
                extractedFields: Object.keys(extractedData.extractedData || {}).length,
                confidence: extractedData.confidence,
                duration: `${duration?.toFixed(2)}ms`
            });
            
            return extractedData;
            
        } catch (error) {
            this.stats.errors++;
            this.logger.error('GeminiNLPProcessor', 'processMultiModal', '❌ 多模态处理失败', { error });
            
            // 返回错误结果
            return {
                documentType,
                extractedData: {},
                confidence: 0,
                error: error.message,
                metadata: {
                    processedAt: new Date().toISOString(),
                    hasImage: !!image,
                    hasText: !!text,
                    hasError: true
                }
            };
        }
    }

    /**
     * @function _buildPrompt
     * @description 构建文本处理提示
     * @private
     */
    _buildPrompt(text, documentType) {
        const template = this.promptTemplates[documentType] || this.promptTemplates.general;
        return `${template}\n\n文本内容：\n${text}`;
    }

    /**
     * @function _buildImagePrompt
     * @description 构建图片处理提示
     * @private
     */
    _buildImagePrompt(documentType) {
        const template = this.promptTemplates[documentType] || this.promptTemplates.general;
        return `${template}\n\n请分析上传的图片，识别其中的文字内容并提取相关信息。`;
    }

    /**
     * @function _buildMultiModalPrompt
     * @description 构建多模态处理提示
     * @private
     */
    _buildMultiModalPrompt(text, documentType) {
        const template = this.promptTemplates[documentType] || this.promptTemplates.general;
        return `${template}\n\n请综合分析以下文本描述和图片内容，提取完整的结构化信息：\n\n文本描述：\n${text}\n\n请同时参考图片中的信息进行分析。`;
    }

    /**
     * @function _prepareImageData
     * @description 准备图片数据
     * @private
     */
    async _prepareImageData(image) {
        if (typeof image === 'string') {
            // 已经是base64字符串
            const mimeType = this._detectImageMimeType(image);
            const base64 = image.replace(/^data:image\/[a-z]+;base64,/, '');
            return { base64, mimeType };
        } else if (image instanceof File) {
            // 文件对象，需要转换
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => {
                    const result = reader.result;
                    const mimeType = image.type || 'image/jpeg';
                    const base64 = result.split(',')[1];
                    resolve({ base64, mimeType });
                };
                reader.onerror = reject;
                reader.readAsDataURL(image);
            });
        } else {
            throw new Error('不支持的图片格式');
        }
    }

    /**
     * @function _detectImageMimeType
     * @description 检测图片MIME类型
     * @private
     */
    _detectImageMimeType(base64String) {
        if (base64String.startsWith('data:image/')) {
            const match = base64String.match(/data:image\/([a-z]+);base64,/);
            return match ? `image/${match[1]}` : 'image/jpeg';
        }
        return 'image/jpeg'; // 默认类型
    }

    /**
     * @function _callGeminiAPI
     * @description 调用Gemini API
     * @private
     */
    async _callGeminiAPI(requestBody) {
        const url = `${this.apiUrl}?key=${this.apiKey}`;
        
        for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
            try {
                this.logger.debug('GeminiNLPProcessor', '_callGeminiAPI', `🌐 API调用尝试 ${attempt}/${this.config.maxRetries}`);
                
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody),
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`API调用失败: ${response.status} ${response.statusText} - ${errorText}`);
                }
                
                const result = await response.json();
                this.logger.debug('GeminiNLPProcessor', '_callGeminiAPI', '✅ API调用成功');
                return result;
                
            } catch (error) {
                this.logger.warn('GeminiNLPProcessor', '_callGeminiAPI', `⚠️ API调用失败 (尝试 ${attempt}/${this.config.maxRetries})`, { error: error.message });
                
                if (attempt === this.config.maxRetries) {
                    throw error;
                }
                
                // 指数退避
                const delay = Math.pow(2, attempt) * 1000;
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }

    /**
     * @function _parseGeminiResponse
     * @description 解析Gemini API响应
     * @private
     */
    _parseGeminiResponse(response, documentType) {
        try {
            if (!response.candidates || response.candidates.length === 0) {
                throw new Error('API响应中没有候选结果');
            }
            
            const candidate = response.candidates[0];
            if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
                throw new Error('API响应格式错误');
            }
            
            const textContent = candidate.content.parts[0].text;
            const parsedData = JSON.parse(textContent);
            
            // 验证和标准化数据
            return this._normalizeExtractedData(parsedData, documentType);
            
        } catch (error) {
            this.logger.error('GeminiNLPProcessor', '_parseGeminiResponse', '❌ 响应解析失败', { error });
            
            // 返回默认结构
            return {
                documentType,
                extractedData: {},
                confidence: 0,
                error: `响应解析失败: ${error.message}`,
                metadata: {
                    processedAt: new Date().toISOString(),
                    hasError: true
                }
            };
        }
    }

    /**
     * @function _normalizeExtractedData
     * @description 标准化提取的数据
     * @private
     */
    _normalizeExtractedData(data, documentType) {
        const normalized = {
            documentType: data.documentType || documentType,
            extractedData: data.extractedData || {},
            confidence: Math.min(Math.max(data.confidence || 0, 0), 1),
            metadata: {
                processedAt: new Date().toISOString(),
                language: data.metadata?.language || 'auto',
                hasImage: data.metadata?.hasImage || false,
                ...data.metadata
            }
        };
        
        // 根据文档类型优化数据
        normalized.extractedData = this._optimizeForDocumentType(normalized.extractedData, documentType);
        
        return normalized;
    }

    /**
     * @function _optimizeForDocumentType
     * @description 根据文档类型优化数据结构
     * @private
     */
    _optimizeForDocumentType(data, documentType) {
        const optimized = { ...data };
        
        switch (documentType) {
            case 'receipt':
                if (!optimized.receiptNumber) {
                    optimized.receiptNumber = this._generateReceiptNumber();
                }
                break;
                
            case 'invoice':
                if (!optimized.invoiceNumber) {
                    optimized.invoiceNumber = this._generateInvoiceNumber();
                }
                if (optimized.totalAmount && !optimized.taxAmount) {
                    optimized.taxAmount = (parseFloat(optimized.totalAmount) * 0.06).toFixed(2);
                }
                break;
                
            case 'quotation':
                if (!optimized.quotationNumber) {
                    optimized.quotationNumber = this._generateQuotationNumber();
                }
                if (!optimized.validUntil) {
                    optimized.validUntil = this._getValidUntilDate();
                }
                break;
                
            case 'driver_agreement':
                if (!optimized.agreementNumber) {
                    optimized.agreementNumber = this._generateAgreementNumber();
                }
                break;
        }
        
        return optimized;
    }

    /**
     * @function _generateReceiptNumber
     * @description 生成收据编号
     * @private
     */
    _generateReceiptNumber() {
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `R${year}${month}${day}${random}`;
    }

    /**
     * @function _generateInvoiceNumber
     * @description 生成发票编号
     * @private
     */
    _generateInvoiceNumber() {
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `INV${year}${month}${day}${random}`;
    }

    /**
     * @function _generateQuotationNumber
     * @description 生成报价单编号
     * @private
     */
    _generateQuotationNumber() {
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `Q${year}${month}${day}${random}`;
    }

    /**
     * @function _generateAgreementNumber
     * @description 生成协议编号
     * @private
     */
    _generateAgreementNumber() {
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `AGR${year}${month}${day}${random}`;
    }

    /**
     * @function _getValidUntilDate
     * @description 获取有效期日期（30天后）
     * @private
     */
    _getValidUntilDate() {
        const date = new Date();
        date.setDate(date.getDate() + 30);
        return date.toISOString().split('T')[0];
    }

    /**
     * @function setApiKey
     * @description 设置API密钥
     * @param {string} apiKey - API密钥
     */
    setApiKey(apiKey) {
        this.apiKey = apiKey;
        this.logger.info('GeminiNLPProcessor', 'setApiKey', '🔑 API密钥已更新');
    }

    /**
     * @function getStats
     * @description 获取处理统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.stats,
            averageProcessingTime: this.stats.apiCalls > 0 
                ? this.stats.processingTime / this.stats.apiCalls 
                : 0,
            successRate: this.stats.apiCalls > 0
                ? ((this.stats.apiCalls - this.stats.errors) / this.stats.apiCalls * 100).toFixed(2) + '%'
                : '0%'
        };
    }

    /**
     * @function reset
     * @description 重置统计信息
     */
    reset() {
        this.stats = {
            processedTexts: 0,
            processedImages: 0,
            extractedFields: 0,
            processingTime: 0,
            apiCalls: 0,
            errors: 0
        };
        this.logger.info('GeminiNLPProcessor', 'reset', '🔄 统计信息已重置');
    }
}

// #region 工厂函数和单例管理

let geminiNLPProcessorInstance = null;

/**
 * @function createGeminiNLPProcessor
 * @description 创建Gemini NLP处理器实例
 * @param {Object} options - 配置选项
 * @returns {GeminiNLPProcessor} 处理器实例
 */
export function createGeminiNLPProcessor(options = {}) {
    return new GeminiNLPProcessor(options);
}

/**
 * @function getGeminiNLPProcessor
 * @description 获取Gemini NLP处理器单例
 * @param {Object} options - 配置选项（仅在首次创建时使用）
 * @returns {GeminiNLPProcessor} 处理器实例
 */
export function getGeminiNLPProcessor(options = {}) {
    if (!geminiNLPProcessorInstance) {
        geminiNLPProcessorInstance = new GeminiNLPProcessor(options);
    }
    return geminiNLPProcessorInstance;
}

/**
 * @function setGeminiNLPProcessor
 * @description 设置Gemini NLP处理器单例
 * @param {GeminiNLPProcessor} instance - 处理器实例
 */
export function setGeminiNLPProcessor(instance) {
    geminiNLPProcessorInstance = instance;
}

// #endregion 