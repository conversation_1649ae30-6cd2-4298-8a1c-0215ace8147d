/**
 * @file 日期工具
 * @description 提供日期处理和格式化功能
 */

/**
 * @class DateUtils
 * @description 日期工具类，提供各种日期操作和格式化方法
 */
export class DateUtils {
    /**
     * 格式化日期
     * @param {Date|string|number} date - 日期对象、日期字符串或时间戳
     * @param {string} format - 格式字符串，支持 YYYY-MM-DD, DD/MM/YYYY 等
     * @param {string} locale - 语言环境，默认为 'zh-CN'
     * @returns {string} 格式化后的日期字符串
     */
    static format(date, format = 'YYYY-MM-DD', locale = 'zh-CN') {
        const dateObj = this.parseDate(date);
        
        if (!dateObj) {
            return '';
        }

        const year = dateObj.getFullYear();
        const month = dateObj.getMonth() + 1;
        const day = dateObj.getDate();
        const hours = dateObj.getHours();
        const minutes = dateObj.getMinutes();
        const seconds = dateObj.getSeconds();

        // 格式化映射
        const formatMap = {
            'YYYY': year.toString(),
            'YY': year.toString().slice(-2),
            'MM': month.toString().padStart(2, '0'),
            'M': month.toString(),
            'DD': day.toString().padStart(2, '0'),
            'D': day.toString(),
            'HH': hours.toString().padStart(2, '0'),
            'H': hours.toString(),
            'mm': minutes.toString().padStart(2, '0'),
            'm': minutes.toString(),
            'ss': seconds.toString().padStart(2, '0'),
            's': seconds.toString()
        };

        let result = format;
        
        // 按长度排序，避免短格式覆盖长格式
        const sortedKeys = Object.keys(formatMap).sort((a, b) => b.length - a.length);
        
        for (const key of sortedKeys) {
            result = result.replace(new RegExp(key, 'g'), formatMap[key]);
        }

        return result;
    }

    /**
     * 解析日期
     * @param {Date|string|number} date - 日期输入
     * @returns {Date|null} 日期对象或null
     */
    static parseDate(date) {
        if (date instanceof Date) {
            return isNaN(date.getTime()) ? null : date;
        }
        
        if (typeof date === 'string' || typeof date === 'number') {
            const parsed = new Date(date);
            return isNaN(parsed.getTime()) ? null : parsed;
        }
        
        return null;
    }

    /**
     * 获取当前日期字符串
     * @param {string} format - 格式字符串
     * @param {string} locale - 语言环境
     * @returns {string} 当前日期字符串
     */
    static now(format = 'YYYY-MM-DD', locale = 'zh-CN') {
        return this.format(new Date(), format, locale);
    }

    /**
     * 获取当前时间戳
     * @returns {number} 当前时间戳
     */
    static timestamp() {
        return Date.now();
    }

    /**
     * 添加天数
     * @param {Date|string|number} date - 基准日期
     * @param {number} days - 要添加的天数
     * @returns {Date|null} 新的日期对象
     */
    static addDays(date, days) {
        const dateObj = this.parseDate(date);
        
        if (!dateObj) {
            return null;
        }
        
        const result = new Date(dateObj);
        result.setDate(result.getDate() + days);
        return result;
    }

    /**
     * 添加月数
     * @param {Date|string|number} date - 基准日期
     * @param {number} months - 要添加的月数
     * @returns {Date|null} 新的日期对象
     */
    static addMonths(date, months) {
        const dateObj = this.parseDate(date);
        
        if (!dateObj) {
            return null;
        }
        
        const result = new Date(dateObj);
        result.setMonth(result.getMonth() + months);
        return result;
    }

    /**
     * 添加年数
     * @param {Date|string|number} date - 基准日期
     * @param {number} years - 要添加的年数
     * @returns {Date|null} 新的日期对象
     */
    static addYears(date, years) {
        const dateObj = this.parseDate(date);
        
        if (!dateObj) {
            return null;
        }
        
        const result = new Date(dateObj);
        result.setFullYear(result.getFullYear() + years);
        return result;
    }

    /**
     * 计算两个日期之间的天数差
     * @param {Date|string|number} date1 - 第一个日期
     * @param {Date|string|number} date2 - 第二个日期
     * @returns {number|null} 天数差，如果日期无效返回null
     */
    static daysBetween(date1, date2) {
        const dateObj1 = this.parseDate(date1);
        const dateObj2 = this.parseDate(date2);
        
        if (!dateObj1 || !dateObj2) {
            return null;
        }
        
        const timeDiff = Math.abs(dateObj2.getTime() - dateObj1.getTime());
        return Math.ceil(timeDiff / (1000 * 3600 * 24));
    }

    /**
     * 检查日期是否在指定范围内
     * @param {Date|string|number} date - 要检查的日期
     * @param {Date|string|number} startDate - 开始日期
     * @param {Date|string|number} endDate - 结束日期
     * @returns {boolean} 是否在范围内
     */
    static isInRange(date, startDate, endDate) {
        const dateObj = this.parseDate(date);
        const startObj = this.parseDate(startDate);
        const endObj = this.parseDate(endDate);
        
        if (!dateObj || !startObj || !endObj) {
            return false;
        }
        
        return dateObj >= startObj && dateObj <= endObj;
    }

    /**
     * 检查是否为今天
     * @param {Date|string|number} date - 要检查的日期
     * @returns {boolean} 是否为今天
     */
    static isToday(date) {
        const dateObj = this.parseDate(date);
        
        if (!dateObj) {
            return false;
        }
        
        const today = new Date();
        return dateObj.getFullYear() === today.getFullYear() &&
               dateObj.getMonth() === today.getMonth() &&
               dateObj.getDate() === today.getDate();
    }

    /**
     * 检查是否为周末
     * @param {Date|string|number} date - 要检查的日期
     * @returns {boolean} 是否为周末
     */
    static isWeekend(date) {
        const dateObj = this.parseDate(date);
        
        if (!dateObj) {
            return false;
        }
        
        const dayOfWeek = dateObj.getDay();
        return dayOfWeek === 0 || dayOfWeek === 6; // 0=Sunday, 6=Saturday
    }

    /**
     * 获取月份的第一天
     * @param {Date|string|number} date - 基准日期
     * @returns {Date|null} 月份第一天的日期对象
     */
    static getFirstDayOfMonth(date) {
        const dateObj = this.parseDate(date);
        
        if (!dateObj) {
            return null;
        }
        
        return new Date(dateObj.getFullYear(), dateObj.getMonth(), 1);
    }

    /**
     * 获取月份的最后一天
     * @param {Date|string|number} date - 基准日期
     * @returns {Date|null} 月份最后一天的日期对象
     */
    static getLastDayOfMonth(date) {
        const dateObj = this.parseDate(date);
        
        if (!dateObj) {
            return null;
        }
        
        return new Date(dateObj.getFullYear(), dateObj.getMonth() + 1, 0);
    }

    /**
     * 获取年龄
     * @param {Date|string|number} birthDate - 出生日期
     * @param {Date|string|number} referenceDate - 参考日期，默认为当前日期
     * @returns {number|null} 年龄，如果日期无效返回null
     */
    static getAge(birthDate, referenceDate = new Date()) {
        const birthObj = this.parseDate(birthDate);
        const refObj = this.parseDate(referenceDate);
        
        if (!birthObj || !refObj) {
            return null;
        }
        
        let age = refObj.getFullYear() - birthObj.getFullYear();
        const monthDiff = refObj.getMonth() - birthObj.getMonth();
        
        if (monthDiff < 0 || (monthDiff === 0 && refObj.getDate() < birthObj.getDate())) {
            age--;
        }
        
        return age;
    }

    /**
     * 获取中文星期名称
     * @param {Date|string|number} date - 日期
     * @returns {string} 中文星期名称
     */
    static getChineseWeekday(date) {
        const dateObj = this.parseDate(date);
        
        if (!dateObj) {
            return '';
        }
        
        const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
        return weekdays[dateObj.getDay()];
    }

    /**
     * 获取中文月份名称
     * @param {Date|string|number} date - 日期
     * @returns {string} 中文月份名称
     */
    static getChineseMonth(date) {
        const dateObj = this.parseDate(date);
        
        if (!dateObj) {
            return '';
        }
        
        const months = ['一月', '二月', '三月', '四月', '五月', '六月', 
                       '七月', '八月', '九月', '十月', '十一月', '十二月'];
        return months[dateObj.getMonth()];
    }

    /**
     * 转换为本地时间字符串
     * @param {Date|string|number} date - 日期
     * @param {string} locale - 语言环境
     * @param {Object} options - 格式选项
     * @returns {string} 本地时间字符串
     */
    static toLocaleString(date, locale = 'zh-CN', options = {}) {
        const dateObj = this.parseDate(date);
        
        if (!dateObj) {
            return '';
        }
        
        const defaultOptions = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        };
        
        return dateObj.toLocaleDateString(locale, { ...defaultOptions, ...options });
    }

    /**
     * 创建日期选择器的选项数组
     * @param {number} startYear - 开始年份
     * @param {number} endYear - 结束年份
     * @returns {Object} 包含年、月、日选项的对象
     */
    static createDatePickerOptions(startYear = 1900, endYear = new Date().getFullYear() + 10) {
        const years = [];
        for (let year = startYear; year <= endYear; year++) {
            years.push({ value: year, label: `${year}年` });
        }
        
        const months = [];
        for (let month = 1; month <= 12; month++) {
            months.push({ value: month, label: `${month}月` });
        }
        
        const days = [];
        for (let day = 1; day <= 31; day++) {
            days.push({ value: day, label: `${day}日` });
        }
        
        return { years, months, days };
    }

    /**
     * 验证日期字符串格式
     * @param {string} dateString - 日期字符串
     * @param {string} format - 期望的格式
     * @returns {boolean} 是否符合格式
     */
    static validateFormat(dateString, format = 'YYYY-MM-DD') {
        if (typeof dateString !== 'string') {
            return false;
        }
        
        // 简单的格式验证
        const formatRegexMap = {
            'YYYY-MM-DD': /^\d{4}-\d{2}-\d{2}$/,
            'DD/MM/YYYY': /^\d{2}\/\d{2}\/\d{4}$/,
            'MM/DD/YYYY': /^\d{2}\/\d{2}\/\d{4}$/,
            'YYYY/MM/DD': /^\d{4}\/\d{2}\/\d{2}$/
        };
        
        const regex = formatRegexMap[format];
        if (!regex) {
            return false;
        }
        
        if (!regex.test(dateString)) {
            return false;
        }
        
        // 验证日期是否有效
        const parsed = this.parseDate(dateString);
        return parsed !== null;
    }
}
