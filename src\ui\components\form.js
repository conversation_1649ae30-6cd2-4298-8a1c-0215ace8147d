/**
 * @file 表单组件 - 表单管理和验证UI组件
 * <AUTHOR> Team
 * @description 
 * 这个文件实现了表单组件，提供：
 * - Form 表单容器组件
 * - FormField 表单字段组件
 * - 统一的表单验证和提交机制
 * - 表单数据收集和处理
 * - 表单状态管理和错误处理
 */

// #region 导入依赖模块
import { BaseComponent } from './base-component.js';
import { Button } from './button.js';
import { Input, TextArea, Select } from './input.js';
import { UI_EVENTS } from '../../core/events/event-types.js';
import { validateNonEmpty } from '../../core/utils/validation.js';
// #endregion

// #region Form 表单组件
/**
 * @class Form - 表单组件
 * @description 表单容器组件，管理表单字段和处理表单提交
 */
export class Form extends BaseComponent {
    /**
     * 构造函数 - 初始化表单组件
     * @param {Object} config - 表单配置
     * @param {Array<Object>} config.fields - 表单字段配置数组
     * @param {Object} config.data - 初始表单数据
     * @param {Function} config.onSubmit - 表单提交处理函数
     * @param {Function} config.onValidate - 表单验证处理函数
     * @param {boolean} config.validateOnChange - 是否在值改变时验证
     * @param {boolean} config.showRequiredMarks - 是否显示必填标记
     */
    constructor(config = {}) {
        super({
            type: 'form',
            name: config.name || 'form',
            ...config
        });
        
        // 表单特有属性
        this.formProps = {
            method: config.method || 'POST',
            action: config.action || '',
            enctype: config.enctype || 'application/x-www-form-urlencoded',
            autocomplete: config.autocomplete || 'off',
            novalidate: config.novalidate || true,
            layout: config.layout || 'vertical', // 'vertical', 'horizontal', 'inline'
            ...config.formProps
        };
        
        // 表单字段配置
        this.fieldConfigs = config.fields || [];
        
        // 表单数据和验证
        this.formData = { ...config.data } || {};
        this.formErrors = {};
        this.fieldComponents = new Map();
        
        // 验证配置
        this.validation = {
            validateOnChange: config.validateOnChange !== false,
            validateOnBlur: config.validateOnBlur !== false,
            validateOnSubmit: config.validateOnSubmit !== false,
            showRequiredMarks: config.showRequiredMarks !== false,
            customValidator: config.onValidate || null,
            ...config.validation
        };
        
        // 事件处理
        this.submitHandler = config.onSubmit || null;
        this.resetHandler = config.onReset || null;
        this.changeHandler = config.onChange || null;
        
        // 表单状态
        this.formState = {
            submitting: false,
            validating: false,
            valid: true,
            dirty: false,
            touched: false,
            submitted: false,
            ...config.formState
        };
        
        // 合并到组件状态
        this.state = { ...this.state, ...this.formState };
        
        // 验证配置
        this._validateFormConfig();
    }

    /**
     * 验证表单配置 - 验证表单特有的配置
     * @private
     */
    _validateFormConfig() {
        // 验证布局类型
        const validLayouts = ['vertical', 'horizontal', 'inline'];
        if (!validLayouts.includes(this.formProps.layout)) {
            console.warn(`无效的表单布局: ${this.formProps.layout}，将使用默认值 'vertical'`);
            this.formProps.layout = 'vertical';
        }
    }

    /**
     * 渲染表单 - 创建表单DOM元素
     * @returns {Promise<HTMLElement>} 表单元素
     */
    async render() {
        // 创建表单元素
        const form = document.createElement('form');
        form.id = this.id;
        form.method = this.formProps.method;
        form.action = this.formProps.action;
        form.enctype = this.formProps.enctype;
        form.autocomplete = this.formProps.autocomplete;
        form.noValidate = this.formProps.novalidate;
        
        // 创建表单字段
        await this._createFormFields(form);
        
        // 创建表单按钮区域
        const buttonArea = this._createButtonArea();
        form.appendChild(buttonArea);
        
        return form;
    }

    /**
     * 创建表单字段 - 根据配置创建表单字段
     * @param {HTMLElement} form - 表单元素
     * @returns {Promise<void>}
     * @private
     */
    async _createFormFields(form) {
        for (const fieldConfig of this.fieldConfigs) {
            try {
                const field = await this._createFormField(fieldConfig);
                if (field) {
                    await field.mount(form);
                    this.fieldComponents.set(fieldConfig.name, field);
                    this.addChild(fieldConfig.name, field);
                }
            } catch (error) {
                console.error(`创建表单字段 ${fieldConfig.name} 失败:`, error);
                this.emit(UI_EVENTS.COMPONENT_ERROR, {
                    component: this,
                    error: error.message,
                    phase: 'field-creation',
                    fieldName: fieldConfig.name
                });
            }
        }
    }

    /**
     * 创建表单字段 - 创建单个表单字段组件
     * @param {Object} fieldConfig - 字段配置
     * @returns {Promise<BaseComponent>} 字段组件
     * @private
     */
    async _createFormField(fieldConfig) {
        const baseConfig = {
            name: fieldConfig.name,
            value: this.formData[fieldConfig.name] || fieldConfig.defaultValue || '',
            required: fieldConfig.required || false,
            onChange: (event, value, component) => this._handleFieldChange(fieldConfig.name, value, component),
            onBlur: (event, component) => this._handleFieldBlur(fieldConfig.name, component),
            ...fieldConfig
        };
        
        let component;
        
        switch (fieldConfig.type) {
            case 'textarea':
                component = new TextArea(baseConfig);
                break;
            case 'select':
                component = new Select(baseConfig);
                break;
            case 'button':
                component = new Button(baseConfig);
                break;
            default:
                component = new Input(baseConfig);
                break;
        }
        
        return component;
    }

    /**
     * 创建按钮区域 - 创建表单提交和重置按钮
     * @returns {HTMLElement} 按钮区域元素
     * @private
     */
    _createButtonArea() {
        const buttonArea = document.createElement('div');
        buttonArea.className = 'form-buttons';
        
        // 创建提交按钮
        const submitButton = new Button({
            text: '提交',
            type: 'submit',
            variant: 'primary',
            onClick: (event) => this._handleSubmit(event)
        });
        
        // 创建重置按钮
        const resetButton = new Button({
            text: '重置',
            type: 'button',
            variant: 'secondary',
            onClick: (event) => this._handleReset(event)
        });
        
        // 添加按钮到区域
        submitButton.mount(buttonArea);
        resetButton.mount(buttonArea);
        
        // 添加到子组件
        this.addChild('submit-button', submitButton);
        this.addChild('reset-button', resetButton);
        
        return buttonArea;
    }

    /**
     * 应用样式 - 应用表单特有的样式类
     * @private
     */
    _applyStyles() {
        if (!this.element) {
            return;
        }
        
        // 构建样式类列表
        const styleClasses = [
            'smartoffice-form',
            'form',
            `form-layout-${this.formProps.layout}`,
            this.state.submitting ? 'form-submitting' : '',
            this.state.valid ? 'form-valid' : 'form-invalid',
            this.state.dirty ? 'form-dirty' : 'form-pristine',
            this.state.touched ? 'form-touched' : 'form-untouched',
            this.state.enabled ? '' : 'form-disabled',
            this.props.className
        ].filter(Boolean);
        
        // 应用样式类
        this.element.className = styleClasses.join(' ');
        
        // 设置自定义样式
        if (this.props.style) {
            Object.assign(this.element.style, this.props.style);
        }
    }

    /**
     * 绑定组件特定事件 - 绑定表单特有的事件
     * @protected
     */
    _bindComponentEvents() {
        if (!this.element) {
            return;
        }
        
        // 绑定表单提交事件
        this._addEventListener(this.element, 'submit', (event) => {
            event.preventDefault();
            this._handleSubmit(event);
        });
        
        // 绑定表单重置事件
        this._addEventListener(this.element, 'reset', (event) => {
            event.preventDefault();
            this._handleReset(event);
        });
        
        // 绑定键盘事件
        this._addEventListener(this.element, 'keydown', (event) => {
            this._handleKeyDown(event);
        });
    }

    /**
     * 处理字段值改变 - 处理表单字段值改变
     * @param {string} fieldName - 字段名称
     * @param {*} value - 新值
     * @param {BaseComponent} component - 字段组件
     * @private
     */
    _handleFieldChange(fieldName, value, component) {
        // 更新表单数据
        this.formData[fieldName] = value;
        
        // 标记表单为脏状态
        this.update({}, { dirty: true });
        
        // 字段级验证
        if (this.validation.validateOnChange) {
            this._validateField(fieldName, value);
        }
        
        // 触发表单改变事件
        this.emit(UI_EVENTS.FORM_FIELD_CHANGED, {
            component: this,
            fieldName,
            value,
            fieldComponent: component,
            formData: { ...this.formData }
        });
        
        // 执行改变处理函数
        if (this.changeHandler) {
            try {
                this.changeHandler(fieldName, value, this.formData, this);
            } catch (error) {
                this.emit(UI_EVENTS.COMPONENT_ERROR, {
                    component: this,
                    error: error.message,
                    phase: 'change',
                    fieldName
                });
            }
        }
    }

    /**
     * 处理字段失焦 - 处理表单字段失去焦点
     * @param {string} fieldName - 字段名称
     * @param {BaseComponent} component - 字段组件
     * @private
     */
    _handleFieldBlur(fieldName, component) {
        // 标记表单为已触碰状态
        this.update({}, { touched: true });
        
        // 字段级验证
        if (this.validation.validateOnBlur) {
            this._validateField(fieldName, this.formData[fieldName]);
        }
        
        // 触发字段失焦事件
        this.emit(UI_EVENTS.FORM_FIELD_BLURRED, {
            component: this,
            fieldName,
            fieldComponent: component
        });
    }

    /**
     * 处理表单提交 - 处理表单提交事件
     * @param {Event} event - 提交事件
     * @private
     */
    async _handleSubmit(event) {
        event.preventDefault();
        
        if (this.state.submitting || !this.state.enabled) {
            return;
        }
        
        try {
            // 标记为提交状态
            this.update({}, { submitting: true, submitted: true });
            
            // 表单验证
            const isValid = await this._validateForm();
            
            if (!isValid) {
                this.update({}, { submitting: false });
                
                this.emit(UI_EVENTS.FORM_VALIDATION_FAILED, {
                    component: this,
                    formData: { ...this.formData },
                    errors: { ...this.formErrors }
                });
                return;
            }
            
            // 触发提交事件
            this.emit(UI_EVENTS.FORM_SUBMIT, {
                component: this,
                event,
                formData: { ...this.formData }
            });
            
            // 执行提交处理函数
            if (this.submitHandler) {
                const result = await this.submitHandler(this.formData, this);
                
                this.emit(UI_EVENTS.FORM_SUBMITTED, {
                    component: this,
                    formData: { ...this.formData },
                    result
                });
            }
            
        } catch (error) {
            this.emit(UI_EVENTS.COMPONENT_ERROR, {
                component: this,
                error: error.message,
                phase: 'submit'
            });
        } finally {
            this.update({}, { submitting: false });
        }
    }

    /**
     * 处理表单重置 - 处理表单重置事件
     * @param {Event} event - 重置事件
     * @private
     */
    _handleReset(event) {
        event.preventDefault();
        
        try {
            // 重置表单数据
            this.formData = {};
            this.formErrors = {};
            
            // 重置所有字段组件
            for (const [fieldName, fieldComponent] of this.fieldComponents.entries()) {
                if (fieldComponent.reset) {
                    fieldComponent.reset();
                }
            }
            
            // 重置表单状态
            this.update({}, {
                valid: true,
                dirty: false,
                touched: false,
                submitted: false
            });
            
            // 触发重置事件
            this.emit(UI_EVENTS.FORM_RESET, {
                component: this,
                event
            });
            
            // 执行重置处理函数
            if (this.resetHandler) {
                this.resetHandler(this);
            }
            
        } catch (error) {
            this.emit(UI_EVENTS.COMPONENT_ERROR, {
                component: this,
                error: error.message,
                phase: 'reset'
            });
        }
    }

    /**
     * 处理键盘事件 - 处理表单键盘交互
     * @param {KeyboardEvent} event - 键盘事件
     * @private
     */
    _handleKeyDown(event) {
        // Ctrl+Enter 快速提交
        if (event.ctrlKey && event.key === 'Enter') {
            event.preventDefault();
            this._handleSubmit(event);
        }
        
        // Escape 键重置表单
        if (event.key === 'Escape') {
            event.preventDefault();
            this._handleReset(event);
        }
    }

    /**
     * 验证表单 - 验证整个表单
     * @returns {Promise<boolean>} 是否有效
     * @private
     */
    async _validateForm() {
        let isValid = true;
        const errors = {};
        
        try {
            // 验证所有字段
            for (const [fieldName, fieldComponent] of this.fieldComponents.entries()) {
                const fieldValid = this._validateField(fieldName, this.formData[fieldName]);
                if (!fieldValid) {
                    isValid = false;
                    errors[fieldName] = this.formErrors[fieldName];
                }
            }
            
            // 自定义表单验证
            if (isValid && this.validation.customValidator) {
                const customResult = await this.validation.customValidator(this.formData, this);
                if (customResult !== true) {
                    isValid = false;
                    if (typeof customResult === 'object') {
                        Object.assign(errors, customResult);
                    }
                }
            }
            
            // 更新表单状态
            this.formErrors = errors;
            this.update({}, { valid: isValid });
            
            // 触发验证事件
            if (isValid) {
                this.emit(UI_EVENTS.FORM_VALIDATION_PASSED, {
                    component: this,
                    formData: { ...this.formData }
                });
            } else {
                this.emit(UI_EVENTS.FORM_VALIDATION_FAILED, {
                    component: this,
                    formData: { ...this.formData },
                    errors: { ...errors }
                });
            }
            
        } catch (error) {
            isValid = false;
            console.error('表单验证错误:', error);
            
            this.emit(UI_EVENTS.COMPONENT_ERROR, {
                component: this,
                error: error.message,
                phase: 'validation'
            });
        }
        
        return isValid;
    }

    /**
     * 验证字段 - 验证单个表单字段
     * @param {string} fieldName - 字段名称
     * @param {*} value - 字段值
     * @returns {boolean} 是否有效
     * @private
     */
    _validateField(fieldName, value) {
        const fieldComponent = this.fieldComponents.get(fieldName);
        
        if (!fieldComponent || !fieldComponent.validate) {
            return true;
        }
        
        try {
            const isValid = fieldComponent.validate();
            
            if (!isValid) {
                this.formErrors[fieldName] = fieldComponent.state.validationMessage || '字段验证失败';
            } else {
                delete this.formErrors[fieldName];
            }
            
            return isValid;
            
        } catch (error) {
            console.error(`验证字段 ${fieldName} 时出错:`, error);
            this.formErrors[fieldName] = '验证过程中出现错误';
            return false;
        }
    }

    /**
     * 设置表单数据 - 设置表单的数据值
     * @param {Object} data - 表单数据
     * @param {boolean} merge - 是否合并现有数据
     */
    setFormData(data, merge = true) {
        // 更新表单数据
        if (merge) {
            this.formData = { ...this.formData, ...data };
        } else {
            this.formData = { ...data };
        }
        
        // 更新字段组件的值
        for (const [fieldName, value] of Object.entries(this.formData)) {
            const fieldComponent = this.fieldComponents.get(fieldName);
            if (fieldComponent && fieldComponent.setValue) {
                fieldComponent.setValue(value);
            }
        }
        
        // 触发数据改变事件
        this.emit(UI_EVENTS.FORM_DATA_CHANGED, {
            component: this,
            formData: { ...this.formData }
        });
    }

    /**
     * 获取表单数据 - 获取当前表单数据
     * @returns {Object} 表单数据
     */
    getFormData() {
        return { ...this.formData };
    }

    /**
     * 获取字段值 - 获取指定字段的值
     * @param {string} fieldName - 字段名称
     * @returns {*} 字段值
     */
    getFieldValue(fieldName) {
        return this.formData[fieldName];
    }

    /**
     * 设置字段值 - 设置指定字段的值
     * @param {string} fieldName - 字段名称
     * @param {*} value - 字段值
     */
    setFieldValue(fieldName, value) {
        this.formData[fieldName] = value;
        
        const fieldComponent = this.fieldComponents.get(fieldName);
        if (fieldComponent && fieldComponent.setValue) {
            fieldComponent.setValue(value);
        }
        
        // 触发字段改变事件
        this.emit(UI_EVENTS.FORM_FIELD_CHANGED, {
            component: this,
            fieldName,
            value,
            fieldComponent,
            formData: { ...this.formData }
        });
    }

    /**
     * 获取字段组件 - 获取指定字段的组件实例
     * @param {string} fieldName - 字段名称
     * @returns {BaseComponent|null} 字段组件
     */
    getFieldComponent(fieldName) {
        return this.fieldComponents.get(fieldName) || null;
    }

    /**
     * 验证表单 - 手动触发表单验证
     * @returns {Promise<boolean>} 是否有效
     */
    async validate() {
        return await this._validateForm();
    }

    /**
     * 提交表单 - 程序化提交表单
     * @returns {Promise<void>}
     */
    async submit() {
        const event = new Event('submit', { bubbles: true, cancelable: true });
        await this._handleSubmit(event);
    }

    /**
     * 重置表单 - 程序化重置表单
     */
    reset() {
        const event = new Event('reset', { bubbles: true, cancelable: true });
        this._handleReset(event);
    }

    /**
     * 清空表单 - 清空表单数据但不重置状态
     */
    clear() {
        this.setFormData({}, false);
    }

    /**
     * 获取表单错误 - 获取表单验证错误
     * @returns {Object} 表单错误对象
     */
    getFormErrors() {
        return { ...this.formErrors };
    }

    /**
     * 设置表单错误 - 设置表单验证错误
     * @param {Object} errors - 错误对象
     */
    setFormErrors(errors) {
        this.formErrors = { ...errors };
        
        // 更新字段组件的验证状态
        for (const [fieldName, error] of Object.entries(errors)) {
            const fieldComponent = this.fieldComponents.get(fieldName);
            if (fieldComponent) {
                fieldComponent.update({}, {
                    valid: false,
                    validationMessage: error
                });
            }
        }
        
        // 更新表单状态
        this.update({}, { valid: Object.keys(errors).length === 0 });
    }

    /**
     * 添加表单字段 - 动态添加表单字段
     * @param {Object} fieldConfig - 字段配置
     * @returns {Promise<BaseComponent>} 字段组件
     */
    async addField(fieldConfig) {
        try {
            const field = await this._createFormField(fieldConfig);
            if (field && this.element) {
                // 找到按钮区域并在其前面插入字段
                const buttonArea = this.element.querySelector('.form-buttons');
                await field.mount(this.element, buttonArea);
                
                this.fieldComponents.set(fieldConfig.name, field);
                this.addChild(fieldConfig.name, field);
                
                // 触发字段添加事件
                this.emit(UI_EVENTS.FORM_FIELD_ADDED, {
                    component: this,
                    fieldName: fieldConfig.name,
                    fieldComponent: field
                });
                
                return field;
            }
        } catch (error) {
            console.error(`添加表单字段 ${fieldConfig.name} 失败:`, error);
            this.emit(UI_EVENTS.COMPONENT_ERROR, {
                component: this,
                error: error.message,
                phase: 'field-addition',
                fieldName: fieldConfig.name
            });
        }
        
        return null;
    }

    /**
     * 移除表单字段 - 动态移除表单字段
     * @param {string} fieldName - 字段名称
     * @returns {Promise<boolean>} 是否成功移除
     */
    async removeField(fieldName) {
        try {
            const fieldComponent = this.fieldComponents.get(fieldName);
            if (fieldComponent) {
                await this.removeChild(fieldName);
                this.fieldComponents.delete(fieldName);
                delete this.formData[fieldName];
                delete this.formErrors[fieldName];
                
                // 触发字段移除事件
                this.emit(UI_EVENTS.FORM_FIELD_REMOVED, {
                    component: this,
                    fieldName,
                    fieldComponent
                });
                
                return true;
            }
        } catch (error) {
            console.error(`移除表单字段 ${fieldName} 失败:`, error);
            this.emit(UI_EVENTS.COMPONENT_ERROR, {
                component: this,
                error: error.message,
                phase: 'field-removal',
                fieldName
            });
        }
        
        return false;
    }

    /**
     * 获取表单信息 - 获取表单的详细信息
     * @returns {Object} 表单信息
     */
    getFormInfo() {
        return {
            ...this._getComponentInfo(),
            formProps: { ...this.formProps },
            formState: {
                submitting: this.state.submitting,
                validating: this.state.validating,
                valid: this.state.valid,
                dirty: this.state.dirty,
                touched: this.state.touched,
                submitted: this.state.submitted
            },
            formData: { ...this.formData },
            formErrors: { ...this.formErrors },
            fieldNames: Array.from(this.fieldComponents.keys()),
            validation: { ...this.validation }
        };
    }
}
// #endregion

// #region FormField 表单字段组件
/**
 * @class FormField - 表单字段包装组件
 * @description 包装表单字段的容器组件，提供统一的布局和标签
 */
export class FormField extends BaseComponent {
    /**
     * 构造函数 - 初始化表单字段组件
     * @param {Object} config - 字段配置
     * @param {BaseComponent} config.field - 字段组件
     * @param {string} config.label - 字段标签
     * @param {string} config.helpText - 帮助文本
     * @param {boolean} config.required - 是否必填
     * @param {string} config.layout - 布局方式 ('vertical', 'horizontal')
     */
    constructor(config = {}) {
        super({
            type: 'form-field',
            name: config.name || 'form-field',
            ...config
        });
        
        // 字段属性
        this.fieldProps = {
            label: config.label || '',
            helpText: config.helpText || '',
            required: config.required || false,
            layout: config.layout || 'vertical',
            ...config.fieldProps
        };
        
        // 字段组件
        this.fieldComponent = config.field || null;
        
        // 字段状态
        this.fieldState = {
            hasError: false,
            errorMessage: '',
            ...config.fieldState
        };
        
        // 合并到组件状态
        this.state = { ...this.state, ...this.fieldState };
    }

    /**
     * 渲染表单字段 - 创建表单字段DOM结构
     * @returns {Promise<HTMLElement>} 字段容器元素
     */
    async render() {
        // 创建字段容器
        const container = document.createElement('div');
        container.id = this.id;
        
        // 创建标签
        if (this.fieldProps.label) {
            const label = this._createFieldLabel();
            container.appendChild(label);
        }
        
        // 创建字段包装器
        const fieldWrapper = this._createFieldWrapper();
        container.appendChild(fieldWrapper);
        
        // 挂载字段组件
        if (this.fieldComponent) {
            await this.fieldComponent.mount(fieldWrapper);
            this.addChild('field', this.fieldComponent);
        }
        
        // 创建帮助文本
        if (this.fieldProps.helpText) {
            const helpText = this._createHelpText();
            container.appendChild(helpText);
        }
        
        // 创建错误消息
        const errorMessage = this._createErrorMessage();
        container.appendChild(errorMessage);
        
        return container;
    }

    /**
     * 创建字段标签 - 创建字段标签元素
     * @returns {HTMLElement} 标签元素
     * @private
     */
    _createFieldLabel() {
        const label = document.createElement('label');
        label.className = 'field-label';
        label.textContent = this.fieldProps.label;
        
        if (this.fieldComponent && this.fieldComponent.element) {
            label.setAttribute('for', this.fieldComponent.element.id);
        }
        
        // 添加必填标记
        if (this.fieldProps.required) {
            const required = document.createElement('span');
            required.className = 'required-mark';
            required.textContent = ' *';
            label.appendChild(required);
        }
        
        return label;
    }

    /**
     * 创建字段包装器 - 创建字段的包装容器
     * @returns {HTMLElement} 包装器元素
     * @private
     */
    _createFieldWrapper() {
        const wrapper = document.createElement('div');
        wrapper.className = 'field-wrapper';
        return wrapper;
    }

    /**
     * 创建帮助文本 - 创建字段帮助文本
     * @returns {HTMLElement} 帮助文本元素
     * @private
     */
    _createHelpText() {
        const helpText = document.createElement('div');
        helpText.className = 'field-help';
        helpText.textContent = this.fieldProps.helpText;
        return helpText;
    }

    /**
     * 创建错误消息 - 创建字段错误消息容器
     * @returns {HTMLElement} 错误消息元素
     * @private
     */
    _createErrorMessage() {
        const errorMessage = document.createElement('div');
        errorMessage.className = 'field-error';
        errorMessage.style.display = 'none';
        return errorMessage;
    }

    /**
     * 应用样式 - 应用表单字段特有的样式类
     * @private
     */
    _applyStyles() {
        if (!this.element) {
            return;
        }
        
        // 构建样式类列表
        const styleClasses = [
            'smartoffice-form-field',
            'form-field',
            `form-field-${this.fieldProps.layout}`,
            this.state.hasError ? 'has-error' : '',
            this.fieldProps.required ? 'required' : '',
            this.state.enabled ? '' : 'disabled',
            this.props.className
        ].filter(Boolean);
        
        // 应用样式类
        this.element.className = styleClasses.join(' ');
        
        // 设置自定义样式
        if (this.props.style) {
            Object.assign(this.element.style, this.props.style);
        }
    }

    /**
     * 设置错误状态 - 设置字段的错误状态和消息
     * @param {string} errorMessage - 错误消息
     */
    setError(errorMessage) {
        this.update({}, {
            hasError: true,
            errorMessage
        });
        
        // 更新错误消息显示
        const errorElement = this.element?.querySelector('.field-error');
        if (errorElement) {
            errorElement.textContent = errorMessage;
            errorElement.style.display = 'block';
        }
    }

    /**
     * 清除错误状态 - 清除字段的错误状态
     */
    clearError() {
        this.update({}, {
            hasError: false,
            errorMessage: ''
        });
        
        // 隐藏错误消息
        const errorElement = this.element?.querySelector('.field-error');
        if (errorElement) {
            errorElement.textContent = '';
            errorElement.style.display = 'none';
        }
    }

    /**
     * 获取字段值 - 获取字段组件的值
     * @returns {*} 字段值
     */
    getValue() {
        if (this.fieldComponent && this.fieldComponent.getValue) {
            return this.fieldComponent.getValue();
        }
        return null;
    }

    /**
     * 设置字段值 - 设置字段组件的值
     * @param {*} value - 字段值
     */
    setValue(value) {
        if (this.fieldComponent && this.fieldComponent.setValue) {
            this.fieldComponent.setValue(value);
        }
    }

    /**
     * 验证字段 - 验证字段组件
     * @returns {boolean} 是否有效
     */
    validate() {
        if (this.fieldComponent && this.fieldComponent.validate) {
            const isValid = this.fieldComponent.validate();
            
            if (!isValid && this.fieldComponent.state.validationMessage) {
                this.setError(this.fieldComponent.state.validationMessage);
            } else {
                this.clearError();
            }
            
            return isValid;
        }
        return true;
    }
}
// #endregion

// #region 工厂函数
/**
 * 创建表单 - 工厂函数，创建表单实例
 * @param {Object} config - 表单配置
 * @returns {Form} 表单实例
 */
export function createForm(config = {}) {
    return new Form(config);
}

/**
 * 创建表单字段 - 工厂函数，创建表单字段实例
 * @param {Object} config - 字段配置
 * @returns {FormField} 表单字段实例
 */
export function createFormField(config = {}) {
    return new FormField(config);
}

/**
 * 创建简单表单 - 快速创建简单的表单
 * @param {Array<Object>} fields - 字段配置数组
 * @param {Object} options - 表单选项
 * @returns {Form} 表单实例
 */
export function createSimpleForm(fields, options = {}) {
    return createForm({
        fields,
        layout: 'vertical',
        validateOnChange: true,
        showRequiredMarks: true,
        ...options
    });
}
// #endregion 