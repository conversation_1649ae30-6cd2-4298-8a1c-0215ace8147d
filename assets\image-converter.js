/**
 * @file 图片转换工具
 * @description 将图片文件转换为 base64 编码并更新到 image-base64.js 文件中
 */

// 导入所需模块
const fs = require('fs');
const path = require('path');

/**
 * 将图片文件转换为 base64 编码
 * @param {string} filePath - 图片文件路径
 * @returns {string} base64 编码的图片数据
 */
function convertImageToBase64(filePath) {
    // 读取图片文件
    const imageData = fs.readFileSync(filePath);
    
    // 获取文件扩展名
    const ext = path.extname(filePath).toLowerCase();
    
    // 确定 MIME 类型
    let mimeType;
    if (ext === '.png') {
        mimeType = 'image/png';
    } else if (ext === '.jpg' || ext === '.jpeg') {
        mimeType = 'image/jpeg';
    } else if (ext === '.gif') {
        mimeType = 'image/gif';
    } else {
        throw new Error(`不支持的图片格式: ${ext}`);
    }
    
    // 转换为 base64 并添加 data URL 前缀
    const base64Data = imageData.toString('base64');
    return `data:${mimeType};base64,${base64Data}`;
}

/**
 * 主函数 - 转换所有图片并更新 image-base64.js 文件
 */
function main() {
    try {
        // 获取项目根目录
        const rootDir = path.resolve(__dirname, '..');
        console.log(`项目根目录: ${rootDir}`);
        
        // 定义图片映射
        const imageMapping = {
            logos: {
                'sky-mirror': 'SMW Header.png',
                'gomyhire': 'GMH Header.png'
            },
            stamps: {
                'sky-mirror': 'SMW Stamp.jpg',
                'gomyhire': 'GOMYHIRE Stamp.png'
            },
            footers: {
                'gomyhire': 'GMH Footer.png'
            }
        };
        
        // 存储转换后的 base64 数据
        const base64Data = {
            logos: {},
            stamps: {},
            footers: {}
        };
        
        // 转换所有图片
        for (const type in imageMapping) {
            for (const company in imageMapping[type]) {
                // 使用项目根目录查找图片，而不是相对于当前脚本的目录
                const imagePath = path.join(rootDir, imageMapping[type][company]);
                try {
                    console.log(`尝试读取图片: ${imagePath}`);
                    if (fs.existsSync(imagePath)) {
                        base64Data[type][company] = convertImageToBase64(imagePath);
                        console.log(`已成功转换 ${imagePath}`);
                    } else {
                        console.error(`文件不存在: ${imagePath}`);
                    }
                } catch (err) {
                    console.error(`转换 ${imagePath} 失败:`, err.message);
                }
            }
        }
        
        // 读取现有的 image-base64.js 文件
        const filePath = path.join(__dirname, 'image-base64.js');
        console.log(`准备更新文件: ${filePath}`);
        
        if (!fs.existsSync(filePath)) {
            console.error(`image-base64.js 文件不存在: ${filePath}`);
            // 如果文件不存在，创建一个新文件
            const initialContent = `/**
 * @file 图片 Base64 数据
 * @description 存储所有图片的 Base64 编码数据
 */

const ImageBase64 = {
    logos: {
    },
    stamps: {
    },
    footers: {
    }
};

// 导出图片 Base64 数据
window.ImageBase64 = ImageBase64;
`;
            fs.writeFileSync(filePath, initialContent, 'utf8');
            console.log(`已创建新的 image-base64.js 文件`);
        }
        
        let content = fs.readFileSync(filePath, 'utf8');
        
        // 更新 base64 数据
        for (const type in base64Data) {
            for (const company in base64Data[type]) {
                if (!base64Data[type][company]) {
                    console.warn(`跳过 ${type}.${company}，没有有效的 base64 数据`);
                    continue;
                }
                
                // 检查文件中是否已存在该公司的图片数据
                const companyPattern = new RegExp(`'${type}':\\s*{[^}]*'${company}':\\s*'[^']*'`, 'g');
                if (companyPattern.test(content)) {
                    // 如果存在，则替换
                    const placeholder = new RegExp(`('${type}':\\s*{[^}]*)'${company}':\\s*'[^']*'`, 'g');
                    const replacement = `$1'${company}': '${base64Data[type][company]}'`;
                    content = content.replace(placeholder, replacement);
                    console.log(`已更新 ${type}.${company} 数据`);
                } else {
                    // 如果不存在，则添加
                    const typePattern = new RegExp(`'${type}':\\s*{([^}]*)}`, 'g');
                    if (typePattern.test(content)) {
                        // 如果类型存在，添加新公司
                        const replacement = `'${type}': {$1    '${company}': '${base64Data[type][company]}',\n}`;
                        content = content.replace(typePattern, replacement);
                    } else {
                        // 如果类型不存在，添加新类型和公司
                        const placeholder = /(const\s+ImageBase64\s*=\s*{)/;
                        const replacement = `$1\n    '${type}': {\n        '${company}': '${base64Data[type][company]}'\n    },`;
                        content = content.replace(placeholder, replacement);
                    }
                    console.log(`已添加 ${type}.${company} 数据`);
                }
            }
        }
        
        // 写入更新后的文件
        fs.writeFileSync(filePath, content, 'utf8');
        console.log('已更新 image-base64.js 文件');
    } catch (err) {
        console.error('转换过程中出错:', err);
    }
}

// 执行主函数
main();
