/**
 * @file 发票文档模型
 * @description 发票文档的数据模型和业务逻辑
 */

import { BaseDocument } from './base-document.js';
import { Validator, ValidationError } from '../core/utils/validation.js';
import { StringUtils } from '../core/utils/string-utils.js';
import { DateUtils } from '../core/utils/date-utils.js';
import { getCompanyImages } from '../core/utils/image-manager.js';

/**
 * @class InvoiceDocument
 * @description 发票文档类，继承自BaseDocument
 */
export class InvoiceDocument extends BaseDocument {
    /**
     * 创建发票文档实例
     * @param {Object} data - 发票数据
     */
    constructor(data = {}) {
        super({ ...data, type: 'invoice' });
        
        // 发票特有字段
        this.invoiceNumber = data.invoiceNumber || '';
        this.issueDate = data.issueDate || new Date();
        this.dueDate = data.dueDate || null;
        this.billTo = data.billTo || {};
        this.shipTo = data.shipTo || {};
        this.items = data.items || [];
        this.currency = data.currency || 'RM';
        this.paymentTerms = data.paymentTerms || '';
        this.paymentMethod = data.paymentMethod || '';
        this.notes = data.notes || '';
        this.internalNotes = data.internalNotes || '';
        
        // 公司信息
        this.companyInfo = data.companyInfo || {};
        
        // 税务信息
        this.taxInfo = data.taxInfo || {};
        
        // 计算字段
        this.subtotal = 0;
        this.taxAmount = 0;
        this.discountAmount = data.discountAmount || 0;
        this.shippingAmount = data.shippingAmount || 0;
        this.totalAmount = 0;
        
        // 状态字段
        this.status = data.status || 'draft'; // draft, sent, paid, overdue, cancelled
        this.paymentStatus = data.paymentStatus || 'unpaid'; // unpaid, partial, paid
        this.paidAmount = data.paidAmount || 0;
        this.paidDate = data.paidDate || null;
        
        // 初始化数据
        this._initializeData(data);
    }

    /**
     * 初始化发票数据
     * @param {Object} data - 原始数据
     * @protected
     */
    _initializeData(data) {
        // 确保日期是Date对象
        if (data.issueDate && !(data.issueDate instanceof Date)) {
            this.issueDate = new Date(data.issueDate);
        }
        
        if (data.dueDate && !(data.dueDate instanceof Date)) {
            this.dueDate = new Date(data.dueDate);
        }
        
        if (data.paidDate && !(data.paidDate instanceof Date)) {
            this.paidDate = new Date(data.paidDate);
        }
        
        // 初始化公司信息
        const companyCode = data.companyCode || data.companyInfo?.companyCode || 'gomyhire';
        const companyImages = getCompanyImages(companyCode);
        
        this.companyInfo = {
            name: data.companyInfo?.name || '',
            address: data.companyInfo?.address || '',
            phone: data.companyInfo?.phone || '',
            email: data.companyInfo?.email || '',
            website: data.companyInfo?.website || '',
            registrationNumber: data.companyInfo?.registrationNumber || '',
            taxNumber: data.companyInfo?.taxNumber || '',
            companyCode: companyCode,
            logo: data.companyInfo?.logo || companyImages.logo || '',
            stamp: data.companyInfo?.stamp || companyImages.stamp || '',
            footer: data.companyInfo?.footer || companyImages.footer || '',
            ...data.companyInfo
        };
        
        // 初始化账单地址
        this.billTo = {
            name: data.billTo?.name || '',
            company: data.billTo?.company || '',
            address: data.billTo?.address || '',
            city: data.billTo?.city || '',
            state: data.billTo?.state || '',
            postalCode: data.billTo?.postalCode || '',
            country: data.billTo?.country || '',
            phone: data.billTo?.phone || '',
            email: data.billTo?.email || '',
            taxNumber: data.billTo?.taxNumber || '',
            ...data.billTo
        };
        
        // 初始化发货地址
        this.shipTo = {
            name: data.shipTo?.name || '',
            company: data.shipTo?.company || '',
            address: data.shipTo?.address || '',
            city: data.shipTo?.city || '',
            state: data.shipTo?.state || '',
            postalCode: data.shipTo?.postalCode || '',
            country: data.shipTo?.country || '',
            phone: data.shipTo?.phone || '',
            ...data.shipTo
        };
        
        // 初始化税务信息
        this.taxInfo = {
            taxRate: data.taxInfo?.taxRate || 0,
            taxType: data.taxInfo?.taxType || 'none',
            taxNumber: data.taxInfo?.taxNumber || '',
            isInclusive: data.taxInfo?.isInclusive || false,
            isExempt: data.taxInfo?.isExempt || false,
            ...data.taxInfo
        };
        
        // 初始化项目列表
        this.items = (data.items || []).map(item => ({
            id: item.id || StringUtils.generateUUID(),
            description: item.description || '',
            quantity: item.quantity || 1,
            unitPrice: item.unitPrice || 0,
            discount: item.discount || 0,
            taxRate: item.taxRate || this.taxInfo.taxRate,
            total: 0,
            ...item
        }));
        
        // 重新计算总金额
        this._calculateTotals();
    }

    /**
     * 更新发票数据
     * @param {Object} data - 要更新的数据
     * @protected
     */
    _updateData(data) {
        // 更新基本字段
        const updateFields = [
            'invoiceNumber', 'currency', 'paymentTerms', 'paymentMethod',
            'notes', 'internalNotes', 'discountAmount', 'shippingAmount',
            'paidAmount'
        ];
        
        updateFields.forEach(field => {
            if (data[field] !== undefined) {
                this[field] = data[field];
            }
        });
        
        // 更新日期
        if (data.issueDate) {
            this.issueDate = data.issueDate instanceof Date ? data.issueDate : new Date(data.issueDate);
        }
        
        if (data.dueDate) {
            this.dueDate = data.dueDate instanceof Date ? data.dueDate : new Date(data.dueDate);
        }
        
        if (data.paidDate) {
            this.paidDate = data.paidDate instanceof Date ? data.paidDate : new Date(data.paidDate);
        }
        
        // 更新复合对象
        if (data.companyInfo) {
            this.companyInfo = { ...this.companyInfo, ...data.companyInfo };
        }
        
        if (data.billTo) {
            this.billTo = { ...this.billTo, ...data.billTo };
        }
        
        if (data.shipTo) {
            this.shipTo = { ...this.shipTo, ...data.shipTo };
        }
        
        if (data.taxInfo) {
            this.taxInfo = { ...this.taxInfo, ...data.taxInfo };
        }
        
        if (data.items) {
            this.items = data.items.map(item => ({
                id: item.id || StringUtils.generateUUID(),
                description: item.description || '',
                quantity: item.quantity || 1,
                unitPrice: item.unitPrice || 0,
                discount: item.discount || 0,
                taxRate: item.taxRate || this.taxInfo.taxRate,
                total: 0,
                ...item
            }));
        }
        
        // 重新计算总金额
        this._calculateTotals();
        
        // 更新支付状态
        this._updatePaymentStatus();
    }

    /**
     * 计算所有金额
     * @private
     */
    _calculateTotals() {
        // 计算每个项目的总额
        this.items.forEach(item => {
            const itemSubtotal = item.quantity * item.unitPrice;
            const itemDiscount = itemSubtotal * (item.discount / 100);
            const itemAfterDiscount = itemSubtotal - itemDiscount;
            
            if (this.taxInfo.isInclusive) {
                // 含税价格
                item.total = itemAfterDiscount;
            } else {
                // 不含税价格
                const itemTax = itemAfterDiscount * (item.taxRate / 100);
                item.total = itemAfterDiscount + itemTax;
            }
        });
        
        // 计算小计
        this.subtotal = this.items.reduce((sum, item) => {
            const itemSubtotal = item.quantity * item.unitPrice;
            const itemDiscount = itemSubtotal * (item.discount / 100);
            return sum + (itemSubtotal - itemDiscount);
        }, 0);
        
        // 计算税额
        if (this.taxInfo.isExempt) {
            this.taxAmount = 0;
        } else if (this.taxInfo.isInclusive) {
            // 含税价格，从总额中提取税额
            this.taxAmount = this.subtotal - (this.subtotal / (1 + this.taxInfo.taxRate / 100));
        } else {
            // 不含税价格，计算税额
            this.taxAmount = this.subtotal * (this.taxInfo.taxRate / 100);
        }
        
        // 计算总金额
        if (this.taxInfo.isInclusive) {
            this.totalAmount = this.subtotal - this.discountAmount + this.shippingAmount;
        } else {
            this.totalAmount = this.subtotal + this.taxAmount - this.discountAmount + this.shippingAmount;
        }
    }

    /**
     * 更新支付状态
     * @private
     */
    _updatePaymentStatus() {
        if (this.paidAmount <= 0) {
            this.paymentStatus = 'unpaid';
        } else if (this.paidAmount >= this.totalAmount) {
            this.paymentStatus = 'paid';
        } else {
            this.paymentStatus = 'partial';
        }
    }

    /**
     * 特定验证
     * @protected
     */
    _validateSpecific() {
        // 验证发票号码
        if (!this.invoiceNumber || this.invoiceNumber.trim() === '') {
            throw new ValidationError('发票号码不能为空', 'invoiceNumber', this.invoiceNumber);
        }
        
        // 验证开票日期
        if (!Validator.isValidDate(this.issueDate)) {
            throw new ValidationError('开票日期格式无效', 'issueDate', this.issueDate);
        }
        
        // 验证到期日期
        if (this.dueDate && !Validator.isValidDate(this.dueDate)) {
            throw new ValidationError('到期日期格式无效', 'dueDate', this.dueDate);
        }
        
        // 验证到期日期不能早于开票日期
        if (this.dueDate && this.dueDate < this.issueDate) {
            throw new ValidationError('到期日期不能早于开票日期', 'dueDate', this.dueDate);
        }
        
        // 验证账单地址
        if (!this.billTo.name || this.billTo.name.trim() === '') {
            throw new ValidationError('账单地址中的姓名不能为空', 'billTo.name', this.billTo.name);
        }
        
        // 验证项目列表
        if (!this.items || this.items.length === 0) {
            throw new ValidationError('发票必须包含至少一个项目', 'items', this.items);
        }
        
        // 验证每个项目
        this.items.forEach((item, index) => {
            if (!item.description || item.description.trim() === '') {
                throw new ValidationError(`项目 ${index + 1} 的描述不能为空`, `items[${index}].description`, item.description);
            }
            
            if (!Validator.isPositiveNumber(item.quantity)) {
                throw new ValidationError(`项目 ${index + 1} 的数量必须为正数`, `items[${index}].quantity`, item.quantity);
            }
            
            if (!Validator.isNonNegativeNumber(item.unitPrice)) {
                throw new ValidationError(`项目 ${index + 1} 的单价不能为负数`, `items[${index}].unitPrice`, item.unitPrice);
            }
            
            if (item.discount < 0 || item.discount > 100) {
                throw new ValidationError(`项目 ${index + 1} 的折扣必须在0-100之间`, `items[${index}].discount`, item.discount);
            }
        });
        
        // 验证货币代码
        const validCurrencies = ['RM', 'USD', 'EUR', 'SGD', 'CNY'];
        if (!validCurrencies.includes(this.currency)) {
            throw new ValidationError(`无效的货币代码: ${this.currency}`, 'currency', this.currency);
        }
        
        // 验证税率
        if (this.taxInfo.taxRate < 0 || this.taxInfo.taxRate > 100) {
            throw new ValidationError('税率必须在0-100之间', 'taxInfo.taxRate', this.taxInfo.taxRate);
        }
        
        // 验证已付金额
        if (this.paidAmount < 0) {
            throw new ValidationError('已付金额不能为负数', 'paidAmount', this.paidAmount);
        }
        
        if (this.paidAmount > this.totalAmount) {
            throw new ValidationError('已付金额不能超过总金额', 'paidAmount', this.paidAmount);
        }
    }

    /**
     * 获取特定数据
     * @returns {Object} 发票特定数据
     * @protected
     */
    _getSpecificData() {
        return {
            invoiceNumber: this.invoiceNumber,
            issueDate: this.issueDate.toISOString(),
            dueDate: this.dueDate ? this.dueDate.toISOString() : null,
            billTo: this.billTo,
            shipTo: this.shipTo,
            items: this.items,
            currency: this.currency,
            paymentTerms: this.paymentTerms,
            paymentMethod: this.paymentMethod,
            notes: this.notes,
            internalNotes: this.internalNotes,
            companyInfo: this.companyInfo,
            taxInfo: this.taxInfo,
            subtotal: this.subtotal,
            taxAmount: this.taxAmount,
            discountAmount: this.discountAmount,
            shippingAmount: this.shippingAmount,
            totalAmount: this.totalAmount,
            paymentStatus: this.paymentStatus,
            paidAmount: this.paidAmount,
            paidDate: this.paidDate ? this.paidDate.toISOString() : null
        };
    }

    /**
     * 添加项目
     * @param {Object} item - 项目数据
     * @returns {InvoiceDocument} 当前实例
     */
    addItem(item) {
        const newItem = {
            id: StringUtils.generateUUID(),
            description: item.description || '',
            quantity: item.quantity || 1,
            unitPrice: item.unitPrice || 0,
            discount: item.discount || 0,
            taxRate: item.taxRate || this.taxInfo.taxRate,
            total: 0,
            ...item
        };
        
        this.items.push(newItem);
        this._calculateTotals();
        this.updatedAt = new Date();
        
        return this;
    }

    /**
     * 更新项目
     * @param {string} itemId - 项目ID
     * @param {Object} updates - 更新数据
     * @returns {InvoiceDocument} 当前实例
     */
    updateItem(itemId, updates) {
        const itemIndex = this.items.findIndex(item => item.id === itemId);
        
        if (itemIndex === -1) {
            throw new ValidationError(`未找到ID为 ${itemId} 的项目`, 'itemId', itemId);
        }
        
        this.items[itemIndex] = { ...this.items[itemIndex], ...updates };
        this._calculateTotals();
        this.updatedAt = new Date();
        
        return this;
    }

    /**
     * 删除项目
     * @param {string} itemId - 项目ID
     * @returns {InvoiceDocument} 当前实例
     */
    removeItem(itemId) {
        const itemIndex = this.items.findIndex(item => item.id === itemId);
        
        if (itemIndex === -1) {
            throw new ValidationError(`未找到ID为 ${itemId} 的项目`, 'itemId', itemId);
        }
        
        this.items.splice(itemIndex, 1);
        this._calculateTotals();
        this.updatedAt = new Date();
        
        return this;
    }

    /**
     * 设置到期日期
     * @param {Date|string|number} dueDate - 到期日期或天数
     * @returns {InvoiceDocument} 当前实例
     */
    setDueDate(dueDate) {
        if (typeof dueDate === 'number') {
            // 如果是数字，表示从开票日期开始的天数
            this.dueDate = DateUtils.addDays(this.issueDate, dueDate);
        } else {
            this.dueDate = dueDate instanceof Date ? dueDate : new Date(dueDate);
        }
        
        this.updatedAt = new Date();
        return this;
    }

    /**
     * 记录付款
     * @param {number} amount - 付款金额
     * @param {Date} date - 付款日期
     * @returns {InvoiceDocument} 当前实例
     */
    recordPayment(amount, date = new Date()) {
        if (!Validator.isPositiveNumber(amount)) {
            throw new ValidationError('付款金额必须为正数', 'amount', amount);
        }
        
        if (this.paidAmount + amount > this.totalAmount) {
            throw new ValidationError('付款总额不能超过发票总金额', 'amount', amount);
        }
        
        this.paidAmount += amount;
        this.paidDate = date instanceof Date ? date : new Date(date);
        
        this._updatePaymentStatus();
        this.updatedAt = new Date();
        
        return this;
    }

    /**
     * 标记为已发送
     * @returns {InvoiceDocument} 当前实例
     */
    markAsSent() {
        this.status = 'sent';
        this.updatedAt = new Date();
        return this;
    }

    /**
     * 标记为已取消
     * @returns {InvoiceDocument} 当前实例
     */
    markAsCancelled() {
        this.status = 'cancelled';
        this.updatedAt = new Date();
        return this;
    }

    /**
     * 获取格式化的小计
     * @returns {string} 格式化的小计
     */
    getFormattedSubtotal() {
        return StringUtils.formatCurrency(this.subtotal, this.currency);
    }

    /**
     * 获取格式化的税额
     * @returns {string} 格式化的税额
     */
    getFormattedTaxAmount() {
        return StringUtils.formatCurrency(this.taxAmount, this.currency);
    }

    /**
     * 获取格式化的总金额
     * @returns {string} 格式化的总金额
     */
    getFormattedTotalAmount() {
        return StringUtils.formatCurrency(this.totalAmount, this.currency);
    }

    /**
     * 获取格式化的已付金额
     * @returns {string} 格式化的已付金额
     */
    getFormattedPaidAmount() {
        return StringUtils.formatCurrency(this.paidAmount, this.currency);
    }

    /**
     * 获取格式化的余额
     * @returns {string} 格式化的余额
     */
    getFormattedBalance() {
        const balance = this.totalAmount - this.paidAmount;
        return StringUtils.formatCurrency(balance, this.currency);
    }

    /**
     * 获取格式化的开票日期
     * @param {string} format - 日期格式
     * @returns {string} 格式化的开票日期
     */
    getFormattedIssueDate(format = 'YYYY-MM-DD') {
        return DateUtils.format(this.issueDate, format);
    }

    /**
     * 获取格式化的到期日期
     * @param {string} format - 日期格式
     * @returns {string} 格式化的到期日期
     */
    getFormattedDueDate(format = 'YYYY-MM-DD') {
        return this.dueDate ? DateUtils.format(this.dueDate, format) : '';
    }

    /**
     * 检查是否逾期
     * @returns {boolean} 是否逾期
     */
    isOverdue() {
        if (!this.dueDate || this.paymentStatus === 'paid') {
            return false;
        }
        
        return new Date() > this.dueDate;
    }

    /**
     * 获取逾期天数
     * @returns {number} 逾期天数
     */
    getOverdueDays() {
        if (!this.isOverdue()) {
            return 0;
        }
        
        return DateUtils.daysBetween(this.dueDate, new Date());
    }

    /**
     * 获取到期天数
     * @returns {number} 到期天数（负数表示逾期）
     */
    getDaysUntilDue() {
        if (!this.dueDate) {
            return Infinity;
        }
        
        return DateUtils.daysBetween(new Date(), this.dueDate);
    }

    /**
     * 获取余额
     * @returns {number} 余额
     */
    getBalance() {
        return this.totalAmount - this.paidAmount;
    }

    /**
     * 检查是否已完全支付
     * @returns {boolean} 是否已完全支付
     */
    isFullyPaid() {
        return this.paymentStatus === 'paid';
    }

    /**
     * 检查是否部分支付
     * @returns {boolean} 是否部分支付
     */
    isPartiallyPaid() {
        return this.paymentStatus === 'partial';
    }

    /**
     * 检查是否未支付
     * @returns {boolean} 是否未支付
     */
    isUnpaid() {
        return this.paymentStatus === 'unpaid';
    }

    /**
     * 获取发票摘要
     * @returns {Object} 发票摘要
     */
    getSummary() {
        return {
            ...super.getSummary(),
            invoiceNumber: this.invoiceNumber,
            billTo: this.billTo.name,
            totalAmount: this.getFormattedTotalAmount(),
            balance: this.getFormattedBalance(),
            issueDate: this.getFormattedIssueDate(),
            dueDate: this.getFormattedDueDate(),
            paymentStatus: this.paymentStatus,
            isOverdue: this.isOverdue(),
            itemCount: this.items.length
        };
    }

    /**
     * 生成发票号码
     * @param {string} prefix - 前缀
     * @returns {string} 生成的发票号码
     * @static
     */
    static generateInvoiceNumber(prefix = 'INV') {
        const timestamp = DateUtils.format(new Date(), 'YYYYMMDD');
        const random = StringUtils.generateRandomString(4, true);
        return `${prefix}-${timestamp}-${random}`;
    }

    /**
     * 从JSON创建发票实例
     * @param {Object} json - JSON对象
     * @returns {InvoiceDocument} 发票实例
     * @static
     */
    static fromJSON(json) {
        const data = {
            ...json,
            issueDate: new Date(json.issueDate),
            dueDate: json.dueDate ? new Date(json.dueDate) : null,
            paidDate: json.paidDate ? new Date(json.paidDate) : null,
            createdAt: new Date(json.createdAt),
            updatedAt: new Date(json.updatedAt)
        };
        
        return new InvoiceDocument(data);
    }

    /**
     * 创建空白发票
     * @param {Object} defaults - 默认值
     * @returns {InvoiceDocument} 空白发票实例
     * @static
     */
    static createBlank(defaults = {}) {
        return new InvoiceDocument({
            invoiceNumber: this.generateInvoiceNumber(),
            issueDate: new Date(),
            dueDate: DateUtils.addDays(new Date(), 30), // 默认30天到期
            ...defaults
        });
    }

    /**
     * 转换为字符串表示
     * @returns {string} 字符串表示
     */
    toString() {
        return `InvoiceDocument(${this.invoiceNumber}, ${this.getFormattedTotalAmount()}, ${this.billTo.name})`;
    }
}
