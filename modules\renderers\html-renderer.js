/**
 * @file HTML渲染器 - 整合后的HTML格式渲染器
 * <AUTHOR> Team
 * @description 
 * 这是重构后的HTML渲染器，整合了js/document-renderer.js和src/renderers/html-renderer.js的功能
 * 专门负责将文档模型渲染为HTML格式
 * 
 * 重构说明：
 * - 继承自统一的BaseRenderer
 * - 整合了原有HTML渲染逻辑
 * - 支持响应式设计和打印优化
 * - 消除了重复代码
 */

// #region 导入依赖
import { UnifiedRenderer } from './unified-renderer.js';
import { getLogger } from '../core/logger.js';
import { DocumentModel } from '../../src/core/rendering/document-model.js';
// #endregion

// #region HTML渲染器类定义
/**
 * @class HTMLRenderer - HTML渲染器
 * @description 专门用于HTML格式的文档渲染，继承自UnifiedRenderer
 */
export class HTMLRenderer extends UnifiedRenderer {
    /**
     * 构造函数 - 初始化HTML渲染器
     * @param {Object} config - 渲染器配置
     */
    constructor(config = {}) {
        super({
            name: 'HTMLRenderer',
            version: '2.0.0',
            type: 'html',
            format: 'html',
            ...config
        });
        
        // HTML特定配置
        this.htmlConfig = {
            doctype: '<!DOCTYPE html>',
            charset: 'utf-8',
            viewport: 'width=device-width, initial-scale=1.0',
            inlineCSS: false,
            minifyHTML: false,
            includeJS: false,
            responsive: true,
            printOptimized: true,
            ...config.htmlConfig
        };
        
        this.logger.info('HTMLRenderer', 'constructor', 'HTML渲染器创建完成');
    }
    
    /**
     * 执行HTML渲染
     * @param {Object} preparedData - 准备好的数据
     * @returns {Promise<Object>} 渲染结果
     * @protected
     */
    async _executeRender(preparedData) {
        const { document, options } = preparedData;
        
        this.logger.info('HTMLRenderer', '_executeRender', '开始HTML渲染');
        
        try {
            // 生成HTML结构
            const htmlContent = await this._generateHTML(document, options);
            
            // 生成CSS样式
            const cssContent = await this._generateCSS(document, options);
            
            // 组装完整HTML
            const fullHTML = await this._assembleHTML(htmlContent, cssContent, options);
            
            return {
                content: fullHTML,
                type: 'html',
                size: fullHTML.length,
                encoding: this.htmlConfig.charset
            };
            
        } catch (error) {
            this.logger.error('HTMLRenderer', '_executeRender', 'HTML渲染失败', error);
            throw error;
        }
    }
    
    /**
     * 生成HTML内容
     * @param {Object} document - 文档对象
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML内容
     * @private
     */
    async _generateHTML(document, options) {
        const { type, data } = document;
        
        switch (type) {
            case 'receipt':
                return await this._generateReceiptHTML(data, options);
            case 'invoice':
                return await this._generateInvoiceHTML(data, options);
            case 'quotation':
                return await this._generateQuotationHTML(data, options);
            case 'driver_agreement':
                return await this._generateDriverAgreementHTML(data, options);
            default:
                throw new Error(`不支持的文档类型: ${type}`);
        }
    }
    
    /**
     * 生成收据HTML
     * @param {Object} data - 收据数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML内容
     * @private
     */
    async _generateReceiptHTML(data, options) {
        const {
            receiptNumber = '',
            date = '',
            customerName = '',
            channel = '',
            items = [],
            totalAmount = 0,
            currency = 'RM',
            notes = '',
            conclusion = ''
        } = data;
        
        return `
            <div class="document-container receipt-document">
                <!-- 页眉 -->
                <div class="document-header">
                    <div class="company-logo">
                        <img src="${await this._getCompanyLogo(options.company)}" alt="Company Logo" />
                    </div>
                </div>
                
                <!-- 文档标题 -->
                <div class="document-title-section">
                    <h1 class="document-title">收据 / Receipt</h1>
                    <div class="document-meta">
                        <span class="document-number">单号: ${receiptNumber}</span>
                        <span class="document-date">日期: ${date}</span>
                    </div>
                </div>
                
                <!-- 客户信息 -->
                <div class="customer-section">
                    <div class="customer-info">
                        <h3>客户信息 / Customer Information</h3>
                        <p><strong>姓名 / Name:</strong> ${customerName}</p>
                        <p><strong>渠道 / Channel:</strong> ${channel}</p>
                    </div>
                </div>
                
                <!-- 服务项目 -->
                <div class="items-section">
                    <h3>服务项目 / Service Items</h3>
                    <table class="items-table">
                        <thead>
                            <tr>
                                <th>项目描述 / Description</th>
                                <th>数量 / Quantity</th>
                                <th>单价 / Unit Price</th>
                                <th>金额 / Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${items.map(item => `
                                <tr>
                                    <td>${item.description || ''}</td>
                                    <td>${item.quantity || 1}</td>
                                    <td>${currency} ${item.unitPrice || 0}</td>
                                    <td>${currency} ${item.amount || 0}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                
                <!-- 总计 -->
                <div class="total-section">
                    <div class="total-amount">
                        <strong>总计 / Total: ${currency} ${totalAmount}</strong>
                    </div>
                </div>
                
                <!-- 备注 -->
                ${notes ? `
                    <div class="notes-section">
                        <h4>备注 / Notes</h4>
                        <p>${notes}</p>
                    </div>
                ` : ''}
                
                <!-- 结语 -->
                ${conclusion ? `
                    <div class="conclusion-section">
                        <p>${conclusion}</p>
                    </div>
                ` : ''}
                
                <!-- 页脚 -->
                <div class="document-footer">
                    <div class="company-stamp">
                        <img src="${await this._getCompanyStamp(options.company)}" alt="Company Stamp" />
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * 生成发票HTML
     * @param {Object} data - 发票数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML内容
     * @private
     */
    async _generateInvoiceHTML(data, options) {
        // 发票HTML生成逻辑（类似收据，但包含税务信息）
        const baseHTML = await this._generateReceiptHTML(data, options);
        
        // 添加发票特定内容
        const taxInfo = data.taxRate ? `
            <div class="tax-section">
                <p><strong>税率 / Tax Rate:</strong> ${data.taxRate}%</p>
                <p><strong>税额 / Tax Amount:</strong> ${data.currency || 'RM'} ${(data.totalAmount * (data.taxRate || 0) / 100).toFixed(2)}</p>
            </div>
        ` : '';
        
        return baseHTML.replace('收据 / Receipt', '发票 / Invoice').replace('</div>', taxInfo + '</div>');
    }
    
    /**
     * 生成报价单HTML
     * @param {Object} data - 报价单数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML内容
     * @private
     */
    async _generateQuotationHTML(data, options) {
        // 报价单HTML生成逻辑
        const baseHTML = await this._generateReceiptHTML(data, options);
        
        // 添加报价单特定内容
        const quotationInfo = `
            <div class="quotation-info">
                ${data.validUntil ? `<p><strong>有效期至 / Valid Until:</strong> ${data.validUntil}</p>` : ''}
                ${data.preparedBy ? `<p><strong>报价人 / Prepared By:</strong> ${data.preparedBy}</p>` : ''}
                ${data.estimatedDelivery ? `<p><strong>预计交付 / Estimated Delivery:</strong> ${data.estimatedDelivery}</p>` : ''}
            </div>
        `;
        
        return baseHTML.replace('收据 / Receipt', '报价单 / Quotation').replace('</div>', quotationInfo + '</div>');
    }
    
    /**
     * 生成司机协议HTML
     * @param {Object} data - 司机协议数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML内容
     * @private
     */
    async _generateDriverAgreementHTML(data, options) {
        // 司机协议HTML生成逻辑（更复杂的法律文档格式）
        return `
            <div class="document-container driver-agreement-document">
                <div class="agreement-header">
                    <h1>驾驶服务协议 / Driver Service Agreement</h1>
                    <p>协议编号 / Agreement No.: ${data.agreementNumber || ''}</p>
                    <p>签署日期 / Date: ${data.date || ''}</p>
                </div>
                
                <div class="parties-section">
                    <div class="party-a">
                        <h3>甲方 / Party A</h3>
                        <p>${data.partyA || ''}</p>
                    </div>
                    <div class="party-b">
                        <h3>乙方 / Party B</h3>
                        <p>${data.partyB || ''}</p>
                    </div>
                </div>
                
                <div class="agreement-content">
                    <h3>协议内容 / Agreement Content</h3>
                    <div class="content-text">
                        ${data.content || ''}
                    </div>
                </div>
                
                <div class="terms-section">
                    <h3>条款 / Terms</h3>
                    <div class="terms-content">
                        ${data.terms || ''}
                    </div>
                </div>
                
                <div class="signature-section">
                    <div class="signatures">
                        <div class="party-a-signature">
                            <p>甲方签名 / Party A Signature:</p>
                            <div class="signature-line"></div>
                        </div>
                        <div class="party-b-signature">
                            <p>乙方签名 / Party B Signature:</p>
                            <div class="signature-line"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * 生成CSS样式
     * @param {Object} document - 文档对象
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} CSS内容
     * @private
     */
    async _generateCSS(document, options) {
        const baseCSS = `
            /* 基础样式 */
            .document-container {
                max-width: 794px;
                margin: 0 auto;
                padding: 20px;
                font-family: 'Arial', sans-serif;
                line-height: 1.6;
                color: #333;
                background: white;
            }
            
            /* 页眉样式 */
            .document-header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #eee;
                padding-bottom: 20px;
            }
            
            .company-logo img {
                max-height: 80px;
                max-width: 100%;
            }
            
            /* 标题样式 */
            .document-title-section {
                text-align: center;
                margin-bottom: 30px;
            }
            
            .document-title {
                font-size: 28px;
                font-weight: bold;
                margin-bottom: 10px;
                color: #2c3e50;
            }
            
            .document-meta {
                display: flex;
                justify-content: space-between;
                font-size: 14px;
                color: #666;
            }
            
            /* 表格样式 */
            .items-table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
            }
            
            .items-table th,
            .items-table td {
                border: 1px solid #ddd;
                padding: 12px;
                text-align: left;
            }
            
            .items-table th {
                background-color: #f8f9fa;
                font-weight: bold;
            }
            
            /* 总计样式 */
            .total-section {
                text-align: right;
                margin: 30px 0;
                font-size: 18px;
            }
            
            /* 页脚样式 */
            .document-footer {
                margin-top: 50px;
                text-align: center;
                border-top: 1px solid #eee;
                padding-top: 20px;
            }
            
            .company-stamp img {
                max-height: 100px;
                max-width: 200px;
            }
            
            /* 打印样式 */
            @media print {
                .document-container {
                    max-width: none;
                    margin: 0;
                    padding: 15mm;
                }
                
                .document-title {
                    font-size: 24px;
                }
            }
            
            /* 响应式样式 */
            @media (max-width: 768px) {
                .document-container {
                    padding: 15px;
                }
                
                .document-meta {
                    flex-direction: column;
                    gap: 5px;
                }
                
                .items-table {
                    font-size: 14px;
                }
            }
        `;
        
        return baseCSS;
    }
    
    /**
     * 组装完整HTML
     * @param {string} htmlContent - HTML内容
     * @param {string} cssContent - CSS内容
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} 完整HTML
     * @private
     */
    async _assembleHTML(htmlContent, cssContent, options) {
        return `
            ${this.htmlConfig.doctype}
            <html lang="zh-CN">
            <head>
                <meta charset="${this.htmlConfig.charset}">
                <meta name="viewport" content="${this.htmlConfig.viewport}">
                <title>SmartOffice Document</title>
                <style>
                    ${cssContent}
                </style>
            </head>
            <body>
                ${htmlContent}
            </body>
            </html>
        `;
    }
    
    /**
     * 获取公司Logo
     * @param {string} company - 公司标识
     * @returns {Promise<string>} Logo URL
     * @private
     */
    async _getCompanyLogo(company) {
        // 这里应该从资源管理器获取Logo
        return `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`;
    }
    
    /**
     * 获取公司印章
     * @param {string} company - 公司标识
     * @returns {Promise<string>} 印章URL
     * @private
     */
    async _getCompanyStamp(company) {
        // 这里应该从资源管理器获取印章
        return `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`;
    }
}
// #endregion

// #region 工厂函数
/**
 * 创建HTML渲染器实例
 * @param {Object} config - 配置
 * @returns {HTMLRenderer} HTML渲染器实例
 */
export function createHTMLRenderer(config = {}) {
    return new HTMLRenderer(config);
}
// #endregion
