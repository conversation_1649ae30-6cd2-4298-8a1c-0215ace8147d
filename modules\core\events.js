/**
 * @file 统一事件系统 - 整合后的事件处理模块
 * <AUTHOR> Team
 * @description 
 * 这是重构后的统一事件系统，整合了js/utils/event-bus.js和src/core/events/的功能
 * 提供完整的事件发布订阅机制
 * 
 * 重构说明：
 * - 整合了EventBus和EventEmitter的功能
 * - 消除了重复的事件处理代码
 * - 建立了统一的事件接口
 * - 支持事件命名空间和通配符
 */

// #region 事件发射器基类
/**
 * @class EventEmitter - 事件发射器基类
 * @description 提供基础的事件发布订阅功能
 */
export class EventEmitter {
    /**
     * 构造函数 - 初始化事件发射器
     */
    constructor() {
        /** @private {Map<string, Set<Function>>} 事件监听器映射表 */
        this._listeners = new Map();
        
        /** @private {Map<string, Set<Function>>} 一次性事件监听器映射表 */
        this._onceListeners = new Map();
        
        /** @private {boolean} 是否启用调试模式 */
        this._debug = false;
        
        /** @private {Object} 事件统计信息 */
        this._stats = {
            totalEvents: 0,
            totalListeners: 0,
            eventCounts: new Map(),
            lastEvent: null
        };
    }
    
    /**
     * 添加事件监听器
     * @param {string} eventType - 事件类型
     * @param {Function} listener - 监听器函数
     * @returns {EventEmitter} 返回自身以支持链式调用
     */
    on(eventType, listener) {
        if (typeof listener !== 'function') {
            throw new Error('监听器必须是函数');
        }
        
        if (!this._listeners.has(eventType)) {
            this._listeners.set(eventType, new Set());
        }
        
        this._listeners.get(eventType).add(listener);
        this._stats.totalListeners++;
        
        if (this._debug) {
            console.log(`[EventEmitter] 添加监听器: ${eventType}`);
        }
        
        return this;
    }
    
    /**
     * 添加一次性事件监听器
     * @param {string} eventType - 事件类型
     * @param {Function} listener - 监听器函数
     * @returns {EventEmitter} 返回自身以支持链式调用
     */
    once(eventType, listener) {
        if (typeof listener !== 'function') {
            throw new Error('监听器必须是函数');
        }
        
        if (!this._onceListeners.has(eventType)) {
            this._onceListeners.set(eventType, new Set());
        }
        
        this._onceListeners.get(eventType).add(listener);
        this._stats.totalListeners++;
        
        if (this._debug) {
            console.log(`[EventEmitter] 添加一次性监听器: ${eventType}`);
        }
        
        return this;
    }
    
    /**
     * 移除事件监听器
     * @param {string} eventType - 事件类型
     * @param {Function} listener - 监听器函数
     * @returns {EventEmitter} 返回自身以支持链式调用
     */
    off(eventType, listener) {
        // 从普通监听器中移除
        if (this._listeners.has(eventType)) {
            const listeners = this._listeners.get(eventType);
            if (listeners.delete(listener)) {
                this._stats.totalListeners--;
                if (listeners.size === 0) {
                    this._listeners.delete(eventType);
                }
            }
        }
        
        // 从一次性监听器中移除
        if (this._onceListeners.has(eventType)) {
            const onceListeners = this._onceListeners.get(eventType);
            if (onceListeners.delete(listener)) {
                this._stats.totalListeners--;
                if (onceListeners.size === 0) {
                    this._onceListeners.delete(eventType);
                }
            }
        }
        
        if (this._debug) {
            console.log(`[EventEmitter] 移除监听器: ${eventType}`);
        }
        
        return this;
    }
    
    /**
     * 触发事件
     * @param {string} eventType - 事件类型
     * @param {...any} args - 事件参数
     * @returns {boolean} 是否有监听器处理了该事件
     */
    emit(eventType, ...args) {
        let hasListeners = false;
        
        // 更新统计信息
        this._stats.totalEvents++;
        this._stats.lastEvent = {
            type: eventType,
            timestamp: new Date(),
            args: args.length
        };
        
        const eventCount = this._stats.eventCounts.get(eventType) || 0;
        this._stats.eventCounts.set(eventType, eventCount + 1);
        
        if (this._debug) {
            console.log(`[EventEmitter] 触发事件: ${eventType}`, args);
        }
        
        // 触发普通监听器
        if (this._listeners.has(eventType)) {
            const listeners = this._listeners.get(eventType);
            for (const listener of listeners) {
                try {
                    listener.apply(this, args);
                    hasListeners = true;
                } catch (error) {
                    console.error(`[EventEmitter] 监听器执行错误 (${eventType}):`, error);
                }
            }
        }
        
        // 触发一次性监听器
        if (this._onceListeners.has(eventType)) {
            const onceListeners = this._onceListeners.get(eventType);
            for (const listener of onceListeners) {
                try {
                    listener.apply(this, args);
                    hasListeners = true;
                } catch (error) {
                    console.error(`[EventEmitter] 一次性监听器执行错误 (${eventType}):`, error);
                }
            }
            // 清除一次性监听器
            this._stats.totalListeners -= onceListeners.size;
            this._onceListeners.delete(eventType);
        }
        
        return hasListeners;
    }
    
    /**
     * 移除指定事件类型的所有监听器
     * @param {string} eventType - 事件类型
     * @returns {EventEmitter} 返回自身以支持链式调用
     */
    removeAllListeners(eventType) {
        if (eventType) {
            // 移除指定事件类型的监听器
            if (this._listeners.has(eventType)) {
                this._stats.totalListeners -= this._listeners.get(eventType).size;
                this._listeners.delete(eventType);
            }
            if (this._onceListeners.has(eventType)) {
                this._stats.totalListeners -= this._onceListeners.get(eventType).size;
                this._onceListeners.delete(eventType);
            }
        } else {
            // 移除所有监听器
            this._listeners.clear();
            this._onceListeners.clear();
            this._stats.totalListeners = 0;
        }
        
        if (this._debug) {
            console.log(`[EventEmitter] 移除所有监听器: ${eventType || 'all'}`);
        }
        
        return this;
    }
    
    /**
     * 获取指定事件类型的监听器数量
     * @param {string} eventType - 事件类型
     * @returns {number} 监听器数量
     */
    listenerCount(eventType) {
        let count = 0;
        
        if (this._listeners.has(eventType)) {
            count += this._listeners.get(eventType).size;
        }
        
        if (this._onceListeners.has(eventType)) {
            count += this._onceListeners.get(eventType).size;
        }
        
        return count;
    }
    
    /**
     * 获取所有事件类型
     * @returns {Array<string>} 事件类型列表
     */
    eventNames() {
        const names = new Set();
        
        for (const eventType of this._listeners.keys()) {
            names.add(eventType);
        }
        
        for (const eventType of this._onceListeners.keys()) {
            names.add(eventType);
        }
        
        return Array.from(names);
    }
    
    /**
     * 启用或禁用调试模式
     * @param {boolean} enabled - 是否启用调试
     */
    setDebug(enabled) {
        this._debug = enabled;
        console.log(`[EventEmitter] 调试模式: ${enabled ? '启用' : '禁用'}`);
    }
    
    /**
     * 获取事件统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this._stats,
            eventCounts: Object.fromEntries(this._stats.eventCounts),
            currentListeners: this._stats.totalListeners,
            eventTypes: this.eventNames().length
        };
    }
    
    /**
     * 重置统计信息
     */
    resetStats() {
        this._stats = {
            totalEvents: 0,
            totalListeners: this._stats.totalListeners, // 保持当前监听器数量
            eventCounts: new Map(),
            lastEvent: null
        };
        
        if (this._debug) {
            console.log('[EventEmitter] 统计信息已重置');
        }
    }
}
// #endregion

// #region 全局事件总线
/**
 * @class EventBus - 全局事件总线
 * @description 提供全局的事件通信机制，支持命名空间和通配符
 */
export class EventBus extends EventEmitter {
    /**
     * 构造函数 - 初始化事件总线
     */
    constructor() {
        super();
        
        /** @private {Map<string, Set<string>>} 命名空间映射 */
        this._namespaces = new Map();
        
        /** @private {Set<string>} 通配符监听器 */
        this._wildcardListeners = new Set();
        
        this.setDebug(false); // 默认关闭调试
    }
    
    /**
     * 触发事件（重写以支持命名空间和通配符）
     * @param {string} eventType - 事件类型
     * @param {...any} args - 事件参数
     * @returns {boolean} 是否有监听器处理了该事件
     */
    emit(eventType, ...args) {
        let hasListeners = super.emit(eventType, ...args);
        
        // 处理命名空间事件
        const parts = eventType.split(':');
        if (parts.length > 1) {
            // 触发命名空间通配符监听器
            for (let i = 1; i < parts.length; i++) {
                const wildcardEvent = parts.slice(0, i).join(':') + ':*';
                if (super.emit(wildcardEvent, ...args)) {
                    hasListeners = true;
                }
            }
        }
        
        // 触发全局通配符监听器
        if (super.emit('*', eventType, ...args)) {
            hasListeners = true;
        }
        
        return hasListeners;
    }
    
    /**
     * 添加命名空间监听器
     * @param {string} namespace - 命名空间
     * @param {Function} listener - 监听器函数
     * @returns {EventBus} 返回自身以支持链式调用
     */
    onNamespace(namespace, listener) {
        const wildcardEvent = namespace + ':*';
        return this.on(wildcardEvent, listener);
    }
    
    /**
     * 添加全局监听器（监听所有事件）
     * @param {Function} listener - 监听器函数
     * @returns {EventBus} 返回自身以支持链式调用
     */
    onAny(listener) {
        return this.on('*', listener);
    }
    
    /**
     * 批量触发事件
     * @param {Array<{type: string, args: Array}>} events - 事件列表
     * @returns {Array<boolean>} 每个事件的处理结果
     */
    emitBatch(events) {
        if (!Array.isArray(events)) {
            throw new Error('事件列表必须是数组');
        }
        
        const results = [];
        for (const event of events) {
            if (!event.type) {
                throw new Error('事件必须包含type属性');
            }
            const result = this.emit(event.type, ...(event.args || []));
            results.push(result);
        }
        return results;
    }
    
    /**
     * 异步触发事件
     * @param {string} eventType - 事件类型
     * @param {...any} args - 事件参数
     * @returns {Promise<boolean>} 是否有监听器处理了该事件
     */
    async emitAsync(eventType, ...args) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const result = this.emit(eventType, ...args);
                resolve(result);
            }, 0);
        });
    }
}
// #endregion

// #region 默认实例和便捷函数
/** @type {EventBus} 默认全局事件总线实例 */
export const defaultEventBus = new EventBus();

/**
 * 创建新的事件总线实例
 * @returns {EventBus} 事件总线实例
 */
export function createEventBus() {
    return new EventBus();
}

/**
 * 创建新的事件发射器实例
 * @returns {EventEmitter} 事件发射器实例
 */
export function createEventEmitter() {
    return new EventEmitter();
}

// 便捷的全局函数
export const on = defaultEventBus.on.bind(defaultEventBus);
export const once = defaultEventBus.once.bind(defaultEventBus);
export const off = defaultEventBus.off.bind(defaultEventBus);
export const emit = defaultEventBus.emit.bind(defaultEventBus);
export const onNamespace = defaultEventBus.onNamespace.bind(defaultEventBus);
export const onAny = defaultEventBus.onAny.bind(defaultEventBus);
// #endregion

// #region 自动注册到全局
if (typeof window !== 'undefined') {
    window.SmartOfficeEvents = {
        EventEmitter,
        EventBus,
        defaultEventBus,
        createEventBus,
        createEventEmitter,
        on,
        once,
        off,
        emit,
        onNamespace,
        onAny
    };
}
// #endregion
