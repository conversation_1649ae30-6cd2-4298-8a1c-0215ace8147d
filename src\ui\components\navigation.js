/**
 * @file 导航组件 - SmartOffice 导航系统
 * <AUTHOR> Team
 * @description 
 * 提供完整的导航功能，包括：
 * - Navigation 基础导航组件
 * - NavItem 导航项组件
 * - Breadcrumb 面包屑导航
 * - Menu 菜单组件
 * - 多级导航支持
 * - 路由集成和状态管理
 * - 完整的事件系统和生命周期管理
 */

import { BaseComponent } from './base-component.js';
import { UI_EVENTS } from '../../core/events/event-types.js';

// #region Navigation主导航组件
/**
 * @class Navigation - 主导航组件
 * @extends BaseComponent
 * @description 可配置的导航组件，支持多级菜单和路由集成
 */
export class Navigation extends BaseComponent {
    /**
     * 构造函数 - 初始化导航组件
     * @param {Object} config - 导航配置
     * @param {Array} config.items - 导航项数组
     * @param {string} config.mode - 导航模式
     * @param {string} config.theme - 主题模式
     */
    constructor(config = {}) {
        super(config);
        
        // 导航属性
        this.navProps = {
            items: config.items || [],
            mode: config.mode || 'horizontal', // horizontal, vertical, inline
            theme: config.theme || 'light', // light, dark
            collapsed: config.collapsed || false, // 是否折叠（垂直模式）
            defaultSelectedKeys: config.defaultSelectedKeys || [],
            defaultOpenKeys: config.defaultOpenKeys || [],
            multiple: config.multiple || false, // 是否允许多选
            selectable: config.selectable !== false, // 是否可选择
            inlineCollapsed: config.inlineCollapsed || false, // 内联模式折叠
            style: config.style || {}, // 自定义样式
            className: config.className || '',
            forceSubMenuRender: config.forceSubMenuRender || false, // 强制渲染子菜单
            triggerSubMenuAction: config.triggerSubMenuAction || 'hover' // click, hover
        };
        
        // 导航状态
        this.navState = {
            selectedKeys: new Set(this.navProps.defaultSelectedKeys),
            openKeys: new Set(this.navProps.defaultOpenKeys),
            activeKey: null, // 当前活跃项
            expandedKeys: new Set(), // 展开的子菜单
            hoveredKey: null, // 悬停项
            focusedKey: null, // 焦点项
            collapsed: this.navProps.collapsed
        };
        
        // DOM元素引用
        this.elements = {
            nav: null,
            container: null,
            header: null,
            body: null,
            footer: null,
            collapseToggle: null
        };
        
        // 事件处理器
        this.handlers = {
            itemClick: this._handleItemClick.bind(this),
            itemHover: this._handleItemHover.bind(this),
            submenuToggle: this._handleSubmenuToggle.bind(this),
            collapseToggle: this._handleCollapseToggle.bind(this),
            keyDown: this._handleKeyDown.bind(this)
        };
        
        // 初始化组件
        this._initializeNavigation();
    }

    /**
     * 初始化导航 - 创建DOM结构和设置事件
     * @private
     */
    _initializeNavigation() {
        this._createNavigationStructure();
        this._applyNavigationStyles();
        this._bindNavigationEvents();
        this._renderNavigationItems();
        
        // 发射初始化完成事件
        this.emit(UI_EVENTS.COMPONENT_INITIALIZED, {
            component: this,
            type: 'navigation'
        });
    }

    /**
     * 创建导航DOM结构 - 构建完整的导航HTML
     * @private
     */
    _createNavigationStructure() {
        // 创建导航容器
        this.elements.nav = document.createElement('nav');
        this.elements.nav.className = `smartoffice-navigation smartoffice-nav-${this.navProps.mode} smartoffice-nav-${this.navProps.theme} ${this.navProps.className}`;
        
        // 设置角色和可访问性
        this.elements.nav.setAttribute('role', 'navigation');
        this.elements.nav.setAttribute('aria-label', '主导航');
        
        // 创建导航主体
        this.elements.container = document.createElement('ul');
        this.elements.container.className = 'nav-container';
        this.elements.container.setAttribute('role', 'menubar');
        
        // 折叠状态
        if (this.navState.collapsed) {
            this.elements.nav.classList.add('nav-collapsed');
        }
        
        this.elements.nav.appendChild(this.elements.container);
        this.container = this.elements.nav;
    }

    /**
     * 应用导航样式 - 设置CSS样式
     * @private
     */
    _applyNavigationStyles() {
        // 基础样式
        Object.assign(this.elements.nav.style, {
            display: 'flex',
            flexDirection: this.navProps.mode === 'horizontal' ? 'row' : 'column',
            backgroundColor: this.navProps.theme === 'dark' ? '#001529' : '#ffffff',
            borderRight: this.navProps.mode === 'vertical' ? '1px solid #f0f0f0' : 'none',
            borderBottom: this.navProps.mode === 'horizontal' ? '1px solid #f0f0f0' : 'none',
            ...this.navProps.style
        });
        
        // 容器样式
        Object.assign(this.elements.container.style, {
            display: 'flex',
            flexDirection: this.navProps.mode === 'horizontal' ? 'row' : 'column',
            margin: '0',
            padding: '0',
            listStyle: 'none',
            width: '100%',
            overflow: this.navProps.mode === 'vertical' ? 'auto' : 'visible'
        });
        
        // 响应式处理
        if (this.navProps.mode === 'vertical') {
            this.elements.nav.style.width = this.navState.collapsed ? '80px' : '256px';
            this.elements.nav.style.transition = 'width 0.2s';
        }
    }

    /**
     * 绑定导航事件 - 设置所有事件监听器
     * @private
     */
    _bindNavigationEvents() {
        // 导航项点击事件
        this.elements.container.addEventListener('click', this.handlers.itemClick);
        
        // 导航项悬停事件
        this.elements.container.addEventListener('mouseenter', this.handlers.itemHover, true);
        this.elements.container.addEventListener('mouseleave', this.handlers.itemHover, true);
        
        // 键盘导航事件
        this.elements.nav.addEventListener('keydown', this.handlers.keyDown);
    }

    /**
     * 渲染导航项 - 生成导航菜单项
     * @private
     */
    _renderNavigationItems() {
        this.elements.container.innerHTML = '';
        
        this.navProps.items.forEach((item, index) => {
            const navItem = this._createNavigationItem(item, index, 0);
            this.elements.container.appendChild(navItem);
        });
    }

    /**
     * 创建导航项 - 生成单个导航项
     * @param {Object} item - 导航项数据
     * @param {number} index - 项目索引
     * @param {number} level - 层级深度
     * @returns {HTMLElement} 导航项元素
     * @private
     */
    _createNavigationItem(item, index, level) {
        const li = document.createElement('li');
        li.className = 'nav-item';
        li.dataset.key = item.key || `item-${index}`;
        li.dataset.level = level;
        
        // 设置可访问性属性
        li.setAttribute('role', item.children ? 'menuitem' : 'none');
        if (item.children) {
            li.setAttribute('aria-haspopup', 'true');
            li.setAttribute('aria-expanded', 'false');
        }
        
        // 创建导航链接
        const link = this._createNavigationLink(item, level);
        li.appendChild(link);
        
        // 创建子菜单
        if (item.children && item.children.length > 0) {
            const submenu = this._createSubmenu(item.children, level + 1);
            li.appendChild(submenu);
            li.classList.add('has-submenu');
        }
        
        // 应用状态
        this._applyItemState(li, item);
        
        return li;
    }

    /**
     * 创建导航链接 - 生成导航链接元素
     * @param {Object} item - 导航项数据
     * @param {number} level - 层级深度
     * @returns {HTMLElement} 链接元素
     * @private
     */
    _createNavigationLink(item, level) {
        const link = document.createElement('a');
        link.className = 'nav-link';
        link.href = item.href || '#';
        link.dataset.key = item.key;
        
        // 设置可访问性
        link.setAttribute('role', 'menuitem');
        link.setAttribute('tabindex', level === 0 ? '0' : '-1');
        
        // 阻止默认行为（如果有自定义处理）
        if (!item.href || item.onClick) {
            link.addEventListener('click', (e) => {
                e.preventDefault();
            });
        }
        
        // 图标
        if (item.icon) {
            const icon = document.createElement('span');
            icon.className = 'nav-icon';
            icon.innerHTML = typeof item.icon === 'string' ? item.icon : '●';
            link.appendChild(icon);
        }
        
        // 标题
        const title = document.createElement('span');
        title.className = 'nav-title';
        title.textContent = item.title || item.label || '';
        link.appendChild(title);
        
        // 子菜单指示器
        if (item.children && item.children.length > 0) {
            const arrow = document.createElement('span');
            arrow.className = 'nav-arrow';
            arrow.innerHTML = this.navProps.mode === 'horizontal' ? '▼' : '▶';
            link.appendChild(arrow);
        }
        
        // 折叠模式下隐藏标题
        if (this.navState.collapsed && level === 0) {
            title.style.display = 'none';
        }
        
        return link;
    }

    /**
     * 创建子菜单 - 生成子菜单容器
     * @param {Array} children - 子菜单项
     * @param {number} level - 层级深度
     * @returns {HTMLElement} 子菜单元素
     * @private
     */
    _createSubmenu(children, level) {
        const submenu = document.createElement('ul');
        submenu.className = 'nav-submenu';
        submenu.setAttribute('role', 'menu');
        submenu.style.display = 'none';
        
        children.forEach((child, index) => {
            const childItem = this._createNavigationItem(child, index, level);
            submenu.appendChild(childItem);
        });
        
        return submenu;
    }

    /**
     * 应用项目状态 - 设置导航项的视觉状态
     * @param {HTMLElement} li - 导航项元素
     * @param {Object} item - 导航项数据
     * @private
     */
    _applyItemState(li, item) {
        const key = item.key;
        
        // 选中状态
        if (this.navState.selectedKeys.has(key)) {
            li.classList.add('selected');
            li.setAttribute('aria-selected', 'true');
        }
        
        // 禁用状态
        if (item.disabled) {
            li.classList.add('disabled');
            li.setAttribute('aria-disabled', 'true');
        }
        
        // 展开状态
        if (this.navState.openKeys.has(key)) {
            li.classList.add('open');
            const submenu = li.querySelector('.nav-submenu');
            if (submenu) {
                submenu.style.display = 'block';
                li.setAttribute('aria-expanded', 'true');
            }
        }
    }

    // #region 公开方法
    /**
     * 设置选中项 - 设置当前选中的导航项
     * @param {string|Array} keys - 选中的键
     */
    setSelectedKeys(keys) {
        const keyArray = Array.isArray(keys) ? keys : [keys];
        
        if (this.navProps.multiple) {
            this.navState.selectedKeys = new Set(keyArray);
        } else {
            this.navState.selectedKeys = new Set(keyArray.slice(0, 1));
        }
        
        this._updateItemStates();
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            type: 'selection',
            selectedKeys: Array.from(this.navState.selectedKeys)
        });
    }

    /**
     * 获取选中项 - 获取当前选中的导航项
     * @returns {Array} 选中项键数组
     */
    getSelectedKeys() {
        return Array.from(this.navState.selectedKeys);
    }

    /**
     * 设置展开项 - 设置展开的子菜单
     * @param {Array} keys - 展开的键数组
     */
    setOpenKeys(keys) {
        this.navState.openKeys = new Set(keys);
        this._updateSubmenuStates();
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            type: 'submenu',
            openKeys: Array.from(this.navState.openKeys)
        });
    }

    /**
     * 获取展开项 - 获取当前展开的子菜单
     * @returns {Array} 展开项键数组
     */
    getOpenKeys() {
        return Array.from(this.navState.openKeys);
    }

    /**
     * 切换折叠状态 - 切换导航的折叠/展开状态
     */
    toggleCollapse() {
        this.navState.collapsed = !this.navState.collapsed;
        this.elements.nav.classList.toggle('nav-collapsed', this.navState.collapsed);
        
        // 更新宽度
        if (this.navProps.mode === 'vertical') {
            this.elements.nav.style.width = this.navState.collapsed ? '80px' : '256px';
        }
        
        // 更新标题显示
        this._updateCollapseState();
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            type: 'collapse',
            collapsed: this.navState.collapsed
        });
    }

    /**
     * 设置导航项 - 更新导航菜单项
     * @param {Array} items - 新的导航项数组
     */
    setItems(items) {
        this.navProps.items = items || [];
        this._renderNavigationItems();
        
        this.emit(UI_EVENTS.COMPONENT_UPDATED, {
            component: this,
            type: 'items',
            items: this.navProps.items
        });
    }

    /**
     * 添加导航项 - 添加新的导航项
     * @param {Object} item - 导航项配置
     * @param {string} parentKey - 父级键（可选）
     */
    addItem(item, parentKey = null) {
        if (parentKey) {
            // 添加到子菜单
            const parentItem = this._findItemByKey(parentKey);
            if (parentItem) {
                if (!parentItem.children) {
                    parentItem.children = [];
                }
                parentItem.children.push(item);
            }
        } else {
            // 添加到根级
            this.navProps.items.push(item);
        }
        
        this._renderNavigationItems();
        
        this.emit(UI_EVENTS.COMPONENT_UPDATED, {
            component: this,
            type: 'addItem',
            item: item,
            parentKey: parentKey
        });
    }

    /**
     * 移除导航项 - 移除指定的导航项
     * @param {string} key - 要移除的项键
     */
    removeItem(key) {
        this._removeItemByKey(key);
        this._renderNavigationItems();
        
        this.emit(UI_EVENTS.COMPONENT_UPDATED, {
            component: this,
            type: 'removeItem',
            key: key
        });
    }
    // #endregion

    // #region 事件处理方法
    /**
     * 处理项目点击 - 导航项点击处理
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleItemClick(e) {
        const link = e.target.closest('.nav-link');
        if (!link) return;
        
        const li = link.closest('.nav-item');
        const key = link.dataset.key;
        const item = this._findItemByKey(key);
        
        // 禁用项不处理
        if (li.classList.contains('disabled')) {
            e.preventDefault();
            return;
        }
        
        // 处理选择
        if (this.navProps.selectable) {
            if (this.navProps.multiple) {
                if (this.navState.selectedKeys.has(key)) {
                    this.navState.selectedKeys.delete(key);
                } else {
                    this.navState.selectedKeys.add(key);
                }
            } else {
                this.navState.selectedKeys.clear();
                this.navState.selectedKeys.add(key);
            }
        }
        
        // 处理子菜单切换
        if (item && item.children && item.children.length > 0) {
            this._toggleSubmenu(key);
        }
        
        // 更新状态
        this._updateItemStates();
        
        // 发射点击事件
        this.emit(UI_EVENTS.COMPONENT_CLICKED, {
            component: this,
            type: 'navItem',
            key: key,
            item: item,
            event: e
        });
        
        // 执行自定义点击处理
        if (item && item.onClick && typeof item.onClick === 'function') {
            item.onClick(item, key, e);
        }
    }

    /**
     * 处理项目悬停 - 导航项悬停处理
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleItemHover(e) {
        const link = e.target.closest('.nav-link');
        if (!link) return;
        
        const key = link.dataset.key;
        const item = this._findItemByKey(key);
        
        if (e.type === 'mouseenter') {
            this.navState.hoveredKey = key;
            
            // 悬停触发子菜单
            if (this.navProps.triggerSubMenuAction === 'hover' && 
                item && item.children && item.children.length > 0) {
                this._showSubmenu(key);
            }
        } else if (e.type === 'mouseleave') {
            this.navState.hoveredKey = null;
            
            // 悬停离开隐藏子菜单
            if (this.navProps.triggerSubMenuAction === 'hover') {
                setTimeout(() => {
                    if (this.navState.hoveredKey !== key) {
                        this._hideSubmenu(key);
                    }
                }, 200);
            }
        }
    }

    /**
     * 处理子菜单切换 - 子菜单展开/折叠处理
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleSubmenuToggle(e) {
        // 由itemClick处理
    }

    /**
     * 处理折叠切换 - 导航折叠/展开处理
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleCollapseToggle(e) {
        this.toggleCollapse();
    }

    /**
     * 处理键盘导航 - 键盘事件处理
     * @param {KeyboardEvent} e - 键盘事件
     * @private
     */
    _handleKeyDown(e) {
        const { key } = e;
        
        switch (key) {
            case 'ArrowDown':
                this._navigateVertical(1);
                e.preventDefault();
                break;
            case 'ArrowUp':
                this._navigateVertical(-1);
                e.preventDefault();
                break;
            case 'ArrowRight':
                this._navigateHorizontal(1);
                e.preventDefault();
                break;
            case 'ArrowLeft':
                this._navigateHorizontal(-1);
                e.preventDefault();
                break;
            case 'Enter':
            case ' ':
                this._activateCurrentItem();
                e.preventDefault();
                break;
            case 'Escape':
                this._closeAllSubmenus();
                e.preventDefault();
                break;
        }
    }
    // #endregion

    // #region 辅助方法
    /**
     * 切换子菜单 - 切换子菜单显示状态
     * @param {string} key - 菜单项键
     * @private
     */
    _toggleSubmenu(key) {
        if (this.navState.openKeys.has(key)) {
            this.navState.openKeys.delete(key);
        } else {
            this.navState.openKeys.add(key);
        }
        
        this._updateSubmenuStates();
    }

    /**
     * 显示子菜单 - 显示指定子菜单
     * @param {string} key - 菜单项键
     * @private
     */
    _showSubmenu(key) {
        this.navState.openKeys.add(key);
        this._updateSubmenuStates();
    }

    /**
     * 隐藏子菜单 - 隐藏指定子菜单
     * @param {string} key - 菜单项键
     * @private
     */
    _hideSubmenu(key) {
        this.navState.openKeys.delete(key);
        this._updateSubmenuStates();
    }

    /**
     * 更新项目状态 - 更新所有导航项的视觉状态
     * @private
     */
    _updateItemStates() {
        const items = this.elements.container.querySelectorAll('.nav-item');
        items.forEach(li => {
            const key = li.dataset.key;
            
            // 选中状态
            li.classList.toggle('selected', this.navState.selectedKeys.has(key));
            li.setAttribute('aria-selected', this.navState.selectedKeys.has(key));
        });
    }

    /**
     * 更新子菜单状态 - 更新所有子菜单的显示状态
     * @private
     */
    _updateSubmenuStates() {
        const items = this.elements.container.querySelectorAll('.nav-item');
        items.forEach(li => {
            const key = li.dataset.key;
            const submenu = li.querySelector('.nav-submenu');
            const arrow = li.querySelector('.nav-arrow');
            
            if (submenu) {
                const isOpen = this.navState.openKeys.has(key);
                
                li.classList.toggle('open', isOpen);
                li.setAttribute('aria-expanded', isOpen);
                submenu.style.display = isOpen ? 'block' : 'none';
                
                if (arrow) {
                    arrow.innerHTML = isOpen ? 
                        (this.navProps.mode === 'horizontal' ? '▲' : '▼') :
                        (this.navProps.mode === 'horizontal' ? '▼' : '▶');
                }
            }
        });
    }

    /**
     * 更新折叠状态 - 更新折叠模式下的显示
     * @private
     */
    _updateCollapseState() {
        const titles = this.elements.container.querySelectorAll('.nav-title');
        titles.forEach(title => {
            if (title.closest('.nav-item').dataset.level === '0') {
                title.style.display = this.navState.collapsed ? 'none' : 'inline';
            }
        });
    }

    /**
     * 根据键查找项目 - 在导航项中查找指定键的项目
     * @param {string} key - 项目键
     * @returns {Object|null} 找到的项目或null
     * @private
     */
    _findItemByKey(key) {
        const findInItems = (items) => {
            for (const item of items) {
                if (item.key === key) {
                    return item;
                }
                if (item.children) {
                    const found = findInItems(item.children);
                    if (found) return found;
                }
            }
            return null;
        };
        
        return findInItems(this.navProps.items);
    }

    /**
     * 根据键移除项目 - 从导航项中移除指定键的项目
     * @param {string} key - 项目键
     * @private
     */
    _removeItemByKey(key) {
        const removeFromItems = (items) => {
            for (let i = 0; i < items.length; i++) {
                if (items[i].key === key) {
                    items.splice(i, 1);
                    return true;
                }
                if (items[i].children) {
                    if (removeFromItems(items[i].children)) {
                        return true;
                    }
                }
            }
            return false;
        };
        
        removeFromItems(this.navProps.items);
    }

    /**
     * 垂直导航 - 键盘垂直导航
     * @param {number} direction - 方向（1向下，-1向上）
     * @private
     */
    _navigateVertical(direction) {
        // 实现键盘垂直导航逻辑
        const focusableItems = this._getFocusableItems();
        const currentIndex = focusableItems.findIndex(item => 
            item === document.activeElement
        );
        
        let nextIndex = currentIndex + direction;
        if (nextIndex < 0) nextIndex = focusableItems.length - 1;
        if (nextIndex >= focusableItems.length) nextIndex = 0;
        
        if (focusableItems[nextIndex]) {
            focusableItems[nextIndex].focus();
        }
    }

    /**
     * 水平导航 - 键盘水平导航
     * @param {number} direction - 方向（1向右，-1向左）
     * @private
     */
    _navigateHorizontal(direction) {
        // 实现键盘水平导航逻辑
        if (this.navProps.mode === 'horizontal') {
            this._navigateVertical(direction);
        } else {
            // 垂直模式下，水平键用于展开/折叠子菜单
            const focused = document.activeElement;
            const li = focused.closest('.nav-item');
            if (li && li.classList.contains('has-submenu')) {
                const key = li.dataset.key;
                if (direction === 1) {
                    this._showSubmenu(key);
                } else {
                    this._hideSubmenu(key);
                }
            }
        }
    }

    /**
     * 激活当前项 - 激活当前焦点项
     * @private
     */
    _activateCurrentItem() {
        const focused = document.activeElement;
        if (focused && focused.classList.contains('nav-link')) {
            focused.click();
        }
    }

    /**
     * 关闭所有子菜单 - 关闭所有打开的子菜单
     * @private
     */
    _closeAllSubmenus() {
        this.navState.openKeys.clear();
        this._updateSubmenuStates();
    }

    /**
     * 获取可聚焦项 - 获取所有可聚焦的导航项
     * @returns {Array} 可聚焦元素数组
     * @private
     */
    _getFocusableItems() {
        return Array.from(this.elements.container.querySelectorAll('.nav-link:not([aria-disabled="true"])'));
    }
    // #endregion

    // #region 生命周期方法
    /**
     * 组件销毁 - 清理资源
     */
    destroy() {
        // 调用父类销毁方法
        super.destroy();
        
        this.emit(UI_EVENTS.COMPONENT_DESTROYED, { component: this });
    }
    // #endregion
}

/**
 * @class Breadcrumb - 面包屑导航组件
 * @extends BaseComponent
 * @description 显示当前页面路径的面包屑导航
 */
export class Breadcrumb extends BaseComponent {
    /**
     * 构造函数 - 初始化面包屑组件
     * @param {Object} config - 面包屑配置
     */
    constructor(config = {}) {
        super(config);
        
        this.breadcrumbProps = {
            items: config.items || [],
            separator: config.separator || '/',
            maxItems: config.maxItems || null,
            className: config.className || ''
        };
        
        this._initializeBreadcrumb();
    }

    /**
     * 初始化面包屑 - 创建DOM结构
     * @private
     */
    _initializeBreadcrumb() {
        this._createBreadcrumbStructure();
        this._renderBreadcrumbItems();
        
        this.emit(UI_EVENTS.COMPONENT_INITIALIZED, {
            component: this,
            type: 'breadcrumb'
        });
    }

    /**
     * 创建面包屑结构 - 构建面包屑HTML
     * @private
     */
    _createBreadcrumbStructure() {
        this.container = document.createElement('nav');
        this.container.className = `smartoffice-breadcrumb ${this.breadcrumbProps.className}`;
        this.container.setAttribute('aria-label', '面包屑导航');
        
        this.listElement = document.createElement('ol');
        this.listElement.className = 'breadcrumb-list';
        
        this.container.appendChild(this.listElement);
    }

    /**
     * 渲染面包屑项 - 生成面包屑项目
     * @private
     */
    _renderBreadcrumbItems() {
        this.listElement.innerHTML = '';
        
        let items = this.breadcrumbProps.items;
        
        // 处理最大项目数限制
        if (this.breadcrumbProps.maxItems && items.length > this.breadcrumbProps.maxItems) {
            items = [
                items[0],
                { title: '...', key: 'ellipsis', disabled: true },
                ...items.slice(-(this.breadcrumbProps.maxItems - 2))
            ];
        }
        
        items.forEach((item, index) => {
            const li = this._createBreadcrumbItem(item, index, index === items.length - 1);
            this.listElement.appendChild(li);
        });
    }

    /**
     * 创建面包屑项 - 生成单个面包屑项
     * @param {Object} item - 面包屑项数据
     * @param {number} index - 项目索引
     * @param {boolean} isLast - 是否为最后一项
     * @returns {HTMLElement} 面包屑项元素
     * @private
     */
    _createBreadcrumbItem(item, index, isLast) {
        const li = document.createElement('li');
        li.className = 'breadcrumb-item';
        
        if (isLast) {
            li.classList.add('current');
            li.setAttribute('aria-current', 'page');
        }
        
        // 创建链接或文本
        if (!isLast && item.href && !item.disabled) {
            const link = document.createElement('a');
            link.href = item.href;
            link.textContent = item.title;
            link.className = 'breadcrumb-link';
            
            if (item.onClick) {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    item.onClick(item, e);
                });
            }
            
            li.appendChild(link);
        } else {
            const span = document.createElement('span');
            span.textContent = item.title;
            span.className = 'breadcrumb-text';
            li.appendChild(span);
        }
        
        // 添加分隔符（除了最后一项）
        if (!isLast) {
            const separator = document.createElement('span');
            separator.className = 'breadcrumb-separator';
            separator.textContent = this.breadcrumbProps.separator;
            li.appendChild(separator);
        }
        
        return li;
    }

    /**
     * 设置面包屑项 - 更新面包屑项目
     * @param {Array} items - 新的面包屑项数组
     */
    setItems(items) {
        this.breadcrumbProps.items = items || [];
        this._renderBreadcrumbItems();
        
        this.emit(UI_EVENTS.COMPONENT_UPDATED, {
            component: this,
            type: 'items',
            items: this.breadcrumbProps.items
        });
    }
}
// #endregion

// #region 工厂函数
/**
 * 创建导航 - 便捷函数创建导航实例
 * @param {Object} config - 导航配置
 * @returns {Navigation} 导航实例
 */
export function createNavigation(config = {}) {
    return new Navigation(config);
}

/**
 * 创建水平导航 - 便捷函数创建水平导航
 * @param {Object} config - 导航配置
 * @returns {Navigation} 导航实例
 */
export function createHorizontalNavigation(config = {}) {
    return new Navigation({
        ...config,
        mode: 'horizontal'
    });
}

/**
 * 创建垂直导航 - 便捷函数创建垂直导航
 * @param {Object} config - 导航配置
 * @returns {Navigation} 导航实例
 */
export function createVerticalNavigation(config = {}) {
    return new Navigation({
        ...config,
        mode: 'vertical'
    });
}

/**
 * 创建面包屑 - 便捷函数创建面包屑实例
 * @param {Object} config - 面包屑配置
 * @returns {Breadcrumb} 面包屑实例
 */
export function createBreadcrumb(config = {}) {
    return new Breadcrumb(config);
}
// #endregion 