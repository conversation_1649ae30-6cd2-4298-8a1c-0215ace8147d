/**
 * @file 状态类型定义
 * @description 定义应用程序中所有状态的类型和结构
 */

/**
 * 文档类型枚举
 * @enum {string}
 */
export const DocumentTypes = {
    RECEIPT: 'receipt',
    INVOICE: 'invoice', 
    QUOTATION: 'quotation',
    DRIVER_AGREEMENT: 'driver_agreement'
};

/**
 * 语言类型枚举
 * @enum {string}
 */
export const Languages = {
    CHINESE: 'zh',
    ENGLISH: 'en'
};

/**
 * 模板类型枚举
 * @enum {string}
 */
export const Templates = {
    DEFAULT: 'default',
    SIMPLE: 'simple',
    ELEGANT: 'elegant'
};

/**
 * 公司类型枚举
 * @enum {string}
 */
export const Companies = {
    SKY_MIRROR: 'sky-mirror',
    GOMYHIRE: 'gomyhire'
};

/**
 * 货币类型枚举
 * @enum {string}
 */
export const Currencies = {
    RM: 'RM',
    USD: 'USD',
    CNY: 'CNY'
};

/**
 * 付款方式枚举
 * @enum {string}
 */
export const PaymentMethods = {
    CASH: 'cash',
    BANK_TRANSFER: 'bank_transfer',
    WECHAT: 'wechat',
    ALIPAY: 'alipay'
};

/**
 * 基础文档状态接口
 * @typedef {Object} BaseDocumentState
 * @property {string} documentType - 文档类型
 * @property {string} language - 语言
 * @property {string} template - 模板
 * @property {string} company - 公司
 * @property {string} currency - 货币
 * @property {string} paymentMethod - 付款方式
 * @property {boolean} showSignature - 是否显示签名区域
 * @property {boolean} bilingualMode - 双语模式
 * @property {string} receiptNumber - 收据号
 * @property {string} receiptDate - 收据日期
 * @property {string} customer - 客户名称
 * @property {string} channel - 渠道
 * @property {Array} items - 服务项目
 * @property {number} totalAmount - 总金额
 * @property {string} notes - 备注
 * @property {string} conclusion - 文档结语
 * @property {string} conclusionCn - 中文结语
 * @property {string} conclusionEn - 英文结语
 */

/**
 * 买方信息接口
 * @typedef {Object} BuyerInfo
 * @property {string} company - 公司名称
 * @property {string} taxid - 税号
 * @property {string} bank - 银行
 * @property {string} account - 账号
 * @property {string} tel - 电话
 */

/**
 * 发票状态接口
 * @typedef {Object} InvoiceState
 * @property {number} taxRate - 税率
 * @property {number} amountBeforeTax - 税前金额
 * @property {number} taxAmount - 税额
 * @property {BuyerInfo} buyer - 买方信息
 */

/**
 * 报价单状态接口
 * @typedef {Object} QuotationState
 * @property {string} validUntil - 有效期
 * @property {string} preparedBy - 报价员
 * @property {string} deliveryTime - 交付时间
 */

/**
 * 司机信息接口
 * @typedef {Object} DriverInfo
 * @property {string} name - 姓名
 * @property {string} id - 身份证号
 * @property {string} phone - 电话
 * @property {string} address - 地址
 * @property {string} licenseNumber - 驾照号
 * @property {string} licenseType - 驾照类型
 * @property {string} vehicleModel - 车型
 * @property {string} plateNumber - 车牌号
 * @property {string} startDate - 开始日期
 * @property {string} endDate - 结束日期
 * @property {string} workArea - 工作区域
 * @property {number} baseSalary - 基本工资
 * @property {number} commission - 提成比例
 * @property {string} paymentMethod - 工资支付方式
 */

/**
 * 司机协议状态接口
 * @typedef {Object} DriverAgreementState
 * @property {DriverInfo} driverInfo - 司机信息
 * @property {string} agreementNumber - 协议编号
 * @property {string} signDate - 签署日期
 * @property {string} effectiveDate - 生效日期
 * @property {string} expiryDate - 到期日期
 */

/**
 * 应用程序全局状态接口
 * @typedef {Object} AppState
 * @property {BaseDocumentState} document - 基础文档状态
 * @property {InvoiceState} invoice - 发票状态
 * @property {QuotationState} quotation - 报价单状态
 * @property {DriverAgreementState} driverAgreement - 司机协议状态
 * @property {Object} ui - UI状态
 * @property {Object} export - 导出状态
 */
