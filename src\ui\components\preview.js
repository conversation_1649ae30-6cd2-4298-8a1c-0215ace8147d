/**
 * @file 预览组件 - 文档预览相关UI组件
 * <AUTHOR> Team
 * @description 
 * 这个文件实现了文档预览组件，提供：
 * - DocumentPreview 文档预览组件
 * - PreviewToolbar 预览工具栏组件
 * - 文档刷新、缩放、主题切换等功能
 * - 预览错误处理和加载状态管理
 * - 基于统一渲染架构的文档预览功能
 */

// #region 导入依赖模块
import { BaseComponent } from './base-component.js';
import { Button, ButtonGroup } from './button.js';
import { Select } from './input.js';
import { UI_EVENTS } from '../../core/events/event-types.js';
import { validateNonEmpty, validateElement } from '../../core/utils/validation.js';
import { UnifiedRenderEngine } from '../../core/rendering/unified-render-engine.js';
import { DocumentModel } from '../../core/rendering/document-model.js';
import { StyleManager } from '../../core/rendering/style-manager.js';
import { PositionManager } from '../../core/rendering/position-manager.js';
import { HTMLRenderer } from '../../renderers/html-renderer.js';
// #endregion

// #region DocumentPreview 文档预览组件
/**
 * @class DocumentPreview - 文档预览组件
 * @description 显示文档预览的主要组件，支持多种显示模式和缩放功能
 */
export class DocumentPreview extends BaseComponent {
    /**
     * 构造函数 - 初始化文档预览组件
     * @param {Object} config - 预览配置
     * @param {Object} config.document - 文档对象
     * @param {Object} config.template - 模板对象
     * @param {Object} config.renderer - 渲染器对象
     * @param {string} config.theme - 预览主题
     * @param {number} config.zoom - 缩放比例 (0.1 - 5.0)
     * @param {boolean} config.showToolbar - 是否显示工具栏
     * @param {Function} config.onPreviewUpdate - 预览更新回调
     * @param {Function} config.onPreviewError - 预览错误回调
     */
    constructor(config = {}) {
        super({
            type: 'document-preview',
            name: config.name || 'document-preview',
            ...config
        });
        
        // 预览特有属性
        this.previewProps = {
            document: config.document || null,
            template: config.template || null,
            renderer: config.renderer || null,
            theme: config.theme || 'default',
            zoom: config.zoom || 1.0,
            showToolbar: config.showToolbar !== false,
            autoRefresh: config.autoRefresh !== false,
            showErrors: config.showErrors !== false,
            // 统一渲染架构配置
            renderEngine: config.renderEngine || null,
            styleManager: config.styleManager || null,
            positionManager: config.positionManager || null,
            ...config.previewProps
        };
        
        // 预览内容和缓存
        this.previewContent = '';
        this.lastRenderHash = '';
        this.renderCache = new Map();
        
        // 统一渲染架构组件
        this.renderEngine = null;
        this.styleManager = null;
        this.positionManager = null;
        this.htmlRenderer = null;
        this.documentModel = null;
        
        // 事件处理
        this.previewUpdateHandler = config.onPreviewUpdate || null;
        this.previewErrorHandler = config.onPreviewError || null;
        this.documentChangeHandler = config.onDocumentChange || null;
        
        // 预览状态
        this.previewState = {
            rendering: false,
            hasError: false,
            errorMessage: '',
            lastRendered: null,
            renderCount: 0,
            ...config.previewState
        };
        
        // 合并到组件状态
        this.state = { ...this.state, ...this.previewState };
        
        // 工具栏组件
        this.toolbar = null;
        
        // 验证配置
        this._validatePreviewConfig();
        
        // 初始化统一渲染架构
        this._initializeRenderingArchitecture();
    }

    /**
     * 验证预览配置 - 验证预览特有的配置
     * @private
     */
    _validatePreviewConfig() {
        // 验证缩放比例
        if (this.previewProps.zoom < 0.1 || this.previewProps.zoom > 5.0) {
            console.warn(`无效的缩放比例: ${this.previewProps.zoom}，将使用默认值 1.0`);
            this.previewProps.zoom = 1.0;
        }
    }
    
    /**
     * 初始化统一渲染架构
     * @private
     */
    async _initializeRenderingArchitecture() {
        try {
            // 初始化样式管理器
            this.styleManager = this.previewProps.styleManager || new StyleManager({
                theme: this.previewProps.theme,
                format: 'html'
            });
            
            // 初始化位置管理器
            this.positionManager = this.previewProps.positionManager || new PositionManager({
                coordinateSystem: 'web',
                unit: 'px'
            });
            
            // 初始化HTML渲染器
            this.htmlRenderer = new HTMLRenderer({
                styleManager: this.styleManager,
                positionManager: this.positionManager
            });
            
            // 初始化统一渲染引擎
            this.renderEngine = this.previewProps.renderEngine || new UnifiedRenderEngine({
                renderers: {
                    html: this.htmlRenderer
                },
                styleManager: this.styleManager,
                positionManager: this.positionManager,
                defaultFormat: 'html'
            });
            
            console.log('[DocumentPreview] 统一渲染架构初始化完成');
            
        } catch (error) {
            console.error('[DocumentPreview] 统一渲染架构初始化失败:', error);
            throw error;
        }
    }

    /**
     * 渲染预览组件 - 创建预览DOM结构
     * @returns {Promise<HTMLElement>} 预览容器元素
     */
    async render() {
        // 创建预览容器
        const container = document.createElement('div');
        container.id = this.id;
        
        // 创建工具栏
        if (this.previewProps.showToolbar) {
            const toolbarContainer = this._createToolbarContainer();
            container.appendChild(toolbarContainer);
            
            // 创建工具栏组件
            this.toolbar = new PreviewToolbar({
                preview: this,
                theme: this.previewProps.theme,
                zoom: this.previewProps.zoom,
                onZoomChange: (zoom) => this.setZoom(zoom),
                onThemeChange: (theme) => this.setTheme(theme),
                onRefresh: () => this.refresh(),
                onFitToWidth: () => this.fitToWidth(),
                onFitToPage: () => this.fitToPage()
            });
            
            await this.toolbar.mount(toolbarContainer);
            this.addChild('toolbar', this.toolbar);
        }
        
        // 创建预览内容区域
        const previewArea = this._createPreviewArea();
        container.appendChild(previewArea);
        
        // 创建加载指示器
        const loadingIndicator = this._createLoadingIndicator();
        container.appendChild(loadingIndicator);
        
        // 创建错误提示区域
        const errorArea = this._createErrorArea();
        container.appendChild(errorArea);
        
        // 初始渲染
        if (this.previewProps.document && this.previewProps.template && this.previewProps.renderer) {
            await this._renderPreview();
        }
        
        return container;
    }

    /**
     * 创建工具栏容器 - 创建工具栏的容器元素
     * @returns {HTMLElement} 工具栏容器
     * @private
     */
    _createToolbarContainer() {
        const toolbar = document.createElement('div');
        toolbar.className = 'preview-toolbar-container';
        return toolbar;
    }

    /**
     * 创建预览区域 - 创建显示预览内容的区域
     * @returns {HTMLElement} 预览区域元素
     * @private
     */
    _createPreviewArea() {
        const previewArea = document.createElement('div');
        previewArea.className = 'preview-area';
        
        // 创建预览内容容器
        const previewContent = document.createElement('div');
        previewContent.className = 'preview-content';
        previewArea.appendChild(previewContent);
        
        return previewArea;
    }

    /**
     * 创建加载指示器 - 创建加载状态的指示器
     * @returns {HTMLElement} 加载指示器元素
     * @private
     */
    _createLoadingIndicator() {
        const loading = document.createElement('div');
        loading.className = 'preview-loading';
        loading.style.display = 'none';
        
        const spinner = document.createElement('div');
        spinner.className = 'loading-spinner';
        loading.appendChild(spinner);
        
        const text = document.createElement('div');
        text.className = 'loading-text';
        text.textContent = '正在渲染文档...';
        loading.appendChild(text);
        
        return loading;
    }

    /**
     * 创建错误区域 - 创建显示错误信息的区域
     * @returns {HTMLElement} 错误区域元素
     * @private
     */
    _createErrorArea() {
        const errorArea = document.createElement('div');
        errorArea.className = 'preview-error';
        errorArea.style.display = 'none';
        
        const errorIcon = document.createElement('div');
        errorIcon.className = 'error-icon';
        errorIcon.innerHTML = '⚠️';
        errorArea.appendChild(errorIcon);
        
        const errorMessage = document.createElement('div');
        errorMessage.className = 'error-message';
        errorArea.appendChild(errorMessage);
        
        const retryButton = document.createElement('button');
        retryButton.className = 'retry-button';
        retryButton.textContent = '重试';
        retryButton.onclick = () => this.refresh();
        errorArea.appendChild(retryButton);
        
        return errorArea;
    }

    /**
     * 应用样式 - 应用预览组件特有的样式类
     * @private
     */
    _applyStyles() {
        if (!this.element) {
            return;
        }
        
        // 构建样式类列表
        const styleClasses = [
            'smartoffice-preview',
            'document-preview',
            `preview-theme-${this.previewProps.theme}`,
            this.state.rendering ? 'preview-rendering' : '',
            this.state.hasError ? 'preview-error' : '',
            this.state.enabled ? '' : 'preview-disabled',
            this.state.visible ? '' : 'preview-hidden',
            this.props.className
        ].filter(Boolean);
        
        // 应用样式类
        this.element.className = styleClasses.join(' ');
        
        // 设置缩放样式
        const previewContent = this.element.querySelector('.preview-content');
        if (previewContent) {
            previewContent.style.transform = `scale(${this.previewProps.zoom})`;
            previewContent.style.transformOrigin = 'top left';
        }
        
        // 设置自定义样式
        if (this.props.style) {
            Object.assign(this.element.style, this.props.style);
        }
    }

    /**
     * 绑定组件特定事件 - 绑定预览组件特有的事件
     * @protected
     */
    _bindComponentEvents() {
        if (!this.element) {
            return;
        }
        
        // 绑定预览区域滚动事件
        const previewArea = this.element.querySelector('.preview-area');
        if (previewArea) {
            this._addEventListener(previewArea, 'scroll', (event) => {
                this._handlePreviewScroll(event);
            });
        }
        
        // 绑定预览内容点击事件
        const previewContent = this.element.querySelector('.preview-content');
        if (previewContent) {
            this._addEventListener(previewContent, 'click', (event) => {
                this._handlePreviewClick(event);
            });
        }
        
        // 绑定键盘事件
        this._addEventListener(this.element, 'keydown', (event) => {
            this._handleKeyDown(event);
        });
    }

    /**
     * 处理预览滚动 - 处理预览区域滚动事件
     * @param {Event} event - 滚动事件
     * @private
     */
    _handlePreviewScroll(event) {
        this.emit(UI_EVENTS.PREVIEW_SCROLLED, {
            component: this,
            event,
            scrollTop: event.target.scrollTop,
            scrollLeft: event.target.scrollLeft
        });
    }

    /**
     * 处理预览点击 - 处理预览内容点击事件
     * @param {Event} event - 点击事件
     * @private
     */
    _handlePreviewClick(event) {
        this.emit(UI_EVENTS.COMPONENT_CLICKED, {
            component: this,
            event,
            target: event.target,
            previewElement: true
        });
    }

    /**
     * 处理键盘事件 - 处理预览组件键盘交互
     * @param {KeyboardEvent} event - 键盘事件
     * @private
     */
    _handleKeyDown(event) {
        // Ctrl + R 刷新预览
        if (event.ctrlKey && event.key === 'r') {
            event.preventDefault();
            this.refresh();
        }
        
        // Ctrl + 0 重置缩放
        if (event.ctrlKey && event.key === '0') {
            event.preventDefault();
            this.setZoom(1.0);
        }
        
        // Ctrl + + 放大
        if (event.ctrlKey && event.key === '+') {
            event.preventDefault();
            this.setZoom(Math.min(this.previewProps.zoom * 1.2, 5.0));
        }
        
        // Ctrl + - 缩小
        if (event.ctrlKey && event.key === '-') {
            event.preventDefault();
            this.setZoom(Math.max(this.previewProps.zoom / 1.2, 0.1));
        }
    }

    /**
     * 渲染预览 - 执行文档预览渲染
     * @returns {Promise<void>}
     * @private
     */
    async _renderPreview() {
        if (this.state.rendering) {
            return;
        }
        
        try {
            // 更新渲染状态
            this.update({}, { rendering: true, hasError: false, errorMessage: '' });
            this._showLoading(true);
            
            // 生成渲染哈希
            const renderHash = this._generateRenderHash();
            
            // 检查缓存
            if (this.renderCache.has(renderHash) && renderHash === this.lastRenderHash) {
                const cachedContent = this.renderCache.get(renderHash);
                this._updatePreviewContent(cachedContent);
                this._showLoading(false);
                this.update({}, { rendering: false });
                return;
            }
            
            let renderResult;
            
            // 使用统一渲染架构或传统渲染器
            if (this.renderEngine && this.previewProps.document && this.previewProps.template) {
                // 创建文档模型
                this.documentModel = await this._createDocumentModel(
                    this.previewProps.document,
                    this.previewProps.template,
                    {
                        theme: this.previewProps.theme,
                        format: 'html',
                        preview: true
                    }
                );
                
                // 使用统一渲染引擎渲染
                renderResult = await this.renderEngine.render(this.documentModel, {
                    format: 'html',
                    theme: this.previewProps.theme
                });
                
                // 提取HTML内容
                renderResult = renderResult.content || renderResult;
                
            } else if (this.previewProps.renderer) {
                // 使用传统渲染器
                renderResult = await this.previewProps.renderer.render(
                    this.previewProps.template,
                    this.previewProps.document.getData(),
                    {
                        theme: this.previewProps.theme,
                        preview: true
                    }
                );
            } else {
                throw new Error('未配置渲染器或统一渲染引擎');
            }
            
            // 缓存渲染结果
            this.renderCache.set(renderHash, renderResult);
            this.lastRenderHash = renderHash;
            
            // 更新预览内容
            this._updatePreviewContent(renderResult);
            
            // 更新统计
            this.update({}, {
                rendering: false,
                renderCount: this.state.renderCount + 1,
                lastRendered: new Date()
            });
            
            // 触发预览更新事件
            this.emit(UI_EVENTS.PREVIEW_UPDATED, {
                component: this,
                renderResult,
                renderHash,
                documentModel: this.documentModel
            });
            
            // 执行预览更新回调
            if (this.previewUpdateHandler) {
                this.previewUpdateHandler(renderResult, this);
            }
            
        } catch (error) {
            // 处理渲染错误
            this._handleRenderError(error);
        } finally {
            this._showLoading(false);
        }
    }
    
    /**
     * 创建文档模型
     * @param {Object} document - 文档数据
     * @param {Object} template - 模板对象
     * @param {Object} options - 选项
     * @returns {Promise<DocumentModel>} 文档模型
     * @private
     */
    async _createDocumentModel(document, template, options) {
        try {
            // 创建文档模型实例
            const documentModel = new DocumentModel({
                id: `preview-${Date.now()}`,
                title: document.title || '预览文档',
                format: options.format || 'html',
                theme: options.theme || 'default'
            });
            
            // 设置模板
            await documentModel.setTemplate(template);
            
            // 设置数据
            await documentModel.setData(document);
            
            // 设置选项
            await documentModel.setOptions(options);
            
            // 解析模板
            await documentModel.parseTemplate();
            
            // 绑定数据
            await documentModel.bindData();
            
            console.log('[DocumentPreview] 文档模型创建完成');
            return documentModel;
            
        } catch (error) {
            console.error('[DocumentPreview] 文档模型创建失败:', error);
            throw error;
        }
    }

    /**
     * 生成渲染哈希 - 生成用于缓存的渲染哈希值
     * @returns {string} 渲染哈希
     * @private
     */
    _generateRenderHash() {
        const hashData = {
            document: this.previewProps.document ? JSON.stringify(this.previewProps.document.getData()) : '',
            template: this.previewProps.template ? this.previewProps.template.name : '',
            renderer: this.previewProps.renderer ? this.previewProps.renderer.type : '',
            theme: this.previewProps.theme,
            zoom: this.previewProps.zoom
        };
        
        return btoa(JSON.stringify(hashData)).replace(/[^a-zA-Z0-9]/g, '');
    }

    /**
     * 更新预览内容 - 更新预览显示的内容
     * @param {string} content - 渲染结果内容
     * @private
     */
    _updatePreviewContent(content) {
        const previewContent = this.element?.querySelector('.preview-content');
        if (previewContent) {
            previewContent.innerHTML = content;
            this.previewContent = content;
        }
    }

    /**
     * 显示加载状态 - 控制加载指示器的显示
     * @param {boolean} show - 是否显示加载
     * @private
     */
    _showLoading(show) {
        const loadingElement = this.element?.querySelector('.preview-loading');
        const previewArea = this.element?.querySelector('.preview-area');
        
        if (loadingElement && previewArea) {
            if (show) {
                loadingElement.style.display = 'flex';
                previewArea.style.opacity = '0.5';
            } else {
                loadingElement.style.display = 'none';
                previewArea.style.opacity = '1';
            }
        }
    }

    /**
     * 处理渲染错误 - 处理预览渲染过程中的错误
     * @param {Error} error - 错误对象
     * @private
     */
    _handleRenderError(error) {
        const errorMessage = error.message || '渲染失败';
        
        // 更新错误状态
        this.update({}, {
            rendering: false,
            hasError: true,
            errorMessage
        });
        
        // 显示错误信息
        if (this.previewProps.showErrors) {
            this._showError(errorMessage);
        }
        
        // 触发错误事件
        this.emit(UI_EVENTS.PREVIEW_ERROR, {
            component: this,
            error: errorMessage,
            originalError: error
        });
        
        // 执行错误回调
        if (this.previewErrorHandler) {
            this.previewErrorHandler(error, this);
        }
        
        console.error('预览渲染错误:', error);
    }

    /**
     * 显示错误信息 - 在界面上显示错误信息
     * @param {string} message - 错误消息
     * @private
     */
    _showError(message) {
        const errorArea = this.element?.querySelector('.preview-error');
        const errorMessage = this.element?.querySelector('.error-message');
        const previewArea = this.element?.querySelector('.preview-area');
        
        if (errorArea && errorMessage && previewArea) {
            errorMessage.textContent = message;
            errorArea.style.display = 'flex';
            previewArea.style.display = 'none';
        }
    }

    /**
     * 隐藏错误信息 - 隐藏错误显示
     * @private
     */
    _hideError() {
        const errorArea = this.element?.querySelector('.preview-error');
        const previewArea = this.element?.querySelector('.preview-area');
        
        if (errorArea && previewArea) {
            errorArea.style.display = 'none';
            previewArea.style.display = 'block';
        }
    }

    /**
     * 设置文档 - 设置要预览的文档
     * @param {Object} document - 文档对象
     */
    setDocument(document) {
        this.previewProps.document = document;
        
        if (this.previewProps.autoRefresh) {
            this.refresh();
        }
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            property: 'document',
            value: document
        });
    }

    /**
     * 设置模板 - 设置预览使用的模板
     * @param {Object} template - 模板对象
     */
    setTemplate(template) {
        this.previewProps.template = template;
        
        if (this.previewProps.autoRefresh) {
            this.refresh();
        }
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            property: 'template',
            value: template
        });
    }

    /**
     * 设置渲染器 - 设置预览使用的渲染器
     * @param {Object} renderer - 渲染器对象
     */
    setRenderer(renderer) {
        this.previewProps.renderer = renderer;
        
        if (this.previewProps.autoRefresh) {
            this.refresh();
        }
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            property: 'renderer',
            value: renderer
        });
    }

    /**
     * 设置主题 - 设置预览主题
     * @param {string} theme - 主题名称
     */
    setTheme(theme) {
        this.previewProps.theme = theme;
        this._applyStyles();
        
        if (this.previewProps.autoRefresh) {
            this.refresh();
        }
        
        // 更新工具栏
        if (this.toolbar && this.toolbar.setTheme) {
            this.toolbar.setTheme(theme);
        }
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            property: 'theme',
            value: theme
        });
    }

    /**
     * 设置缩放 - 设置预览缩放比例
     * @param {number} zoom - 缩放比例
     */
    setZoom(zoom) {
        if (zoom < 0.1 || zoom > 5.0) {
            console.warn(`缩放比例超出范围: ${zoom}`);
            return;
        }
        
        this.previewProps.zoom = zoom;
        this._applyStyles();
        
        // 更新工具栏
        if (this.toolbar && this.toolbar.setZoom) {
            this.toolbar.setZoom(zoom);
        }
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            property: 'zoom',
            value: zoom
        });
    }

    /**
     * 刷新预览 - 强制重新渲染预览
     */
    async refresh() {
        this._hideError();
        this.renderCache.clear();
        this.lastRenderHash = '';
        
        if (this.previewProps.document && this.previewProps.template && this.previewProps.renderer) {
            await this._renderPreview();
        }
        
        this.emit(UI_EVENTS.PREVIEW_REFRESH, {
            component: this
        });
    }

    /**
     * 适应宽度 - 调整缩放以适应容器宽度
     */
    fitToWidth() {
        const previewArea = this.element?.querySelector('.preview-area');
        const previewContent = this.element?.querySelector('.preview-content');
        
        if (previewArea && previewContent) {
            const containerWidth = previewArea.clientWidth;
            const contentWidth = previewContent.scrollWidth;
            
            if (contentWidth > 0) {
                const zoom = containerWidth / contentWidth * 0.95; // 留5%边距
                this.setZoom(Math.max(Math.min(zoom, 5.0), 0.1));
            }
        }
    }

    /**
     * 适应页面 - 调整缩放以适应整个容器
     */
    fitToPage() {
        const previewArea = this.element?.querySelector('.preview-area');
        const previewContent = this.element?.querySelector('.preview-content');
        
        if (previewArea && previewContent) {
            const containerWidth = previewArea.clientWidth;
            const containerHeight = previewArea.clientHeight;
            const contentWidth = previewContent.scrollWidth;
            const contentHeight = previewContent.scrollHeight;
            
            if (contentWidth > 0 && contentHeight > 0) {
                const zoomWidth = containerWidth / contentWidth * 0.95;
                const zoomHeight = containerHeight / contentHeight * 0.95;
                const zoom = Math.min(zoomWidth, zoomHeight);
                this.setZoom(Math.max(Math.min(zoom, 5.0), 0.1));
            }
        }
    }

    /**
     * 获取预览内容 - 获取当前预览的HTML内容
     * @returns {string} 预览HTML内容
     */
    getPreviewContent() {
        return this.previewContent;
    }

    /**
     * 清空缓存 - 清空渲染缓存
     */
    clearCache() {
        this.renderCache.clear();
        this.lastRenderHash = '';
    }

    /**
     * 获取预览信息 - 获取预览组件的详细信息
     * @returns {Object} 预览信息
     */
    getPreviewInfo() {
        return {
            ...this._getComponentInfo(),
            previewProps: { ...this.previewProps },
            previewState: {
                rendering: this.state.rendering,
                hasError: this.state.hasError,
                errorMessage: this.state.errorMessage,
                lastRendered: this.state.lastRendered,
                renderCount: this.state.renderCount
            },
            cacheInfo: {
                cacheSize: this.renderCache.size,
                lastRenderHash: this.lastRenderHash
            }
        };
    }
}
// #endregion

// #region PreviewToolbar 预览工具栏组件
/**
 * @class PreviewToolbar - 预览工具栏组件
 * @description 预览组件的工具栏，提供缩放、主题切换、刷新等功能
 */
export class PreviewToolbar extends BaseComponent {
    /**
     * 构造函数 - 初始化预览工具栏
     * @param {Object} config - 工具栏配置
     * @param {DocumentPreview} config.preview - 关联的预览组件
     * @param {string} config.theme - 当前主题
     * @param {number} config.zoom - 当前缩放比例
     * @param {Function} config.onZoomChange - 缩放改变回调
     * @param {Function} config.onThemeChange - 主题改变回调
     * @param {Function} config.onRefresh - 刷新回调
     * @param {Function} config.onFitToWidth - 适应宽度回调
     * @param {Function} config.onFitToPage - 适应页面回调
     */
    constructor(config = {}) {
        super({
            type: 'preview-toolbar',
            name: config.name || 'preview-toolbar',
            ...config
        });
        
        // 工具栏属性
        this.toolbarProps = {
            preview: config.preview || null,
            theme: config.theme || 'default',
            zoom: config.zoom || 1.0,
            showZoomControls: config.showZoomControls !== false,
            showThemeSelector: config.showThemeSelector !== false,
            showFitControls: config.showFitControls !== false,
            showRefreshButton: config.showRefreshButton !== false,
            ...config.toolbarProps
        };
        
        // 事件处理
        this.zoomChangeHandler = config.onZoomChange || null;
        this.themeChangeHandler = config.onThemeChange || null;
        this.refreshHandler = config.onRefresh || null;
        this.fitToWidthHandler = config.onFitToWidth || null;
        this.fitToPageHandler = config.onFitToPage || null;
        
        // 工具栏组件
        this.zoomInButton = null;
        this.zoomOutButton = null;
        this.zoomResetButton = null;
        this.zoomDisplay = null;
        this.themeSelector = null;
        this.refreshButton = null;
        this.fitButtons = null;
    }

    /**
     * 渲染工具栏 - 创建工具栏DOM结构
     * @returns {Promise<HTMLElement>} 工具栏元素
     */
    async render() {
        // 创建工具栏容器
        const toolbar = document.createElement('div');
        toolbar.id = this.id;
        
        // 创建工具栏组
        const leftGroup = this._createLeftGroup();
        const centerGroup = this._createCenterGroup();
        const rightGroup = this._createRightGroup();
        
        toolbar.appendChild(leftGroup);
        toolbar.appendChild(centerGroup);
        toolbar.appendChild(rightGroup);
        
        return toolbar;
    }

    /**
     * 创建左侧工具组 - 创建缩放控制
     * @returns {HTMLElement} 左侧工具组
     * @private
     */
    _createLeftGroup() {
        const group = document.createElement('div');
        group.className = 'toolbar-group toolbar-left';
        
        if (this.toolbarProps.showZoomControls) {
            // 缩放控制
            const zoomGroup = document.createElement('div');
            zoomGroup.className = 'zoom-controls';
            
            // 缩小按钮
            this.zoomOutButton = new Button({
                text: '',
                icon: 'fas fa-search-minus',
                size: 'sm',
                variant: 'secondary',
                tooltip: '缩小 (Ctrl+-)',
                onClick: () => this._handleZoomOut()
            });
            
            // 缩放显示
            this.zoomDisplay = document.createElement('span');
            this.zoomDisplay.className = 'zoom-display';
            this.zoomDisplay.textContent = `${Math.round(this.toolbarProps.zoom * 100)}%`;
            this.zoomDisplay.title = '当前缩放比例';
            
            // 放大按钮
            this.zoomInButton = new Button({
                text: '',
                icon: 'fas fa-search-plus',
                size: 'sm',
                variant: 'secondary',
                tooltip: '放大 (Ctrl++)',
                onClick: () => this._handleZoomIn()
            });
            
            // 重置缩放按钮
            this.zoomResetButton = new Button({
                text: '',
                icon: 'fas fa-expand-arrows-alt',
                size: 'sm',
                variant: 'secondary',
                tooltip: '重置缩放 (Ctrl+0)',
                onClick: () => this._handleZoomReset()
            });
            
            // 挂载按钮
            this.zoomOutButton.mount(zoomGroup);
            zoomGroup.appendChild(this.zoomDisplay);
            this.zoomInButton.mount(zoomGroup);
            this.zoomResetButton.mount(zoomGroup);
            
            // 添加到子组件
            this.addChild('zoom-out', this.zoomOutButton);
            this.addChild('zoom-in', this.zoomInButton);
            this.addChild('zoom-reset', this.zoomResetButton);
            
            group.appendChild(zoomGroup);
        }
        
        return group;
    }

    /**
     * 创建中央工具组 - 创建适应控制
     * @returns {HTMLElement} 中央工具组
     * @private
     */
    _createCenterGroup() {
        const group = document.createElement('div');
        group.className = 'toolbar-group toolbar-center';
        
        if (this.toolbarProps.showFitControls) {
            // 适应控制按钮组
            this.fitButtons = new ButtonGroup({
                orientation: 'horizontal',
                size: 'sm',
                buttons: [
                    {
                        text: '适应宽度',
                        icon: 'fas fa-arrows-alt-h',
                        variant: 'secondary',
                        onClick: () => this._handleFitToWidth()
                    },
                    {
                        text: '适应页面',
                        icon: 'fas fa-expand',
                        variant: 'secondary',
                        onClick: () => this._handleFitToPage()
                    }
                ]
            });
            
            this.fitButtons.mount(group);
            this.addChild('fit-buttons', this.fitButtons);
        }
        
        return group;
    }

    /**
     * 创建右侧工具组 - 创建主题和刷新控制
     * @returns {HTMLElement} 右侧工具组
     * @private
     */
    _createRightGroup() {
        const group = document.createElement('div');
        group.className = 'toolbar-group toolbar-right';
        
        if (this.toolbarProps.showThemeSelector) {
            // 主题选择器
            this.themeSelector = new Select({
                label: '',
                placeholder: '选择主题',
                value: this.toolbarProps.theme,
                size: 'sm',
                options: [
                    { value: 'default', text: '默认主题' },
                    { value: 'classic', text: '经典主题' },
                    { value: 'modern', text: '现代主题' },
                    { value: 'elegant', text: '优雅主题' },
                    { value: 'formal', text: '正式主题' }
                ],
                onChange: (event, value) => this._handleThemeChange(value)
            });
            
            this.themeSelector.mount(group);
            this.addChild('theme-selector', this.themeSelector);
        }
        
        if (this.toolbarProps.showRefreshButton) {
            // 刷新按钮
            this.refreshButton = new Button({
                text: '',
                icon: 'fas fa-sync-alt',
                size: 'sm',
                variant: 'primary',
                tooltip: '刷新预览 (Ctrl+R)',
                onClick: () => this._handleRefresh()
            });
            
            this.refreshButton.mount(group);
            this.addChild('refresh', this.refreshButton);
        }
        
        return group;
    }

    /**
     * 应用样式 - 应用工具栏特有的样式类
     * @private
     */
    _applyStyles() {
        if (!this.element) {
            return;
        }
        
        // 构建样式类列表
        const styleClasses = [
            'smartoffice-preview-toolbar',
            'preview-toolbar',
            this.state.enabled ? '' : 'toolbar-disabled',
            this.props.className
        ].filter(Boolean);
        
        // 应用样式类
        this.element.className = styleClasses.join(' ');
        
        // 设置自定义样式
        if (this.props.style) {
            Object.assign(this.element.style, this.props.style);
        }
    }

    /**
     * 处理缩小 - 处理缩小按钮点击
     * @private
     */
    _handleZoomOut() {
        const newZoom = Math.max(this.toolbarProps.zoom / 1.2, 0.1);
        this.setZoom(newZoom);
        
        if (this.zoomChangeHandler) {
            this.zoomChangeHandler(newZoom);
        }
    }

    /**
     * 处理放大 - 处理放大按钮点击
     * @private
     */
    _handleZoomIn() {
        const newZoom = Math.min(this.toolbarProps.zoom * 1.2, 5.0);
        this.setZoom(newZoom);
        
        if (this.zoomChangeHandler) {
            this.zoomChangeHandler(newZoom);
        }
    }

    /**
     * 处理重置缩放 - 处理重置缩放按钮点击
     * @private
     */
    _handleZoomReset() {
        this.setZoom(1.0);
        
        if (this.zoomChangeHandler) {
            this.zoomChangeHandler(1.0);
        }
    }

    /**
     * 处理适应宽度 - 处理适应宽度按钮点击
     * @private
     */
    _handleFitToWidth() {
        if (this.fitToWidthHandler) {
            this.fitToWidthHandler();
        }
    }

    /**
     * 处理适应页面 - 处理适应页面按钮点击
     * @private
     */
    _handleFitToPage() {
        if (this.fitToPageHandler) {
            this.fitToPageHandler();
        }
    }

    /**
     * 处理主题改变 - 处理主题选择器改变
     * @param {string} theme - 新主题
     * @private
     */
    _handleThemeChange(theme) {
        this.toolbarProps.theme = theme;
        
        if (this.themeChangeHandler) {
            this.themeChangeHandler(theme);
        }
    }

    /**
     * 处理刷新 - 处理刷新按钮点击
     * @private
     */
    _handleRefresh() {
        if (this.refreshHandler) {
            this.refreshHandler();
        }
    }

    /**
     * 设置缩放 - 更新工具栏显示的缩放比例
     * @param {number} zoom - 缩放比例
     */
    setZoom(zoom) {
        this.toolbarProps.zoom = zoom;
        
        if (this.zoomDisplay) {
            this.zoomDisplay.textContent = `${Math.round(zoom * 100)}%`;
        }
    }

    /**
     * 设置主题 - 更新工具栏显示的主题
     * @param {string} theme - 主题名称
     */
    setTheme(theme) {
        this.toolbarProps.theme = theme;
        
        if (this.themeSelector) {
            this.themeSelector.setValue(theme);
        }
    }
}
// #endregion

// #region 工厂函数
/**
 * 创建文档预览 - 工厂函数，创建文档预览实例
 * @param {Object} config - 预览配置
 * @returns {DocumentPreview} 预览实例
 */
export function createDocumentPreview(config = {}) {
    return new DocumentPreview(config);
}

/**
 * 创建预览工具栏 - 工厂函数，创建预览工具栏实例
 * @param {Object} config - 工具栏配置
 * @returns {PreviewToolbar} 工具栏实例
 */
export function createPreviewToolbar(config = {}) {
    return new PreviewToolbar(config);
}
// #endregion