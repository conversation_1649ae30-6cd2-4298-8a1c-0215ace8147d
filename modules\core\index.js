/**
 * @file 核心模块统一入口 - 整合后的核心系统导出
 * <AUTHOR> Team
 * @description 
 * 这是重构后的核心模块统一入口，整合了所有核心功能模块
 * 提供统一的核心系统接口和便捷的访问方式
 * 
 * 重构说明：
 * - 整合了事件系统、日志系统、配置管理、应用启动器
 * - 消除了重复的核心功能代码
 * - 建立了统一的核心模块接口
 * - 提供了便捷的全局访问方式
 */

// #region 导入所有核心模块
import {
    EventEmitter,
    EventBus,
    defaultEventBus,
    createEventBus,
    createEventEmitter,
    on,
    once,
    off,
    emit,
    onNamespace,
    onAny
} from './events.js';

import {
    Logger,
    LogLevel,
    createLogger,
    getLogger,
    logInfo,
    logDebug,
    logError
} from './logger.js';

import {
    ConfigManager,
    createConfigManager,
    getConfigManager,
    getConfig,
    setConfig,
    watchConfig,
    DEFAULT_CONFIG
} from './config.js';

import {
    AppBootstrap,
    createAppBootstrap,
    getAppBootstrap,
    startApp
} from './bootstrap.js';
// #endregion

// #region 核心系统管理器
/**
 * @class CoreSystem - 核心系统管理器
 * @description 统一管理所有核心模块，提供高级功能
 */
export class CoreSystem {
    /**
     * 构造函数 - 初始化核心系统
     */
    constructor() {
        this.logger = getLogger();
        this.config = getConfigManager();
        this.events = defaultEventBus;
        this.bootstrap = null;
        
        this.isInitialized = false;
        this.modules = new Map();
        this.startupTime = null;
        
        this.logger.info('CoreSystem', 'constructor', '核心系统管理器创建完成');
    }
    
    /**
     * 初始化核心系统
     * @param {Object} config - 初始化配置
     * @returns {Promise<boolean>} 初始化是否成功
     */
    async initialize(config = {}) {
        if (this.isInitialized) {
            this.logger.warn('CoreSystem', 'initialize', '核心系统已经初始化');
            return true;
        }
        
        try {
            this.startupTime = new Date();
            this.logger.info('CoreSystem', 'initialize', '开始初始化核心系统');
            
            // 创建并启动应用启动器
            this.bootstrap = createAppBootstrap(config);
            await this.bootstrap.start();
            
            // 注册核心模块
            this._registerCoreModules();
            
            // 设置系统级事件监听
            this._setupSystemEvents();
            
            this.isInitialized = true;
            
            // 触发系统初始化完成事件
            this.events.emit('core:system:initialized', {
                timestamp: new Date(),
                modules: Array.from(this.modules.keys())
            });
            
            this.logger.info('CoreSystem', 'initialize', '核心系统初始化完成');
            
            return true;
            
        } catch (error) {
            this.logger.error('CoreSystem', 'initialize', '核心系统初始化失败', error);
            throw error;
        }
    }
    
    /**
     * 注册核心模块
     * @private
     */
    _registerCoreModules() {
        const modules = [
            { name: 'events', instance: this.events, version: '2.0.0' },
            { name: 'logger', instance: this.logger, version: '2.0.0' },
            { name: 'config', instance: this.config, version: '2.0.0' },
            { name: 'bootstrap', instance: this.bootstrap, version: '2.0.0' }
        ];
        
        for (const module of modules) {
            this.modules.set(module.name, {
                ...module,
                registeredAt: new Date()
            });
        }
        
        this.logger.info('CoreSystem', '_registerCoreModules', `已注册 ${modules.length} 个核心模块`);
    }
    
    /**
     * 设置系统级事件监听
     * @private
     */
    _setupSystemEvents() {
        // 监听配置变更
        this.events.on('config:changed', (data) => {
            this.logger.debug('CoreSystem', 'configChanged', `配置变更: ${data.path}`, data);
        });
        
        // 监听应用错误
        this.events.on('app:error', (error) => {
            this.logger.error('CoreSystem', 'appError', '应用错误', error);
        });
        
        // 监听性能事件
        this.events.on('app:performance:memory', (memInfo) => {
            if (memInfo.used > memInfo.limit * 0.9) {
                this.logger.warn('CoreSystem', 'memoryWarning', '内存使用率过高', memInfo);
            }
        });
        
        this.logger.debug('CoreSystem', '_setupSystemEvents', '系统级事件监听设置完成');
    }
    
    /**
     * 获取模块
     * @param {string} name - 模块名称
     * @returns {Object|null} 模块实例
     */
    getModule(name) {
        const module = this.modules.get(name);
        return module ? module.instance : null;
    }
    
    /**
     * 获取所有模块信息
     * @returns {Object} 模块信息
     */
    getModules() {
        const moduleInfo = {};
        for (const [name, module] of this.modules.entries()) {
            moduleInfo[name] = {
                name: module.name,
                version: module.version,
                registeredAt: module.registeredAt,
                isActive: !!module.instance
            };
        }
        return moduleInfo;
    }
    
    /**
     * 获取系统状态
     * @returns {Object} 系统状态
     */
    getSystemStatus() {
        return {
            isInitialized: this.isInitialized,
            startupTime: this.startupTime,
            modules: this.getModules(),
            config: this.config.get('app'),
            performance: {
                uptime: this.startupTime ? Date.now() - this.startupTime.getTime() : 0,
                memoryUsage: performance.memory ? {
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize,
                    limit: performance.memory.jsHeapSizeLimit
                } : null
            }
        };
    }
    
    /**
     * 重启核心系统
     * @param {Object} config - 重启配置
     * @returns {Promise<boolean>} 重启是否成功
     */
    async restart(config = {}) {
        this.logger.info('CoreSystem', 'restart', '开始重启核心系统');
        
        try {
            // 关闭当前系统
            await this.shutdown();
            
            // 重新初始化
            await this.initialize(config);
            
            this.logger.info('CoreSystem', 'restart', '核心系统重启完成');
            
            return true;
            
        } catch (error) {
            this.logger.error('CoreSystem', 'restart', '核心系统重启失败', error);
            throw error;
        }
    }
    
    /**
     * 关闭核心系统
     * @returns {Promise<boolean>} 关闭是否成功
     */
    async shutdown() {
        this.logger.info('CoreSystem', 'shutdown', '开始关闭核心系统');
        
        try {
            // 触发系统关闭事件
            this.events.emit('core:system:shutdown:start');
            
            // 关闭应用启动器
            if (this.bootstrap) {
                await this.bootstrap.shutdown();
                this.bootstrap = null;
            }
            
            // 清理模块
            this.modules.clear();
            
            // 重置状态
            this.isInitialized = false;
            this.startupTime = null;
            
            // 触发系统关闭完成事件
            this.events.emit('core:system:shutdown:complete');
            
            this.logger.info('CoreSystem', 'shutdown', '核心系统关闭完成');
            
            return true;
            
        } catch (error) {
            this.logger.error('CoreSystem', 'shutdown', '核心系统关闭失败', error);
            throw error;
        }
    }
}
// #endregion

// #region 全局核心系统实例
let globalCoreSystem = null;

/**
 * 创建核心系统
 * @returns {CoreSystem} 核心系统实例
 */
export function createCoreSystem() {
    return new CoreSystem();
}

/**
 * 获取全局核心系统
 * @returns {CoreSystem} 全局核心系统实例
 */
export function getCoreSystem() {
    if (!globalCoreSystem) {
        globalCoreSystem = new CoreSystem();
    }
    return globalCoreSystem;
}

/**
 * 快速初始化核心系统
 * @param {Object} config - 初始化配置
 * @returns {Promise<CoreSystem>} 核心系统实例
 */
export async function initializeCoreSystem(config = {}) {
    const coreSystem = getCoreSystem();
    await coreSystem.initialize(config);
    return coreSystem;
}
// #endregion

// #region 便捷访问函数
/**
 * 获取事件总线
 * @returns {EventBus} 事件总线实例
 */
export function getEventBus() {
    return defaultEventBus;
}

/**
 * 获取日志记录器
 * @returns {Logger} 日志记录器实例
 */
export function getCoreLogger() {
    return getLogger();
}

/**
 * 获取配置管理器
 * @returns {ConfigManager} 配置管理器实例
 */
export function getCoreConfig() {
    return getConfigManager();
}

/**
 * 快速记录日志
 * @param {string} level - 日志级别
 * @param {string} module - 模块名称
 * @param {string} message - 日志消息
 * @param {any} data - 附加数据
 */
export function log(level, module, message, data = null) {
    const logger = getLogger();
    logger.log(level, 'Core', module, message, data);
}

/**
 * 快速触发事件
 * @param {string} eventType - 事件类型
 * @param {...any} args - 事件参数
 */
export function triggerEvent(eventType, ...args) {
    return defaultEventBus.emit(eventType, ...args);
}

/**
 * 快速监听事件
 * @param {string} eventType - 事件类型
 * @param {Function} callback - 回调函数
 * @returns {Function} 取消监听的函数
 */
export function listenEvent(eventType, callback) {
    defaultEventBus.on(eventType, callback);
    return () => defaultEventBus.off(eventType, callback);
}
// #endregion

// #region 统一导出
export {
    // 事件系统
    EventEmitter,
    EventBus,
    defaultEventBus,
    createEventBus,
    createEventEmitter,
    on,
    once,
    off,
    emit,
    onNamespace,
    onAny,
    
    // 日志系统
    Logger,
    LogLevel,
    createLogger,
    getLogger,
    logInfo,
    logDebug,
    logError,
    
    // 配置管理
    ConfigManager,
    createConfigManager,
    getConfigManager,
    getConfig,
    setConfig,
    watchConfig,
    DEFAULT_CONFIG,
    
    // 应用启动
    AppBootstrap,
    createAppBootstrap,
    getAppBootstrap,
    startApp
};
// #endregion

// #region 自动注册到全局
if (typeof window !== 'undefined') {
    window.SmartOfficeCore = {
        // 核心系统
        CoreSystem,
        createCoreSystem,
        getCoreSystem,
        initializeCoreSystem,
        
        // 便捷访问
        getEventBus,
        getCoreLogger,
        getCoreConfig,
        log,
        triggerEvent,
        listenEvent,
        
        // 所有模块
        EventEmitter,
        EventBus,
        Logger,
        ConfigManager,
        AppBootstrap,
        
        // 默认实例
        defaultEventBus,
        getLogger: getLogger,
        getConfigManager: getConfigManager
    };
    
    // 设置全局快捷方式
    window.SmartOffice = window.SmartOffice || {};
    window.SmartOffice.Core = window.SmartOfficeCore;
}
// #endregion
