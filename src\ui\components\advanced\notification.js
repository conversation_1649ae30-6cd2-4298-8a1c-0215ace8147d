/**
 * @file 通知组件 - SmartOffice 高级UI组件
 * <AUTHOR> Team
 * @description 
 * 这个文件实现了通知组件，提供：
 * - 多种通知类型（成功、警告、错误、信息）
 * - 自动关闭和手动关闭
 * - 位置控制
 * - 动画效果
 * - 批量管理
 * - 全局通知管理器
 */

// #region 导入依赖模块
import { BaseComponent } from '../base-component.js';
import { UIEvents } from '../../../core/events/event-types.js';
import { getLogger } from '../../../core/utils/logger.js';
// #endregion

// #region 常量定义
/**
 * 通知类型
 */
const NOTIFICATION_TYPES = {
    SUCCESS: 'success',
    INFO: 'info',
    WARNING: 'warning',
    ERROR: 'error'
};

/**
 * 通知位置
 */
const NOTIFICATION_POSITIONS = {
    TOP_LEFT: 'top-left',
    TOP_RIGHT: 'top-right',
    TOP_CENTER: 'top-center',
    BOTTOM_LEFT: 'bottom-left',
    BOTTOM_RIGHT: 'bottom-right',
    BOTTOM_CENTER: 'bottom-center'
};

/**
 * 动画类型
 */
const ANIMATION_TYPES = {
    SLIDE: 'slide',
    FADE: 'fade',
    BOUNCE: 'bounce',
    ZOOM: 'zoom'
};

/**
 * 默认配置
 */
const DEFAULT_CONFIG = {
    type: NOTIFICATION_TYPES.INFO,
    position: NOTIFICATION_POSITIONS.TOP_RIGHT,
    duration: 4500,
    closable: true,
    showIcon: true,
    animation: ANIMATION_TYPES.SLIDE,
    animationDuration: 300,
    maxWidth: 400,
    zIndex: 1010
};
// #endregion

// #region Notification 通知组件类
/**
 * @class Notification - 通知组件
 * @description 单个通知实例
 */
export class Notification extends BaseComponent {
    /**
     * 构造函数 - 初始化通知组件
     * @param {Object} config - 通知配置
     */
    constructor(config = {}) {
        super(config);
        
        this.logger = getLogger();
        this.logger.debug('Notification', 'constructor', '初始化通知组件');
        
        // 配置
        this.config = {
            ...DEFAULT_CONFIG,
            ...config
        };
        
        // 状态
        this.isVisible = false;
        this.isAnimating = false;
        this.autoCloseTimer = null;
        
        // DOM元素
        this.element = null;
        this.iconElement = null;
        this.contentElement = null;
        this.closeButton = null;
        
        // 创建通知
        this._createNotification();
        
        this.logger.info('Notification', 'constructor', '通知组件初始化完成', {
            config: this.config
        });
    }

    /**
     * 创建通知
     * @private
     */
    _createNotification() {
        // 创建主容器
        this.element = document.createElement('div');
        this.element.className = `notification notification-${this.config.type}`;
        this.element.setAttribute('role', 'alert');
        this.element.setAttribute('aria-live', 'polite');
        
        // 设置样式
        this._applyStyles();
        
        // 创建内容
        this._createContent();
        
        // 绑定事件
        this._bindEvents();
    }

    /**
     * 应用样式
     * @private
     */
    _applyStyles() {
        const typeColors = {
            [NOTIFICATION_TYPES.SUCCESS]: {
                background: 'var(--so-success-color-light, #f6ffed)',
                border: 'var(--so-success-color, #52c41a)',
                color: 'var(--so-success-color-dark, #389e0d)'
            },
            [NOTIFICATION_TYPES.INFO]: {
                background: 'var(--so-info-color-light, #e6f7ff)',
                border: 'var(--so-info-color, #1890ff)',
                color: 'var(--so-info-color-dark, #096dd9)'
            },
            [NOTIFICATION_TYPES.WARNING]: {
                background: 'var(--so-warning-color-light, #fffbe6)',
                border: 'var(--so-warning-color, #faad14)',
                color: 'var(--so-warning-color-dark, #d48806)'
            },
            [NOTIFICATION_TYPES.ERROR]: {
                background: 'var(--so-error-color-light, #fff2f0)',
                border: 'var(--so-error-color, #ff4d4f)',
                color: 'var(--so-error-color-dark, #cf1322)'
            }
        };
        
        const colors = typeColors[this.config.type];
        
        this.element.style.cssText = `
            position: relative;
            display: flex;
            align-items: flex-start;
            padding: var(--so-padding-md, 16px);
            margin-bottom: var(--so-margin-sm, 8px);
            background-color: ${colors.background};
            border: 1px solid ${colors.border};
            border-radius: var(--so-border-radius-base, 6px);
            box-shadow: var(--so-box-shadow, 0 2px 8px rgba(0, 0, 0, 0.15));
            color: ${colors.color};
            font-size: var(--so-font-size-base, 14px);
            line-height: 1.5;
            max-width: ${this.config.maxWidth}px;
            word-wrap: break-word;
            opacity: 0;
            transform: translateX(100%);
            transition: all ${this.config.animationDuration}ms ease;
            z-index: ${this.config.zIndex};
        `;
    }

    /**
     * 创建内容
     * @private
     */
    _createContent() {
        // 创建图标
        if (this.config.showIcon) {
            this.iconElement = document.createElement('div');
            this.iconElement.className = 'notification-icon';
            this.iconElement.innerHTML = this._getIcon();
            this.iconElement.style.cssText = `
                margin-right: var(--so-margin-sm, 8px);
                font-size: 16px;
                line-height: 1;
                flex-shrink: 0;
            `;
            this.element.appendChild(this.iconElement);
        }
        
        // 创建内容区域
        this.contentElement = document.createElement('div');
        this.contentElement.className = 'notification-content';
        this.contentElement.style.cssText = `
            flex: 1;
            min-width: 0;
        `;
        
        // 设置标题
        if (this.config.title) {
            const titleElement = document.createElement('div');
            titleElement.className = 'notification-title';
            titleElement.textContent = this.config.title;
            titleElement.style.cssText = `
                font-weight: 500;
                margin-bottom: var(--so-margin-xs, 4px);
            `;
            this.contentElement.appendChild(titleElement);
        }
        
        // 设置描述
        if (this.config.description) {
            const descElement = document.createElement('div');
            descElement.className = 'notification-description';
            
            if (typeof this.config.description === 'string') {
                descElement.textContent = this.config.description;
            } else if (this.config.description instanceof HTMLElement) {
                descElement.appendChild(this.config.description);
            }
            
            descElement.style.cssText = `
                opacity: 0.85;
                line-height: 1.4;
            `;
            this.contentElement.appendChild(descElement);
        }
        
        this.element.appendChild(this.contentElement);
        
        // 创建关闭按钮
        if (this.config.closable) {
            this.closeButton = document.createElement('button');
            this.closeButton.className = 'notification-close';
            this.closeButton.innerHTML = '×';
            this.closeButton.setAttribute('aria-label', '关闭通知');
            this.closeButton.style.cssText = `
                position: absolute;
                top: var(--so-padding-sm, 8px);
                right: var(--so-padding-sm, 8px);
                background: none;
                border: none;
                font-size: 18px;
                cursor: pointer;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: inherit;
                opacity: 0.6;
                transition: opacity 0.2s ease;
            `;
            
            this.closeButton.addEventListener('click', () => this.close());
            this.closeButton.addEventListener('mouseenter', () => {
                this.closeButton.style.opacity = '1';
            });
            this.closeButton.addEventListener('mouseleave', () => {
                this.closeButton.style.opacity = '0.6';
            });
            
            this.element.appendChild(this.closeButton);
        }
    }

    /**
     * 获取图标
     * @returns {string} 图标HTML
     * @private
     */
    _getIcon() {
        const icons = {
            [NOTIFICATION_TYPES.SUCCESS]: '✓',
            [NOTIFICATION_TYPES.INFO]: 'ℹ',
            [NOTIFICATION_TYPES.WARNING]: '⚠',
            [NOTIFICATION_TYPES.ERROR]: '✕'
        };
        
        return icons[this.config.type] || icons[NOTIFICATION_TYPES.INFO];
    }

    /**
     * 绑定事件
     * @private
     */
    _bindEvents() {
        // 鼠标悬停暂停自动关闭
        this.element.addEventListener('mouseenter', () => {
            this._pauseAutoClose();
        });
        
        this.element.addEventListener('mouseleave', () => {
            this._resumeAutoClose();
        });
        
        // 点击事件
        if (this.config.onClick) {
            this.element.addEventListener('click', (e) => {
                if (e.target !== this.closeButton) {
                    this.config.onClick(e, this);
                }
            });
            this.element.style.cursor = 'pointer';
        }
    }

    /**
     * 显示通知
     * @returns {Promise} Promise对象
     */
    show() {
        return new Promise((resolve) => {
            if (this.isVisible || this.isAnimating) {
                resolve(this);
                return;
            }
            
            this.isAnimating = true;
            
            // 应用显示动画
            requestAnimationFrame(() => {
                this._applyShowAnimation().then(() => {
                    this.isVisible = true;
                    this.isAnimating = false;
                    
                    // 设置自动关闭
                    this._setAutoClose();
                    
                    // 触发显示事件
                    this.emit(UIEvents.NOTIFICATION_SHOWN, {
                        notification: this,
                        timestamp: Date.now()
                    });
                    
                    resolve(this);
                });
            });
        });
    }

    /**
     * 关闭通知
     * @returns {Promise} Promise对象
     */
    close() {
        return new Promise((resolve) => {
            if (!this.isVisible || this.isAnimating) {
                resolve(this);
                return;
            }
            
            this.isAnimating = true;
            
            // 清除自动关闭定时器
            this._clearAutoClose();
            
            // 应用隐藏动画
            this._applyHideAnimation().then(() => {
                this.isVisible = false;
                this.isAnimating = false;
                
                // 触发关闭事件
                this.emit(UIEvents.NOTIFICATION_CLOSED, {
                    notification: this,
                    timestamp: Date.now()
                });
                
                resolve(this);
            });
        });
    }

    /**
     * 应用显示动画
     * @returns {Promise} Promise对象
     * @private
     */
    _applyShowAnimation() {
        return new Promise((resolve) => {
            switch (this.config.animation) {
                case ANIMATION_TYPES.SLIDE:
                    this.element.style.opacity = '1';
                    this.element.style.transform = 'translateX(0)';
                    break;
                    
                case ANIMATION_TYPES.FADE:
                    this.element.style.transform = 'translateX(0)';
                    this.element.style.opacity = '1';
                    break;
                    
                case ANIMATION_TYPES.BOUNCE:
                    this.element.style.transform = 'translateX(0) scale(1)';
                    this.element.style.opacity = '1';
                    this.element.style.transition = `all ${this.config.animationDuration}ms cubic-bezier(0.68, -0.55, 0.265, 1.55)`;
                    break;
                    
                case ANIMATION_TYPES.ZOOM:
                    this.element.style.transform = 'translateX(0) scale(1)';
                    this.element.style.opacity = '1';
                    break;
                    
                default:
                    this.element.style.opacity = '1';
                    this.element.style.transform = 'translateX(0)';
                    break;
            }
            
            setTimeout(resolve, this.config.animationDuration);
        });
    }

    /**
     * 应用隐藏动画
     * @returns {Promise} Promise对象
     * @private
     */
    _applyHideAnimation() {
        return new Promise((resolve) => {
            this.element.style.transition = `all ${this.config.animationDuration}ms ease`;
            
            switch (this.config.animation) {
                case ANIMATION_TYPES.SLIDE:
                    this.element.style.transform = 'translateX(100%)';
                    this.element.style.opacity = '0';
                    break;
                    
                case ANIMATION_TYPES.FADE:
                    this.element.style.opacity = '0';
                    break;
                    
                case ANIMATION_TYPES.BOUNCE:
                case ANIMATION_TYPES.ZOOM:
                    this.element.style.transform = 'translateX(100%) scale(0.8)';
                    this.element.style.opacity = '0';
                    break;
                    
                default:
                    this.element.style.transform = 'translateX(100%)';
                    this.element.style.opacity = '0';
                    break;
            }
            
            setTimeout(resolve, this.config.animationDuration);
        });
    }

    /**
     * 设置自动关闭
     * @private
     */
    _setAutoClose() {
        if (this.config.duration > 0) {
            this.autoCloseTimer = setTimeout(() => {
                this.close();
            }, this.config.duration);
        }
    }

    /**
     * 暂停自动关闭
     * @private
     */
    _pauseAutoClose() {
        if (this.autoCloseTimer) {
            clearTimeout(this.autoCloseTimer);
            this.autoCloseTimer = null;
        }
    }

    /**
     * 恢复自动关闭
     * @private
     */
    _resumeAutoClose() {
        if (this.config.duration > 0 && this.isVisible) {
            this._setAutoClose();
        }
    }

    /**
     * 清除自动关闭
     * @private
     */
    _clearAutoClose() {
        if (this.autoCloseTimer) {
            clearTimeout(this.autoCloseTimer);
            this.autoCloseTimer = null;
        }
    }

    /**
     * 获取元素
     * @returns {HTMLElement} 通知元素
     */
    getElement() {
        return this.element;
    }

    /**
     * 检查是否可见
     * @returns {boolean} 是否可见
     */
    isShown() {
        return this.isVisible;
    }

    /**
     * 销毁通知
     */
    destroy() {
        // 清除定时器
        this._clearAutoClose();
        
        // 移除DOM元素
        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }
        
        // 清理引用
        this.element = null;
        this.iconElement = null;
        this.contentElement = null;
        this.closeButton = null;
        
        // 调用父类销毁方法
        super.destroy();
        
        this.logger.info('Notification', 'destroy', '通知已销毁');
    }
}
// #endregion

// #region NotificationManager 通知管理器类
/**
 * @class NotificationManager - 通知管理器
 * @description 管理所有通知实例
 */
export class NotificationManager {
    /**
     * 构造函数 - 初始化通知管理器
     */
    constructor() {
        this.logger = getLogger();
        this.logger.debug('NotificationManager', 'constructor', '初始化通知管理器');
        
        // 通知容器
        this.containers = new Map();
        
        // 通知列表
        this.notifications = new Map();
        
        // 配置
        this.config = {
            maxCount: 5,
            spacing: 8,
            offset: 24
        };
        
        // 创建容器
        this._createContainers();
        
        this.logger.info('NotificationManager', 'constructor', '通知管理器初始化完成');
    }

    /**
     * 创建容器
     * @private
     */
    _createContainers() {
        Object.values(NOTIFICATION_POSITIONS).forEach(position => {
            const container = document.createElement('div');
            container.className = `notification-container notification-container-${position}`;
            
            // 设置容器样式
            this._applyContainerStyles(container, position);
            
            document.body.appendChild(container);
            this.containers.set(position, container);
        });
    }

    /**
     * 应用容器样式
     * @param {HTMLElement} container - 容器元素
     * @param {string} position - 位置
     * @private
     */
    _applyContainerStyles(container, position) {
        const baseStyles = {
            position: 'fixed',
            zIndex: 1010,
            pointerEvents: 'none'
        };
        
        const positionStyles = {
            [NOTIFICATION_POSITIONS.TOP_LEFT]: {
                top: `${this.config.offset}px`,
                left: `${this.config.offset}px`
            },
            [NOTIFICATION_POSITIONS.TOP_RIGHT]: {
                top: `${this.config.offset}px`,
                right: `${this.config.offset}px`
            },
            [NOTIFICATION_POSITIONS.TOP_CENTER]: {
                top: `${this.config.offset}px`,
                left: '50%',
                transform: 'translateX(-50%)'
            },
            [NOTIFICATION_POSITIONS.BOTTOM_LEFT]: {
                bottom: `${this.config.offset}px`,
                left: `${this.config.offset}px`
            },
            [NOTIFICATION_POSITIONS.BOTTOM_RIGHT]: {
                bottom: `${this.config.offset}px`,
                right: `${this.config.offset}px`
            },
            [NOTIFICATION_POSITIONS.BOTTOM_CENTER]: {
                bottom: `${this.config.offset}px`,
                left: '50%',
                transform: 'translateX(-50%)'
            }
        };
        
        Object.assign(container.style, baseStyles, positionStyles[position]);
    }

    /**
     * 显示通知
     * @param {Object} config - 通知配置
     * @returns {Notification} 通知实例
     */
    show(config) {
        const notification = new Notification(config);
        const position = config.position || DEFAULT_CONFIG.position;
        const container = this.containers.get(position);
        
        if (!container) {
            this.logger.error('NotificationManager', 'show', '无效的通知位置', { position });
            return null;
        }
        
        // 检查最大数量限制
        this._checkMaxCount(position);
        
        // 添加到容器
        notification.getElement().style.pointerEvents = 'auto';
        
        if (position.includes('bottom')) {
            container.insertBefore(notification.getElement(), container.firstChild);
        } else {
            container.appendChild(notification.getElement());
        }
        
        // 保存通知
        const notificationId = this._generateId();
        this.notifications.set(notificationId, {
            instance: notification,
            position,
            id: notificationId
        });
        
        // 监听关闭事件
        notification.on(UIEvents.NOTIFICATION_CLOSED, () => {
            this._removeNotification(notificationId);
        });
        
        // 显示通知
        notification.show();
        
        this.logger.info('NotificationManager', 'show', '通知已显示', {
            id: notificationId,
            position,
            config
        });
        
        return notification;
    }

    /**
     * 检查最大数量限制
     * @param {string} position - 位置
     * @private
     */
    _checkMaxCount(position) {
        const container = this.containers.get(position);
        const notifications = Array.from(container.children);
        
        if (notifications.length >= this.config.maxCount) {
            // 移除最旧的通知
            const oldestNotification = notifications[0];
            const notificationData = Array.from(this.notifications.values())
                .find(data => data.instance.getElement() === oldestNotification);
            
            if (notificationData) {
                notificationData.instance.close();
            }
        }
    }

    /**
     * 移除通知
     * @param {string} notificationId - 通知ID
     * @private
     */
    _removeNotification(notificationId) {
        const notificationData = this.notifications.get(notificationId);
        
        if (notificationData) {
            const { instance } = notificationData;
            
            // 延迟移除DOM元素
            setTimeout(() => {
                instance.destroy();
            }, instance.config.animationDuration);
            
            this.notifications.delete(notificationId);
        }
    }

    /**
     * 关闭所有通知
     * @param {string} position - 位置（可选）
     */
    closeAll(position = null) {
        const notificationsToClose = Array.from(this.notifications.values())
            .filter(data => !position || data.position === position);
        
        notificationsToClose.forEach(data => {
            data.instance.close();
        });
        
        this.logger.info('NotificationManager', 'closeAll', '已关闭所有通知', { position });
    }

    /**
     * 获取通知数量
     * @param {string} position - 位置（可选）
     * @returns {number} 通知数量
     */
    getCount(position = null) {
        if (position) {
            return Array.from(this.notifications.values())
                .filter(data => data.position === position).length;
        }
        return this.notifications.size;
    }

    /**
     * 设置配置
     * @param {Object} config - 配置
     */
    setConfig(config) {
        Object.assign(this.config, config);
        
        // 重新应用容器样式
        this.containers.forEach((container, position) => {
            this._applyContainerStyles(container, position);
        });
    }

    /**
     * 生成ID
     * @returns {string} ID
     * @private
     */
    _generateId() {
        return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 销毁管理器
     */
    destroy() {
        // 关闭所有通知
        this.closeAll();
        
        // 移除容器
        this.containers.forEach(container => {
            if (container.parentNode) {
                container.parentNode.removeChild(container);
            }
        });
        
        // 清理引用
        this.containers.clear();
        this.notifications.clear();
        
        this.logger.info('NotificationManager', 'destroy', '通知管理器已销毁');
    }
}
// #endregion

// #region 全局实例
/**
 * 全局通知管理器实例
 */
const globalNotificationManager = new NotificationManager();
// #endregion

// #region 静态方法
/**
 * 显示成功通知
 * @param {Object|string} config - 配置或消息
 * @returns {Notification} 通知实例
 */
Notification.success = function(config) {
    if (typeof config === 'string') {
        config = { description: config };
    }
    
    return globalNotificationManager.show({
        type: NOTIFICATION_TYPES.SUCCESS,
        title: '成功',
        ...config
    });
};

/**
 * 显示信息通知
 * @param {Object|string} config - 配置或消息
 * @returns {Notification} 通知实例
 */
Notification.info = function(config) {
    if (typeof config === 'string') {
        config = { description: config };
    }
    
    return globalNotificationManager.show({
        type: NOTIFICATION_TYPES.INFO,
        title: '信息',
        ...config
    });
};

/**
 * 显示警告通知
 * @param {Object|string} config - 配置或消息
 * @returns {Notification} 通知实例
 */
Notification.warning = function(config) {
    if (typeof config === 'string') {
        config = { description: config };
    }
    
    return globalNotificationManager.show({
        type: NOTIFICATION_TYPES.WARNING,
        title: '警告',
        ...config
    });
};

/**
 * 显示错误通知
 * @param {Object|string} config - 配置或消息
 * @returns {Notification} 通知实例
 */
Notification.error = function(config) {
    if (typeof config === 'string') {
        config = { description: config };
    }
    
    return globalNotificationManager.show({
        type: NOTIFICATION_TYPES.ERROR,
        title: '错误',
        duration: 0, // 错误通知不自动关闭
        ...config
    });
};

/**
 * 关闭所有通知
 * @param {string} position - 位置（可选）
 */
Notification.closeAll = function(position) {
    globalNotificationManager.closeAll(position);
};

/**
 * 获取通知管理器
 * @returns {NotificationManager} 通知管理器
 */
Notification.getManager = function() {
    return globalNotificationManager;
};
// #endregion

// #region 导出常量
export { 
    NOTIFICATION_TYPES, 
    NOTIFICATION_POSITIONS, 
    ANIMATION_TYPES,
    globalNotificationManager
};
// #endregion

// #region 导出
export default Notification;
// #endregion