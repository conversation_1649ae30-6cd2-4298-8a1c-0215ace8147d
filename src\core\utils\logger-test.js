/**
 * @file 日志系统测试脚本
 * @description 全面测试SmartOffice日志系统的功能、性能和可靠性
 */

// #region 导入依赖模块
import { getLogger, LogLevel } from './logger.js';
import { createDebugPanel, showDebugPanel, hideDebugPanel } from './debug-panel.js';
// #endregion

// #region 测试配置
const TEST_CONFIG = {
    testDuration: 30000,        // 测试持续时间: 30秒
    logInterval: 100,           // 日志间隔: 100ms
    stressTestLogs: 1000,       // 压力测试日志数量
    performanceTestIterations: 100, // 性能测试迭代次数
    enableDebugPanel: true      // 启用调试面板
};
// #endregion

// #region LoggerTester 测试类
/**
 * @class LoggerTester - 日志系统测试器
 * @description 提供全面的日志系统功能和性能测试
 */
export class LoggerTester {
    /**
     * 构造函数 - 初始化测试器
     * @param {Object} config - 测试配置
     */
    constructor(config = {}) {
        this.config = { ...TEST_CONFIG, ...config };
        this.logger = getLogger();
        this.testResults = {
            basicFunctionality: { passed: 0, failed: 0, tests: [] },
            performance: { passed: 0, failed: 0, tests: [] },
            stressTest: { passed: 0, failed: 0, tests: [] },
            debugPanel: { passed: 0, failed: 0, tests: [] },
            integration: { passed: 0, failed: 0, tests: [] }
        };
        this.startTime = Date.now();
        
        console.log('🧪 日志系统测试器初始化完成');
        console.log('📊 测试配置:', this.config);
    }

    /**
     * 运行所有测试
     * @returns {Promise<Object>} 测试结果
     */
    async runAllTests() {
        console.log('\n🚀 开始运行SmartOffice日志系统完整测试套件...\n');
        
        try {
            // 基础功能测试
            console.log('📋 1. 基础功能测试');
            await this.testBasicFunctionality();
            
            // 性能测试
            console.log('\n⚡ 2. 性能测试');
            await this.testPerformance();
            
            // 压力测试
            console.log('\n🔥 3. 压力测试');
            await this.testStressScenarios();
            
            // 调试面板测试
            console.log('\n🖥️ 4. 调试面板测试');
            await this.testDebugPanel();
            
            // 集成测试
            console.log('\n🔗 5. 集成测试');
            await this.testIntegration();
            
            // 生成测试报告
            return this.generateTestReport();
            
        } catch (error) {
            console.error('❌ 测试套件执行失败:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 基础功能测试
     */
    async testBasicFunctionality() {
        const tests = [
            { name: '日志级别测试', test: () => this.testLogLevels() },
            { name: '性能标记测试', test: () => this.testPerformanceMarks() },
            { name: '计数器测试', test: () => this.testCounters() },
            { name: '数据附加测试', test: () => this.testDataAttachment() },
            { name: '模块过滤测试', test: () => this.testModuleFiltering() }
        ];
        
        for (const { name, test } of tests) {
            try {
                console.log(`  📝 ${name}...`);
                await test();
                this.testResults.basicFunctionality.passed++;
                this.testResults.basicFunctionality.tests.push({ name, status: 'passed' });
                console.log(`  ✅ ${name} 通过`);
            } catch (error) {
                this.testResults.basicFunctionality.failed++;
                this.testResults.basicFunctionality.tests.push({ 
                    name, 
                    status: 'failed', 
                    error: error.message 
                });
                console.log(`  ❌ ${name} 失败:`, error.message);
            }
        }
    }

    /**
     * 测试日志级别
     */
    testLogLevels() {
        const testModule = 'LoggerTest';
        const testFunction = 'testLogLevels';
        
        // 测试所有日志级别
        this.logger.trace(testModule, testFunction, '这是TRACE级别日志', { level: 'trace' });
        this.logger.debug(testModule, testFunction, '这是DEBUG级别日志', { level: 'debug' });
        this.logger.info(testModule, testFunction, '这是INFO级别日志', { level: 'info' });
        this.logger.warn(testModule, testFunction, '这是WARN级别日志', { level: 'warn' });
        this.logger.error(testModule, testFunction, '这是ERROR级别日志', { level: 'error' });
        this.logger.fatal(testModule, testFunction, '这是FATAL级别日志', { level: 'fatal' });
        
        // 验证日志记录
        const logs = this.logger.getLogs();
        const recentLogs = logs.slice(-6);
        
        if (recentLogs.length !== 6) {
            throw new Error(`期望6条日志，实际${recentLogs.length}条`);
        }
        
        const expectedLevels = ['TRACE', 'DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL'];
        for (let i = 0; i < 6; i++) {
            if (recentLogs[i].level !== expectedLevels[i]) {
                throw new Error(`第${i + 1}条日志级别错误: 期望${expectedLevels[i]}, 实际${recentLogs[i].level}`);
            }
        }
    }

    /**
     * 测试性能标记
     */
    testPerformanceMarks() {
        const markName = 'test_performance_mark';
        const testModule = 'LoggerTest';
        const testFunction = 'testPerformanceMarks';
        
        // 开始性能标记
        this.logger.startPerformanceMark(markName, testModule, testFunction);
        
        // 模拟一些操作
        for (let i = 0; i < 1000; i++) {
            Math.sqrt(i);
        }
        
        // 结束性能标记
        const duration = this.logger.endPerformanceMark(markName, testModule, testFunction);
        
        if (typeof duration !== 'number' || duration < 0) {
            throw new Error('性能标记返回值无效');
        }
        
        if (duration > 100) {
            console.warn(`  ⚠️ 性能标记耗时较长: ${duration.toFixed(2)}ms`);
        }
    }

    /**
     * 测试计数器
     */
    testCounters() {
        const counterName = 'test_counter';
        const testModule = 'LoggerTest';
        
        // 重置计数器
        this.logger.resetCounter(counterName);
        
        // 增加计数器
        for (let i = 0; i < 10; i++) {
            this.logger.incrementCounter(counterName, testModule);
        }
        
        // 验证计数器值
        const count = this.logger.getCounter(counterName);
        if (count !== 10) {
            throw new Error(`计数器值错误: 期望10, 实际${count}`);
        }
    }

    /**
     * 测试数据附加
     */
    testDataAttachment() {
        const testModule = 'LoggerTest';
        const testFunction = 'testDataAttachment';
        
        const testData = {
            string: 'test string',
            number: 42,
            boolean: true,
            array: [1, 2, 3],
            object: { nested: 'value' },
            null: null,
            undefined: undefined
        };
        
        this.logger.info(testModule, testFunction, '数据附加测试', testData);
        
        const logs = this.logger.getLogs();
        const lastLog = logs[logs.length - 1];
        
        if (!lastLog.data) {
            throw new Error('日志数据附加失败');
        }
        
        // 验证数据完整性
        if (JSON.stringify(lastLog.data.string) !== JSON.stringify(testData.string)) {
            throw new Error('字符串数据附加错误');
        }
        
        if (lastLog.data.number !== testData.number) {
            throw new Error('数字数据附加错误');
        }
        
        if (lastLog.data.boolean !== testData.boolean) {
            throw new Error('布尔数据附加错误');
        }
    }

    /**
     * 测试模块过滤
     */
    testModuleFiltering() {
        const modules = ['TestModule1', 'TestModule2', 'TestModule3'];
        const testFunction = 'testModuleFiltering';
        
        // 生成不同模块的日志
        modules.forEach((module, index) => {
            this.logger.info(module, testFunction, `来自${module}的日志`, { index });
        });
        
        // 验证日志记录
        const logs = this.logger.getLogs();
        const recentLogs = logs.slice(-3);
        
        for (let i = 0; i < 3; i++) {
            if (recentLogs[i].module !== modules[i]) {
                throw new Error(`模块记录错误: 期望${modules[i]}, 实际${recentLogs[i].module}`);
            }
        }
    }

    /**
     * 性能测试
     */
    async testPerformance() {
        const tests = [
            { name: '日志记录性能', test: () => this.testLoggingPerformance() },
            { name: '性能标记性能', test: () => this.testPerformanceMarkPerformance() },
            { name: '内存使用测试', test: () => this.testMemoryUsage() },
            { name: '大数据附加性能', test: () => this.testLargeDataPerformance() }
        ];
        
        for (const { name, test } of tests) {
            try {
                console.log(`  ⚡ ${name}...`);
                await test();
                this.testResults.performance.passed++;
                this.testResults.performance.tests.push({ name, status: 'passed' });
                console.log(`  ✅ ${name} 通过`);
            } catch (error) {
                this.testResults.performance.failed++;
                this.testResults.performance.tests.push({ 
                    name, 
                    status: 'failed', 
                    error: error.message 
                });
                console.log(`  ❌ ${name} 失败:`, error.message);
            }
        }
    }

    /**
     * 测试日志记录性能
     */
    testLoggingPerformance() {
        const iterations = this.config.performanceTestIterations;
        const startTime = performance.now();
        
        for (let i = 0; i < iterations; i++) {
            this.logger.info('PerformanceTest', 'testLoggingPerformance', `性能测试日志 ${i}`, { iteration: i });
        }
        
        const endTime = performance.now();
        const totalTime = endTime - startTime;
        const avgTime = totalTime / iterations;
        
        console.log(`    📊 ${iterations}次日志记录耗时: ${totalTime.toFixed(2)}ms`);
        console.log(`    📊 平均每次记录耗时: ${avgTime.toFixed(3)}ms`);
        
        if (avgTime > 1) {
            throw new Error(`日志记录性能不达标: 平均${avgTime.toFixed(3)}ms > 1ms`);
        }
    }

    /**
     * 测试性能标记性能
     */
    testPerformanceMarkPerformance() {
        const iterations = this.config.performanceTestIterations;
        const startTime = performance.now();
        
        for (let i = 0; i < iterations; i++) {
            const markName = `performance_test_${i}`;
            this.logger.startPerformanceMark(markName, 'PerformanceTest', 'testPerformanceMarkPerformance');
            this.logger.endPerformanceMark(markName, 'PerformanceTest', 'testPerformanceMarkPerformance');
        }
        
        const endTime = performance.now();
        const totalTime = endTime - startTime;
        const avgTime = totalTime / iterations;
        
        console.log(`    📊 ${iterations}次性能标记耗时: ${totalTime.toFixed(2)}ms`);
        console.log(`    📊 平均每次标记耗时: ${avgTime.toFixed(3)}ms`);
        
        if (avgTime > 0.5) {
            throw new Error(`性能标记性能不达标: 平均${avgTime.toFixed(3)}ms > 0.5ms`);
        }
    }

    /**
     * 测试内存使用
     */
    testMemoryUsage() {
        const initialLogs = this.logger.getLogs().length;
        
        // 生成大量日志
        for (let i = 0; i < 1000; i++) {
            this.logger.info('MemoryTest', 'testMemoryUsage', `内存测试日志 ${i}`, { 
                iteration: i,
                timestamp: Date.now(),
                data: new Array(100).fill(i)
            });
        }
        
        const finalLogs = this.logger.getLogs().length;
        const addedLogs = finalLogs - initialLogs;
        
        console.log(`    📊 添加了 ${addedLogs} 条日志`);
        
        if (addedLogs !== 1000) {
            throw new Error(`日志数量不匹配: 期望1000, 实际${addedLogs}`);
        }
    }

    /**
     * 测试大数据附加性能
     */
    testLargeDataPerformance() {
        const largeData = {
            array: new Array(1000).fill(0).map((_, i) => ({ id: i, value: `item_${i}` })),
            text: 'x'.repeat(10000),
            nested: {
                level1: {
                    level2: {
                        level3: {
                            data: new Array(100).fill('nested data')
                        }
                    }
                }
            }
        };
        
        const startTime = performance.now();
        
        for (let i = 0; i < 10; i++) {
            this.logger.info('LargeDataTest', 'testLargeDataPerformance', `大数据测试 ${i}`, largeData);
        }
        
        const endTime = performance.now();
        const totalTime = endTime - startTime;
        const avgTime = totalTime / 10;
        
        console.log(`    📊 10次大数据附加耗时: ${totalTime.toFixed(2)}ms`);
        console.log(`    📊 平均每次大数据附加耗时: ${avgTime.toFixed(2)}ms`);
        
        if (avgTime > 5) {
            throw new Error(`大数据附加性能不达标: 平均${avgTime.toFixed(2)}ms > 5ms`);
        }
    }

    /**
     * 压力测试
     */
    async testStressScenarios() {
        const tests = [
            { name: '高频日志记录', test: () => this.testHighFrequencyLogging() },
            { name: '并发性能标记', test: () => this.testConcurrentPerformanceMarks() },
            { name: '长时间运行测试', test: () => this.testLongRunning() }
        ];
        
        for (const { name, test } of tests) {
            try {
                console.log(`  🔥 ${name}...`);
                await test();
                this.testResults.stressTest.passed++;
                this.testResults.stressTest.tests.push({ name, status: 'passed' });
                console.log(`  ✅ ${name} 通过`);
            } catch (error) {
                this.testResults.stressTest.failed++;
                this.testResults.stressTest.tests.push({ 
                    name, 
                    status: 'failed', 
                    error: error.message 
                });
                console.log(`  ❌ ${name} 失败:`, error.message);
            }
        }
    }

    /**
     * 高频日志记录测试
     */
    async testHighFrequencyLogging() {
        const startTime = Date.now();
        const endTime = startTime + 5000; // 5秒测试
        let logCount = 0;
        
        console.log('    🔥 开始5秒高频日志记录...');
        
        while (Date.now() < endTime) {
            this.logger.info('StressTest', 'testHighFrequencyLogging', `高频日志 ${logCount}`, { 
                count: logCount,
                timestamp: Date.now()
            });
            logCount++;
            
            // 稍微延迟以避免阻塞
            if (logCount % 100 === 0) {
                await new Promise(resolve => setTimeout(resolve, 1));
            }
        }
        
        const actualDuration = Date.now() - startTime;
        const logsPerSecond = logCount / (actualDuration / 1000);
        
        console.log(`    📊 5秒内记录了 ${logCount} 条日志`);
        console.log(`    📊 平均每秒 ${logsPerSecond.toFixed(0)} 条日志`);
        
        if (logsPerSecond < 100) {
            throw new Error(`高频日志记录性能不达标: ${logsPerSecond.toFixed(0)} logs/sec < 100 logs/sec`);
        }
    }

    /**
     * 并发性能标记测试
     */
    async testConcurrentPerformanceMarks() {
        const concurrency = 50;
        const promises = [];
        
        console.log(`    🔥 开始${concurrency}个并发性能标记...`);
        
        for (let i = 0; i < concurrency; i++) {
            promises.push(this.performConcurrentMark(i));
        }
        
        const results = await Promise.all(promises);
        const successful = results.filter(r => r.success).length;
        const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
        
        console.log(`    📊 ${successful}/${concurrency} 个性能标记成功`);
        console.log(`    📊 平均标记耗时: ${avgDuration.toFixed(2)}ms`);
        
        if (successful < concurrency) {
            throw new Error(`并发性能标记失败率过高: ${concurrency - successful}/${concurrency}`);
        }
    }

    /**
     * 执行并发性能标记
     * @param {number} index - 标记索引
     * @returns {Promise<Object>} 执行结果
     */
    async performConcurrentMark(index) {
        const markName = `concurrent_mark_${index}`;
        const startTime = Date.now();
        
        try {
            this.logger.startPerformanceMark(markName, 'StressTest', 'performConcurrentMark');
            
            // 模拟一些异步操作
            await new Promise(resolve => setTimeout(resolve, Math.random() * 10));
            
            const duration = this.logger.endPerformanceMark(markName, 'StressTest', 'performConcurrentMark');
            
            return {
                success: true,
                duration: Date.now() - startTime,
                markDuration: duration
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
                duration: Date.now() - startTime
            };
        }
    }

    /**
     * 长时间运行测试
     */
    async testLongRunning() {
        const duration = 10000; // 10秒
        const startTime = Date.now();
        const endTime = startTime + duration;
        let iterations = 0;
        
        console.log(`    🔥 开始${duration / 1000}秒长时间运行测试...`);
        
        while (Date.now() < endTime) {
            // 混合不同类型的日志操作
            this.logger.info('LongRunningTest', 'testLongRunning', `长时间运行迭代 ${iterations}`);
            this.logger.incrementCounter('long_running_iterations', 'LongRunningTest');
            
            if (iterations % 10 === 0) {
                const markName = `long_running_mark_${iterations}`;
                this.logger.startPerformanceMark(markName, 'LongRunningTest', 'testLongRunning');
                await new Promise(resolve => setTimeout(resolve, 5));
                this.logger.endPerformanceMark(markName, 'LongRunningTest', 'testLongRunning');
            }
            
            iterations++;
            await new Promise(resolve => setTimeout(resolve, 1));
        }
        
        const actualDuration = Date.now() - startTime;
        const iterationsPerSecond = iterations / (actualDuration / 1000);
        
        console.log(`    📊 ${actualDuration / 1000}秒内完成 ${iterations} 次迭代`);
        console.log(`    📊 平均每秒 ${iterationsPerSecond.toFixed(0)} 次迭代`);
        
        if (iterationsPerSecond < 50) {
            throw new Error(`长时间运行性能不达标: ${iterationsPerSecond.toFixed(0)} iterations/sec < 50 iterations/sec`);
        }
    }

    /**
     * 调试面板测试
     */
    async testDebugPanel() {
        if (!this.config.enableDebugPanel) {
            console.log('  ⏭️ 调试面板测试已跳过 (disabled)');
            return;
        }
        
        const tests = [
            { name: '调试面板创建', test: () => this.testDebugPanelCreation() },
            { name: '调试面板显示隐藏', test: () => this.testDebugPanelVisibility() },
            { name: '日志过滤功能', test: () => this.testDebugPanelFiltering() }
        ];
        
        for (const { name, test } of tests) {
            try {
                console.log(`  🖥️ ${name}...`);
                await test();
                this.testResults.debugPanel.passed++;
                this.testResults.debugPanel.tests.push({ name, status: 'passed' });
                console.log(`  ✅ ${name} 通过`);
            } catch (error) {
                this.testResults.debugPanel.failed++;
                this.testResults.debugPanel.tests.push({ 
                    name, 
                    status: 'failed', 
                    error: error.message 
                });
                console.log(`  ❌ ${name} 失败:`, error.message);
            }
        }
    }

    /**
     * 测试调试面板创建
     */
    testDebugPanelCreation() {
        const panel = createDebugPanel({
            theme: 'dark',
            position: 'bottom-right',
            width: 800,
            height: 400
        });
        
        if (!panel) {
            throw new Error('调试面板创建失败');
        }
        
        if (typeof panel.show !== 'function' || typeof panel.hide !== 'function') {
            throw new Error('调试面板缺少必要方法');
        }
    }

    /**
     * 测试调试面板显示隐藏
     */
    async testDebugPanelVisibility() {
        // 显示调试面板
        showDebugPanel();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 隐藏调试面板
        hideDebugPanel();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 再次显示
        showDebugPanel();
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    /**
     * 测试调试面板过滤功能
     */
    testDebugPanelFiltering() {
        // 生成不同级别的测试日志
        this.logger.debug('FilterTest', 'testDebugPanelFiltering', 'DEBUG级别日志');
        this.logger.info('FilterTest', 'testDebugPanelFiltering', 'INFO级别日志');
        this.logger.warn('FilterTest', 'testDebugPanelFiltering', 'WARN级别日志');
        this.logger.error('FilterTest', 'testDebugPanelFiltering', 'ERROR级别日志');
        
        // 这里应该有调试面板的DOM交互测试
        // 由于这是单元测试，暂时跳过DOM操作验证
        console.log('    📝 调试面板过滤功能需要手动验证');
    }

    /**
     * 集成测试
     */
    async testIntegration() {
        const tests = [
            { name: '多组件协作', test: () => this.testMultiComponentCollaboration() },
            { name: '错误处理集成', test: () => this.testErrorHandlingIntegration() },
            { name: '全局状态同步', test: () => this.testGlobalStateSync() }
        ];
        
        for (const { name, test } of tests) {
            try {
                console.log(`  🔗 ${name}...`);
                await test();
                this.testResults.integration.passed++;
                this.testResults.integration.tests.push({ name, status: 'passed' });
                console.log(`  ✅ ${name} 通过`);
            } catch (error) {
                this.testResults.integration.failed++;
                this.testResults.integration.tests.push({ 
                    name, 
                    status: 'failed', 
                    error: error.message 
                });
                console.log(`  ❌ ${name} 失败:`, error.message);
            }
        }
    }

    /**
     * 测试多组件协作
     */
    async testMultiComponentCollaboration() {
        // 模拟多个组件同时使用日志系统
        const components = ['ComponentA', 'ComponentB', 'ComponentC'];
        const operations = ['initialize', 'process', 'cleanup'];
        
        for (const component of components) {
            for (const operation of operations) {
                const markName = `${component}_${operation}`;
                
                this.logger.startPerformanceMark(markName, component, operation);
                this.logger.info(component, operation, `${component} 正在执行 ${operation}`);
                
                // 模拟异步操作
                await new Promise(resolve => setTimeout(resolve, Math.random() * 10));
                
                this.logger.endPerformanceMark(markName, component, operation);
                this.logger.incrementCounter(`${component}_operations`, component);
            }
        }
        
        // 验证所有操作都有日志记录
        const logs = this.logger.getLogs();
        const recentLogs = logs.slice(-9); // 3组件 × 3操作 = 9条日志
        
        if (recentLogs.length < 9) {
            throw new Error(`多组件协作日志记录不完整: ${recentLogs.length} < 9`);
        }
        
        // 验证计数器
        for (const component of components) {
            const count = this.logger.getCounter(`${component}_operations`);
            if (count < 3) {
                throw new Error(`组件${component}计数器错误: ${count} < 3`);
            }
        }
    }

    /**
     * 测试错误处理集成
     */
    async testErrorHandlingIntegration() {
        const testErrors = [
            new Error('测试错误1'),
            new TypeError('测试类型错误'),
            new ReferenceError('测试引用错误')
        ];
        
        for (let i = 0; i < testErrors.length; i++) {
            const error = testErrors[i];
            
            try {
                // 模拟抛出错误
                throw error;
            } catch (e) {
                this.logger.error('ErrorTest', 'testErrorHandlingIntegration', 
                    `捕获错误 ${i + 1}`, {
                        error: e.message,
                        stack: e.stack,
                        type: e.constructor.name
                    });
            }
        }
        
        // 验证错误日志记录
        const logs = this.logger.getLogs();
        const errorLogs = logs.filter(log => log.level === 'ERROR').slice(-3);
        
        if (errorLogs.length < 3) {
            throw new Error(`错误日志记录不完整: ${errorLogs.length} < 3`);
        }
        
        // 验证错误信息完整性
        for (let i = 0; i < errorLogs.length; i++) {
            const log = errorLogs[i];
            if (!log.data || !log.data.error) {
                throw new Error(`错误日志 ${i + 1} 缺少错误信息`);
            }
        }
    }

    /**
     * 测试全局状态同步
     */
    async testGlobalStateSync() {
        // 获取日志统计信息
        const stats = this.logger.getStats();
        
        if (!stats || typeof stats !== 'object') {
            throw new Error('日志统计信息获取失败');
        }
        
        const requiredFields = ['totalLogs', 'errorCount', 'warnCount', 'infoCount'];
        for (const field of requiredFields) {
            if (!(field in stats)) {
                throw new Error(`统计信息缺少字段: ${field}`);
            }
            if (typeof stats[field] !== 'number') {
                throw new Error(`统计字段${field}类型错误: ${typeof stats[field]}`);
            }
        }
        
        // 验证性能数据
        const performanceData = this.logger.getPerformanceData();
        if (!performanceData || typeof performanceData !== 'object') {
            throw new Error('性能数据获取失败');
        }
        
        const requiredPerfFields = ['activeMarks', 'completedMarks'];
        for (const field of requiredPerfFields) {
            if (!(field in performanceData)) {
                throw new Error(`性能数据缺少字段: ${field}`);
            }
        }
    }

    /**
     * 生成测试报告
     * @returns {Object} 测试报告
     */
    generateTestReport() {
        const endTime = Date.now();
        const totalDuration = endTime - this.startTime;
        
        // 计算总体统计
        const totalPassed = Object.values(this.testResults).reduce((sum, category) => sum + category.passed, 0);
        const totalFailed = Object.values(this.testResults).reduce((sum, category) => sum + category.failed, 0);
        const totalTests = totalPassed + totalFailed;
        const successRate = totalTests > 0 ? (totalPassed / totalTests) * 100 : 0;
        
        const report = {
            summary: {
                totalTests,
                totalPassed,
                totalFailed,
                successRate: parseFloat(successRate.toFixed(2)),
                duration: totalDuration,
                timestamp: new Date().toISOString()
            },
            categories: this.testResults,
            systemInfo: {
                userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Node.js',
                memoryUsage: typeof performance !== 'undefined' && performance.memory ? 
                    performance.memory : 'Not available',
                loggerStats: this.logger.getStats(),
                performanceData: this.logger.getPerformanceData()
            }
        };
        
        // 打印测试报告
        this.printTestReport(report);
        
        return report;
    }

    /**
     * 打印测试报告
     * @param {Object} report - 测试报告
     */
    printTestReport(report) {
        console.log('\n' + '='.repeat(80));
        console.log('🧪 SmartOffice 日志系统测试报告');
        console.log('='.repeat(80));
        
        // 总体统计
        console.log('\n📊 总体统计:');
        console.log(`   总测试数: ${report.summary.totalTests}`);
        console.log(`   通过: ${report.summary.totalPassed} ✅`);
        console.log(`   失败: ${report.summary.totalFailed} ${report.summary.totalFailed > 0 ? '❌' : ''}`);
        console.log(`   成功率: ${report.summary.successRate}%`);
        console.log(`   总耗时: ${report.summary.duration}ms`);
        
        // 分类统计
        console.log('\n📋 分类统计:');
        for (const [category, results] of Object.entries(report.categories)) {
            const total = results.passed + results.failed;
            const rate = total > 0 ? ((results.passed / total) * 100).toFixed(1) : '0.0';
            console.log(`   ${category}: ${results.passed}/${total} (${rate}%) ${results.failed > 0 ? '⚠️' : '✅'}`);
        }
        
        // 系统信息
        console.log('\n🖥️ 系统信息:');
        console.log(`   日志总数: ${report.systemInfo.loggerStats.totalLogs}`);
        console.log(`   性能标记: ${report.systemInfo.performanceData.completedMarks}`);
        if (report.systemInfo.memoryUsage && typeof report.systemInfo.memoryUsage === 'object') {
            console.log(`   内存使用: ${(report.systemInfo.memoryUsage.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`);
        }
        
        // 失败详情
        if (report.summary.totalFailed > 0) {
            console.log('\n❌ 失败详情:');
            for (const [category, results] of Object.entries(report.categories)) {
                const failedTests = results.tests.filter(test => test.status === 'failed');
                if (failedTests.length > 0) {
                    console.log(`   ${category}:`);
                    failedTests.forEach(test => {
                        console.log(`     - ${test.name}: ${test.error}`);
                    });
                }
            }
        }
        
        console.log('\n' + '='.repeat(80));
        
        if (report.summary.successRate >= 90) {
            console.log('🎉 测试结果: 优秀! 日志系统运行良好。');
        } else if (report.summary.successRate >= 70) {
            console.log('⚠️ 测试结果: 良好，但有改进空间。');
        } else {
            console.log('🚨 测试结果: 需要注意，存在较多问题。');
        }
        
        console.log('='.repeat(80) + '\n');
    }
}
// #endregion

// #region 便捷测试函数
/**
 * 运行快速测试
 * @returns {Promise<Object>} 测试结果
 */
export async function runQuickTest() {
    console.log('🚀 开始快速日志系统测试...\n');
    
    const tester = new LoggerTester({
        performanceTestIterations: 10,
        enableDebugPanel: false
    });
    
    return await tester.runAllTests();
}

/**
 * 运行完整测试
 * @returns {Promise<Object>} 测试结果
 */
export async function runFullTest() {
    console.log('🚀 开始完整日志系统测试...\n');
    
    const tester = new LoggerTester({
        performanceTestIterations: 100,
        enableDebugPanel: true
    });
    
    return await tester.runAllTests();
}

/**
 * 运行压力测试
 * @returns {Promise<Object>} 测试结果
 */
export async function runStressTest() {
    console.log('🚀 开始日志系统压力测试...\n');
    
    const tester = new LoggerTester({
        performanceTestIterations: 1000,
        stressTestLogs: 10000,
        enableDebugPanel: true
    });
    
    return await tester.runAllTests();
}
// #endregion

// #region 自动运行（如果直接执行此文件）
if (typeof window !== 'undefined' && window.location) {
    // 浏览器环境
    window.LoggerTester = LoggerTester;
    window.runQuickTest = runQuickTest;
    window.runFullTest = runFullTest;
    window.runStressTest = runStressTest;
    
    console.log('📋 日志测试工具已加载到全局作用域:');
    console.log('   - LoggerTester: 测试器类');
    console.log('   - runQuickTest(): 快速测试');
    console.log('   - runFullTest(): 完整测试');
    console.log('   - runStressTest(): 压力测试');
}
// #endregion 