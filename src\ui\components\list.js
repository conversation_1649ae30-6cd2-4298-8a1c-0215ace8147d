/**
 * @file 列表组件 - SmartOffice 数据展示列表系统
 * <AUTHOR> Team
 * @description 
 * 提供完整的列表功能，包括：
 * - List 基础列表组件
 * - ListItem 列表项组件
 * - 虚拟滚动支持
 * - 多种布局模式
 * - 筛选和选择功能
 * - 完整的事件系统和生命周期管理
 */

import { BaseComponent } from './base-component.js';
import { UI_EVENTS } from '../../core/events/event-types.js';

// #region List主组件
/**
 * @class List - 列表主组件
 * @extends BaseComponent
 * @description 可配置的数据列表组件，支持虚拟滚动和多种布局
 */
export class List extends BaseComponent {
    /**
     * 构造函数 - 初始化列表组件
     * @param {Object} config - 列表配置
     * @param {Array} config.data - 列表数据
     * @param {Function} config.itemRender - 列表项渲染函数
     * @param {boolean} config.virtual - 是否启用虚拟滚动
     * @param {string} config.layout - 布局模式
     */
    constructor(config = {}) {
        super(config);
        
        // 列表属性
        this.listProps = {
            data: config.data || [],
            itemRender: config.itemRender || null, // 自定义渲染函数
            layout: config.layout || 'vertical', // vertical, horizontal, grid
            itemHeight: config.itemHeight || 'auto', // 列表项高度
            itemWidth: config.itemWidth || 'auto', // 列表项宽度（grid模式）
            columns: config.columns || 1, // 网格列数
            gap: config.gap || '8px', // 列表项间距
            selectable: config.selectable || false, // 是否可选择
            multiSelect: config.multiSelect || false, // 多选模式
            bordered: config.bordered !== false, // 边框
            hoverable: config.hoverable !== false, // 悬停效果
            size: config.size || 'medium', // 尺寸: small, medium, large
            virtual: config.virtual || false, // 虚拟滚动
            virtualItemHeight: config.virtualItemHeight || 50, // 虚拟滚动项高度
            overscan: config.overscan || 5, // 虚拟滚动预渲染数量
            height: config.height || 'auto', // 列表容器高度
            maxHeight: config.maxHeight || null, // 最大高度
            loading: config.loading || false, // 加载状态
            emptyText: config.emptyText || '暂无数据', // 空数据提示
            itemKey: config.itemKey || 'id', // 项目键字段
            className: config.className || ''
        };
        
        // 列表状态
        this.listState = {
            selectedItems: new Set(), // 选中项
            filteredData: [], // 筛选后的数据
            displayData: [], // 显示的数据
            loading: this.listProps.loading,
            scrollTop: 0, // 滚动位置
            visibleRange: { start: 0, end: 0 }, // 可见范围
            containerHeight: 0, // 容器高度
            totalHeight: 0, // 总高度
            filter: null // 筛选函数
        };
        
        // DOM元素引用
        this.elements = {
            list: null,
            container: null,
            viewport: null,
            spacer: null,
            loadingOverlay: null
        };
        
        // 事件处理器
        this.handlers = {
            scroll: this._handleScroll.bind(this),
            itemClick: this._handleItemClick.bind(this),
            itemSelect: this._handleItemSelect.bind(this),
            resize: this._handleResize.bind(this)
        };
        
        // 初始化组件
        this._initializeList();
    }

    /**
     * 初始化列表 - 创建DOM结构和设置事件
     * @private
     */
    _initializeList() {
        this._createListStructure();
        this._applyListStyles();
        this._bindListEvents();
        this._processData();
        
        // 发射初始化完成事件
        this.emit(UI_EVENTS.COMPONENT_INITIALIZED, {
            component: this,
            type: 'list'
        });
    }

    /**
     * 创建列表DOM结构 - 构建完整的列表HTML
     * @private
     */
    _createListStructure() {
        // 创建列表容器
        this.elements.list = document.createElement('div');
        this.elements.list.className = `smartoffice-list smartoffice-list-${this.listProps.size} smartoffice-list-${this.listProps.layout} ${this.listProps.className}`;
        
        if (this.listProps.virtual) {
            // 虚拟滚动结构
            this._createVirtualListStructure();
        } else {
            // 普通列表结构
            this._createNormalListStructure();
        }
        
        // 创建加载遮罩
        this._createLoadingOverlay();
        
        this.elements.list.appendChild(this.elements.loadingOverlay);
        this.container = this.elements.list;
    }

    /**
     * 创建虚拟列表结构 - 虚拟滚动列表
     * @private
     */
    _createVirtualListStructure() {
        // 视窗容器
        this.elements.viewport = document.createElement('div');
        this.elements.viewport.className = 'list-viewport';
        
        // 内容容器
        this.elements.container = document.createElement('div');
        this.elements.container.className = 'list-container';
        
        // 占位元素
        this.elements.spacer = document.createElement('div');
        this.elements.spacer.className = 'list-spacer';
        
        this.elements.container.appendChild(this.elements.spacer);
        this.elements.viewport.appendChild(this.elements.container);
        this.elements.list.appendChild(this.elements.viewport);
    }

    /**
     * 创建普通列表结构 - 标准列表
     * @private
     */
    _createNormalListStructure() {
        this.elements.container = document.createElement('div');
        this.elements.container.className = 'list-container';
        this.elements.list.appendChild(this.elements.container);
    }

    /**
     * 创建加载遮罩 - 加载状态显示
     * @private
     */
    _createLoadingOverlay() {
        this.elements.loadingOverlay = document.createElement('div');
        this.elements.loadingOverlay.className = 'list-loading-overlay';
        this.elements.loadingOverlay.style.display = 'none';
        
        const loadingContent = document.createElement('div');
        loadingContent.className = 'loading-content';
        loadingContent.innerHTML = `
            <div class="loading-spinner"></div>
            <span>加载中...</span>
        `;
        
        this.elements.loadingOverlay.appendChild(loadingContent);
    }

    /**
     * 应用列表样式 - 设置CSS样式
     * @private
     */
    _applyListStyles() {
        // 容器样式
        Object.assign(this.elements.list.style, {
            position: 'relative',
            width: '100%',
            backgroundColor: 'white',
            borderRadius: '6px',
            overflow: 'hidden'
        });
        
        // 边框样式
        if (this.listProps.bordered) {
            this.elements.list.style.border = '1px solid #e0e0e0';
        }
        
        // 高度设置
        if (this.listProps.height !== 'auto') {
            this.elements.list.style.height = this.listProps.height;
        }
        
        if (this.listProps.maxHeight) {
            this.elements.list.style.maxHeight = this.listProps.maxHeight;
        }
        
        // 虚拟滚动样式
        if (this.listProps.virtual && this.elements.viewport) {
            Object.assign(this.elements.viewport.style, {
                height: '100%',
                overflowY: 'auto',
                position: 'relative'
            });
            
            Object.assign(this.elements.container.style, {
                position: 'relative'
            });
        }
        
        // 布局样式
        this._applyLayoutStyles();
        
        // 加载遮罩样式
        Object.assign(this.elements.loadingOverlay.style, {
            position: 'absolute',
            top: '0',
            left: '0',
            right: '0',
            bottom: '0',
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: '10'
        });
    }

    /**
     * 应用布局样式 - 根据布局模式设置样式
     * @private
     */
    _applyLayoutStyles() {
        const containerStyle = this.elements.container.style;
        
        switch (this.listProps.layout) {
            case 'horizontal':
                containerStyle.display = 'flex';
                containerStyle.flexDirection = 'row';
                containerStyle.gap = this.listProps.gap;
                containerStyle.overflowX = 'auto';
                break;
                
            case 'grid':
                containerStyle.display = 'grid';
                containerStyle.gridTemplateColumns = `repeat(${this.listProps.columns}, 1fr)`;
                containerStyle.gap = this.listProps.gap;
                break;
                
            default: // vertical
                containerStyle.display = 'flex';
                containerStyle.flexDirection = 'column';
                containerStyle.gap = this.listProps.gap;
                break;
        }
    }

    /**
     * 绑定列表事件 - 设置所有事件监听器
     * @private
     */
    _bindListEvents() {
        // 容器点击事件
        this.elements.container.addEventListener('click', this.handlers.itemClick);
        
        // 虚拟滚动事件
        if (this.listProps.virtual && this.elements.viewport) {
            this.elements.viewport.addEventListener('scroll', this.handlers.scroll);
        }
        
        // 窗口大小变化事件
        window.addEventListener('resize', this.handlers.resize);
    }

    /**
     * 处理数据 - 筛选和虚拟滚动计算
     * @private
     */
    _processData() {
        // 应用筛选
        this._applyFilter();
        
        // 计算虚拟滚动
        if (this.listProps.virtual) {
            this._calculateVirtualScroll();
        }
        
        // 渲染列表
        this._renderList();
    }

    /**
     * 应用筛选 - 根据筛选条件过滤数据
     * @private
     */
    _applyFilter() {
        if (this.listState.filter && typeof this.listState.filter === 'function') {
            this.listState.filteredData = this.listProps.data.filter(this.listState.filter);
        } else {
            this.listState.filteredData = [...this.listProps.data];
        }
    }

    /**
     * 计算虚拟滚动 - 计算可见范围和位置
     * @private
     */
    _calculateVirtualScroll() {
        if (!this.listProps.virtual || !this.elements.viewport) {
            this.listState.displayData = this.listState.filteredData;
            return;
        }
        
        const containerHeight = this.elements.viewport.clientHeight;
        const itemHeight = this.listProps.virtualItemHeight;
        const totalItems = this.listState.filteredData.length;
        const totalHeight = totalItems * itemHeight;
        
        // 计算可见范围
        const scrollTop = this.listState.scrollTop;
        const visibleStart = Math.max(0, Math.floor(scrollTop / itemHeight) - this.listProps.overscan);
        const visibleEnd = Math.min(totalItems, Math.ceil((scrollTop + containerHeight) / itemHeight) + this.listProps.overscan);
        
        this.listState.visibleRange = { start: visibleStart, end: visibleEnd };
        this.listState.containerHeight = containerHeight;
        this.listState.totalHeight = totalHeight;
        
        // 获取可见数据
        this.listState.displayData = this.listState.filteredData.slice(visibleStart, visibleEnd);
        
        // 更新占位元素高度
        if (this.elements.spacer) {
            this.elements.spacer.style.height = totalHeight + 'px';
        }
    }

    /**
     * 渲染列表 - 生成列表项
     * @private
     */
    _renderList() {
        // 清空现有内容
        const items = this.elements.container.querySelectorAll('.list-item');
        items.forEach(item => item.remove());
        
        // 空数据提示
        if (this.listState.filteredData.length === 0) {
            this._renderEmptyState();
            return;
        }
        
        // 渲染数据项
        this.listState.displayData.forEach((item, index) => {
            const listItem = this._createListItem(item, index);
            
            if (this.listProps.virtual) {
                // 虚拟滚动位置
                const actualIndex = this.listState.visibleRange.start + index;
                const top = actualIndex * this.listProps.virtualItemHeight;
                listItem.style.position = 'absolute';
                listItem.style.top = top + 'px';
                listItem.style.width = '100%';
                listItem.style.height = this.listProps.virtualItemHeight + 'px';
            }
            
            this.elements.container.appendChild(listItem);
        });
    }

    /**
     * 渲染空状态 - 无数据时的显示
     * @private
     */
    _renderEmptyState() {
        const emptyElement = document.createElement('div');
        emptyElement.className = 'list-empty';
        emptyElement.textContent = this.listProps.emptyText;
        
        Object.assign(emptyElement.style, {
            padding: '40px 20px',
            textAlign: 'center',
            color: '#999',
            fontSize: '14px'
        });
        
        this.elements.container.appendChild(emptyElement);
    }

    /**
     * 创建列表项 - 生成单个列表项
     * @param {*} item - 项目数据
     * @param {number} index - 项目索引
     * @returns {HTMLElement} 列表项元素
     * @private
     */
    _createListItem(item, index) {
        const listItem = document.createElement('div');
        listItem.className = 'list-item';
        listItem.dataset.itemIndex = index;
        listItem.dataset.itemKey = this._getItemKey(item);
        
        // 应用项目样式
        this._applyItemStyles(listItem);
        
        // 渲染项目内容
        if (this.listProps.itemRender && typeof this.listProps.itemRender === 'function') {
            const rendered = this.listProps.itemRender(item, index);
            if (typeof rendered === 'string') {
                listItem.innerHTML = rendered;
            } else if (rendered instanceof HTMLElement) {
                listItem.appendChild(rendered);
            } else {
                listItem.textContent = String(rendered);
            }
        } else {
            // 默认渲染
            listItem.textContent = typeof item === 'object' ? JSON.stringify(item) : String(item);
        }
        
        // 选择功能
        if (this.listProps.selectable) {
            this._addSelectionToItem(listItem, item);
        }
        
        // 选中状态
        if (this.listState.selectedItems.has(this._getItemKey(item))) {
            listItem.classList.add('selected');
        }
        
        return listItem;
    }

    /**
     * 应用项目样式 - 设置列表项样式
     * @param {HTMLElement} listItem - 列表项元素
     * @private
     */
    _applyItemStyles(listItem) {
        Object.assign(listItem.style, {
            padding: this._getItemPadding(),
            backgroundColor: 'white',
            borderBottom: this.listProps.bordered ? '1px solid #f0f0f0' : 'none',
            cursor: this.listProps.selectable ? 'pointer' : 'default',
            transition: 'background-color 0.2s',
            display: 'flex',
            alignItems: 'center'
        });
        
        // 固定尺寸设置
        if (this.listProps.itemHeight !== 'auto') {
            listItem.style.height = this.listProps.itemHeight;
        }
        
        if (this.listProps.itemWidth !== 'auto') {
            listItem.style.width = this.listProps.itemWidth;
        }
        
        // 悬停效果
        if (this.listProps.hoverable) {
            listItem.addEventListener('mouseenter', () => {
                if (!listItem.classList.contains('selected')) {
                    listItem.style.backgroundColor = '#f5f5f5';
                }
            });
            
            listItem.addEventListener('mouseleave', () => {
                if (!listItem.classList.contains('selected')) {
                    listItem.style.backgroundColor = 'white';
                }
            });
        }
    }

    /**
     * 获取项目内边距 - 根据尺寸获取内边距
     * @returns {string} 内边距值
     * @private
     */
    _getItemPadding() {
        const paddingMap = {
            small: '8px 12px',
            medium: '12px 16px',
            large: '16px 20px'
        };
        return paddingMap[this.listProps.size] || paddingMap.medium;
    }

    /**
     * 添加选择功能 - 为列表项添加选择功能
     * @param {HTMLElement} listItem - 列表项元素
     * @param {*} item - 项目数据
     * @private
     */
    _addSelectionToItem(listItem, item) {
        const checkbox = document.createElement('input');
        checkbox.type = this.listProps.multiSelect ? 'checkbox' : 'radio';
        checkbox.className = 'item-select-checkbox';
        checkbox.name = this.listProps.multiSelect ? `select-${this.id}` : `select-${this.id}`;
        checkbox.dataset.itemKey = this._getItemKey(item);
        
        Object.assign(checkbox.style, {
            marginRight: '8px',
            flexShrink: '0'
        });
        
        listItem.insertBefore(checkbox, listItem.firstChild);
    }

    /**
     * 获取项目键 - 获取项目的唯一标识
     * @param {*} item - 项目数据
     * @returns {string} 项目键
     * @private
     */
    _getItemKey(item) {
        if (typeof item === 'object' && item !== null) {
            return String(item[this.listProps.itemKey] || JSON.stringify(item));
        }
        return String(item);
    }

    // #region 公开方法
    /**
     * 设置数据 - 更新列表数据
     * @param {Array} data - 新数据
     */
    setData(data) {
        this.listProps.data = data || [];
        this.listState.selectedItems.clear();
        this._processData();
        
        this.emit(UI_EVENTS.COMPONENT_UPDATED, {
            component: this,
            type: 'data',
            data: this.listProps.data
        });
    }

    /**
     * 获取数据 - 获取当前列表数据
     * @returns {Array} 列表数据
     */
    getData() {
        return this.listProps.data;
    }

    /**
     * 获取选中项 - 获取当前选中的项目
     * @returns {Array} 选中项目数据
     */
    getSelectedItems() {
        return this.listProps.data.filter(item => {
            const key = this._getItemKey(item);
            return this.listState.selectedItems.has(key);
        });
    }

    /**
     * 设置选中项 - 设置选中的项目
     * @param {Array} itemKeys - 项目键数组
     */
    setSelectedItems(itemKeys) {
        this.listState.selectedItems.clear();
        itemKeys.forEach(key => {
            this.listState.selectedItems.add(String(key));
        });
        
        this._updateItemSelection();
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            type: 'selection',
            selectedItems: this.getSelectedItems()
        });
    }

    /**
     * 筛选 - 设置筛选函数
     * @param {Function} filterFn - 筛选函数
     */
    setFilter(filterFn) {
        this.listState.filter = filterFn;
        this._processData();
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            type: 'filter',
            filter: filterFn
        });
    }

    /**
     * 清除筛选 - 清除筛选条件
     */
    clearFilter() {
        this.listState.filter = null;
        this._processData();
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            type: 'clearFilter'
        });
    }

    /**
     * 滚动到指定项 - 滚动到指定项目
     * @param {string|number} itemKey - 项目键或索引
     */
    scrollToItem(itemKey) {
        if (!this.listProps.virtual) {
            // 普通列表滚动
            const itemElement = this.elements.container.querySelector(`[data-item-key="${itemKey}"]`);
            if (itemElement) {
                itemElement.scrollIntoView({ behavior: 'smooth' });
            }
        } else {
            // 虚拟滚动
            const index = this.listState.filteredData.findIndex(item => this._getItemKey(item) === String(itemKey));
            if (index >= 0) {
                const scrollTop = index * this.listProps.virtualItemHeight;
                this.elements.viewport.scrollTop = scrollTop;
            }
        }
    }

    /**
     * 刷新 - 重新渲染列表
     */
    refresh() {
        this._processData();
        
        this.emit(UI_EVENTS.COMPONENT_UPDATED, {
            component: this,
            type: 'refresh'
        });
    }

    /**
     * 设置加载状态 - 显示/隐藏加载遮罩
     * @param {boolean} loading - 是否加载中
     */
    setLoading(loading) {
        this.listState.loading = loading;
        this.elements.loadingOverlay.style.display = loading ? 'flex' : 'none';
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            type: 'loading',
            loading: loading
        });
    }
    // #endregion

    // #region 事件处理方法
    /**
     * 处理滚动 - 虚拟滚动处理
     * @param {ScrollEvent} e - 滚动事件
     * @private
     */
    _handleScroll(e) {
        if (!this.listProps.virtual) return;
        
        this.listState.scrollTop = e.target.scrollTop;
        this._calculateVirtualScroll();
        this._renderList();
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            type: 'scroll',
            scrollTop: this.listState.scrollTop
        });
    }

    /**
     * 处理项目点击 - 项目点击和选择
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleItemClick(e) {
        const listItem = e.target.closest('.list-item');
        if (!listItem) return;
        
        const itemKey = listItem.dataset.itemKey;
        const itemData = this._getItemDataByKey(itemKey);
        
        // 处理选择checkbox点击
        if (e.target.matches('.item-select-checkbox')) {
            this._toggleItemSelection(itemKey, e.target.checked);
            return;
        }
        
        // 项目点击事件
        this.emit(UI_EVENTS.COMPONENT_CLICKED, {
            component: this,
            type: 'item',
            itemKey: itemKey,
            itemData: itemData,
            event: e
        });
        
        // 如果可选择且不是多选，点击项目进行选择
        if (this.listProps.selectable && !this.listProps.multiSelect) {
            this._toggleItemSelection(itemKey, true);
        }
    }

    /**
     * 处理项目选择 - 选择/取消选择项目
     * @param {MouseEvent} e - 鼠标事件
     * @private
     */
    _handleItemSelect(e) {
        // 这个方法由itemClick处理
    }

    /**
     * 处理窗口大小变化 - 重新计算虚拟滚动
     * @param {ResizeEvent} e - 大小变化事件
     * @private
     */
    _handleResize(e) {
        if (this.listProps.virtual) {
            this._calculateVirtualScroll();
            this._renderList();
        }
    }
    // #endregion

    // #region 辅助方法
    /**
     * 切换项目选择 - 选择/取消选择项目
     * @param {string} itemKey - 项目键
     * @param {boolean} selected - 是否选中
     * @private
     */
    _toggleItemSelection(itemKey, selected) {
        if (selected) {
            if (!this.listProps.multiSelect) {
                this.listState.selectedItems.clear();
            }
            this.listState.selectedItems.add(itemKey);
        } else {
            this.listState.selectedItems.delete(itemKey);
        }
        
        this._updateItemSelection();
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            type: 'itemSelect',
            itemKey: itemKey,
            selected: selected,
            selectedItems: this.getSelectedItems()
        });
    }

    /**
     * 更新项目选择状态 - 更新UI中的选择状态
     * @private
     */
    _updateItemSelection() {
        const items = this.elements.container.querySelectorAll('.list-item');
        items.forEach(item => {
            const itemKey = item.dataset.itemKey;
            const checkbox = item.querySelector('.item-select-checkbox');
            const isSelected = this.listState.selectedItems.has(itemKey);
            
            item.classList.toggle('selected', isSelected);
            if (checkbox) {
                checkbox.checked = isSelected;
            }
            
            // 更新背景色
            if (isSelected) {
                item.style.backgroundColor = '#e6f7ff';
            } else {
                item.style.backgroundColor = 'white';
            }
        });
    }

    /**
     * 根据键获取项目数据 - 通过项目键获取完整数据
     * @param {string} itemKey - 项目键
     * @returns {*} 项目数据
     * @private
     */
    _getItemDataByKey(itemKey) {
        return this.listProps.data.find(item => this._getItemKey(item) === itemKey) || null;
    }
    // #endregion

    // #region 生命周期方法
    /**
     * 组件销毁 - 清理资源
     */
    destroy() {
        // 移除窗口事件监听器
        window.removeEventListener('resize', this.handlers.resize);
        
        // 调用父类销毁方法
        super.destroy();
        
        this.emit(UI_EVENTS.COMPONENT_DESTROYED, { component: this });
    }
    // #endregion
}

/**
 * @class ListItem - 列表项组件
 * @extends BaseComponent
 * @description 单个列表项组件
 */
export class ListItem extends BaseComponent {
    /**
     * 构造函数 - 初始化列表项组件
     * @param {Object} config - 列表项配置
     * @param {*} config.data - 项目数据
     * @param {Function} config.render - 渲染函数
     */
    constructor(config = {}) {
        super(config);
        
        this.itemProps = {
            data: config.data || null,
            render: config.render || null,
            selectable: config.selectable || false,
            selected: config.selected || false,
            className: config.className || ''
        };
        
        this.itemState = {
            selected: this.itemProps.selected
        };
        
        this._initializeItem();
    }

    /**
     * 初始化列表项 - 创建DOM结构
     * @private
     */
    _initializeItem() {
        this._createItemStructure();
        this._applyItemStyles();
        
        this.emit(UI_EVENTS.COMPONENT_INITIALIZED, {
            component: this,
            type: 'listItem'
        });
    }

    /**
     * 创建项目结构 - 构建列表项HTML
     * @private
     */
    _createItemStructure() {
        this.container = document.createElement('div');
        this.container.className = `smartoffice-list-item ${this.itemProps.className}`;
        
        // 渲染内容
        if (this.itemProps.render && typeof this.itemProps.render === 'function') {
            const rendered = this.itemProps.render(this.itemProps.data);
            if (typeof rendered === 'string') {
                this.container.innerHTML = rendered;
            } else if (rendered instanceof HTMLElement) {
                this.container.appendChild(rendered);
            } else {
                this.container.textContent = String(rendered);
            }
        } else {
            this.container.textContent = String(this.itemProps.data);
        }
    }

    /**
     * 应用项目样式 - 设置列表项样式
     * @private
     */
    _applyItemStyles() {
        Object.assign(this.container.style, {
            padding: '12px 16px',
            borderBottom: '1px solid #f0f0f0',
            cursor: this.itemProps.selectable ? 'pointer' : 'default',
            transition: 'background-color 0.2s',
            backgroundColor: this.itemState.selected ? '#e6f7ff' : 'white'
        });
        
        if (this.itemProps.selectable) {
            this.container.addEventListener('click', () => {
                this.toggle();
            });
        }
    }

    /**
     * 选择项目 - 设置选中状态
     */
    select() {
        this.itemState.selected = true;
        this.container.style.backgroundColor = '#e6f7ff';
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            type: 'select',
            selected: true
        });
    }

    /**
     * 取消选择 - 取消选中状态
     */
    deselect() {
        this.itemState.selected = false;
        this.container.style.backgroundColor = 'white';
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            type: 'deselect',
            selected: false
        });
    }

    /**
     * 切换选择 - 切换选中状态
     */
    toggle() {
        if (this.itemState.selected) {
            this.deselect();
        } else {
            this.select();
        }
    }

    /**
     * 获取选中状态 - 获取当前选中状态
     * @returns {boolean} 是否选中
     */
    isSelected() {
        return this.itemState.selected;
    }
}
// #endregion

// #region 工厂函数
/**
 * 创建列表 - 便捷函数创建列表实例
 * @param {Object} config - 列表配置
 * @returns {List} 列表实例
 */
export function createList(config = {}) {
    return new List(config);
}

/**
 * 创建虚拟列表 - 便捷函数创建虚拟滚动列表
 * @param {Object} config - 列表配置
 * @returns {List} 列表实例
 */
export function createVirtualList(config = {}) {
    return new List({
        ...config,
        virtual: true,
        virtualItemHeight: config.itemHeight || 50
    });
}

/**
 * 创建网格列表 - 便捷函数创建网格布局列表
 * @param {Object} config - 列表配置
 * @returns {List} 列表实例
 */
export function createGridList(config = {}) {
    return new List({
        ...config,
        layout: 'grid',
        columns: config.columns || 3
    });
}

/**
 * 创建可选择列表 - 便捷函数创建可选择列表
 * @param {Object} config - 列表配置
 * @returns {List} 列表实例
 */
export function createSelectableList(config = {}) {
    return new List({
        ...config,
        selectable: true,
        multiSelect: config.multiSelect !== false
    });
}

/**
 * 创建列表项 - 便捷函数创建列表项
 * @param {*} data - 项目数据
 * @param {Object} options - 额外选项
 * @returns {ListItem} 列表项实例
 */
export function createListItem(data, options = {}) {
    return new ListItem({
        data,
        ...options
    });
}
// #endregion 