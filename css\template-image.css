/* @file 模板图片样式文件 */
/* @project SmartOffice */
/* 此文件包含与模板图片相关的样式规则 */

/* #region 公司Logo样式 */
.company-logo {
    /* 公司Logo基本样式 */
    max-height: 70px; /* 减小最大高度为70像素，适合A4页面 */
    max-width: 80%; /* 限制最大宽度，避免过宽 */
    object-fit: contain; /* 保持图片纵横比，完整显示图片并适应容器 */
    margin: 0 auto; /* 居中显示 */
    display: block; /* 设置为块级元素 */
    transition: all 0.3s ease; /* 设置过渡效果，持续0.3秒，缓动函数为ease */
}

/* 司机协议中的公司Logo样式 - 保持原始比例 */
.company-header-image {
    max-height: none !important; /* 移除最大高度限制 */
    height: auto !important; /* 自动高度 */
    width: auto !important; /* 自动宽度 */
    max-width: 100% !important; /* 最大宽度为容器宽度 */
    object-fit: contain !important; /* 保持图片纵横比 */
}

/* 不同公司Logo的特定样式 */
.company-logo[data-company="smw"] {
    /* Sky Mirror World Tour公司Logo样式 */
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1)); /* 减小阴影效果 */
}

.company-logo[data-company="gmh"] {
    /* GoMyHire Travel公司Logo样式 */
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1)); /* 减小阴影效果 */
}
/* #endregion 公司Logo样式 */

/* #region 公司印章样式 */
.stamp-image {
    /* 公司印章基本样式 */
    max-width: 90px; /* 减小最大宽度为90像素，适合A4页面 */
    max-height: 90px; /* 减小最大高度为90像素，适合A4页面 */
    object-fit: contain; /* 保持图片纵横比，完整显示图片并适应容器 */
    opacity: 0.85; /* 调整不透明度，使印章看起来更真实 */
    transform: rotate(-5deg); /* 稍微旋转印章，增加真实感 */
    transition: all 0.3s ease; /* 设置过渡效果，持续0.3秒，缓动函数为ease */
    margin-right: 10px; /* 添加右边距 */
}

/* 印章容器样式 */
.stamp-section {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 100px; /* 固定高度 */
}

/* 不同公司印章的特定样式 */
.stamp-image[data-company="smw"] {
    /* Sky Mirror World Tour公司印章样式 */
    filter: hue-rotate(0deg) contrast(1.05); /* 不调整色相，略微增加对比度 */
}

.stamp-image[data-company="gmh"] {
    /* GoMyHire Travel公司印章样式 */
    filter: hue-rotate(30deg) contrast(1.05); /* 稍微调整色相，略微增加对比度 */
}
/* #endregion 公司印章样式 */

/* #region 模板背景图片样式 */
.template-background {
    /* 模板背景图片基本样式 */
    position: absolute; /* 绝对定位 */
    top: 0; /* 顶部对齐 */
    left: 0; /* 左侧对齐 */
    width: 100%; /* 宽度100% */
    height: 100%; /* 高度100% */
    opacity: 0.05; /* 设置不透明度为0.05，作为水印效果 */
    pointer-events: none; /* 禁用鼠标事件，使背景图片不影响交互 */
    z-index: 0; /* 设置层级为0，确保内容在上层 */
    object-fit: cover; /* 覆盖整个容器，可能裁剪图片 */
}

/* 不同模板背景的特定样式 */
.template-background[data-template="classic"] {
    /* 经典模板背景样式 */
    opacity: 0.03; /* 更低的不透明度 */
    background-position: center; /* 居中背景图片 */
}

.template-background[data-template="modern"] {
    /* 现代模板背景样式 */
    opacity: 0.04; /* 稍高的不透明度 */
    background-position: top right; /* 右上角对齐背景图片 */
}

.template-background[data-template="elegant"] {
    /* 优雅模板背景样式 */
    opacity: 0.05; /* 标准不透明度 */
    background-position: center; /* 居中背景图片 */
}

.template-background[data-template="tourism"] {
    /* 旅游模板背景样式 */
    opacity: 0.07; /* 较高的不透明度 */
    background-position: bottom center; /* 底部居中对齐背景图片 */
}
/* #endregion 模板背景图片样式 */

/* #region 文档页眉页脚图片样式 */
.header-image,
.footer-image {
    /* 页眉页脚图片基本样式 */
    display: block; /* 设置为块级元素 */
    width: 100%; /* 宽度设置为100%，填满容器 */
    height: auto; /* 高度自适应，保持原始比例 */
    margin: 0 auto; /* 居中显示 */
    object-fit: contain; /* 保持图片纵横比，完整显示图片 */
    max-height: auto; /* 限制最大高度，确保不占用太多A4空间 */
}

.header-image {
    /* 页眉图片特定样式 */
    margin-bottom: 5px; /* 减小下外边距 */
}

.footer-image {
    /* 页脚图片特定样式 */
    margin-top: 10px; /* 减小上外边距 */
    opacity: 0.9; /* 调整不透明度 */
    max-height: 60px; /* 页脚图片高度略小 */
}

/* 公司Logo容器样式优化 */
.company-logo-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80px; /* 固定高度，确保一致性 */
    overflow: hidden; /* 隐藏溢出内容 */
}
/* #endregion 文档页眉页脚图片样式 */

/* #region 响应式调整 */
@media (max-width: 768px) {
    /* 移动设备样式调整 */
    .company-logo {
        width: 100%; /* 保持宽度填满 */
    }

    .stamp-image {
        max-width: 70px; /* 减小印章最大宽度 */
        max-height: 70px; /* 减小印章最大高度 */
    }

    .header-image {
        width: 100%; /* 保持宽度填满 */
        margin-bottom: 1rem; /* 减小下外边距 */
    }

    .footer-image {
        width: 100%; /* 保持宽度填满 */
        margin-top: 1rem; /* 减小上外边距 */
    }
}

@media (max-width: 480px) {
    /* 小型移动设备样式调整 */
    .stamp-image {
        max-width: 60px; /* 进一步减小印章最大宽度 */
        max-height: 60px; /* 进一步减小印章最大高度 */
    }
}
/* #endregion 响应式调整 */

/* #region 公司信息样式 */
.company-info-container {
    /* 公司信息外层容器样式 */
    margin: 0 auto 10px; /* 上下外边距，底部增加间距 */
    padding: 0 0 10px; /* 底部增加内边距 */
    border-bottom: 1px solid #e5e7eb; /* 添加底部边框 */
    max-width: 90%; /* 限制最大宽度 */
}

.company-info {
    /* 公司信息容器样式 */
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
    font-size: 9pt;
    line-height: 1.3;
    color: #6b7280; /* 灰色文本 */
}

.company-info-item {
    /* 公司信息项目样式 */
    margin: 2px 0;
    padding: 0;
    white-space: normal;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 公司地址样式 */
.company-address {
    font-style: normal;
    margin-top: 5px;
}

/* 确保公司信息在小屏幕上正确显示 */
@media (max-width: 768px) {
    .company-info {
        font-size: 8pt;
    }

    .company-info-container {
        max-width: 100%;
        padding: 0 0 5px;
        margin-bottom: 5px;
    }
}
/* #endregion 公司信息样式 */
