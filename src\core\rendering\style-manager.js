/**
 * SmartOffice 2.0 样式管理器
 * 统一管理所有样式，确保预览和导出的样式一致性
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-12-19
 */

/**
 * 样式管理器类
 * 负责样式的编译、合并、优化和应用
 */
export class StyleManager {
    /**
     * 构造函数
     * @param {Object} config - 样式配置
     */
    constructor(config = {}) {
        this.config = config;
        
        // 样式存储
        this.baseStyles = new Map();
        this.themeStyles = new Map();
        this.componentStyles = new Map();
        this.formatStyles = new Map();
        this.customStyles = new Map();
        
        // CSS变量系统
        this.cssVariables = new Map();
        
        // 样式缓存
        this.compiledCache = new Map();
        
        // 样式优先级
        this.stylePriority = {
            base: 1,
            theme: 2,
            component: 3,
            format: 4,
            custom: 5,
            inline: 6
        };
        
        // 支持的输出格式
        this.supportedFormats = ['html', 'pdf', 'image', 'print'];
        
        this._initializeBaseStyles();
        this._initializeCSSVariables();
        
        console.log('[StyleManager] 样式管理器初始化完成');
    }
    
    /**
     * 编译样式
     * @param {string} theme - 主题名称
     * @param {Array} components - 组件列表
     * @param {string} format - 输出格式
     * @returns {Promise<Object>} 编译后的样式
     */
    async compileStyles(theme = 'default', components = [], format = 'html') {
        const cacheKey = this._generateCacheKey(theme, components, format);
        
        // 检查缓存
        if (this.compiledCache.has(cacheKey)) {
            console.log('[StyleManager] 使用缓存的样式');
            return this.compiledCache.get(cacheKey);
        }
        
        try {
            console.log(`[StyleManager] 开始编译样式 - 主题: ${theme}, 格式: ${format}`);
            
            // 收集所有样式
            const styleCollection = await this._collectStyles(theme, components, format);
            
            // 处理CSS变量
            const processedStyles = this._processCSSVariables(styleCollection, format);
            
            // 合并样式
            const mergedStyles = this._mergeStyles(processedStyles);
            
            // 优化样式
            const optimizedStyles = this._optimizeStyles(mergedStyles, format);
            
            // 生成最终样式
            const finalStyles = this._generateFinalStyles(optimizedStyles, format);
            
            // 缓存结果
            this.compiledCache.set(cacheKey, finalStyles);
            
            console.log('[StyleManager] 样式编译完成');
            return finalStyles;
            
        } catch (error) {
            console.error('[StyleManager] 样式编译失败:', error);
            throw new Error(`样式编译失败: ${error.message}`);
        }
    }
    
    /**
     * 注册基础样式
     * @param {string} name - 样式名称
     * @param {Object} styles - 样式对象
     */
    registerBaseStyles(name, styles) {
        this.baseStyles.set(name, styles);
        this._clearCache();
        console.log(`[StyleManager] 基础样式已注册: ${name}`);
    }
    
    /**
     * 注册主题样式
     * @param {string} theme - 主题名称
     * @param {Object} styles - 样式对象
     */
    registerThemeStyles(theme, styles) {
        this.themeStyles.set(theme, styles);
        this._clearCache();
        console.log(`[StyleManager] 主题样式已注册: ${theme}`);
    }
    
    /**
     * 注册组件样式
     * @param {string} component - 组件名称
     * @param {Object} styles - 样式对象
     */
    registerComponentStyles(component, styles) {
        this.componentStyles.set(component, styles);
        this._clearCache();
        console.log(`[StyleManager] 组件样式已注册: ${component}`);
    }
    
    /**
     * 注册格式样式
     * @param {string} format - 格式名称
     * @param {Object} styles - 样式对象
     */
    registerFormatStyles(format, styles) {
        this.formatStyles.set(format, styles);
        this._clearCache();
        console.log(`[StyleManager] 格式样式已注册: ${format}`);
    }
    
    /**
     * 设置CSS变量
     * @param {string} name - 变量名
     * @param {string} value - 变量值
     * @param {string} format - 格式（可选）
     */
    setCSSVariable(name, value, format = 'all') {
        if (!this.cssVariables.has(format)) {
            this.cssVariables.set(format, new Map());
        }
        
        this.cssVariables.get(format).set(name, value);
        this._clearCache();
        
        console.log(`[StyleManager] CSS变量已设置: ${name} = ${value} (${format})`);
    }
    
    /**
     * 获取CSS变量
     * @param {string} name - 变量名
     * @param {string} format - 格式
     * @returns {string} 变量值
     */
    getCSSVariable(name, format = 'all') {
        // 优先获取格式特定的变量
        if (this.cssVariables.has(format) && this.cssVariables.get(format).has(name)) {
            return this.cssVariables.get(format).get(name);
        }
        
        // 回退到通用变量
        if (this.cssVariables.has('all') && this.cssVariables.get('all').has(name)) {
            return this.cssVariables.get('all').get(name);
        }
        
        return null;
    }
    
    /**
     * 生成CSS字符串
     * @param {Object} styles - 样式对象
     * @param {string} format - 输出格式
     * @returns {string} CSS字符串
     */
    generateCSS(styles, format = 'html') {
        let css = '';
        
        // 添加CSS变量定义
        css += this._generateCSSVariables(format);
        
        // 添加样式规则
        css += this._generateCSSRules(styles);
        
        // 格式特定的优化
        css = this._optimizeCSSForFormat(css, format);
        
        return css;
    }
    
    /**
     * 验证样式
     * @param {Object} styles - 样式对象
     * @returns {Object} 验证结果
     */
    validateStyles(styles) {
        const errors = [];
        const warnings = [];
        
        // 验证样式结构
        if (!styles || typeof styles !== 'object') {
            errors.push('样式必须是对象类型');
            return { isValid: false, errors, warnings };
        }
        
        // 验证CSS属性
        for (const [selector, rules] of Object.entries(styles)) {
            if (typeof rules !== 'object') {
                warnings.push(`选择器 '${selector}' 的规则不是对象类型`);
                continue;
            }
            
            for (const [property, value] of Object.entries(rules)) {
                // 验证CSS属性名
                if (!this._isValidCSSProperty(property)) {
                    warnings.push(`无效的CSS属性: ${property}`);
                }
                
                // 验证CSS值
                if (!this._isValidCSSValue(property, value)) {
                    warnings.push(`CSS属性 '${property}' 的值无效: ${value}`);
                }
            }
        }
        
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
    
    /**
     * 更新配置
     * @param {Object} newConfig - 新配置
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this._clearCache();
        console.log('[StyleManager] 配置已更新');
    }
    
    /**
     * 清除缓存
     */
    clearCache() {
        this._clearCache();
        console.log('[StyleManager] 缓存已清除');
    }
    
    /**
     * 获取统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            baseStylesCount: this.baseStyles.size,
            themeStylesCount: this.themeStyles.size,
            componentStylesCount: this.componentStyles.size,
            formatStylesCount: this.formatStyles.size,
            customStylesCount: this.customStyles.size,
            cssVariablesCount: Array.from(this.cssVariables.values())
                .reduce((total, map) => total + map.size, 0),
            cacheSize: this.compiledCache.size
        };
    }
    
    /**
     * 销毁样式管理器
     */
    destroy() {
        this.baseStyles.clear();
        this.themeStyles.clear();
        this.componentStyles.clear();
        this.formatStyles.clear();
        this.customStyles.clear();
        this.cssVariables.clear();
        this.compiledCache.clear();
        
        console.log('[StyleManager] 样式管理器已销毁');
    }
    
    // ==================== 私有方法 ====================
    
    /**
     * 初始化基础样式
     * @private
     */
    _initializeBaseStyles() {
        // A4页面基础样式
        this.registerBaseStyles('page', {
            '.document-container': {
                width: 'var(--a4-width-px)',
                height: 'var(--a4-height-px)',
                margin: '0 auto',
                position: 'relative',
                backgroundColor: 'var(--color-background)',
                fontFamily: 'var(--font-family-primary)',
                fontSize: 'var(--font-size-base)',
                color: 'var(--color-text)',
                lineHeight: '1.5'
            },
            '.content-area': {
                position: 'absolute',
                top: 'var(--margin-top)',
                left: 'var(--margin-left)',
                right: 'var(--margin-right)',
                bottom: 'var(--margin-bottom)',
                overflow: 'hidden'
            }
        });
        
        // 页眉页脚基础样式
        this.registerBaseStyles('header-footer', {
            '.document-header': {
                position: 'absolute',
                top: '0',
                left: '0',
                right: '0',
                height: 'var(--header-height)',
                zIndex: '10',
                borderBottom: '1px solid var(--color-border)'
            },
            '.document-footer': {
                position: 'absolute',
                bottom: '0',
                left: '0',
                right: '0',
                height: 'var(--footer-height)',
                zIndex: '10',
                borderTop: '1px solid var(--color-border)'
            }
        });
        
        // 印章基础样式
        this.registerBaseStyles('stamp', {
            '.document-stamp': {
                position: 'absolute',
                width: 'var(--stamp-width)',
                height: 'var(--stamp-height)',
                bottom: 'var(--stamp-bottom)',
                right: 'var(--stamp-right)',
                transform: 'rotate(var(--stamp-rotation))',
                zIndex: '10000',
                pointerEvents: 'none'
            },
            '.document-stamp img': {
                width: '100%',
                height: '100%',
                objectFit: 'contain'
            }
        });
    }
    
    /**
     * 初始化CSS变量
     * @private
     */
    _initializeCSSVariables() {
        // 通用变量
        const commonVariables = new Map([
            // A4纸张尺寸
            ['--a4-width', '210mm'],
            ['--a4-height', '297mm'],
            ['--a4-width-px', '794px'],
            ['--a4-height-px', '1123px'],
            
            // 页边距
            ['--margin-top', '20mm'],
            ['--margin-bottom', '20mm'],
            ['--margin-left', '15mm'],
            ['--margin-right', '15mm'],
            
            // 页眉页脚
            ['--header-height', '15mm'],
            ['--footer-height', '10mm'],
            
            // 印章定位
            ['--stamp-width', '20mm'],
            ['--stamp-height', '20mm'],
            ['--stamp-bottom', '25%'],
            ['--stamp-right', '10%'],
            ['--stamp-rotation', '-5deg'],
            
            // 字体
            ['--font-family-primary', '"Microsoft YaHei", Arial, sans-serif'],
            ['--font-size-base', '12px'],
            ['--font-size-title', '18px'],
            ['--font-size-small', '10px'],
            
            // 颜色
            ['--color-text', '#333333'],
            ['--color-border', '#cccccc'],
            ['--color-background', '#ffffff']
        ]);
        
        this.cssVariables.set('all', commonVariables);
        
        // PDF特定变量
        const pdfVariables = new Map([
            ['--margin-top', '76px'],
            ['--margin-bottom', '76px'],
            ['--margin-left', '57px'],
            ['--margin-right', '57px'],
            ['--header-height', '57px'],
            ['--footer-height', '38px']
        ]);
        
        this.cssVariables.set('pdf', pdfVariables);
        
        // 打印特定变量
        const printVariables = new Map([
            ['--margin-top', '20mm'],
            ['--margin-bottom', '20mm'],
            ['--margin-left', '15mm'],
            ['--margin-right', '15mm']
        ]);
        
        this.cssVariables.set('print', printVariables);
    }
    
    /**
     * 收集样式
     * @param {string} theme - 主题
     * @param {Array} components - 组件列表
     * @param {string} format - 格式
     * @returns {Promise<Object>} 样式集合
     * @private
     */
    async _collectStyles(theme, components, format) {
        const styleCollection = {
            base: {},
            theme: {},
            components: {},
            format: {},
            custom: {}
        };
        
        // 收集基础样式
        for (const [name, styles] of this.baseStyles) {
            styleCollection.base[name] = styles;
        }
        
        // 收集主题样式
        if (this.themeStyles.has(theme)) {
            styleCollection.theme = this.themeStyles.get(theme);
        }
        
        // 收集组件样式
        for (const component of components) {
            const componentName = component.name || component.type;
            if (this.componentStyles.has(componentName)) {
                styleCollection.components[componentName] = this.componentStyles.get(componentName);
            }
        }
        
        // 收集格式样式
        if (this.formatStyles.has(format)) {
            styleCollection.format = this.formatStyles.get(format);
        }
        
        // 收集自定义样式
        for (const [name, styles] of this.customStyles) {
            styleCollection.custom[name] = styles;
        }
        
        return styleCollection;
    }
    
    /**
     * 处理CSS变量
     * @param {Object} styleCollection - 样式集合
     * @param {string} format - 格式
     * @returns {Object} 处理后的样式
     * @private
     */
    _processCSSVariables(styleCollection, format) {
        const processedStyles = JSON.parse(JSON.stringify(styleCollection));
        
        // 递归处理所有样式中的CSS变量
        const processObject = (obj) => {
            for (const [key, value] of Object.entries(obj)) {
                if (typeof value === 'object' && value !== null) {
                    processObject(value);
                } else if (typeof value === 'string' && value.includes('var(')) {
                    obj[key] = this._resolveCSSVariable(value, format);
                }
            }
        };
        
        processObject(processedStyles);
        return processedStyles;
    }
    
    /**
     * 解析CSS变量
     * @param {string} value - 包含变量的值
     * @param {string} format - 格式
     * @returns {string} 解析后的值
     * @private
     */
    _resolveCSSVariable(value, format) {
        return value.replace(/var\(([^)]+)\)/g, (match, varName) => {
            const resolvedValue = this.getCSSVariable(varName.trim(), format);
            return resolvedValue || match;
        });
    }
    
    /**
     * 合并样式
     * @param {Object} styleCollection - 样式集合
     * @returns {Object} 合并后的样式
     * @private
     */
    _mergeStyles(styleCollection) {
        const mergedStyles = {};
        
        // 按优先级合并样式
        const priorities = ['base', 'theme', 'components', 'format', 'custom'];
        
        for (const priority of priorities) {
            const styles = styleCollection[priority];
            if (styles && typeof styles === 'object') {
                this._deepMerge(mergedStyles, styles);
            }
        }
        
        return mergedStyles;
    }
    
    /**
     * 深度合并对象
     * @param {Object} target - 目标对象
     * @param {Object} source - 源对象
     * @private
     */
    _deepMerge(target, source) {
        for (const [key, value] of Object.entries(source)) {
            if (value && typeof value === 'object' && !Array.isArray(value)) {
                if (!target[key] || typeof target[key] !== 'object') {
                    target[key] = {};
                }
                this._deepMerge(target[key], value);
            } else {
                target[key] = value;
            }
        }
    }
    
    /**
     * 优化样式
     * @param {Object} styles - 样式对象
     * @param {string} format - 格式
     * @returns {Object} 优化后的样式
     * @private
     */
    _optimizeStyles(styles, format) {
        let optimizedStyles = JSON.parse(JSON.stringify(styles));
        
        // 移除重复的样式规则
        optimizedStyles = this._removeDuplicateRules(optimizedStyles);
        
        // 合并相似的选择器
        optimizedStyles = this._mergeSimilarSelectors(optimizedStyles);
        
        // 格式特定的优化
        optimizedStyles = this._applyFormatOptimizations(optimizedStyles, format);
        
        return optimizedStyles;
    }
    
    /**
     * 移除重复的样式规则
     * @param {Object} styles - 样式对象
     * @returns {Object} 优化后的样式
     * @private
     */
    _removeDuplicateRules(styles) {
        // 实现去重逻辑
        return styles;
    }
    
    /**
     * 合并相似的选择器
     * @param {Object} styles - 样式对象
     * @returns {Object} 优化后的样式
     * @private
     */
    _mergeSimilarSelectors(styles) {
        // 实现选择器合并逻辑
        return styles;
    }
    
    /**
     * 应用格式特定的优化
     * @param {Object} styles - 样式对象
     * @param {string} format - 格式
     * @returns {Object} 优化后的样式
     * @private
     */
    _applyFormatOptimizations(styles, format) {
        if (format === 'pdf') {
            // PDF特定优化：移除不支持的CSS属性
            return this._removePDFUnsupportedProperties(styles);
        } else if (format === 'print') {
            // 打印特定优化
            return this._applyPrintOptimizations(styles);
        }
        
        return styles;
    }
    
    /**
     * 移除PDF不支持的CSS属性
     * @param {Object} styles - 样式对象
     * @returns {Object} 优化后的样式
     * @private
     */
    _removePDFUnsupportedProperties(styles) {
        const unsupportedProperties = [
            'animation',
            'transition',
            'transform-style',
            'perspective',
            'filter'
        ];
        
        const removeUnsupported = (obj) => {
            for (const [key, value] of Object.entries(obj)) {
                if (typeof value === 'object' && value !== null) {
                    removeUnsupported(value);
                } else if (unsupportedProperties.includes(key)) {
                    delete obj[key];
                }
            }
        };
        
        const optimizedStyles = JSON.parse(JSON.stringify(styles));
        removeUnsupported(optimizedStyles);
        return optimizedStyles;
    }
    
    /**
     * 应用打印优化
     * @param {Object} styles - 样式对象
     * @returns {Object} 优化后的样式
     * @private
     */
    _applyPrintOptimizations(styles) {
        // 打印优化逻辑
        return styles;
    }
    
    /**
     * 生成最终样式
     * @param {Object} styles - 样式对象
     * @param {string} format - 格式
     * @returns {Object} 最终样式
     * @private
     */
    _generateFinalStyles(styles, format) {
        return {
            css: this.generateCSS(styles, format),
            rules: styles,
            variables: this._getVariablesForFormat(format),
            format: format,
            timestamp: Date.now()
        };
    }
    
    /**
     * 生成CSS变量定义
     * @param {string} format - 格式
     * @returns {string} CSS变量字符串
     * @private
     */
    _generateCSSVariables(format) {
        let css = ':root {\n';
        
        // 添加通用变量
        if (this.cssVariables.has('all')) {
            for (const [name, value] of this.cssVariables.get('all')) {
                css += `  ${name}: ${value};\n`;
            }
        }
        
        // 添加格式特定变量
        if (this.cssVariables.has(format)) {
            for (const [name, value] of this.cssVariables.get(format)) {
                css += `  ${name}: ${value};\n`;
            }
        }
        
        css += '}\n\n';
        return css;
    }
    
    /**
     * 生成CSS规则
     * @param {Object} styles - 样式对象
     * @returns {string} CSS规则字符串
     * @private
     */
    _generateCSSRules(styles) {
        let css = '';
        
        const generateRules = (obj, parentSelector = '') => {
            for (const [key, value] of Object.entries(obj)) {
                if (typeof value === 'object' && value !== null) {
                    const selector = parentSelector ? `${parentSelector} ${key}` : key;
                    if (this._isSelector(key)) {
                        css += `${selector} {\n`;
                        generateRules(value, '');
                        css += '}\n\n';
                    } else {
                        generateRules(value, selector);
                    }
                } else {
                    css += `  ${this._kebabCase(key)}: ${value};\n`;
                }
            }
        };
        
        generateRules(styles);
        return css;
    }
    
    /**
     * 判断是否为CSS选择器
     * @param {string} str - 字符串
     * @returns {boolean} 是否为选择器
     * @private
     */
    _isSelector(str) {
        return str.startsWith('.') || str.startsWith('#') || str.startsWith('@') || 
               str.includes(':') || str.includes('[') || /^[a-zA-Z]/.test(str);
    }
    
    /**
     * 转换为kebab-case
     * @param {string} str - 字符串
     * @returns {string} kebab-case字符串
     * @private
     */
    _kebabCase(str) {
        return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();
    }
    
    /**
     * 优化CSS格式
     * @param {string} css - CSS字符串
     * @param {string} format - 格式
     * @returns {string} 优化后的CSS
     * @private
     */
    _optimizeCSSForFormat(css, format) {
        if (format === 'html') {
            // HTML格式保持原样
            return css;
        } else if (format === 'pdf') {
            // PDF格式移除媒体查询
            return css.replace(/@media[^{]+\{[^{}]*\{[^{}]*\}[^{}]*\}/g, '');
        } else if (format === 'print') {
            // 打印格式只保留打印相关的样式
            return css;
        }
        
        return css;
    }
    
    /**
     * 获取格式对应的变量
     * @param {string} format - 格式
     * @returns {Object} 变量对象
     * @private
     */
    _getVariablesForFormat(format) {
        const variables = {};
        
        // 添加通用变量
        if (this.cssVariables.has('all')) {
            for (const [name, value] of this.cssVariables.get('all')) {
                variables[name] = value;
            }
        }
        
        // 添加格式特定变量
        if (this.cssVariables.has(format)) {
            for (const [name, value] of this.cssVariables.get(format)) {
                variables[name] = value;
            }
        }
        
        return variables;
    }
    
    /**
     * 验证CSS属性
     * @param {string} property - CSS属性
     * @returns {boolean} 是否有效
     * @private
     */
    _isValidCSSProperty(property) {
        // 简单的CSS属性验证
        const validProperties = [
            'color', 'background', 'font', 'margin', 'padding', 'border',
            'width', 'height', 'position', 'top', 'left', 'right', 'bottom',
            'display', 'flex', 'grid', 'text', 'line-height', 'z-index'
        ];
        
        return validProperties.some(valid => property.startsWith(valid)) || 
               property.startsWith('--'); // CSS变量
    }
    
    /**
     * 验证CSS值
     * @param {string} property - CSS属性
     * @param {string} value - CSS值
     * @returns {boolean} 是否有效
     * @private
     */
    _isValidCSSValue(property, value) {
        // 简单的CSS值验证
        return typeof value === 'string' || typeof value === 'number';
    }
    
    /**
     * 生成缓存键
     * @param {string} theme - 主题
     * @param {Array} components - 组件列表
     * @param {string} format - 格式
     * @returns {string} 缓存键
     * @private
     */
    _generateCacheKey(theme, components, format) {
        const componentNames = components.map(c => c.name || c.type).sort();
        return `${theme}-${componentNames.join(',')}-${format}`;
    }
    
    /**
     * 清除缓存
     * @private
     */
    _clearCache() {
        this.compiledCache.clear();
    }
}