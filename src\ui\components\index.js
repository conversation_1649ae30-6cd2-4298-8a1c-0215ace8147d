/**
 * @file UI组件导出 - SmartOffice UI组件系统统一导出
 * <AUTHOR> Team
 * @description 
 * 统一导出所有UI组件，提供便捷的组件访问接口
 * 包含基础组件、表单组件、数据展示组件、导航组件、布局组件等
 */

// #region 基础组件
export { BaseComponent } from './base-component.js';
export { 
    Button, 
    ButtonGroup,
    createButton,
    createButtonGroup,
    createPrimaryButton,
    createSecondaryButton,
    createDangerButton
} from './button.js';

// #region 输入组件
export { 
    Input, 
    TextArea, 
    Select,
    createInput,
    createTextArea,
    createSelect,
    createPasswordInput,
    createEmailInput,
    createNumberInput,
    createSearchInput
} from './input.js';

// #region 表单组件
export { 
    Form, 
    FormField,
    createForm,
    createFormField,
    createValidationForm
} from './form.js';

// #region 预览组件
export { 
    DocumentPreview,
    PreviewToolbar,
    createDocumentPreview,
    createPreviewToolbar
} from './preview.js';

// #region 导出控制组件
export { 
    ExportControl,
    ExportFormatSelector,
    ExportProgressDialog,
    createExportControl,
    createExportFormatSelector,
    createExportProgressDialog
} from './export-control.js';

// #region 高级UI组件
export { 
    Dialog,
    DialogManager,
    createDialog,
    createConfirmDialog,
    createPromptDialog,
    createInputDialog
} from './dialog.js';

export { 
    Notification,
    NotificationManager,
    createNotification,
    createSuccessNotification,
    createWarningNotification,
    createErrorNotification,
    createInfoNotification
} from './notification.js';

// #region 数据展示组件
export { 
    Table,
    TableColumn,
    createTable,
    createSelectableTable,
    createPaginationTable,
    createTableColumn
} from './table.js';

export { 
    List,
    ListItem,
    createList,
    createVirtualList,
    createGridList,
    createSelectableList,
    createListItem
} from './list.js';

// #region 导航组件
export { 
    Navigation,
    Breadcrumb,
    createNavigation,
    createHorizontalNavigation,
    createVerticalNavigation,
    createBreadcrumb
} from './navigation.js';

// #region 布局组件
export { 
    Layout,
    Container,
    Grid,
    Panel,
    createLayout,
    createVerticalLayout,
    createHorizontalLayout,
    createContainer,
    createFluidContainer,
    createGrid,
    createPanel,
    createCollapsiblePanel
} from './layout.js';

// #region 组件管理
export { ComponentRegistry, UIComponentManager } from './component-registry.js';

// #region 组件注册
// 导入组件管理器
import { ComponentRegistry } from './component-registry.js';
import { BaseComponent } from './base-component.js';
import { Button, ButtonGroup } from './button.js';
import { Input, TextArea, Select } from './input.js';
import { Form, FormField } from './form.js';
import { DocumentPreview, PreviewToolbar } from './preview.js';
import { ExportControl, ExportFormatSelector, ExportProgressDialog } from './export-control.js';
import { Dialog, DialogManager } from './dialog.js';
import { Notification, NotificationManager } from './notification.js';
import { Table, TableColumn } from './table.js';
import { List, ListItem } from './list.js';
import { Navigation, Breadcrumb } from './navigation.js';
import { Layout, Container, Grid, Panel } from './layout.js';

/**
 * 注册所有UI组件到组件注册表
 * @function registerAllComponents
 * @description 将所有UI组件注册到ComponentRegistry中，便于统一管理和访问
 */
function registerAllComponents() {
    const registry = ComponentRegistry.getInstance();
    
    // 基础组件
    registry.register('BaseComponent', BaseComponent);
    registry.register('Button', Button);
    registry.register('ButtonGroup', ButtonGroup);
    
    // 输入组件
    registry.register('Input', Input);
    registry.register('TextArea', TextArea);
    registry.register('Select', Select);
    
    // 表单组件
    registry.register('Form', Form);
    registry.register('FormField', FormField);
    
    // 预览组件
    registry.register('DocumentPreview', DocumentPreview);
    registry.register('PreviewToolbar', PreviewToolbar);
    
    // 导出控制组件
    registry.register('ExportControl', ExportControl);
    registry.register('ExportFormatSelector', ExportFormatSelector);
    registry.register('ExportProgressDialog', ExportProgressDialog);
    
    // 高级UI组件
    registry.register('Dialog', Dialog);
    registry.register('DialogManager', DialogManager);
    registry.register('Notification', Notification);
    registry.register('NotificationManager', NotificationManager);
    
    // 数据展示组件
    registry.register('Table', Table);
    registry.register('TableColumn', TableColumn);
    registry.register('List', List);
    registry.register('ListItem', ListItem);
    
    // 导航组件
    registry.register('Navigation', Navigation);
    registry.register('Breadcrumb', Breadcrumb);
    
    // 布局组件
    registry.register('Layout', Layout);
    registry.register('Container', Container);
    registry.register('Grid', Grid);
    registry.register('Panel', Panel);
}

// 自动注册所有组件
registerAllComponents();

/**
 * 获取组件注册表实例
 * @function getComponentRegistry
 * @returns {ComponentRegistry} 组件注册表实例
 */
export function getComponentRegistry() {
    return ComponentRegistry.getInstance();
}

/**
 * 创建组件实例
 * @function createComponent
 * @param {string} componentName - 组件名称
 * @param {Object} config - 组件配置
 * @returns {BaseComponent} 组件实例
 */
export function createComponent(componentName, config = {}) {
    const registry = ComponentRegistry.getInstance();
    return registry.create(componentName, config);
}

// #region 组件类型映射
/**
 * 组件类型映射表
 * @constant COMPONENT_TYPES
 * @description 提供组件名称到类的映射，便于类型检查和自动完成
 */
export const COMPONENT_TYPES = {
    // 基础组件
    BASE_COMPONENT: 'BaseComponent',
    BUTTON: 'Button',
    BUTTON_GROUP: 'ButtonGroup',
    
    // 输入组件
    INPUT: 'Input',
    TEXTAREA: 'TextArea',
    SELECT: 'Select',
    
    // 表单组件
    FORM: 'Form',
    FORM_FIELD: 'FormField',
    
    // 预览组件
    DOCUMENT_PREVIEW: 'DocumentPreview',
    PREVIEW_TOOLBAR: 'PreviewToolbar',
    
    // 导出控制组件
    EXPORT_CONTROL: 'ExportControl',
    EXPORT_FORMAT_SELECTOR: 'ExportFormatSelector',
    EXPORT_PROGRESS_DIALOG: 'ExportProgressDialog',
    
    // 高级UI组件
    DIALOG: 'Dialog',
    DIALOG_MANAGER: 'DialogManager',
    NOTIFICATION: 'Notification',
    NOTIFICATION_MANAGER: 'NotificationManager',
    
    // 数据展示组件
    TABLE: 'Table',
    TABLE_COLUMN: 'TableColumn',
    LIST: 'List',
    LIST_ITEM: 'ListItem',
    
    // 导航组件
    NAVIGATION: 'Navigation',
    BREADCRUMB: 'Breadcrumb',
    
    // 布局组件
    LAYOUT: 'Layout',
    CONTAINER: 'Container',
    GRID: 'Grid',
    PANEL: 'Panel'
};

// #region 组件分类
/**
 * 组件分类映射
 * @constant COMPONENT_CATEGORIES
 * @description 按功能分类组织组件，便于查找和使用
 */
export const COMPONENT_CATEGORIES = {
    BASIC: [
        'BaseComponent',
        'Button',
        'ButtonGroup'
    ],
    INPUT: [
        'Input',
        'TextArea',
        'Select'
    ],
    FORM: [
        'Form',
        'FormField'
    ],
    PREVIEW: [
        'DocumentPreview',
        'PreviewToolbar'
    ],
    EXPORT: [
        'ExportControl',
        'ExportFormatSelector',
        'ExportProgressDialog'
    ],
    ADVANCED: [
        'Dialog',
        'DialogManager',
        'Notification',
        'NotificationManager'
    ],
    DATA_DISPLAY: [
        'Table',
        'TableColumn',
        'List',
        'ListItem'
    ],
    NAVIGATION: [
        'Navigation',
        'Breadcrumb'
    ],
    LAYOUT: [
        'Layout',
        'Container',
        'Grid',
        'Panel'
    ]
};

// #region 组件统计信息
/**
 * 组件统计信息
 * @constant COMPONENT_STATS
 * @description 提供组件系统的统计信息
 */
export const COMPONENT_STATS = {
    totalComponents: Object.keys(COMPONENT_TYPES).length,
    categories: Object.keys(COMPONENT_CATEGORIES).length,
    componentsByCategory: Object.entries(COMPONENT_CATEGORIES).reduce((acc, [category, components]) => {
        acc[category] = components.length;
        return acc;
    }, {}),
    lastUpdated: new Date().toISOString()
};

// #region 验证函数
/**
 * 验证组件是否已注册
 * @function isComponentRegistered
 * @param {string} componentName - 组件名称
 * @returns {boolean} 是否已注册
 */
export function isComponentRegistered(componentName) {
    const registry = ComponentRegistry.getInstance();
    return registry.has(componentName);
}

/**
 * 获取所有已注册的组件名称
 * @function getRegisteredComponents
 * @returns {Array<string>} 已注册组件名称列表
 */
export function getRegisteredComponents() {
    const registry = ComponentRegistry.getInstance();
    return registry.getRegisteredComponents();
}

/**
 * 按分类获取组件
 * @function getComponentsByCategory
 * @param {string} category - 分类名称
 * @returns {Array<string>} 该分类下的组件列表
 */
export function getComponentsByCategory(category) {
    return COMPONENT_CATEGORIES[category] || [];
}

/**
 * 获取组件信息
 * @function getComponentInfo
 * @param {string} componentName - 组件名称
 * @returns {Object} 组件信息
 */
export function getComponentInfo(componentName) {
    const registry = ComponentRegistry.getInstance();
    const componentClass = registry.get(componentName);
    
    if (!componentClass) {
        return null;
    }
    
    // 查找组件所属分类
    const category = Object.entries(COMPONENT_CATEGORIES).find(([cat, components]) => 
        components.includes(componentName)
    );
    
    return {
        name: componentName,
        class: componentClass,
        category: category ? category[0] : 'UNKNOWN',
        registered: true,
        description: componentClass.description || '暂无描述'
    };
}

/**
 * 批量创建组件
 * @function createComponents
 * @param {Array<Object>} componentConfigs - 组件配置数组
 * @returns {Array<BaseComponent>} 组件实例数组
 */
export function createComponents(componentConfigs) {
    const registry = ComponentRegistry.getInstance();
    return componentConfigs.map(config => {
        const { type, ...options } = config;
        return registry.create(type, options);
    });
}

// #endregion

/**
 * 输出组件系统信息到控制台
 * @function logComponentSystemInfo
 */
export function logComponentSystemInfo() {
    console.group('🎨 SmartOffice UI Component System');
    console.log('📊 组件统计:', COMPONENT_STATS);
    console.log('📂 组件分类:', COMPONENT_CATEGORIES);
    console.log('🔧 已注册组件:', getRegisteredComponents());
    console.groupEnd();
}

// 开发模式下输出系统信息
if (process?.env?.NODE_ENV === 'development') {
    logComponentSystemInfo();
} 