/**
 * @file 导出系统统一入口 - 集成所有导出器和导出功能
 * <AUTHOR> Team
 * @description 
 * 这个文件是导出系统的统一入口，提供：
 * - 所有导出器类的统一导出
 * - 预设导出器的快速创建
 * - 导出管理器的统一接口
 * - 便捷的导出函数
 * - 全局导出器注册和管理
 */

// #region 导入所有导出器模块
import { 
    BaseExporter, 
    ExporterRegistry, 
    ExportEngine,
    defaultExporterRegistry,
    defaultExportEngine
} from './base-exporter.js';

import { 
    PDFExporter,
    createPDFExporter,
    createPresetPDFExporter,
    exportToPDF
} from './pdf-exporter.js';

import {
    ImageExporter,
    createImageExporter,
    createPresetImageExporter,
    exportToImage
} from './image-exporter.js';

import {
    PrintExporter,
    createPrintExporter,
    createPresetPrintExporter,
    printDocument
} from './print-exporter.js';

import { EXPORT_EVENTS } from '../core/events/event-types.js';
import { getLogger } from '../core/utils/logger.js';
// #endregion

// #region ExportManager 导出管理器
/**
 * @class ExportManager - 导出管理器
 * @description 统一管理所有导出器，提供高级导出功能和便捷接口
 */
export class ExportManager {
    /**
     * 构造函数 - 初始化导出管理器
     * @param {Object} config - 配置对象
     * @param {ExporterRegistry} config.registry - 导出器注册表
     * @param {ExportEngine} config.engine - 导出引擎
     */
    constructor(config = {}) {
        // 初始化日志记录器
        this.logger = getLogger();
        this.logger.startPerformanceMark('export_manager_construction', 'ExportManager', 'constructor');
        this.logger.info('ExportManager', 'constructor', '开始构造导出管理器', {
            configKeys: Object.keys(config)
        });
        
        // 导出器注册表和引擎
        this.registry = config.registry || new ExporterRegistry();
        this.engine = config.engine || new ExportEngine(this.registry);
        
        this.logger.debug('ExportManager', 'constructor', '注册表和引擎初始化完成', {
            hasCustomRegistry: !!config.registry,
            hasCustomEngine: !!config.engine
        });
        
        // 管理器配置
        this.config = {
            autoRegisterDefaults: config.autoRegisterDefaults !== false,
            defaultExporterType: config.defaultExporterType || 'pdf',
            enableGlobalAccess: config.enableGlobalAccess !== false,
            ...config.managerConfig
        };
        
        this.logger.trace('ExportManager', 'constructor', '管理器配置设置完成', {
            config: this.config
        });
        
        // 预设导出器映射
        this.presetExporters = new Map();
        
        // 导出历史
        this.exportHistory = [];
        this.maxHistorySize = config.maxHistorySize || 100;
        
        this.logger.trace('ExportManager', 'constructor', '存储结构初始化完成');
        
        // 初始化管理器
        this.logger.debug('ExportManager', 'constructor', '开始初始化管理器功能');
        this._initializeManager();
        
        const constructionDuration = this.logger.endPerformanceMark('export_manager_construction', 'ExportManager', 'constructor');
        this.logger.info('ExportManager', 'constructor', '✅ 导出管理器构造完成', {
            duration: `${constructionDuration?.toFixed(2)}ms`,
            registrySize: this.registry.getAll().length,
            presetExportersCount: this.presetExporters.size,
            enabledFeatures: {
                autoRegisterDefaults: this.config.autoRegisterDefaults,
                globalAccess: this.config.enableGlobalAccess
            }
        });
        
        // 记录构造统计
        this.logger.incrementCounter('export_manager_instances', 'ExportManager');
    }

    /**
     * 初始化管理器 - 注册默认导出器和设置全局访问
     * @private
     */
    _initializeManager() {
        if (this.config.autoRegisterDefaults) {
            this._registerDefaultExporters();
        }
        
        if (this.config.enableGlobalAccess) {
            this._setupGlobalAccess();
        }
        
        // 设置默认导出器
        if (this.config.defaultExporterType) {
            this._setDefaultExporter();
        }
    }

    /**
     * 注册默认导出器 - 注册PDF、图片、打印导出器
     * @private
     */
    _registerDefaultExporters() {
        try {
            // 注册PDF导出器
            const pdfExporter = createPDFExporter({ name: 'default-pdf-exporter' });
            this.registry.register(pdfExporter);
            
            // 注册图片导出器
            const imageExporter = createImageExporter({ name: 'default-image-exporter' });
            this.registry.register(imageExporter);
            
            // 注册打印导出器
            const printExporter = createPrintExporter({ name: 'default-print-exporter' });
            this.registry.register(printExporter);
            
            // 注册预设导出器
            this._registerPresetExporters();
            
        } catch (error) {
            console.warn('注册默认导出器失败:', error.message);
        }
    }

    /**
     * 注册预设导出器 - 注册各种预设配置的导出器
     * @private
     */
    _registerPresetExporters() {
        // PDF预设导出器
        const pdfPresets = ['default', 'high-quality', 'print', 'web', 'archive'];
        pdfPresets.forEach(preset => {
            try {
                const exporter = createPresetPDFExporter(preset);
                this.registry.register(exporter);
                this.presetExporters.set(`pdf-${preset}`, exporter);
            } catch (error) {
                console.warn(`注册PDF预设导出器 ${preset} 失败:`, error.message);
            }
        });
        
        // 图片预设导出器
        const imagePresets = ['default', 'high-quality', 'web', 'thumbnail', 'print'];
        imagePresets.forEach(preset => {
            try {
                const exporter = createPresetImageExporter(preset);
                this.registry.register(exporter);
                this.presetExporters.set(`image-${preset}`, exporter);
            } catch (error) {
                console.warn(`注册图片预设导出器 ${preset} 失败:`, error.message);
            }
        });
        
        // 打印预设导出器
        const printPresets = ['default', 'minimal', 'high-quality', 'draft', 'formal'];
        printPresets.forEach(preset => {
            try {
                const exporter = createPresetPrintExporter(preset);
                this.registry.register(exporter);
                this.presetExporters.set(`print-${preset}`, exporter);
            } catch (error) {
                console.warn(`注册打印预设导出器 ${preset} 失败:`, error.message);
            }
        });
    }

    /**
     * 设置默认导出器 - 根据配置设置默认导出器
     * @private
     */
    _setDefaultExporter() {
        const exporterName = `default-${this.config.defaultExporterType}-exporter`;
        try {
            this.engine.setDefaultExporter(exporterName);
        } catch (error) {
            console.warn('设置默认导出器失败:', error.message);
        }
    }

    /**
     * 设置全局访问 - 在window对象上暴露导出器管理器
     * @private
     */
    _setupGlobalAccess() {
        if (typeof window !== 'undefined') {
            window.SmartOfficeExportManager = this;
            window.SmartOfficeExporters = {
                manager: this,
                registry: this.registry,
                engine: this.engine,
                // 便捷函数
                exportToPDF,
                exportToImage,
                printDocument
            };
        }
    }

    /**
     * 导出文档 - 使用指定导出器或默认导出器导出文档
     * @param {Object} content - 要导出的内容
     * @param {Object} options - 导出选项
     * @param {string} options.type - 导出类型 ('pdf', 'image', 'print')
     * @param {string} options.preset - 预设名称
     * @param {string} options.exporter - 指定导出器名称
     * @returns {Promise<Object>} 导出结果
     */
    async export(content, options = {}) {
        const startTime = Date.now();
        
        try {
            // 确定使用的导出器
            const exporterName = this._resolveExporterName(options);
            
            // 执行导出
            const result = await this.engine.export(content, {
                ...options,
                exporter: exporterName
            });
            
            // 记录导出历史
            this._recordExportHistory({
                content,
                options,
                result,
                exporterName,
                duration: Date.now() - startTime,
                timestamp: new Date().toISOString()
            });
            
            return result;
            
        } catch (error) {
            // 记录失败的导出历史
            this._recordExportHistory({
                content,
                options,
                error: error.message,
                duration: Date.now() - startTime,
                timestamp: new Date().toISOString()
            });
            
            throw error;
        }
    }

    /**
     * 解析导出器名称 - 根据选项确定使用的导出器
     * @param {Object} options - 导出选项
     * @returns {string} 导出器名称
     * @private
     */
    _resolveExporterName(options) {
        // 如果指定了导出器名称，直接使用
        if (options.exporter) {
            return options.exporter;
        }
        
        // 如果指定了预设，使用预设导出器
        if (options.preset && options.type) {
            const presetKey = `${options.type}-${options.preset}`;
            if (this.presetExporters.has(presetKey)) {
                return this.presetExporters.get(presetKey).name;
            }
        }
        
        // 根据类型使用默认导出器
        if (options.type) {
            return `default-${options.type}-exporter`;
        }
        
        // 使用配置的默认导出器
        return `default-${this.config.defaultExporterType}-exporter`;
    }

    /**
     * 记录导出历史 - 记录导出操作的历史信息
     * @param {Object} record - 历史记录
     * @private
     */
    _recordExportHistory(record) {
        this.exportHistory.unshift(record);
        
        // 限制历史记录大小
        if (this.exportHistory.length > this.maxHistorySize) {
            this.exportHistory = this.exportHistory.slice(0, this.maxHistorySize);
        }
    }

    /**
     * 批量导出 - 同时导出多种格式或多个文档
     * @param {Object|Array} content - 要导出的内容（单个对象或数组）
     * @param {Array<Object>} exportConfigs - 导出配置数组
     * @returns {Promise<Array<Object>>} 导出结果数组
     */
    async batchExport(content, exportConfigs) {
        const results = [];
        const exportPromises = [];
        
        for (let i = 0; i < exportConfigs.length; i++) {
            const config = exportConfigs[i];
            const contentToExport = Array.isArray(content) ? content[i] : content;
            
            exportPromises.push(
                this.export(contentToExport, config)
                    .then(result => {
                        results[i] = { success: true, result };
                    })
                    .catch(error => {
                        results[i] = { success: false, error: error.message };
                    })
            );
        }
        
        await Promise.all(exportPromises);
        return results;
    }

    /**
     * 多格式导出 - 将同一内容导出为多种格式
     * @param {Object} content - 要导出的内容
     * @param {Array<string>} types - 导出类型数组 ['pdf', 'image', 'print']
     * @param {Object} baseOptions - 基础导出选项
     * @returns {Promise<Object>} 多格式导出结果
     */
    async exportMultiFormat(content, types = ['pdf', 'image'], baseOptions = {}) {
        const results = {};
        const exportPromises = [];
        
        for (const type of types) {
            const exportOptions = {
                ...baseOptions,
                type: type,
                fileName: baseOptions.fileName ? 
                    `${baseOptions.fileName}_${type}` : 
                    `document_${type}`
            };
            
            exportPromises.push(
                this.export(content, exportOptions)
                    .then(result => {
                        results[type] = { success: true, result };
                    })
                    .catch(error => {
                        results[type] = { success: false, error: error.message };
                    })
            );
        }
        
        await Promise.all(exportPromises);
        return results;
    }

    /**
     * 快速导出 - 使用最常用的配置快速导出
     * @param {Object} content - 要导出的内容
     * @param {string} type - 导出类型 ('pdf', 'image', 'print')
     * @param {Object} options - 简化的导出选项
     * @returns {Promise<Object>} 导出结果
     */
    async quickExport(content, type = 'pdf', options = {}) {
        const quickOptions = {
            type: type,
            preset: 'default',
            autoDownload: options.autoDownload !== false,
            fileName: options.fileName,
            quality: options.quality || 'medium',
            ...options
        };
        
        return await this.export(content, quickOptions);
    }

    /**
     * 创建自定义导出器 - 创建并注册自定义配置的导出器
     * @param {string} type - 导出器类型 ('pdf', 'image', 'print')
     * @param {Object} config - 导出器配置
     * @param {string} name - 导出器名称
     * @returns {BaseExporter} 创建的导出器实例
     */
    createCustomExporter(type, config = {}, name = null) {
        const exporterName = name || `custom-${type}-exporter-${Date.now()}`;
        const exporterConfig = { ...config, name: exporterName };
        
        let exporter;
        
        switch (type.toLowerCase()) {
            case 'pdf':
                exporter = createPDFExporter(exporterConfig);
                break;
            case 'image':
                exporter = createImageExporter(exporterConfig);
                break;
            case 'print':
                exporter = createPrintExporter(exporterConfig);
                break;
            default:
                throw new Error(`不支持的导出器类型: ${type}`);
        }
        
        // 注册导出器
        this.registry.register(exporter);
        
        return exporter;
    }

    /**
     * 获取导出器 - 根据名称获取已注册的导出器
     * @param {string} name - 导出器名称
     * @returns {BaseExporter|null} 导出器实例
     */
    getExporter(name) {
        return this.registry.get(name);
    }

    /**
     * 获取预设导出器 - 根据类型和预设名称获取预设导出器
     * @param {string} type - 导出器类型
     * @param {string} preset - 预设名称
     * @returns {BaseExporter|null} 导出器实例
     */
    getPresetExporter(type, preset) {
        const presetKey = `${type}-${preset}`;
        return this.presetExporters.get(presetKey);
    }

    /**
     * 列出所有导出器 - 获取所有已注册的导出器信息
     * @returns {Array<Object>} 导出器信息数组
     */
    listExporters() {
        return this.registry.getAll().map(exporter => exporter.getInfo());
    }

    /**
     * 获取导出统计 - 获取导出操作的统计信息
     * @returns {Object} 统计信息
     */
    getExportStats() {
        const engineStats = this.engine.getStats();
        const historyStats = this._getHistoryStats();
        
        return {
            ...engineStats,
            history: historyStats,
            presetExporters: this.presetExporters.size,
            totalExporters: this.registry.getStats().totalRegistered
        };
    }

    /**
     * 获取历史统计 - 分析导出历史的统计信息
     * @returns {Object} 历史统计
     * @private
     */
    _getHistoryStats() {
        const total = this.exportHistory.length;
        const successful = this.exportHistory.filter(record => !record.error).length;
        const failed = total - successful;
        
        const byType = {};
        const byPreset = {};
        
        this.exportHistory.forEach(record => {
            if (record.options) {
                const type = record.options.type || 'unknown';
                byType[type] = (byType[type] || 0) + 1;
                
                if (record.options.preset) {
                    const preset = record.options.preset;
                    byPreset[preset] = (byPreset[preset] || 0) + 1;
                }
            }
        });
        
        const totalDuration = this.exportHistory.reduce((sum, record) => sum + (record.duration || 0), 0);
        const averageDuration = total > 0 ? totalDuration / total : 0;
        
        return {
            total,
            successful,
            failed,
            successRate: total > 0 ? (successful / total) * 100 : 0,
            averageDuration,
            byType,
            byPreset
        };
    }

    /**
     * 清理资源 - 清理管理器和所有导出器的资源
     */
    cleanup() {
        // 清理所有注册的导出器
        this.registry.clear();
        
        // 清理预设导出器映射
        this.presetExporters.clear();
        
        // 清理导出历史
        this.exportHistory = [];
        
        // 清理导出引擎
        this.engine.cleanup();
        
        // 清理全局访问
        if (typeof window !== 'undefined') {
            delete window.SmartOfficeExportManager;
            delete window.SmartOfficeExporters;
        }
    }
}
// #endregion

// #region 创建默认实例
/**
 * 创建默认导出管理器实例
 */
export const defaultExportManager = new ExportManager({
    registry: defaultExporterRegistry,
    engine: defaultExportEngine
});
// #endregion

// #region 便捷导出函数
/**
 * 智能导出 - 根据内容和选项智能选择导出方式
 * @param {Object} content - 要导出的内容
 * @param {Object} options - 导出选项
 * @returns {Promise<Object>} 导出结果
 */
export async function smartExport(content, options = {}) {
    return await defaultExportManager.export(content, options);
}

/**
 * 快速PDF导出 - 快速导出PDF格式
 * @param {Object} content - 要导出的内容
 * @param {Object} options - PDF导出选项
 * @returns {Promise<Object>} 导出结果
 */
export async function quickExportToPDF(content, options = {}) {
    return await defaultExportManager.quickExport(content, 'pdf', options);
}

/**
 * 快速图片导出 - 快速导出图片格式
 * @param {Object} content - 要导出的内容
 * @param {Object} options - 图片导出选项
 * @returns {Promise<Object>} 导出结果
 */
export async function quickExportToImage(content, options = {}) {
    return await defaultExportManager.quickExport(content, 'image', options);
}

/**
 * 快速打印 - 快速打印文档
 * @param {Object} content - 要打印的内容
 * @param {Object} options - 打印选项
 * @returns {Promise<Object>} 打印结果
 */
export async function quickPrint(content, options = {}) {
    return await defaultExportManager.quickExport(content, 'print', options);
}

/**
 * 一键多格式导出 - 一次操作导出多种格式
 * @param {Object} content - 要导出的内容
 * @param {Array<string>} formats - 格式数组 ['pdf', 'image', 'print']
 * @param {Object} options - 基础导出选项
 * @returns {Promise<Object>} 多格式导出结果
 */
export async function exportAllFormats(content, formats = ['pdf', 'image'], options = {}) {
    return await defaultExportManager.exportMultiFormat(content, formats, options);
}
// #endregion

// #region 统一导出所有模块
// 导出器类
export {
    BaseExporter,
    ExporterRegistry,
    ExportEngine,
    PDFExporter,
    ImageExporter,
    PrintExporter
};

// 工厂函数
export {
    createPDFExporter,
    createPresetPDFExporter,
    createImageExporter,
    createPresetImageExporter,
    createPrintExporter,
    createPresetPrintExporter
};

// 便捷导出函数
export {
    exportToPDF,
    exportToImage,
    printDocument
};

// 默认实例
export {
    defaultExporterRegistry,
    defaultExportEngine
};

// 事件类型
export {
    EXPORT_EVENTS
};
// #endregion 