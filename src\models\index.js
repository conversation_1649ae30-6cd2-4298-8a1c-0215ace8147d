/**
 * @file 文档模型统一导出
 * @description 提供所有文档模型的统一访问接口
 */

import { BaseDocument } from './base-document.js';
import { ReceiptDocument } from './receipt-document.js';
import { InvoiceDocument } from './invoice-document.js';
import { DriverAgreementDocument } from './driver-agreement-document.js';

/**
 * @class DocumentFactory
 * @description 文档工厂类，用于创建不同类型的文档实例
 */
export class DocumentFactory {
    /**
     * 支持的文档类型映射
     * @static
     * @private
     */
    static #documentTypes = {
        'receipt': ReceiptDocument,
        'invoice': InvoiceDocument,
        'driver-agreement': DriverAgreementDocument
    };

    /**
     * 创建文档实例
     * @param {string} type - 文档类型
     * @param {Object} data - 文档数据
     * @returns {BaseDocument} 文档实例
     * @static
     */
    static create(type, data = {}) {
        const DocumentClass = this.#documentTypes[type];
        
        if (!DocumentClass) {
            throw new Error(`不支持的文档类型: ${type}`);
        }
        
        return new DocumentClass(data);
    }

    /**
     * 从JSON创建文档实例
     * @param {Object} json - JSON对象
     * @returns {BaseDocument} 文档实例
     * @static
     */
    static fromJSON(json) {
        if (!json.type) {
            throw new Error('JSON对象必须包含type字段');
        }
        
        const DocumentClass = this.#documentTypes[json.type];
        
        if (!DocumentClass) {
            throw new Error(`不支持的文档类型: ${json.type}`);
        }
        
        return DocumentClass.fromJSON(json);
    }

    /**
     * 创建空白文档
     * @param {string} type - 文档类型
     * @param {Object} defaults - 默认值
     * @returns {BaseDocument} 空白文档实例
     * @static
     */
    static createBlank(type, defaults = {}) {
        const DocumentClass = this.#documentTypes[type];
        
        if (!DocumentClass) {
            throw new Error(`不支持的文档类型: ${type}`);
        }
        
        if (typeof DocumentClass.createBlank === 'function') {
            return DocumentClass.createBlank(defaults);
        }
        
        return new DocumentClass(defaults);
    }

    /**
     * 获取支持的文档类型列表
     * @returns {Array<string>} 文档类型列表
     * @static
     */
    static getSupportedTypes() {
        return Object.keys(this.#documentTypes);
    }

    /**
     * 检查是否支持指定的文档类型
     * @param {string} type - 文档类型
     * @returns {boolean} 是否支持
     * @static
     */
    static isSupported(type) {
        return type in this.#documentTypes;
    }

    /**
     * 注册新的文档类型
     * @param {string} type - 文档类型
     * @param {Function} DocumentClass - 文档类
     * @static
     */
    static register(type, DocumentClass) {
        if (typeof DocumentClass !== 'function') {
            throw new Error('DocumentClass必须是一个构造函数');
        }
        
        if (!DocumentClass.prototype instanceof BaseDocument) {
            throw new Error('DocumentClass必须继承自BaseDocument');
        }
        
        this.#documentTypes[type] = DocumentClass;
    }

    /**
     * 取消注册文档类型
     * @param {string} type - 文档类型
     * @static
     */
    static unregister(type) {
        delete this.#documentTypes[type];
    }

    /**
     * 获取文档类型的中文名称
     * @param {string} type - 文档类型
     * @returns {string} 中文名称
     * @static
     */
    static getTypeName(type) {
        const typeNames = {
            'receipt': '收据',
            'invoice': '发票',
            'driver-agreement': '司机协议'
        };
        
        return typeNames[type] || type;
    }

    /**
     * 获取文档类型的描述
     * @param {string} type - 文档类型
     * @returns {string} 类型描述
     * @static
     */
    static getTypeDescription(type) {
        const descriptions = {
            'receipt': '用于记录收款的凭证文档',
            'invoice': '用于向客户开具账单的商业文档',
            'driver-agreement': '司机与公司之间的雇佣或合作协议'
        };
        
        return descriptions[type] || '未知文档类型';
    }

    /**
     * 获取所有文档类型的信息
     * @returns {Array<Object>} 文档类型信息列表
     * @static
     */
    static getAllTypeInfo() {
        return this.getSupportedTypes().map(type => ({
            type,
            name: this.getTypeName(type),
            description: this.getTypeDescription(type),
            class: this.#documentTypes[type].name
        }));
    }

    /**
     * 批量创建文档
     * @param {Array<Object>} documents - 文档数据数组
     * @returns {Array<BaseDocument>} 文档实例数组
     * @static
     */
    static createBatch(documents) {
        if (!Array.isArray(documents)) {
            throw new Error('documents参数必须是数组');
        }
        
        return documents.map(doc => {
            if (!doc.type) {
                throw new Error('每个文档对象必须包含type字段');
            }
            
            return this.create(doc.type, doc);
        });
    }

    /**
     * 从JSON数组批量创建文档
     * @param {Array<Object>} jsonArray - JSON对象数组
     * @returns {Array<BaseDocument>} 文档实例数组
     * @static
     */
    static fromJSONBatch(jsonArray) {
        if (!Array.isArray(jsonArray)) {
            throw new Error('jsonArray参数必须是数组');
        }
        
        return jsonArray.map(json => this.fromJSON(json));
    }

    /**
     * 验证文档数据
     * @param {string} type - 文档类型
     * @param {Object} data - 文档数据
     * @returns {Array} 验证错误数组
     * @static
     */
    static validate(type, data) {
        try {
            const document = this.create(type, data);
            return document.validate();
        } catch (error) {
            return [error];
        }
    }

    /**
     * 获取文档模板数据
     * @param {string} type - 文档类型
     * @returns {Object} 模板数据
     * @static
     */
    static getTemplate(type) {
        const DocumentClass = this.#documentTypes[type];
        
        if (!DocumentClass) {
            throw new Error(`不支持的文档类型: ${type}`);
        }
        
        // 创建一个空白实例来获取默认结构
        const blank = new DocumentClass();
        const template = blank.toJSON();
        
        // 清空敏感或不必要的字段
        template.id = '';
        template.createdAt = '';
        template.updatedAt = '';
        
        return template;
    }
}

/**
 * @class DocumentCollection
 * @description 文档集合类，用于管理多个文档实例
 */
export class DocumentCollection {
    /**
     * 创建文档集合实例
     * @param {Array<BaseDocument>} documents - 文档数组
     */
    constructor(documents = []) {
        this.documents = [];
        this.addDocuments(documents);
    }

    /**
     * 添加文档
     * @param {BaseDocument} document - 文档实例
     * @returns {DocumentCollection} 当前实例
     */
    addDocument(document) {
        if (!(document instanceof BaseDocument)) {
            throw new Error('document必须是BaseDocument的实例');
        }
        
        this.documents.push(document);
        return this;
    }

    /**
     * 批量添加文档
     * @param {Array<BaseDocument>} documents - 文档数组
     * @returns {DocumentCollection} 当前实例
     */
    addDocuments(documents) {
        if (!Array.isArray(documents)) {
            throw new Error('documents参数必须是数组');
        }
        
        documents.forEach(doc => this.addDocument(doc));
        return this;
    }

    /**
     * 根据ID获取文档
     * @param {string} id - 文档ID
     * @returns {BaseDocument|null} 文档实例或null
     */
    getById(id) {
        return this.documents.find(doc => doc.getId() === id) || null;
    }

    /**
     * 根据类型获取文档
     * @param {string} type - 文档类型
     * @returns {Array<BaseDocument>} 文档数组
     */
    getByType(type) {
        return this.documents.filter(doc => doc.getType() === type);
    }

    /**
     * 根据状态获取文档
     * @param {string} status - 文档状态
     * @returns {Array<BaseDocument>} 文档数组
     */
    getByStatus(status) {
        return this.documents.filter(doc => doc.status === status);
    }

    /**
     * 删除文档
     * @param {string} id - 文档ID
     * @returns {boolean} 是否删除成功
     */
    removeDocument(id) {
        const index = this.documents.findIndex(doc => doc.getId() === id);
        
        if (index === -1) {
            return false;
        }
        
        this.documents.splice(index, 1);
        return true;
    }

    /**
     * 清空所有文档
     * @returns {DocumentCollection} 当前实例
     */
    clear() {
        this.documents = [];
        return this;
    }

    /**
     * 获取文档数量
     * @returns {number} 文档数量
     */
    count() {
        return this.documents.length;
    }

    /**
     * 检查是否为空
     * @returns {boolean} 是否为空
     */
    isEmpty() {
        return this.documents.length === 0;
    }

    /**
     * 转换为数组
     * @returns {Array<BaseDocument>} 文档数组
     */
    toArray() {
        return [...this.documents];
    }

    /**
     * 转换为JSON数组
     * @returns {Array<Object>} JSON对象数组
     */
    toJSONArray() {
        return this.documents.map(doc => doc.toJSON());
    }

    /**
     * 验证所有文档
     * @returns {Object} 验证结果
     */
    validateAll() {
        const results = {};
        
        this.documents.forEach(doc => {
            const errors = doc.validate();
            if (errors.length > 0) {
                results[doc.getId()] = errors;
            }
        });
        
        return results;
    }

    /**
     * 获取统计信息
     * @returns {Object} 统计信息
     */
    getStatistics() {
        const stats = {
            total: this.documents.length,
            byType: {},
            byStatus: {}
        };
        
        this.documents.forEach(doc => {
            const type = doc.getType();
            const status = doc.status;
            
            stats.byType[type] = (stats.byType[type] || 0) + 1;
            stats.byStatus[status] = (stats.byStatus[status] || 0) + 1;
        });
        
        return stats;
    }

    /**
     * 过滤文档
     * @param {Function} predicate - 过滤函数
     * @returns {DocumentCollection} 新的文档集合
     */
    filter(predicate) {
        const filtered = this.documents.filter(predicate);
        return new DocumentCollection(filtered);
    }

    /**
     * 排序文档
     * @param {Function} compareFn - 比较函数
     * @returns {DocumentCollection} 当前实例
     */
    sort(compareFn) {
        this.documents.sort(compareFn);
        return this;
    }

    /**
     * 按创建时间排序
     * @param {boolean} ascending - 是否升序
     * @returns {DocumentCollection} 当前实例
     */
    sortByCreatedAt(ascending = true) {
        return this.sort((a, b) => {
            const diff = a.getCreatedAt() - b.getCreatedAt();
            return ascending ? diff : -diff;
        });
    }

    /**
     * 按更新时间排序
     * @param {boolean} ascending - 是否升序
     * @returns {DocumentCollection} 当前实例
     */
    sortByUpdatedAt(ascending = true) {
        return this.sort((a, b) => {
            const diff = a.getUpdatedAt() - b.getUpdatedAt();
            return ascending ? diff : -diff;
        });
    }

    /**
     * 迭代器支持
     * @returns {Iterator} 迭代器
     */
    [Symbol.iterator]() {
        return this.documents[Symbol.iterator]();
    }
}

// 导出所有模型类和工具类
export {
    BaseDocument,
    ReceiptDocument,
    InvoiceDocument,
    DriverAgreementDocument
};

// 默认导出工厂类
export default DocumentFactory;
