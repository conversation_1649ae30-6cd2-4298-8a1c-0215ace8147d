/**
 * @file 日志管理器模块 - 传统架构版本
 * <AUTHOR> Team
 * @description 
 * 提供统一的日志记录、性能监控和错误处理功能
 * 转换自ES6模块系统到传统script标签架构
 * 
 * 功能特性：
 * - 多级别日志记录
 * - 性能监控和标记
 * - 全局错误处理
 * - 自动日志刷新
 * - 控制台格式化输出
 */

(function(SmartOffice) {
    'use strict';
    
    // 检查命名空间
    if (!SmartOffice || !SmartOffice.Core) {
        throw new Error('SmartOffice.Core命名空间未初始化');
    }
    
    // 注册模块
    SmartOffice.Modules.register('Core.Logger', []);
    
    // #region 日志级别定义
    /**
     * 日志级别枚举
     */
    const LogLevel = {
        TRACE: 'TRACE',
        DEBUG: 'DEBUG', 
        INFO: 'INFO',
        WARN: 'WARN',
        ERROR: 'ERROR',
        FATAL: 'FATAL'
    };

    const LOG_PRIORITY = {
        [LogLevel.TRACE]: 0,
        [LogLevel.DEBUG]: 1,
        [LogLevel.INFO]: 2,
        [LogLevel.WARN]: 3,
        [LogLevel.ERROR]: 4,
        [LogLevel.FATAL]: 5
    };
    // #endregion
    
    // #region 日志管理器类
    /**
     * @class Logger - 日志管理器类
     * @description 提供完整的日志记录、性能监控和错误处理功能
     */
    class Logger {
        /**
         * 构造函数 - 初始化日志管理器
         * @param {Object} config - 日志配置选项
         */
        constructor(config = {}) {
            this.config = {
                level: LogLevel.INFO,
                enableConsole: true,
                enableStorage: true,
                enablePerformance: true,
                enableDebugPanel: false,
                maxLogEntries: 1000,
                autoFlush: true,
                flushInterval: 5000,
                format: {
                    timestamp: true,
                    level: true,
                    module: true,
                    function: true,
                    emoji: true
                },
                ...config
            };

            this.logs = [];
            this.performanceMarks = new Map();
            this.debugCounters = new Map();
            this.isInitialized = false;
            this.flushTimer = null;

            this.levelIcons = {
                [LogLevel.TRACE]: '🔍',
                [LogLevel.DEBUG]: '🐛',
                [LogLevel.INFO]: 'ℹ️',
                [LogLevel.WARN]: '⚠️',
                [LogLevel.ERROR]: '❌',
                [LogLevel.FATAL]: '💥'
            };

            this.levelColors = {
                [LogLevel.TRACE]: '#888888',
                [LogLevel.DEBUG]: '#00BCD4',
                [LogLevel.INFO]: '#2196F3',
                [LogLevel.WARN]: '#FF9800',
                [LogLevel.ERROR]: '#F44336',
                [LogLevel.FATAL]: '#9C27B0'
            };

            this._initialize();
        }

        /**
         * 初始化日志管理器
         * @private
         */
        _initialize() {
            try {
                console.log('📝 正在初始化日志管理器...');
                this._setupGlobalErrorHandling();
                
                if (this.config.autoFlush) {
                    this._startAutoFlush();
                }
                
                this.isInitialized = true;
                this.info('Logger', '_initialize', '日志管理器初始化完成');
            } catch (error) {
                console.error('❌ 日志管理器初始化失败:', error);
                throw error;
            }
        }

        /**
         * 设置全局错误处理
         * @private
         */
        _setupGlobalErrorHandling() {
            window.addEventListener('error', (event) => {
                this.error('Global', 'error', '全局错误捕获', {
                    message: event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    error: event.error
                });
            });

            window.addEventListener('unhandledrejection', (event) => {
                this.error('Global', 'unhandledrejection', '未处理的Promise拒绝', {
                    reason: event.reason,
                    promise: event.promise
                });
                event.preventDefault();
            });
        }

        /**
         * 记录日志
         * @param {string} level - 日志级别
         * @param {string} module - 模块名称
         * @param {string} functionName - 函数名称
         * @param {string} message - 日志消息
         * @param {*} data - 附加数据
         */
        log(level, module, functionName, message, data = null) {
            if (LOG_PRIORITY[level] < LOG_PRIORITY[this.config.level]) {
                return;
            }

            const logEntry = {
                timestamp: new Date().toISOString(),
                level,
                module,
                function: functionName,
                message,
                data,
                id: this._generateLogId()
            };

            this.logs.push(logEntry);

            if (this.config.enableConsole) {
                this._outputToConsole(logEntry);
            }
        }

        /**
         * 输出到控制台
         * @param {Object} logEntry - 日志条目
         * @private
         */
        _outputToConsole(logEntry) {
            const { level, module, function: fn, message, data } = logEntry;
            const timestamp = new Date(logEntry.timestamp).toLocaleTimeString();

            let logMessage = '';
            
            if (this.config.format.emoji) {
                logMessage += `${this.levelIcons[level]} `;
            }
            
            if (this.config.format.timestamp) {
                logMessage += `[${timestamp}] `;
            }
            
            if (this.config.format.level) {
                logMessage += `${level} `;
            }
            
            if (this.config.format.module) {
                logMessage += `${module}`;
            }
            
            if (this.config.format.function && fn) {
                logMessage += `.${fn}()`;
            }
            
            logMessage += `: ${message}`;

            const consoleMethod = this._getConsoleMethod(level);
            
            if (data) {
                consoleMethod(logMessage, data);
            } else {
                consoleMethod(logMessage);
            }
        }

        /**
         * 获取控制台方法
         * @param {string} level - 日志级别
         * @returns {Function} 控制台方法
         * @private
         */
        _getConsoleMethod(level) {
            switch (level) {
                case LogLevel.TRACE:
                case LogLevel.DEBUG:
                    return console.debug.bind(console);
                case LogLevel.INFO:
                    return console.info.bind(console);
                case LogLevel.WARN:
                    return console.warn.bind(console);
                case LogLevel.ERROR:
                case LogLevel.FATAL:
                    return console.error.bind(console);
                default:
                    return console.log.bind(console);
            }
        }

        /**
         * 生成日志ID
         * @returns {string} 日志ID
         * @private
         */
        _generateLogId() {
            return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        // 便捷日志方法
        trace(module, functionName, message, data = null) {
            this.log(LogLevel.TRACE, module, functionName, message, data);
        }

        debug(module, functionName, message, data = null) {
            this.log(LogLevel.DEBUG, module, functionName, message, data);
        }

        info(module, functionName, message, data = null) {
            this.log(LogLevel.INFO, module, functionName, message, data);
        }

        warn(module, functionName, message, data = null) {
            this.log(LogLevel.WARN, module, functionName, message, data);
        }

        error(module, functionName, message, data = null) {
            this.log(LogLevel.ERROR, module, functionName, message, data);
        }

        fatal(module, functionName, message, data = null) {
            this.log(LogLevel.FATAL, module, functionName, message, data);
        }

        /**
         * 开始性能标记
         * @param {string} name - 标记名称
         * @param {string} module - 模块名称
         * @param {string} functionName - 函数名称
         */
        startPerformanceMark(name, module, functionName) {
            if (!this.config.enablePerformance) return;

            const markData = {
                name,
                module,
                function: functionName,
                startTime: performance.now(),
                timestamp: new Date().toISOString()
            };

            this.performanceMarks.set(name, markData);
            this.debug(module, functionName, `开始性能标记: ${name}`, {
                startTime: markData.startTime
            });
        }

        /**
         * 结束性能标记
         * @param {string} name - 标记名称
         * @param {string} module - 模块名称
         * @param {string} functionName - 函数名称
         * @returns {number|null} 持续时间
         */
        endPerformanceMark(name, module, functionName) {
            if (!this.config.enablePerformance) return null;

            const markData = this.performanceMarks.get(name);
            if (!markData) {
                this.warn(module, functionName, `性能标记不存在: ${name}`);
                return null;
            }

            const endTime = performance.now();
            const duration = endTime - markData.startTime;

            this.performanceMarks.delete(name);

            this.info(module, functionName, `性能标记完成: ${name}`, {
                duration: `${duration.toFixed(2)}ms`,
                startTime: markData.startTime,
                endTime
            });

            return duration;
        }
        
        /**
         * 启动自动刷新
         * @private
         */
        _startAutoFlush() {
            if (this.flushTimer) {
                clearInterval(this.flushTimer);
            }
            
            this.flushTimer = setInterval(() => {
                this._flushLogs();
            }, this.config.flushInterval);
        }

        /**
         * 刷新日志到存储
         * @private
         */
        _flushLogs() {
            if (!this.config.enableStorage || this.logs.length === 0) {
                return;
            }
            
            try {
                const logData = {
                    logs: this.logs.slice(-this.config.maxLogEntries),
                    timestamp: new Date().toISOString(),
                    config: this.config
                };
                
                localStorage.setItem('smartoffice_logs', JSON.stringify(logData));
                
                if (this.logs.length > this.config.maxLogEntries) {
                    this.logs = this.logs.slice(-this.config.maxLogEntries);
                }
            } catch (error) {
                console.warn('⚠️ 日志存储失败:', error);
            }
        }

        /**
         * 销毁日志管理器
         */
        destroy() {
            if (this.flushTimer) {
                clearInterval(this.flushTimer);
                this.flushTimer = null;
            }
            
            this._flushLogs();
            this.logs = [];
            this.performanceMarks.clear();
            this.debugCounters.clear();
            this.isInitialized = false;
        }
    }
    // #endregion

    // #region 全局日志实例管理
    let globalLogger = null;

    /**
     * 创建日志管理器实例
     * @param {Object} config - 配置选项
     * @returns {Logger} 日志管理器实例
     */
    function createLogger(config = {}) {
        return new Logger(config);
    }

    /**
     * 获取全局日志管理器实例
     * @returns {Logger} 全局日志管理器
     */
    function getLogger() {
        if (!globalLogger) {
            globalLogger = new Logger({
                level: LogLevel.DEBUG,
                enableConsole: true,
                enableStorage: true,
                enablePerformance: true
            });
        }
        return globalLogger;
    }

    // 便捷函数
    function logInfo(module, functionName, message, data = null) {
        getLogger().info(module, functionName, message, data);
    }

    function logDebug(module, functionName, message, data = null) {
        getLogger().debug(module, functionName, message, data);
    }

    function logError(module, functionName, message, data = null) {
        getLogger().error(module, functionName, message, data);
    }

    function logWarn(module, functionName, message, data = null) {
        getLogger().warn(module, functionName, message, data);
    }

    function logTrace(module, functionName, message, data = null) {
        getLogger().trace(module, functionName, message, data);
    }

    function logFatal(module, functionName, message, data = null) {
        getLogger().fatal(module, functionName, message, data);
    }
    // #endregion

    // #region 暴露到命名空间
    // 暴露日志级别
    SmartOffice.Core.LogLevel = LogLevel;

    // 暴露日志管理器类
    SmartOffice.Core.Logger = Logger;

    // 暴露工厂函数
    SmartOffice.Core.createLogger = createLogger;
    SmartOffice.Core.getLogger = getLogger;

    // 暴露便捷函数
    SmartOffice.Core.logInfo = logInfo;
    SmartOffice.Core.logDebug = logDebug;
    SmartOffice.Core.logError = logError;
    SmartOffice.Core.logWarn = logWarn;
    SmartOffice.Core.logTrace = logTrace;
    SmartOffice.Core.logFatal = logFatal;

    // 标记模块已加载
    SmartOffice.Modules.markLoaded('Core.Logger');

    // 输出加载信息
    console.info('📝 日志管理器模块加载完成 (传统架构)');
    // #endregion

})(window.SmartOffice);
