/**
 * @file 渲染器系统统一导出 - 整合所有渲染器和管理组件
 * <AUTHOR> Team
 * @description 
 * 这个文件作为渲染器系统的统一入口，导出所有渲染器类和工具函数，包括：
 * - 所有渲染器类（BaseRenderer、HTMLRenderer、PDFRenderer、PrintRenderer）
 * - 渲染器注册表和管理组件
 * - 工厂函数和预设配置
 * - 渲染器引擎和统一接口
 */

// #region 导入所有渲染器类
import { 
    BaseRenderer, 
    RendererRegistry, 
    RenderingEngine,
    defaultRegistry,
    defaultRenderingEngine
} from './base-renderer.js';

import { 
    HTMLRenderer, 
    createHTMLRenderer, 
    createPresetHTMLRenderer 
} from './html-renderer.js';

import { 
    PDFRenderer, 
    createPDFRenderer, 
    createPresetPDFRenderer 
} from './pdf-renderer.js';

import { 
    PrintRenderer, 
    createPrintRenderer, 
    createPresetPrintRenderer 
} from './print-renderer.js';

import { getLogger } from '../core/utils/logger.js';
// #endregion

// #region 渲染器管理器类定义
/**
 * @class RendererManager - 渲染器管理器类
 * @description 高级渲染器管理器，提供统一的渲染器管理和使用接口
 */
export class RendererManager {
    /**
     * 构造函数 - 初始化渲染器管理器
     * @param {RendererRegistry} registry - 渲染器注册表实例（可选）
     */
    constructor(registry = null) {
        // 初始化日志记录器
        this.logger = getLogger();
        this.logger.startPerformanceMark('renderer_manager_construction', 'RendererManager', 'constructor');
        this.logger.info('RendererManager', 'constructor', '开始构造渲染器管理器', {
            hasCustomRegistry: !!registry
        });
        
        // 使用传入的注册表或创建新的注册表
        this.registry = registry || new RendererRegistry();
        this.engine = new RenderingEngine(this.registry);
        
        this.logger.debug('RendererManager', 'constructor', '注册表和引擎初始化完成', {
            registryType: registry ? 'custom' : 'default'
        });
        
        // 默认渲染器配置
        this.defaultRenderers = new Map();
        this.presetConfigs = new Map();
        
        this.logger.trace('RendererManager', 'constructor', '存储结构初始化完成');
        
        // 初始化默认渲染器
        this.logger.debug('RendererManager', 'constructor', '开始初始化默认渲染器');
        this._initializeDefaultRenderers();
        
        // 设置预设配置
        this.logger.debug('RendererManager', 'constructor', '开始设置预设配置');
        this._setupPresetConfigs();
        
        const constructionDuration = this.logger.endPerformanceMark('renderer_manager_construction', 'RendererManager', 'constructor');
        this.logger.info('RendererManager', 'constructor', '✅ 渲染器管理器构造完成', {
            duration: `${constructionDuration?.toFixed(2)}ms`,
            registrySize: this.registry.getAll().length,
            defaultRenderersCount: this.defaultRenderers.size,
            presetConfigsCount: this.presetConfigs.size
        });
        
        // 记录构造统计
        this.logger.incrementCounter('renderer_manager_instances', 'RendererManager');
    }

    /**
     * 初始化默认渲染器 - 创建并注册默认的渲染器实例
     * @private
     */
    _initializeDefaultRenderers() {
        try {
            // 创建HTML渲染器
            const htmlRenderer = createHTMLRenderer({
                name: 'default-html-renderer'
            });
            this.registry.register(htmlRenderer);
            this.defaultRenderers.set('html', 'default-html-renderer');

            // 创建PDF渲染器
            const pdfRenderer = createPDFRenderer({
                name: 'default-pdf-renderer'
            });
            this.registry.register(pdfRenderer);
            this.defaultRenderers.set('pdf', 'default-pdf-renderer');

            // 创建打印渲染器
            const printRenderer = createPrintRenderer({
                name: 'default-print-renderer'
            });
            this.registry.register(printRenderer);
            this.defaultRenderers.set('print', 'default-print-renderer');

            // 设置默认渲染器
            this.engine.setDefaultRenderer('default-html-renderer');
            
        } catch (error) {
            console.error('初始化默认渲染器失败:', error.message);
        }
    }

    /**
     * 设置预设配置 - 定义常用的预设渲染器配置
     * @private
     */
    _setupPresetConfigs() {
        // HTML预设
        this.presetConfigs.set('html-default', {
            type: 'html',
            factory: createPresetHTMLRenderer,
            preset: 'default'
        });
        
        this.presetConfigs.set('html-print', {
            type: 'html',
            factory: createPresetHTMLRenderer,
            preset: 'print'
        });
        
        this.presetConfigs.set('html-preview', {
            type: 'html',
            factory: createPresetHTMLRenderer,
            preset: 'preview'
        });

        // PDF预设
        this.presetConfigs.set('pdf-default', {
            type: 'pdf',
            factory: createPresetPDFRenderer,
            preset: 'default'
        });
        
        this.presetConfigs.set('pdf-high-quality', {
            type: 'pdf',
            factory: createPresetPDFRenderer,
            preset: 'high-quality'
        });
        
        this.presetConfigs.set('pdf-print', {
            type: 'pdf',
            factory: createPresetPDFRenderer,
            preset: 'print'
        });

        // 打印预设
        this.presetConfigs.set('print-default', {
            type: 'print',
            factory: createPresetPrintRenderer,
            preset: 'default'
        });
        
        this.presetConfigs.set('print-minimal', {
            type: 'print',
            factory: createPresetPrintRenderer,
            preset: 'minimal'
        });
        
        this.presetConfigs.set('print-high-quality', {
            type: 'print',
            factory: createPresetPrintRenderer,
            preset: 'high-quality'
        });
    }

    /**
     * 渲染文档 - 使用指定渲染器渲染文档
     * @param {Object} template - 模板对象
     * @param {Object} data - 渲染数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<Object>} 渲染结果
     */
    async render(template, data, options = {}) {
        return this.engine.render(template, data, options);
    }

    /**
     * 创建渲染器 - 根据类型和配置创建渲染器实例
     * @param {string} type - 渲染器类型 ('html', 'pdf', 'print')
     * @param {Object} config - 渲染器配置
     * @returns {BaseRenderer} 渲染器实例
     */
    createRenderer(type, config = {}) {
        const factories = {
            'html': createHTMLRenderer,
            'pdf': createPDFRenderer,
            'print': createPrintRenderer
        };

        const factory = factories[type];
        if (!factory) {
            throw new Error(`不支持的渲染器类型: ${type}`);
        }

        return factory(config);
    }

    /**
     * 创建预设渲染器 - 根据预设名称创建渲染器
     * @param {string} presetName - 预设名称
     * @param {Object} config - 额外配置
     * @returns {BaseRenderer} 渲染器实例
     */
    createPresetRenderer(presetName, config = {}) {
        const presetConfig = this.presetConfigs.get(presetName);
        if (!presetConfig) {
            throw new Error(`未找到预设配置: ${presetName}`);
        }

        return presetConfig.factory(presetConfig.preset, config);
    }

    /**
     * 注册渲染器 - 将渲染器添加到注册表
     * @param {BaseRenderer} renderer - 渲染器实例
     * @param {boolean} setAsDefault - 是否设置为默认渲染器
     */
    registerRenderer(renderer, setAsDefault = false) {
        this.registry.register(renderer);
        
        if (setAsDefault) {
            this.engine.setDefaultRenderer(renderer.name);
        }
    }

    /**
     * 获取渲染器 - 根据名称获取已注册的渲染器
     * @param {string} name - 渲染器名称
     * @returns {BaseRenderer|null} 渲染器实例
     */
    getRenderer(name) {
        return this.registry.get(name);
    }

    /**
     * 获取渲染器列表 - 返回所有已注册的渲染器信息
     * @returns {Array<Object>} 渲染器信息数组
     */
    getRendererList() {
        return this.registry.getAll().map(renderer => ({
            name: renderer.name,
            type: renderer.type,
            version: renderer.version,
            description: renderer.description
        }));
    }

    /**
     * 按类型获取渲染器 - 根据类型筛选渲染器
     * @param {string} type - 渲染器类型
     * @returns {Array<BaseRenderer>} 渲染器实例数组
     */
    getRenderersByType(type) {
        return this.registry.getByType(type);
    }

    /**
     * 移除渲染器 - 从注册表中移除指定的渲染器
     * @param {string} name - 渲染器名称
     * @returns {boolean} 是否成功移除
     */
    removeRenderer(name) {
        return this.registry.unregister(name);
    }

    /**
     * 获取预设列表 - 返回所有可用的预设配置
     * @returns {Array<Object>} 预设信息数组
     */
    getPresetList() {
        const presets = [];
        
        for (const [name, config] of this.presetConfigs.entries()) {
            presets.push({
                name,
                type: config.type,
                preset: config.preset,
                description: this._getPresetDescription(name)
            });
        }
        
        return presets;
    }

    /**
     * 获取预设描述 - 返回预设的详细描述
     * @param {string} presetName - 预设名称
     * @returns {string} 预设描述
     * @private
     */
    _getPresetDescription(presetName) {
        const descriptions = {
            'html-default': '默认HTML渲染器，支持响应式和打印优化',
            'html-print': '打印优化的HTML渲染器，移除交互功能',
            'html-preview': '预览专用HTML渲染器，简化文档结构',
            'pdf-default': '默认PDF渲染器，中等质量和压缩',
            'pdf-high-quality': '高质量PDF渲染器，适合打印和归档',
            'pdf-print': '打印优化PDF渲染器，最佳打印效果',
            'print-default': '默认打印渲染器，支持打印预览和设置',
            'print-minimal': '最简打印渲染器，快速打印无预览',
            'print-high-quality': '高质量打印渲染器，最佳打印效果'
        };
        
        return descriptions[presetName] || '未知预设';
    }

    /**
     * 批量渲染 - 使用不同渲染器同时渲染多个格式
     * @param {Object} template - 模板对象
     * @param {Object} data - 渲染数据
     * @param {Array<string>} formats - 要渲染的格式数组
     * @param {Object} options - 渲染选项
     * @returns {Promise<Object>} 包含所有格式结果的对象
     */
    async batchRender(template, data, formats = ['html', 'pdf'], options = {}) {
        const results = {};
        const renderPromises = [];

        for (const format of formats) {
            const defaultRenderer = this.defaultRenderers.get(format);
            if (defaultRenderer) {
                const renderOptions = {
                    ...options,
                    renderer: defaultRenderer
                };
                
                renderPromises.push(
                    this.render(template, data, renderOptions)
                        .then(result => {
                            results[format] = result;
                        })
                        .catch(error => {
                            results[format] = { error: error.message };
                        })
                );
            } else {
                results[format] = { error: `不支持的格式: ${format}` };
            }
        }

        await Promise.all(renderPromises);
        return results;
    }

    /**
     * 获取统计信息 - 返回渲染器管理器的统计数据
     * @returns {Object} 统计信息对象
     */
    getStats() {
        const registryStats = this.registry.getStats();
        
        return {
            ...registryStats,
            defaultRenderers: Object.fromEntries(this.defaultRenderers),
            availablePresets: this.presetConfigs.size,
            engineConfig: this.engine.config
        };
    }

    /**
     * 清理资源 - 清理所有渲染器和注册表
     */
    cleanup() {
        this.registry.clear();
        this.defaultRenderers.clear();
        this.presetConfigs.clear();
    }
}
// #endregion

// #region 便捷函数
/**
 * 创建渲染器管理器 - 工厂函数，创建配置好的渲染器管理器
 * @param {Object} config - 管理器配置
 * @returns {RendererManager} 渲染器管理器实例
 */
export function createRendererManager(config = {}) {
    return new RendererManager(config.registry);
}

/**
 * 快速渲染 - 使用默认配置快速渲染文档
 * @param {Object} template - 模板对象
 * @param {Object} data - 渲染数据
 * @param {string} format - 输出格式 ('html', 'pdf', 'print')
 * @param {Object} options - 渲染选项
 * @returns {Promise<Object>} 渲染结果
 */
export async function quickRender(template, data, format = 'html', options = {}) {
    // 创建临时管理器
    const manager = createRendererManager();
    
    try {
        // 设置渲染器
        const defaultRenderer = manager.defaultRenderers.get(format);
        if (!defaultRenderer) {
            throw new Error(`不支持的输出格式: ${format}`);
        }
        
        const renderOptions = {
            ...options,
            renderer: defaultRenderer
        };
        
        // 执行渲染
        return await manager.render(template, data, renderOptions);
        
    } finally {
        // 清理资源
        manager.cleanup();
    }
}

/**
 * 渲染为HTML - 专门用于HTML格式渲染的便捷函数
 * @param {Object} template - 模板对象
 * @param {Object} data - 渲染数据
 * @param {Object} options - HTML渲染选项
 * @returns {Promise<Object>} HTML渲染结果
 */
export async function renderToHTML(template, data, options = {}) {
    const renderer = createHTMLRenderer(options.rendererConfig);
    return await renderer.render(template, data, options);
}

/**
 * 渲染为PDF - 专门用于PDF格式渲染的便捷函数
 * @param {Object} template - 模板对象
 * @param {Object} data - 渲染数据
 * @param {Object} options - PDF渲染选项
 * @returns {Promise<Object>} PDF渲染结果
 */
export async function renderToPDF(template, data, options = {}) {
    const renderer = createPDFRenderer(options.rendererConfig);
    return await renderer.render(template, data, options);
}

/**
 * 渲染为打印 - 专门用于打印格式渲染的便捷函数
 * @param {Object} template - 模板对象
 * @param {Object} data - 渲染数据
 * @param {Object} options - 打印渲染选项
 * @returns {Promise<Object>} 打印渲染结果
 */
export async function renderToPrint(template, data, options = {}) {
    const renderer = createPrintRenderer(options.rendererConfig);
    return await renderer.render(template, data, options);
}

/**
 * 多格式渲染 - 同时渲染多种格式的便捷函数
 * @param {Object} template - 模板对象
 * @param {Object} data - 渲染数据
 * @param {Array<string>} formats - 格式数组
 * @param {Object} options - 渲染选项
 * @returns {Promise<Object>} 多格式渲染结果
 */
export async function renderMultiFormat(template, data, formats = ['html', 'pdf'], options = {}) {
    const manager = createRendererManager();
    
    try {
        return await manager.batchRender(template, data, formats, options);
    } finally {
        manager.cleanup();
    }
}
// #endregion

// #region 默认实例
// 创建默认的渲染器管理器实例
export const defaultRendererManager = createRendererManager();

// 设置全局默认渲染器（如果需要）
if (typeof window !== 'undefined') {
    window.SmartOfficeRenderers = {
        manager: defaultRendererManager,
        quickRender,
        renderToHTML,
        renderToPDF,
        renderToPrint,
        renderMultiFormat
    };
}
// #endregion

// #region 导出所有组件
// 导出基础类
export {
    BaseRenderer,
    RendererRegistry,
    RenderingEngine,
    defaultRegistry,
    defaultRenderingEngine
};

// 导出渲染器类
export {
    HTMLRenderer,
    PDFRenderer,
    PrintRenderer
};

// 导出工厂函数
export {
    createHTMLRenderer,
    createPresetHTMLRenderer,
    createPDFRenderer,
    createPresetPDFRenderer,
    createPrintRenderer,
    createPresetPrintRenderer
};

// 导出类型常量
export const RENDERER_TYPES = {
    HTML: 'html',
    PDF: 'pdf',
    PRINT: 'print'
};

// 导出预设常量
export const RENDERER_PRESETS = {
    HTML: {
        DEFAULT: 'html-default',
        PRINT: 'html-print',
        PREVIEW: 'html-preview'
    },
    PDF: {
        DEFAULT: 'pdf-default',
        HIGH_QUALITY: 'pdf-high-quality',
        PRINT: 'pdf-print'
    },
    PRINT: {
        DEFAULT: 'print-default',
        MINIMAL: 'print-minimal',
        HIGH_QUALITY: 'print-high-quality'
    }
};
// #endregion 