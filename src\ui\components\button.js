/**
 * @file 按钮组件 - 可复用的按钮UI组件
 * <AUTHOR> Team
 * @description 
 * 这个文件实现了按钮组件，提供：
 * - Button 基础按钮组件
 * - 多种按钮样式和状态支持
 * - 图标和文本组合显示
 * - 加载状态和禁用状态
 * - 按钮组和下拉按钮功能
 */

// #region 导入依赖模块
import { BaseComponent } from './base-component.js';
import { UI_EVENTS } from '../../core/events/event-types.js';
import { validateNonEmpty } from '../../core/utils/validation.js';
// #endregion

// #region Button 按钮组件
/**
 * @class Button - 按钮组件
 * @description 可复用的按钮组件，支持多种样式、状态和交互
 */
export class Button extends BaseComponent {
    /**
     * 构造函数 - 初始化按钮组件
     * @param {Object} config - 按钮配置
     * @param {string} config.text - 按钮文本
     * @param {string} config.icon - 图标类名
     * @param {string} config.variant - 按钮变体 ('primary', 'secondary', 'success', 'danger', 'warning', 'info', 'light', 'dark')
     * @param {string} config.size - 按钮大小 ('xs', 'sm', 'md', 'lg', 'xl')
     * @param {boolean} config.outline - 是否为轮廓按钮
     * @param {boolean} config.block - 是否为块级按钮
     * @param {Function} config.onClick - 点击事件处理函数
     * @param {HTMLElement} config.container - 容器元素
     */
    constructor(config = {}) {
        super({
            type: 'button',
            name: config.name || 'button',
            ...config
        });
        
        // 按钮特有属性
        this.buttonProps = {
            text: config.text || '',
            icon: config.icon || '',
            variant: config.variant || 'primary',
            size: config.size || 'md',
            outline: config.outline || false,
            block: config.block || false,
            type: config.type || 'button', // 'button', 'submit', 'reset'
            tooltip: config.tooltip || '',
            ...config.buttonProps
        };
        
        // 事件处理
        this.clickHandler = config.onClick || null;
        
        // 按钮状态
        this.buttonState = {
            pressed: false,
            active: false,
            ...config.buttonState
        };
        
        // 合并到组件状态
        this.state = { ...this.state, ...this.buttonState };
        
        // 验证配置
        this._validateButtonConfig();
    }

    /**
     * 验证按钮配置 - 验证按钮特有的配置
     * @private
     */
    _validateButtonConfig() {
        // 验证按钮变体
        const validVariants = ['primary', 'secondary', 'success', 'danger', 'warning', 'info', 'light', 'dark'];
        if (!validVariants.includes(this.buttonProps.variant)) {
            console.warn(`无效的按钮变体: ${this.buttonProps.variant}，将使用默认值 'primary'`);
            this.buttonProps.variant = 'primary';
        }
        
        // 验证按钮大小
        const validSizes = ['xs', 'sm', 'md', 'lg', 'xl'];
        if (!validSizes.includes(this.buttonProps.size)) {
            console.warn(`无效的按钮大小: ${this.buttonProps.size}，将使用默认值 'md'`);
            this.buttonProps.size = 'md';
        }
        
        // 验证按钮类型
        const validTypes = ['button', 'submit', 'reset'];
        if (!validTypes.includes(this.buttonProps.type)) {
            console.warn(`无效的按钮类型: ${this.buttonProps.type}，将使用默认值 'button'`);
            this.buttonProps.type = 'button';
        }
    }

    /**
     * 渲染按钮 - 创建按钮DOM元素
     * @returns {Promise<HTMLElement>} 按钮元素
     */
    async render() {
        // 创建按钮元素
        const button = document.createElement('button');
        button.type = this.buttonProps.type;
        button.id = this.id;
        
        // 设置按钮内容
        const content = this._createButtonContent();
        button.appendChild(content);
        
        // 设置属性
        this._setButtonAttributes(button);
        
        return button;
    }

    /**
     * 创建按钮内容 - 创建按钮的内部内容结构
     * @returns {DocumentFragment} 按钮内容片段
     * @private
     */
    _createButtonContent() {
        const fragment = document.createDocumentFragment();
        
        // 创建图标元素
        if (this.buttonProps.icon) {
            const iconElement = this._createIconElement();
            fragment.appendChild(iconElement);
        }
        
        // 创建文本元素
        if (this.buttonProps.text) {
            const textElement = this._createTextElement();
            fragment.appendChild(textElement);
        }
        
        // 创建加载指示器
        const loadingElement = this._createLoadingElement();
        fragment.appendChild(loadingElement);
        
        return fragment;
    }

    /**
     * 创建图标元素 - 创建按钮图标
     * @returns {HTMLElement} 图标元素
     * @private
     */
    _createIconElement() {
        const icon = document.createElement('i');
        icon.className = `btn-icon ${this.buttonProps.icon}`;
        icon.setAttribute('aria-hidden', 'true');
        return icon;
    }

    /**
     * 创建文本元素 - 创建按钮文本
     * @returns {HTMLElement} 文本元素
     * @private
     */
    _createTextElement() {
        const text = document.createElement('span');
        text.className = 'btn-text';
        text.textContent = this.buttonProps.text;
        return text;
    }

    /**
     * 创建加载指示器 - 创建加载状态的指示器
     * @returns {HTMLElement} 加载指示器元素
     * @private
     */
    _createLoadingElement() {
        const loading = document.createElement('span');
        loading.className = 'btn-loading';
        loading.style.display = 'none';
        
        // 创建加载动画
        const spinner = document.createElement('span');
        spinner.className = 'btn-spinner';
        loading.appendChild(spinner);
        
        return loading;
    }

    /**
     * 设置按钮属性 - 设置按钮的各种属性
     * @param {HTMLElement} button - 按钮元素
     * @private
     */
    _setButtonAttributes(button) {
        // 设置禁用状态
        button.disabled = !this.state.enabled;
        
        // 设置ARIA属性
        if (this.buttonProps.tooltip) {
            button.setAttribute('title', this.buttonProps.tooltip);
            button.setAttribute('aria-label', this.buttonProps.tooltip);
        }
        
        // 设置按钮状态属性
        if (this.state.pressed) {
            button.setAttribute('aria-pressed', 'true');
        }
        
        if (this.state.active) {
            button.classList.add('active');
        }
    }

    /**
     * 应用样式 - 应用按钮特有的样式类
     * @private
     */
    _applyStyles() {
        if (!this.element) {
            return;
        }
        
        // 构建样式类列表
        const styleClasses = [
            'smartoffice-button',
            'btn',
            `btn-${this.buttonProps.variant}`,
            `btn-${this.buttonProps.size}`,
            this.buttonProps.outline ? `btn-outline-${this.buttonProps.variant}` : '',
            this.buttonProps.block ? 'btn-block' : '',
            this.state.loading ? 'btn-loading' : '',
            this.state.enabled ? '' : 'disabled',
            this.state.visible ? '' : 'hidden',
            this.props.className
        ].filter(Boolean);
        
        // 应用样式类
        this.element.className = styleClasses.join(' ');
        
        // 设置自定义样式
        if (this.props.style) {
            Object.assign(this.element.style, this.props.style);
        }
    }

    /**
     * 绑定组件特定事件 - 绑定按钮特有的事件
     * @protected
     */
    _bindComponentEvents() {
        if (!this.element) {
            return;
        }
        
        // 绑定点击事件
        this._addEventListener(this.element, 'click', (event) => {
            this._handleClick(event);
        });
        
        // 绑定键盘事件
        this._addEventListener(this.element, 'keydown', (event) => {
            this._handleKeyDown(event);
        });
        
        // 绑定鼠标事件
        this._addEventListener(this.element, 'mousedown', (event) => {
            this._handleMouseDown(event);
        });
        
        this._addEventListener(this.element, 'mouseup', (event) => {
            this._handleMouseUp(event);
        });
        
        this._addEventListener(this.element, 'mouseleave', (event) => {
            this._handleMouseLeave(event);
        });
    }

    /**
     * 处理点击事件 - 处理按钮点击
     * @param {Event} event - 点击事件
     * @private
     */
    _handleClick(event) {
        if (!this.state.enabled || this.state.loading) {
            event.preventDefault();
            return;
        }
        
        // 触发自定义点击事件
        this.emit(UI_EVENTS.COMPONENT_CLICKED, {
            component: this,
            event,
            button: this
        });
        
        // 执行点击处理函数
        if (this.clickHandler) {
            try {
                this.clickHandler(event, this);
            } catch (error) {
                this.emit(UI_EVENTS.COMPONENT_ERROR, {
                    component: this,
                    error: error.message,
                    phase: 'click'
                });
            }
        }
    }

    /**
     * 处理键盘事件 - 处理键盘交互
     * @param {KeyboardEvent} event - 键盘事件
     * @private
     */
    _handleKeyDown(event) {
        if (!this.state.enabled || this.state.loading) {
            return;
        }
        
        // 空格键或回车键触发点击
        if (event.key === ' ' || event.key === 'Enter') {
            event.preventDefault();
            this._handleClick(event);
        }
    }

    /**
     * 处理鼠标按下 - 处理鼠标按下状态
     * @param {MouseEvent} event - 鼠标事件
     * @private
     */
    _handleMouseDown(event) {
        if (!this.state.enabled || this.state.loading) {
            return;
        }
        
        this.update({}, { pressed: true });
    }

    /**
     * 处理鼠标释放 - 处理鼠标释放状态
     * @param {MouseEvent} event - 鼠标事件
     * @private
     */
    _handleMouseUp(event) {
        this.update({}, { pressed: false });
    }

    /**
     * 处理鼠标离开 - 处理鼠标离开状态
     * @param {MouseEvent} event - 鼠标事件
     * @private
     */
    _handleMouseLeave(event) {
        this.update({}, { pressed: false });
    }

    /**
     * 设置按钮文本 - 更新按钮显示文本
     * @param {string} text - 新的按钮文本
     */
    setText(text) {
        validateNonEmpty(text, '按钮文本不能为空');
        
        this.buttonProps.text = text;
        
        if (this.element) {
            const textElement = this.element.querySelector('.btn-text');
            if (textElement) {
                textElement.textContent = text;
            }
        }
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            property: 'text',
            value: text
        });
    }

    /**
     * 设置按钮图标 - 更新按钮图标
     * @param {string} icon - 新的图标类名
     */
    setIcon(icon) {
        this.buttonProps.icon = icon;
        
        if (this.element) {
            const iconElement = this.element.querySelector('.btn-icon');
            if (iconElement) {
                iconElement.className = `btn-icon ${icon}`;
            }
        }
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            property: 'icon',
            value: icon
        });
    }

    /**
     * 设置按钮变体 - 更新按钮样式变体
     * @param {string} variant - 按钮变体
     */
    setVariant(variant) {
        this.buttonProps.variant = variant;
        this._validateButtonConfig();
        this._applyStyles();
        
        this.emit(UI_EVENTS.COMPONENT_CHANGED, {
            component: this,
            property: 'variant',
            value: variant
        });
    }

    /**
     * 设置加载状态 - 设置按钮加载状态
     * @param {boolean} loading - 是否加载中
     */
    setLoading(loading) {
        super.setLoading(loading);
        
        if (this.element) {
            const loadingElement = this.element.querySelector('.btn-loading');
            const textElement = this.element.querySelector('.btn-text');
            const iconElement = this.element.querySelector('.btn-icon');
            
            if (loading) {
                // 显示加载指示器，隐藏文本和图标
                if (loadingElement) loadingElement.style.display = 'inline-block';
                if (textElement) textElement.style.display = 'none';
                if (iconElement) iconElement.style.display = 'none';
                this.element.disabled = true;
            } else {
                // 隐藏加载指示器，显示文本和图标
                if (loadingElement) loadingElement.style.display = 'none';
                if (textElement) textElement.style.display = 'inline';
                if (iconElement) iconElement.style.display = 'inline';
                this.element.disabled = !this.state.enabled;
            }
        }
    }

    /**
     * 设置活动状态 - 设置按钮活动状态
     * @param {boolean} active - 是否活动
     */
    setActive(active) {
        this.update({}, { active });
    }

    /**
     * 模拟点击 - 程序化触发按钮点击
     */
    click() {
        if (this.element && this.state.enabled && !this.state.loading) {
            this.element.click();
        }
    }

    /**
     * 获取按钮信息 - 获取按钮的详细信息
     * @returns {Object} 按钮信息
     */
    getButtonInfo() {
        return {
            ...this._getComponentInfo(),
            buttonProps: { ...this.buttonProps },
            buttonState: {
                pressed: this.state.pressed,
                active: this.state.active
            }
        };
    }
}
// #endregion

// #region ButtonGroup 按钮组组件
/**
 * @class ButtonGroup - 按钮组组件
 * @description 管理一组相关按钮的组件
 */
export class ButtonGroup extends BaseComponent {
    /**
     * 构造函数 - 初始化按钮组
     * @param {Object} config - 按钮组配置
     * @param {Array<Object>} config.buttons - 按钮配置数组
     * @param {string} config.orientation - 方向 ('horizontal', 'vertical')
     * @param {boolean} config.toggle - 是否为切换按钮组
     * @param {string} config.selection - 选择模式 ('single', 'multiple', 'none')
     */
    constructor(config = {}) {
        super({
            type: 'button-group',
            name: config.name || 'button-group',
            ...config
        });
        
        // 按钮组配置
        this.groupConfig = {
            orientation: config.orientation || 'horizontal',
            toggle: config.toggle || false,
            selection: config.selection || 'none',
            size: config.size || 'md',
            ...config.groupConfig
        };
        
        // 按钮配置
        this.buttonConfigs = config.buttons || [];
        
        // 按钮实例
        this.buttons = [];
        this.selectedButtons = new Set();
    }

    /**
     * 渲染按钮组 - 创建按钮组DOM结构
     * @returns {Promise<HTMLElement>} 按钮组元素
     */
    async render() {
        // 创建按钮组容器
        const group = document.createElement('div');
        group.id = this.id;
        group.role = 'group';
        
        // 创建按钮实例
        await this._createButtons();
        
        // 添加按钮到容器
        for (const button of this.buttons) {
            await button.mount(group);
        }
        
        return group;
    }

    /**
     * 创建按钮实例 - 根据配置创建按钮
     * @returns {Promise<void>}
     * @private
     */
    async _createButtons() {
        this.buttons = [];
        
        for (let i = 0; i < this.buttonConfigs.length; i++) {
            const buttonConfig = {
                ...this.buttonConfigs[i],
                size: this.buttonConfigs[i].size || this.groupConfig.size,
                onClick: (event, button) => this._handleButtonClick(event, button, i)
            };
            
            const button = new Button(buttonConfig);
            this.buttons.push(button);
            this.addChild(`button-${i}`, button);
        }
    }

    /**
     * 处理按钮点击 - 处理按钮组中按钮的点击
     * @param {Event} event - 点击事件
     * @param {Button} button - 被点击的按钮
     * @param {number} index - 按钮索引
     * @private
     */
    _handleButtonClick(event, button, index) {
        if (this.groupConfig.toggle) {
            this._handleToggleClick(button, index);
        }
        
        // 触发按钮组点击事件
        this.emit(UI_EVENTS.COMPONENT_CLICKED, {
            component: this,
            event,
            button,
            index,
            selectedButtons: Array.from(this.selectedButtons)
        });
        
        // 执行原始点击处理函数
        const originalHandler = this.buttonConfigs[index].onClick;
        if (originalHandler) {
            originalHandler(event, button);
        }
    }

    /**
     * 处理切换点击 - 处理切换按钮的选择状态
     * @param {Button} button - 按钮实例
     * @param {number} index - 按钮索引
     * @private
     */
    _handleToggleClick(button, index) {
        if (this.groupConfig.selection === 'single') {
            // 单选模式：取消其他按钮选择，选择当前按钮
            this.selectedButtons.clear();
            this.buttons.forEach(btn => btn.setActive(false));
            
            this.selectedButtons.add(index);
            button.setActive(true);
            
        } else if (this.groupConfig.selection === 'multiple') {
            // 多选模式：切换当前按钮状态
            if (this.selectedButtons.has(index)) {
                this.selectedButtons.delete(index);
                button.setActive(false);
            } else {
                this.selectedButtons.add(index);
                button.setActive(true);
            }
        }
    }

    /**
     * 应用样式 - 应用按钮组样式
     * @private
     */
    _applyStyles() {
        if (!this.element) {
            return;
        }
        
        const styleClasses = [
            'smartoffice-button-group',
            'btn-group',
            this.groupConfig.orientation === 'vertical' ? 'btn-group-vertical' : '',
            this.groupConfig.toggle ? 'btn-group-toggle' : '',
            `btn-group-${this.groupConfig.size}`,
            this.props.className
        ].filter(Boolean);
        
        this.element.className = styleClasses.join(' ');
        
        if (this.props.style) {
            Object.assign(this.element.style, this.props.style);
        }
    }

    /**
     * 选择按钮 - 程序化选择按钮
     * @param {number} index - 按钮索引
     */
    selectButton(index) {
        if (index >= 0 && index < this.buttons.length) {
            const button = this.buttons[index];
            this._handleToggleClick(button, index);
        }
    }

    /**
     * 获取选中的按钮 - 获取当前选中的按钮索引
     * @returns {Array<number>} 选中按钮的索引数组
     */
    getSelectedButtons() {
        return Array.from(this.selectedButtons);
    }

    /**
     * 清除选择 - 清除所有按钮的选择状态
     */
    clearSelection() {
        this.selectedButtons.clear();
        this.buttons.forEach(btn => btn.setActive(false));
    }
}
// #endregion

// #region 工厂函数
/**
 * 创建按钮 - 工厂函数，创建按钮实例
 * @param {Object} config - 按钮配置
 * @returns {Button} 按钮实例
 */
export function createButton(config = {}) {
    return new Button(config);
}

/**
 * 创建按钮组 - 工厂函数，创建按钮组实例
 * @param {Object} config - 按钮组配置
 * @returns {ButtonGroup} 按钮组实例
 */
export function createButtonGroup(config = {}) {
    return new ButtonGroup(config);
}

/**
 * 创建预设按钮 - 创建预设样式的按钮
 * @param {string} preset - 预设名称 ('primary', 'secondary', 'success', 'danger', 'warning', 'info')
 * @param {string} text - 按钮文本
 * @param {Object} config - 额外配置
 * @returns {Button} 按钮实例
 */
export function createPresetButton(preset, text, config = {}) {
    const presetConfigs = {
        'primary': { variant: 'primary', icon: 'fas fa-check' },
        'secondary': { variant: 'secondary', icon: 'fas fa-times' },
        'success': { variant: 'success', icon: 'fas fa-check-circle' },
        'danger': { variant: 'danger', icon: 'fas fa-exclamation-triangle' },
        'warning': { variant: 'warning', icon: 'fas fa-exclamation-circle' },
        'info': { variant: 'info', icon: 'fas fa-info-circle' },
        'save': { variant: 'primary', icon: 'fas fa-save', text: '保存' },
        'cancel': { variant: 'secondary', icon: 'fas fa-times', text: '取消' },
        'delete': { variant: 'danger', icon: 'fas fa-trash', text: '删除' },
        'edit': { variant: 'info', icon: 'fas fa-edit', text: '编辑' },
        'download': { variant: 'success', icon: 'fas fa-download', text: '下载' },
        'upload': { variant: 'primary', icon: 'fas fa-upload', text: '上传' }
    };
    
    const presetConfig = presetConfigs[preset];
    if (!presetConfig) {
        throw new Error(`不支持的按钮预设: ${preset}`);
    }
    
    return createButton({
        text: text || presetConfig.text,
        ...presetConfig,
        ...config
    });
}
// #endregion 