/**
 * @file 日志管理器模块
 * <AUTHOR> Team
 * @description 
 * 从index.html中提取的日志管理功能
 * 提供统一的日志记录、性能监控、调试功能
 */

// #region 日志级别定义
/**
 * 日志级别枚举
 * @enum {number}
 */
export const LogLevel = {
    TRACE: 0,    // 跟踪级别 - 最详细的日志信息
    DEBUG: 1,    // 调试级别 - 调试信息
    INFO: 2,     // 信息级别 - 一般信息
    WARN: 3,     // 警告级别 - 警告信息
    ERROR: 4     // 错误级别 - 错误信息
};
// #endregion

// #region 日志管理器类
/**
 * @class Logger - 日志管理器
 * @description 提供统一的日志记录、性能监控、调试功能
 */
export class Logger {
    /**
     * 构造函数
     * @param {Object} config - 日志配置
     * @param {number} config.level - 日志级别
     * @param {boolean} config.enableConsole - 是否启用控制台输出
     * @param {boolean} config.enableStorage - 是否启用本地存储
     * @param {boolean} config.enablePerformance - 是否启用性能监控
     */
    constructor(config = {}) {
        this.config = {
            level: config.level || LogLevel.INFO,
            enableConsole: config.enableConsole !== false,
            enableStorage: config.enableStorage !== false,
            enablePerformance: config.enablePerformance !== false,
            maxLogs: config.maxLogs || 1000,
            flushInterval: config.flushInterval || 30000
        };
        
        this.logs = [];
        this.performanceMarks = new Map();
        this.debugCounters = new Map();
        this.isInitialized = false;
        this.flushTimer = null;
        
        this._initialize();
    }

    /**
     * 初始化日志管理器
     * @private
     */
    _initialize() {
        if (this.isInitialized) return;
        
        // 设置定时刷新
        if (this.config.enableStorage && this.config.flushInterval > 0) {
            this.flushTimer = setInterval(() => {
                this._flushLogs();
            }, this.config.flushInterval);
        }
        
        this.isInitialized = true;
        this.info('Logger', '_initialize', '日志管理器初始化完成', this.config);
    }

    /**
     * 记录跟踪级别日志
     * @param {string} module - 模块名称
     * @param {string} functionName - 函数名称
     * @param {string} message - 日志消息
     * @param {*} data - 附加数据
     */
    trace(module, functionName, message, data = null) {
        this._log(LogLevel.TRACE, module, functionName, message, data);
    }

    /**
     * 记录调试级别日志
     * @param {string} module - 模块名称
     * @param {string} functionName - 函数名称
     * @param {string} message - 日志消息
     * @param {*} data - 附加数据
     */
    debug(module, functionName, message, data = null) {
        this._log(LogLevel.DEBUG, module, functionName, message, data);
    }

    /**
     * 记录信息级别日志
     * @param {string} module - 模块名称
     * @param {string} functionName - 函数名称
     * @param {string} message - 日志消息
     * @param {*} data - 附加数据
     */
    info(module, functionName, message, data = null) {
        this._log(LogLevel.INFO, module, functionName, message, data);
    }

    /**
     * 记录警告级别日志
     * @param {string} module - 模块名称
     * @param {string} functionName - 函数名称
     * @param {string} message - 日志消息
     * @param {*} data - 附加数据
     */
    warn(module, functionName, message, data = null) {
        this._log(LogLevel.WARN, module, functionName, message, data);
    }

    /**
     * 记录错误级别日志
     * @param {string} module - 模块名称
     * @param {string} functionName - 函数名称
     * @param {string} message - 日志消息
     * @param {*} data - 附加数据
     */
    error(module, functionName, message, data = null) {
        this._log(LogLevel.ERROR, module, functionName, message, data);
    }

    /**
     * 开始性能标记
     * @param {string} markName - 标记名称
     * @param {string} module - 模块名称
     * @param {string} functionName - 函数名称
     */
    startPerformanceMark(markName, module, functionName) {
        if (!this.config.enablePerformance) return;
        
        const startTime = performance.now();
        this.performanceMarks.set(markName, {
            startTime,
            module,
            functionName
        });
        
        this.trace(module, functionName, `性能标记开始: ${markName}`);
    }

    /**
     * 结束性能标记
     * @param {string} markName - 标记名称
     * @param {string} module - 模块名称
     * @param {string} functionName - 函数名称
     * @returns {number} 执行时间（毫秒）
     */
    endPerformanceMark(markName, module, functionName) {
        if (!this.config.enablePerformance) return null;
        
        const mark = this.performanceMarks.get(markName);
        if (!mark) {
            this.warn(module, functionName, `性能标记未找到: ${markName}`);
            return null;
        }
        
        const endTime = performance.now();
        const duration = endTime - mark.startTime;
        
        this.performanceMarks.delete(markName);
        this.debug(module, functionName, `性能标记完成: ${markName}`, {
            duration: `${duration.toFixed(2)}ms`
        });
        
        return duration;
    }

    /**
     * 增加计数器
     * @param {string} counterName - 计数器名称
     * @param {string} module - 模块名称
     */
    incrementCounter(counterName, module) {
        const key = `${module}.${counterName}`;
        const current = this.debugCounters.get(key) || 0;
        this.debugCounters.set(key, current + 1);
        
        this.trace(module, 'incrementCounter', `计数器递增: ${counterName}`, {
            count: current + 1
        });
    }

    /**
     * 获取计数器值
     * @param {string} counterName - 计数器名称
     * @param {string} module - 模块名称
     * @returns {number} 计数器值
     */
    getCounter(counterName, module) {
        const key = `${module}.${counterName}`;
        return this.debugCounters.get(key) || 0;
    }

    /**
     * 内部日志记录方法
     * @private
     * @param {number} level - 日志级别
     * @param {string} module - 模块名称
     * @param {string} functionName - 函数名称
     * @param {string} message - 日志消息
     * @param {*} data - 附加数据
     */
    _log(level, module, functionName, message, data = null) {
        if (level < this.config.level) return;
        
        const logEntry = {
            timestamp: Date.now(),
            level,
            module,
            function: functionName,
            message,
            data
        };
        
        // 添加到日志数组
        this.logs.push(logEntry);
        
        // 限制日志数量
        if (this.logs.length > this.config.maxLogs) {
            this.logs.shift();
        }
        
        // 控制台输出
        if (this.config.enableConsole) {
            this._outputToConsole(logEntry);
        }
    }

    /**
     * 输出到控制台
     * @private
     * @param {Object} logEntry - 日志条目
     */
    _outputToConsole(logEntry) {
        const levelNames = ['TRACE', 'DEBUG', 'INFO', 'WARN', 'ERROR'];
        const levelName = levelNames[logEntry.level];
        const timestamp = new Date(logEntry.timestamp).toLocaleTimeString();
        
        const prefix = `[${timestamp}] ${levelName} ${logEntry.module}.${logEntry.function || 'unknown'}():`;
        const message = logEntry.message;
        
        switch (logEntry.level) {
            case LogLevel.TRACE:
            case LogLevel.DEBUG:
                console.debug(prefix, message, logEntry.data || '');
                break;
            case LogLevel.INFO:
                console.info(prefix, message, logEntry.data || '');
                break;
            case LogLevel.WARN:
                console.warn(prefix, message, logEntry.data || '');
                break;
            case LogLevel.ERROR:
                console.error(prefix, message, logEntry.data || '');
                break;
        }
    }

    /**
     * 刷新日志到本地存储
     * @private
     */
    _flushLogs() {
        if (!this.config.enableStorage || this.logs.length === 0) return;
        
        try {
            const logsToStore = this.logs.slice(-100); // 只存储最近100条
            localStorage.setItem('smartoffice_logs', JSON.stringify(logsToStore));
        } catch (error) {
            console.warn('日志存储失败:', error);
        }
    }

    /**
     * 获取格式化的日志文本
     * @returns {string} 格式化的日志文本
     */
    getFormattedLogs() {
        const levelNames = ['TRACE', 'DEBUG', 'INFO', 'WARN', 'ERROR'];
        
        return this.logs.map(log => {
            const timestamp = new Date(log.timestamp).toLocaleString();
            const dataStr = log.data ? ` | Data: ${JSON.stringify(log.data)}` : '';
            return `[${timestamp}] ${levelNames[log.level]} ${log.module}.${log.function || 'unknown'}(): ${log.message}${dataStr}`;
        }).join('\n');
    }

    /**
     * 销毁日志管理器
     */
    destroy() {
        if (this.flushTimer) {
            clearInterval(this.flushTimer);
            this.flushTimer = null;
        }
        
        this._flushLogs();
        this.logs = [];
        this.performanceMarks.clear();
        this.debugCounters.clear();
        this.isInitialized = false;
    }
}
// #endregion

// #region 全局日志实例
let globalLogger = null;

/**
 * 创建日志管理器实例
 * @param {Object} config - 配置对象
 * @returns {Logger} 日志管理器实例
 */
export function createLogger(config = {}) {
    return new Logger(config);
}

/**
 * 获取全局日志管理器实例
 * @returns {Logger} 全局日志管理器实例
 */
export function getLogger() {
    if (!globalLogger) {
        globalLogger = new Logger({
            level: LogLevel.DEBUG,
            enableConsole: true,
            enableStorage: true,
            enablePerformance: true
        });
    }
    return globalLogger;
}

/**
 * 便捷的信息日志函数
 * @param {string} module - 模块名称
 * @param {string} functionName - 函数名称
 * @param {string} message - 日志消息
 * @param {*} data - 附加数据
 */
export function logInfo(module, functionName, message, data = null) {
    getLogger().info(module, functionName, message, data);
}

/**
 * 便捷的调试日志函数
 * @param {string} module - 模块名称
 * @param {string} functionName - 函数名称
 * @param {string} message - 日志消息
 * @param {*} data - 附加数据
 */
export function logDebug(module, functionName, message, data = null) {
    getLogger().debug(module, functionName, message, data);
}

/**
 * 便捷的错误日志函数
 * @param {string} module - 模块名称
 * @param {string} functionName - 函数名称
 * @param {string} message - 日志消息
 * @param {*} data - 附加数据
 */
export function logError(module, functionName, message, data = null) {
    getLogger().error(module, functionName, message, data);
}
// #endregion
