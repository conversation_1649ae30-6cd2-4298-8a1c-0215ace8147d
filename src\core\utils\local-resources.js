/**
 * @file 本地资源管理器
 * @description 管理CDN资源的本地版本，实现完全离线运行
 * - 管理CSS、JS、字体等静态资源
 * - 提供资源加载和缓存机制
 * - 支持资源版本管理
 * <AUTHOR> Team
 */

import { getLogger } from './logger.js';

/**
 * @class LocalResourceManager
 * @description 本地资源管理器类
 * 负责管理所有本地化的CDN资源
 */
export class LocalResourceManager {
    constructor(config = {}) {
        this.logger = getLogger();
        this.logger.info('LocalResourceManager', 'constructor', '📦 初始化本地资源管理器');
        
        this.config = {
            resourcePath: config.resourcePath || './assets/libs/',
            enableCaching: config.enableCaching !== false,
            cacheExpiry: config.cacheExpiry || 24 * 60 * 60 * 1000, // 24小时
            ...config
        };
        
        // 资源映射表
        this.resourceMap = this._initializeResourceMap();
        
        // 加载状态跟踪
        this.loadedResources = new Set();
        this.loadingPromises = new Map();
        
        // 缓存管理
        this.cache = new Map();
        
        this.logger.info('LocalResourceManager', 'constructor', '✅ 本地资源管理器初始化完成');
    }

    /**
     * @function _initializeResourceMap
     * @description 初始化资源映射表
     * @returns {Object} 资源映射配置
     */
    _initializeResourceMap() {
        return {
            // CSS 资源
            css: {
                'tailwindcss': {
                    url: 'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
                    local: 'css/tailwind.min.css',
                    version: '2.2.19',
                    integrity: 'sha384-...',
                    fallback: this._getTailwindFallback()
                },
                'fontawesome': {
                    url: 'https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css',
                    local: 'css/fontawesome.min.css',
                    version: '6.4.0',
                    integrity: 'sha384-...',
                    fallback: this._getFontAwesomeFallback()
                },
                'google-fonts': {
                    url: 'https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&family=Roboto:wght@300;400;500;700&display=swap',
                    local: 'css/google-fonts.css',
                    version: '1.0.0',
                    fallback: this._getGoogleFontsFallback()
                }
            },
            
            // JavaScript 资源
            js: {
                'html2canvas': {
                    url: 'https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js',
                    local: 'js/html2canvas.min.js',
                    version: '1.4.1',
                    integrity: 'sha384-...',
                    fallback: this._getHtml2CanvasFallback()
                },
                'jspdf': {
                    url: 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js',
                    local: 'js/jspdf.umd.min.js',
                    version: '2.5.1',
                    integrity: 'sha384-...',
                    fallback: this._getJsPDFFallback()
                }
            },
            
            // 字体资源
            fonts: {
                'noto-sans-sc': {
                    local: 'fonts/noto-sans-sc/',
                    formats: ['woff2', 'woff', 'ttf']
                },
                'roboto': {
                    local: 'fonts/roboto/',
                    formats: ['woff2', 'woff', 'ttf']
                }
            }
        };
    }

    /**
     * @function loadResource
     * @description 加载指定资源
     * @param {string} type - 资源类型 (css/js/fonts)
     * @param {string} name - 资源名称
     * @param {Object} options - 加载选项
     * @returns {Promise} 加载Promise
     */
    async loadResource(type, name, options = {}) {
        const resourceKey = `${type}:${name}`;
        
        // 如果已经加载，直接返回
        if (this.loadedResources.has(resourceKey)) {
            this.logger.debug('LocalResourceManager', 'loadResource', '资源已加载', { type, name });
            return true;
        }
        
        // 如果正在加载，返回现有Promise
        if (this.loadingPromises.has(resourceKey)) {
            this.logger.debug('LocalResourceManager', 'loadResource', '资源加载中', { type, name });
            return this.loadingPromises.get(resourceKey);
        }
        
        // 开始加载
        const loadPromise = this._doLoadResource(type, name, options);
        this.loadingPromises.set(resourceKey, loadPromise);
        
        try {
            const result = await loadPromise;
            this.loadedResources.add(resourceKey);
            this.loadingPromises.delete(resourceKey);
            
            this.logger.info('LocalResourceManager', 'loadResource', '✅ 资源加载成功', { type, name });
            return result;
        } catch (error) {
            this.loadingPromises.delete(resourceKey);
            this.logger.error('LocalResourceManager', 'loadResource', '资源加载失败', { type, name, error });
            throw error;
        }
    }

    /**
     * @function _doLoadResource
     * @description 执行资源加载
     * @param {string} type - 资源类型
     * @param {string} name - 资源名称
     * @param {Object} options - 加载选项
     * @returns {Promise} 加载Promise
     */
    async _doLoadResource(type, name, options) {
        const resource = this.resourceMap[type]?.[name];
        if (!resource) {
            throw new Error(`未找到资源: ${type}:${name}`);
        }
        
        switch (type) {
            case 'css':
                return this._loadCSS(resource, options);
            case 'js':
                return this._loadJS(resource, options);
            case 'fonts':
                return this._loadFonts(resource, options);
            default:
                throw new Error(`不支持的资源类型: ${type}`);
        }
    }

    /**
     * @function _loadCSS
     * @description 加载CSS资源
     * @param {Object} resource - 资源配置
     * @param {Object} options - 加载选项
     * @returns {Promise} 加载Promise
     */
    async _loadCSS(resource, options) {
        return new Promise((resolve, reject) => {
            // 检查是否已存在
            const existingLink = document.querySelector(`link[href*="${resource.local}"]`);
            if (existingLink) {
                resolve(true);
                return;
            }
            
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.type = 'text/css';
            
            // 优先使用本地资源
            const resourcePath = this.config.resourcePath + resource.local;
            link.href = resourcePath;
            
            link.onload = () => {
                this.logger.debug('LocalResourceManager', '_loadCSS', 'CSS加载成功', { href: link.href });
                resolve(true);
            };
            
            link.onerror = () => {
                this.logger.warn('LocalResourceManager', '_loadCSS', '本地CSS加载失败，尝试fallback', { href: link.href });
                
                // 使用fallback
                if (resource.fallback) {
                    this._injectFallbackCSS(resource.fallback);
                    resolve(true);
                } else {
                    reject(new Error(`CSS资源加载失败: ${resource.local}`));
                }
            };
            
            document.head.appendChild(link);
        });
    }

    /**
     * @function _loadJS
     * @description 加载JavaScript资源
     * @param {Object} resource - 资源配置
     * @param {Object} options - 加载选项
     * @returns {Promise} 加载Promise
     */
    async _loadJS(resource, options) {
        return new Promise((resolve, reject) => {
            // 检查是否已存在
            const existingScript = document.querySelector(`script[src*="${resource.local}"]`);
            if (existingScript) {
                resolve(true);
                return;
            }
            
            const script = document.createElement('script');
            script.type = 'text/javascript';
            script.async = options.async !== false;
            
            // 优先使用本地资源
            const resourcePath = this.config.resourcePath + resource.local;
            script.src = resourcePath;
            
            script.onload = () => {
                this.logger.debug('LocalResourceManager', '_loadJS', 'JS加载成功', { src: script.src });
                resolve(true);
            };
            
            script.onerror = () => {
                this.logger.warn('LocalResourceManager', '_loadJS', '本地JS加载失败，尝试fallback', { src: script.src });
                
                // 使用fallback
                if (resource.fallback) {
                    this._injectFallbackJS(resource.fallback);
                    resolve(true);
                } else {
                    reject(new Error(`JS资源加载失败: ${resource.local}`));
                }
            };
            
            document.head.appendChild(script);
        });
    }

    /**
     * @function _loadFonts
     * @description 加载字体资源
     * @param {Object} resource - 资源配置
     * @param {Object} options - 加载选项
     * @returns {Promise} 加载Promise
     */
    async _loadFonts(resource, options) {
        // 字体通过CSS @font-face规则加载
        const fontCSS = this._generateFontCSS(resource);
        this._injectFallbackCSS(fontCSS);
        return true;
    }

    /**
     * @function _injectFallbackCSS
     * @description 注入fallback CSS
     * @param {string} css - CSS内容
     */
    _injectFallbackCSS(css) {
        const style = document.createElement('style');
        style.type = 'text/css';
        style.textContent = css;
        document.head.appendChild(style);
    }

    /**
     * @function _injectFallbackJS
     * @description 注入fallback JavaScript
     * @param {string} js - JavaScript内容
     */
    _injectFallbackJS(js) {
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.textContent = js;
        document.head.appendChild(script);
    }

    /**
     * @function loadAllResources
     * @description 加载所有必需资源
     * @param {Array} resourceList - 资源列表
     * @returns {Promise} 加载Promise
     */
    async loadAllResources(resourceList = null) {
        const defaultResources = [
            { type: 'css', name: 'tailwindcss' },
            { type: 'css', name: 'fontawesome' },
            { type: 'css', name: 'google-fonts' },
            { type: 'js', name: 'html2canvas' },
            { type: 'js', name: 'jspdf' }
        ];
        
        const resourcesToLoad = resourceList || defaultResources;
        
        this.logger.info('LocalResourceManager', 'loadAllResources', '🚀 开始加载所有资源', {
            count: resourcesToLoad.length
        });
        
        const loadPromises = resourcesToLoad.map(({ type, name, options }) => 
            this.loadResource(type, name, options)
        );
        
        try {
            await Promise.all(loadPromises);
            this.logger.info('LocalResourceManager', 'loadAllResources', '✅ 所有资源加载完成');
            return true;
        } catch (error) {
            this.logger.error('LocalResourceManager', 'loadAllResources', '部分资源加载失败', { error });
            throw error;
        }
    }

    /**
     * @function _getTailwindFallback
     * @description 获取Tailwind CSS的fallback样式
     * @returns {string} CSS内容
     */
    _getTailwindFallback() {
        return `
            /* Tailwind CSS Fallback - 基础样式 */
            .container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
            .flex { display: flex; }
            .flex-col { flex-direction: column; }
            .items-center { align-items: center; }
            .justify-between { justify-content: space-between; }
            .w-full { width: 100%; }
            .h-full { height: 100%; }
            .p-4 { padding: 1rem; }
            .m-4 { margin: 1rem; }
            .text-center { text-align: center; }
            .bg-blue-600 { background-color: #2563eb; }
            .text-white { color: white; }
            .rounded { border-radius: 0.25rem; }
            .shadow { box-shadow: 0 1px 3px rgba(0,0,0,0.12); }
            .border { border: 1px solid #e5e7eb; }
            .hidden { display: none; }
            .block { display: block; }
            .inline-block { display: inline-block; }
            .relative { position: relative; }
            .absolute { position: absolute; }
            .fixed { position: fixed; }
            .top-0 { top: 0; }
            .left-0 { left: 0; }
            .right-0 { right: 0; }
            .bottom-0 { bottom: 0; }
            .z-10 { z-index: 10; }
            .z-50 { z-index: 50; }
            .cursor-pointer { cursor: pointer; }
            .select-none { user-select: none; }
            .overflow-hidden { overflow: hidden; }
            .overflow-auto { overflow: auto; }
            .transition { transition: all 0.15s ease-in-out; }
            .hover\\:bg-blue-700:hover { background-color: #1d4ed8; }
            .focus\\:outline-none:focus { outline: none; }
            .focus\\:ring-2:focus { box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5); }
        `;
    }

    /**
     * @function _getFontAwesomeFallback
     * @description 获取Font Awesome的fallback样式
     * @returns {string} CSS内容
     */
    _getFontAwesomeFallback() {
        return `
            /* Font Awesome Fallback - 基础图标 */
            .fas, .far, .fab, .fa { 
                font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands", sans-serif;
                font-weight: 900;
                display: inline-block;
                font-style: normal;
                font-variant: normal;
                text-rendering: auto;
                line-height: 1;
            }
            .fa-magic::before { content: "✨"; }
            .fa-download::before { content: "⬇"; }
            .fa-print::before { content: "🖨"; }
            .fa-eye::before { content: "👁"; }
            .fa-edit::before { content: "✏"; }
            .fa-trash::before { content: "🗑"; }
            .fa-plus::before { content: "+"; }
            .fa-minus::before { content: "-"; }
            .fa-times::before { content: "×"; }
            .fa-check::before { content: "✓"; }
            .fa-chevron-down::before { content: "▼"; }
            .fa-chevron-up::before { content: "▲"; }
            .fa-chevron-left::before { content: "◀"; }
            .fa-chevron-right::before { content: "▶"; }
            .fa-file-image::before { content: "🖼"; }
            .fa-cog::before { content: "⚙"; }
            .fa-info-circle::before { content: "ℹ"; }
            .fa-exclamation-triangle::before { content: "⚠"; }
            .fa-times-circle::before { content: "❌"; }
            .fa-check-circle::before { content: "✅"; }
        `;
    }

    /**
     * @function _getGoogleFontsFallback
     * @description 获取Google Fonts的fallback样式
     * @returns {string} CSS内容
     */
    _getGoogleFontsFallback() {
        return `
            /* Google Fonts Fallback */
            body, .font-noto {
                font-family: "Noto Sans SC", "Microsoft YaHei", "微软雅黑", "SimHei", "黑体", sans-serif;
            }
            .font-roboto {
                font-family: "Roboto", "Arial", "Helvetica", sans-serif;
            }
            /* 字重定义 */
            .font-light { font-weight: 300; }
            .font-normal { font-weight: 400; }
            .font-medium { font-weight: 500; }
            .font-bold { font-weight: 700; }
        `;
    }

    /**
     * @function _getHtml2CanvasFallback
     * @description 获取html2canvas的fallback实现
     * @returns {string} JavaScript内容
     */
    _getHtml2CanvasFallback() {
        return `
            // html2canvas Fallback - 简化实现
            window.html2canvas = function(element, options = {}) {
                return new Promise((resolve, reject) => {
                    try {
                        // 创建一个简单的canvas
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        
                        // 设置canvas尺寸
                        const rect = element.getBoundingClientRect();
                        canvas.width = rect.width;
                        canvas.height = rect.height;
                        
                        // 简单的文本渲染
                        ctx.fillStyle = '#ffffff';
                        ctx.fillRect(0, 0, canvas.width, canvas.height);
                        ctx.fillStyle = '#000000';
                        ctx.font = '16px Arial';
                        ctx.fillText('文档预览 (简化版)', 20, 30);
                        ctx.fillText('请安装完整版html2canvas以获得最佳效果', 20, 60);
                        
                        resolve(canvas);
                    } catch (error) {
                        reject(error);
                    }
                });
            };
        `;
    }

    /**
     * @function _getJsPDFFallback
     * @description 获取jsPDF的fallback实现
     * @returns {string} JavaScript内容
     */
    _getJsPDFFallback() {
        return `
            // jsPDF Fallback - 简化实现
            window.jsPDF = function(options = {}) {
                return {
                    addImage: function(imageData, format, x, y, width, height) {
                        console.log('jsPDF Fallback: addImage called');
                    },
                    save: function(filename) {
                        // 创建一个简单的文本文件作为fallback
                        const content = 'SmartOffice 文档\\n\\n这是一个简化的PDF导出。\\n请安装完整版jsPDF以获得最佳效果。';
                        const blob = new Blob([content], { type: 'text/plain' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = filename.replace('.pdf', '.txt');
                        a.click();
                        URL.revokeObjectURL(url);
                    }
                };
            };
        `;
    }

    /**
     * @function _generateFontCSS
     * @description 生成字体CSS
     * @param {Object} resource - 字体资源配置
     * @returns {string} CSS内容
     */
    _generateFontCSS(resource) {
        // 简化的字体CSS生成
        return `
            @font-face {
                font-family: "${resource.name}";
                src: url("${this.config.resourcePath}${resource.local}") format("woff2");
                font-display: swap;
            }
        `;
    }

    /**
     * @function getResourceStatus
     * @description 获取资源加载状态
     * @returns {Object} 状态信息
     */
    getResourceStatus() {
        return {
            loaded: Array.from(this.loadedResources),
            loading: Array.from(this.loadingPromises.keys()),
            cached: this.cache.size,
            config: this.config
        };
    }

    /**
     * @function clearCache
     * @description 清除资源缓存
     */
    clearCache() {
        this.cache.clear();
        this.logger.info('LocalResourceManager', 'clearCache', '🗑️ 资源缓存已清除');
    }

    /**
     * @function destroy
     * @description 销毁资源管理器
     */
    destroy() {
        this.clearCache();
        this.loadedResources.clear();
        this.loadingPromises.clear();
        this.logger.info('LocalResourceManager', 'destroy', '🔄 本地资源管理器已销毁');
    }
}

// 单例实例
let resourceManagerInstance = null;

/**
 * @function createLocalResourceManager
 * @description 创建本地资源管理器实例
 * @param {Object} config - 配置选项
 * @returns {LocalResourceManager} 资源管理器实例
 */
export function createLocalResourceManager(config = {}) {
    if (!resourceManagerInstance) {
        resourceManagerInstance = new LocalResourceManager(config);
    }
    return resourceManagerInstance;
}

/**
 * @function getLocalResourceManager
 * @description 获取本地资源管理器实例
 * @returns {LocalResourceManager} 资源管理器实例
 */
export function getLocalResourceManager() {
    return createLocalResourceManager();
}

/**
 * @function loadOfflineResources
 * @description 快速加载离线资源
 * @param {Array} resourceList - 资源列表
 * @returns {Promise} 加载Promise
 */
export async function loadOfflineResources(resourceList = null) {
    const manager = getLocalResourceManager();
    return manager.loadAllResources(resourceList);
}

// 默认导出
export default LocalResourceManager; 